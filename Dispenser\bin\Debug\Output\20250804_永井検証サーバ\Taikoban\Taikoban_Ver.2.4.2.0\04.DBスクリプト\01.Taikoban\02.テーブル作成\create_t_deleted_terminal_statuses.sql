USE [$(Param1)]
GO
/****** Object:  Table [dbo].[t_deleted_terminal_statuses]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_deleted_terminal_statuses]') AND type in (N'U'))
DROP TABLE [dbo].[t_deleted_terminal_statuses]
GO

CREATE TABLE [dbo].[t_deleted_terminal_statuses](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[deletion_datetime] [datetime2](7) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[modification_datetime] [datetime2](7) NOT NULL,
	[terminal_name] [nvarchar](255) NOT NULL,
	[ip_address] [nvarchar](255) NOT NULL,
	[windows_session] [int] NOT NULL,
	[windows_session_change_datetime] [datetime2](7) NOT NULL,
	[authentication] [int] NOT NULL,
	[authenticated_logon_id] [nvarchar](255) NOT NULL,
	[os_version] [nvarchar](255) NOT NULL,
	[installed_package_version] [nvarchar](255) NOT NULL,
	[package_installation_datetime] [datetime2](7) NOT NULL,
	[package_installation_status] [bit] NOT NULL,
	[authentication_enabled] [bit] NOT NULL,
	[connection_status] [bit] NOT NULL,
 CONSTRAINT [PK_t_deleted_terminal_statuses] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_deleted_terminal_statuses] ADD  CONSTRAINT [DF_t_deleted_terminal_statuses_deletion_datetime]  DEFAULT (getdate()) FOR [deletion_datetime]
GO
ALTER TABLE [dbo].[t_deleted_terminal_statuses] ADD  CONSTRAINT [DF_t_deleted_terminal_statuses_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO
ALTER TABLE [dbo].[t_deleted_terminal_statuses] ADD  CONSTRAINT [DF_t_deleted_terminal_statuses_modification_datetime]  DEFAULT (getdate()) FOR [modification_datetime]
GO
ALTER TABLE [dbo].[t_deleted_terminal_statuses] ADD  CONSTRAINT [DF_t_deleted_terminal_statuses_terminal_name]  DEFAULT ('') FOR [terminal_name]
GO
ALTER TABLE [dbo].[t_deleted_terminal_statuses] ADD  CONSTRAINT [DF_t_deleted_terminal_statuses_ip_address]  DEFAULT ('') FOR [ip_address]
GO
ALTER TABLE [dbo].[t_deleted_terminal_statuses] ADD  CONSTRAINT [DF_t_deleted_terminal_statuses_windows_session]  DEFAULT ((0)) FOR [windows_session]
GO
ALTER TABLE [dbo].[t_deleted_terminal_statuses] ADD  CONSTRAINT [DF_t_deleted_terminal_statuses_windows_session_change_datetime]  DEFAULT (getdate()) FOR [windows_session_change_datetime]
GO
ALTER TABLE [dbo].[t_deleted_terminal_statuses] ADD  CONSTRAINT [DF_t_deleted_terminal_statuses_authentication]  DEFAULT ((0)) FOR [authentication]
GO
ALTER TABLE [dbo].[t_deleted_terminal_statuses] ADD  CONSTRAINT [DF_t_deleted_terminal_statuses_authenticated_logon_id]  DEFAULT ('') FOR [authenticated_logon_id]
GO
ALTER TABLE [dbo].[t_deleted_terminal_statuses] ADD  CONSTRAINT [DF_t_deleted_terminal_statuses_os_version]  DEFAULT ('') FOR [os_version]
GO
ALTER TABLE [dbo].[t_deleted_terminal_statuses] ADD  CONSTRAINT [DF_t_deleted_terminal_statuses_installed_package_version]  DEFAULT ('') FOR [installed_package_version]
GO
ALTER TABLE [dbo].[t_deleted_terminal_statuses] ADD  CONSTRAINT [DF_t_deleted_terminal_statuses_package_installation_datetime]  DEFAULT (getdate()) FOR [package_installation_datetime]
GO
ALTER TABLE [dbo].[t_deleted_terminal_statuses] ADD  CONSTRAINT [DF_t_deleted_terminal_statuses_package_installation_status]  DEFAULT ((1)) FOR [package_installation_status]
GO
ALTER TABLE [dbo].[t_deleted_terminal_statuses] ADD  CONSTRAINT [DF_t_deleted_terminal_statuses_authentication_enabled]  DEFAULT ((1)) FOR [authentication_enabled]
GO
ALTER TABLE [dbo].[t_deleted_terminal_statuses] ADD  CONSTRAINT [DF__t_deleted__conne__7B5B524B]  DEFAULT ('false') FOR [connection_status]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_deleted_terminal_statuses', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'削除日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_deleted_terminal_statuses', @level2type=N'COLUMN',@level2name=N'deletion_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_deleted_terminal_statuses', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_deleted_terminal_statuses', @level2type=N'COLUMN',@level2name=N'modification_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'端末名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_deleted_terminal_statuses', @level2type=N'COLUMN',@level2name=N'terminal_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'IPアドレス' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_deleted_terminal_statuses', @level2type=N'COLUMN',@level2name=N'ip_address'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Windowsセッション状態 0:ログオフ 1:ロック 2:ログオン' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_deleted_terminal_statuses', @level2type=N'COLUMN',@level2name=N'windows_session'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Windowsセッション状態変化日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_deleted_terminal_statuses', @level2type=N'COLUMN',@level2name=N'windows_session_change_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'認証状態 0:未認証 1:認証済み' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_deleted_terminal_statuses', @level2type=N'COLUMN',@level2name=N'authentication'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'認証されたユーザーのログオンID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_deleted_terminal_statuses', @level2type=N'COLUMN',@level2name=N'authenticated_logon_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'OSのバージョン' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_deleted_terminal_statuses', @level2type=N'COLUMN',@level2name=N'os_version'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'システムのバージョン' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_deleted_terminal_statuses', @level2type=N'COLUMN',@level2name=N'installed_package_version'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'システムの更新日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_deleted_terminal_statuses', @level2type=N'COLUMN',@level2name=N'package_installation_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'システムの更新成否 0:失敗 1:成功' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_deleted_terminal_statuses', @level2type=N'COLUMN',@level2name=N'package_installation_status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'システム利用 0:無効 1:有効' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_deleted_terminal_statuses', @level2type=N'COLUMN',@level2name=N'authentication_enabled'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'端末の接続情報 0:オフライン 1:オンライン' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_deleted_terminal_statuses', @level2type=N'COLUMN',@level2name=N'connection_status'
GO
