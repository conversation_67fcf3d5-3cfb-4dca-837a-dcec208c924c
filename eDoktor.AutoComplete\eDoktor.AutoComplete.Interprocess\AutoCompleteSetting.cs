﻿// AutoCompleteSetting.cs

namespace eDoktor.AutoComplete.Interprocess
{
    using Common.ByteArrayExtensions;

    public class AutoCompleteSetting
    {
        #region Properties
        public string Name { get; set; }
        public int SystemKey { get; set; }
        public string SecurityKey { get; set; }
        public string ProcessName { get; set; }
        public string TargetWindowClassName { get; set; }
        public string TargetWindowCaption { get; set; }
        public int UserElementIndex { get; set; }
        public string UserElementAutomationId { get; set; }
        public string UserElementClassName { get; set; }
        public string UserElementCaption { get; set; }
        public int PasswordElementIndex { get; set; }
        public string PasswordElementAutomationId { get; set; }
        public string PasswordElementClassName { get; set; }
        public string PasswordElementCaption { get; set; }
        public bool PasswordElementIsPassword { get; set; }
        // ▼ ADD パスワード入力方式追加対応 2024/08/07 eDoktor Y.Kihara
        public PasswordInputMethodType PasswordInputMethod { get; set; }
        public int PasswordFocusingWait { get; set; }
        public int PasswordInputInterval { get; set; }
        // ▲ ADD パスワード入力方式追加対応 2024/08/07 eDoktor Y.Kihara
        public int ButtonElementIndex { get; set; }
        public string ButtonElementAutomationId { get; set; }
        public string ButtonElementClassName { get; set; }
        public string ButtonElementCaption { get; set; }
        public ButtonClickMethodType ButtonClickMethod { get; set; }
        public int ButtonClickKeyCode { get; set; }
        public int ButtonClickX { get; set; }
        public int ButtonClickY { get; set; }
        public ActionOnExitType ActionOnExit { get; set; }
        // ▼ ADD ログインボタンを自動押下しない対応 2023/01/10 eDoktor Y.Kihara
        public bool EnableDonotAutoLoginCheckbox { get; set; }
        public string DonotAutoLoginCheckboxMessage { get; set; }
        // ▲ ADD ログインボタンを自動押下しない対応 2023/01/10 eDoktor Y.Kihara
        // ▼ ADD 認証開始ボタンの表示設定追加対応 2023/03/31 eDoktor Y.Kihara
        public bool NotAllowedOneFactorAuthentication { get; set; }
        // ▲ ADD 認証開始ボタンの表示設定追加対応 2023/03/31 eDoktor Y.Kihara
        // ▼ ADD 閉じるボタンの表示設定追加対応 2024/07/22 eDoktor Y.Kihara
        public bool HiddenCloseButton { get; set; }
        // ▲ ADD 閉じるボタンの表示設定追加対応 2024/07/22 eDoktor Y.Kihara
        public bool Variable { get; set; }
        #endregion
    }

    // ▼ ADD パスワード入力方式追加対応 2024/08/07 eDoktor Y.Kihara
    public enum PasswordInputMethodType
    {
        Automation,
        Key,
    }
    // ▲ ADD パスワード入力方式追加対応 2024/08/07 eDoktor Y.Kihara

    public enum ButtonClickMethodType
    {
        Automation,
        Key,
        Click
    }

    public enum ActionOnExitType
    {
        None,
        CloseMainWindow,
        KillProcess
    }

    public class AutoCompleteInfo
    {
        #region Properties
        public AutoCompleteSetting[] AutoCompleteSettings { get; set; }
        #endregion

        #region Public Methods
        public override bool Equals(object obj)
        {
            if (obj is AutoCompleteInfo)
            {
                byte[] serializedT1 = Common.Serializer<AutoCompleteInfo>.Serialize(this);
                byte[] serializedT2 = Common.Serializer<AutoCompleteInfo>.Serialize(obj as AutoCompleteInfo);

                return serializedT1.BinaryEquals(serializedT2);
            }

            return base.Equals(obj);
        }

        public override int GetHashCode()
        {
            return base.GetHashCode();
        }
        #endregion
    }
}
