USE [$(Param1)]
GO
/****** Object:  Table [dbo].[t_autocomplete_settings]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_autocomplete_settings]') AND type in (N'U'))
DROP TABLE [dbo].[t_autocomplete_settings]
GO

CREATE TABLE [dbo].[t_autocomplete_settings](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[registered_by] [int] NOT NULL,
	[modification_datetime] [datetime2](7) NOT NULL,
	[modified_by] [int] NOT NULL,
	[invalidated] [bit] NOT NULL,
	[invalidation_datetime] [datetime2](7) NULL,
	[invalidated_by] [int] NOT NULL,
	[logically_deleted] [bit] NOT NULL,
	[logical_deletion_datetime] [datetime2](7) NULL,
	[logically_deleted_by] [int] NOT NULL,
	[application_id] [int] NOT NULL,
	[process_name] [nvarchar](255) NOT NULL,
	[target_window_class_name] [nvarchar](255) NOT NULL,
	[target_window_caption] [nvarchar](255) NOT NULL,
	[user_element_index] [int] NOT NULL,
	[user_element_automation_id] [nvarchar](255) NOT NULL,
	[user_element_class_name] [nvarchar](255) NOT NULL,
	[user_element_caption] [nvarchar](255) NOT NULL,
	[password_element_index] [int] NOT NULL,
	[password_element_automation_id] [nvarchar](255) NOT NULL,
	[password_element_class_name] [nvarchar](255) NOT NULL,
	[password_element_caption] [nvarchar](255) NOT NULL,
	[password_element_is_password] [bit] NOT NULL,
	[password_input_method] [int] NOT NULL,
	[password_focusing_wait] [int] NOT NULL,
	[password_input_interval] [int] NOT NULL,
	[button_element_index] [int] NOT NULL,
	[button_element_automation_id] [nvarchar](255) NOT NULL,
	[button_element_class_name] [nvarchar](255) NOT NULL,
	[button_element_caption] [nvarchar](255) NOT NULL,
	[button_click_method] [int] NOT NULL,
	[button_click_key_code] [int] NOT NULL,
	[button_click_x] [int] NOT NULL,
	[button_click_y] [int] NOT NULL,
	[action_on_exit] [int] NOT NULL,
	[enable_donot_auto_login_checkbox] [bit] NOT NULL,
	[donot_auto_login_checkbox_message] [nvarchar](255) NOT NULL,
	[not_allowed_one_factor_authentication] [bit] NOT NULL,
	[hidden_close_button] [bit] NOT NULL,
	[variable] [bit] NOT NULL
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_registered_by]  DEFAULT ((0)) FOR [registered_by]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_modification_datetime]  DEFAULT (getdate()) FOR [modification_datetime]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_modified_by]  DEFAULT ((0)) FOR [modified_by]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_invalidated]  DEFAULT ('false') FOR [invalidated]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_invalidated_by]  DEFAULT ((0)) FOR [invalidated_by]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_logically_deleted]  DEFAULT ('false') FOR [logically_deleted]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_logically_deleted_by]  DEFAULT ((0)) FOR [logically_deleted_by]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_application_id]  DEFAULT ((0)) FOR [application_id]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_process_name]  DEFAULT ('') FOR [process_name]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_target_window_class_name]  DEFAULT ('') FOR [target_window_class_name]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_target_window_caption]  DEFAULT ('') FOR [target_window_caption]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_user_element_index]  DEFAULT ((-1)) FOR [user_element_index]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_user_element_automation_id]  DEFAULT ('') FOR [user_element_automation_id]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_user_element_class_name]  DEFAULT ('') FOR [user_element_class_name]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_user_element_caption]  DEFAULT ('') FOR [user_element_caption]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_password_element_index]  DEFAULT ((-1)) FOR [password_element_index]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_password_element_automation_id]  DEFAULT ('') FOR [password_element_automation_id]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_password_element_class_name]  DEFAULT ('') FOR [password_element_class_name]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_password_element_caption]  DEFAULT ('') FOR [password_element_caption]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_password_element_is_password]  DEFAULT ((0)) FOR [password_element_is_password]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_password_input_method]  DEFAULT ((0)) FOR [password_input_method]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_password_focusing_wait]  DEFAULT ((0)) FOR [password_focusing_wait]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_password_input_interval]  DEFAULT ((0)) FOR [password_input_interval]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_button_element_index]  DEFAULT ((-1)) FOR [button_element_index]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_button_element_automation_id]  DEFAULT ('') FOR [button_element_automation_id]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_button_element_class_name]  DEFAULT ('') FOR [button_element_class_name]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_button_element_caption]  DEFAULT ('') FOR [button_element_caption]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_button_click_method]  DEFAULT ((0)) FOR [button_click_method]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_button_click_key_code]  DEFAULT ((0)) FOR [button_click_key_code]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_button_click_x]  DEFAULT ((0)) FOR [button_click_x]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_button_click_y]  DEFAULT ((0)) FOR [button_click_y]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_action_on_exit]  DEFAULT ((0)) FOR [action_on_exit]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_enable_donot_auto_login_checkbox]  DEFAULT ((0)) FOR [enable_donot_auto_login_checkbox]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_donot_auto_login_checkbox_message]  DEFAULT ('') FOR [donot_auto_login_checkbox_message]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_not_allowed_one_factor_authentication]  DEFAULT ((0)) FOR [not_allowed_one_factor_authentication]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_hidden_close_button]  DEFAULT ((0)) FOR [hidden_close_button]
GO
ALTER TABLE [dbo].[t_autocomplete_settings] ADD  CONSTRAINT [DF_t_autocomplete_settings_not_allowed_variable]  DEFAULT ((0)) FOR [variable]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'registered_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'modification_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'modified_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'invalidated'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効化日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'invalidation_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効化を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'invalidated_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'logically_deleted'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'logical_deletion_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'logically_deleted_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'アプリケーションID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'application_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'プロセス名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'process_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ウィンドウクラス名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'target_window_class_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ウィンドウキャプション' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'target_window_caption'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ユーザー要素インデックス' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'user_element_index'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ユーザー要素オートメーションID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'user_element_automation_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ユーザー要素クラス名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'user_element_class_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ユーザー要素キャプション' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'user_element_caption'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード要素インデックス' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'password_element_index'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード要素オートメーションID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'password_element_automation_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード要素クラス名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'password_element_class_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード要素キャプション' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'password_element_caption'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード要素パスワードか' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'password_element_is_password'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード入力方式:0=オートメーション,1=キー' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'password_input_method'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード入力前待機時間(キー入力時)(ms)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'password_focusing_wait'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード入力間隔(キー入力時)(ms)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'password_input_interval'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ボタン要素インデックス' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'button_element_index'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ボタン要素オートメーションID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'button_element_automation_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ボタン要素クラス名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'button_element_class_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ボタン要素キャプション' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'button_element_caption'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ボタンクリック方式:0=オートメーション,1=キー,2=クリック' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'button_click_method'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ボタンクリックキーコード' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'button_click_key_code'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ボタンクリックX座標' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'button_click_x'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ボタンクリックY座標' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'button_click_y'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'終了時の動作:0=何もしない,1=メインウィンドウクローズ,2=プロセスキル' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'action_on_exit'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ログオンボタンを自動押下しないチェックボックスを表示するか' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'enable_donot_auto_login_checkbox'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ログオンボタンを自動押下しないチェックボックスの表示メッセージ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'donot_auto_login_checkbox_message'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'一要素認証を許可しない:false=許可する、true=許可しない' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'not_allowed_one_factor_authentication'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'閉じるボタンを非表示にする:false=表示する、true=表示しない' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'hidden_close_button'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'常時監視が必要なプロセスか' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_autocomplete_settings', @level2type=N'COLUMN',@level2name=N'variable'
GO
