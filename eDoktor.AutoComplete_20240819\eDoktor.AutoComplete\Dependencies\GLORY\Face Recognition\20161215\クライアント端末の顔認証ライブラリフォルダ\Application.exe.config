﻿  <system.diagnostics>
    <sources>
      <source name="GFRNetworkLogSource" switchName="GFRNetworkLogSwitch" switchType="System.Diagnostics.SourceSwitch">
        <listeners>
          <add name="GFRNetworkLogDaily"/>
        </listeners>
      </source>
    </sources>
    <switches>
      <add name="GFRNetworkLogSwitch" value="Information"/>
    </switches>
    <sharedListeners>
      <add name="GFRNetworkLogDaily" type="GRLog.DailyFileLogTraceListener, GRLog" initializeData="Log_%DATEPLACE%.log" RouteDirectory=".\GFRNetworkLogs" StoredDays="10"/>
    </sharedListeners>
    <trace autoflush="true" indentsize="4">
      <listeners>
        <add name="GFRNetworkLogDaily"/>
      </listeners>
    </trace>
  </system.diagnostics>

