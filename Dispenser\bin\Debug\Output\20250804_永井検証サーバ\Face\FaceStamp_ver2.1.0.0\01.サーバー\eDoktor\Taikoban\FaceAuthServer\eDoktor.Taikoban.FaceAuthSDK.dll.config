﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
	<configSections>
		<sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
			<section name="eDoktor.Taikoban.FaceAuthLicenseCheck.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
		</sectionGroup>
	</configSections>

	<appSettings>
		<!-- License -->
		<!-- <add key="LicenseFilePath" value="D:\eDoktor\LicenseFile\ISP-417_Win2019srv_Face.lic"/> -->
		<!-- 121サーバー -->
		<add key="LicenseFilePath" value="D:\eDoktor\LicenseFile\ISP-417_WIN2022-TK.lic"/>
		
		<!-- ローカル 
		<add key="LicenseFilePath" value="C:\pj\edoktor\Taikoban-FaceAuth-GlorySDK-main\Taikoban-FaceAuth-GlorySDK-main\Source\x64\Debug\ISP-417_WIN2022-TK.lic"/>
		-->
		<!-- デモ用ベンダーID -->
		<!--<add key="LicenseID" value="**********"/>-->
		<!-- 本番ベンダーID -->
		<add key="LicenseID" value="**********"/>

		<!-- Initialize -->
		<add key="ParallelNum" value="2"/>
		<add key="TemporaryNum" value="1"/>
		<add key="Lifespan" value="10"/>
	</appSettings>

	<userSettings>
		<eDoktor.Taikoban.FaceAuthLicenseCheck.Properties.Settings>
			<setting name="LicenseFilePath" serializeAs="String">
				<value>ISP-417_Win2019srv_Face.lic</value>
			</setting>
			<setting name="LicenseID" serializeAs="String">
				<!--<value>**********</value>-->
				<value>**********</value>
			</setting>
		</eDoktor.Taikoban.FaceAuthLicenseCheck.Properties.Settings>
	</userSettings>
</configuration>