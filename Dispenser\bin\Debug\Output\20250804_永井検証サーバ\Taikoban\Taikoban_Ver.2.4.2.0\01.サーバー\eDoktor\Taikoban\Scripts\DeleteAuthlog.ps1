﻿# スクリプト名：Taikoban認証ログ削除
# ファイル名：DeleteAuthlog.ps1
# 処理概要：Taikobanの認証ログをCSV形式で出力し削除する・一定期間変更のない端末ステータスを削除する
# 実行サーバ：TaikobanServer#1
# 実行ユーザ：SQL Server エージェント サービスのアカウント
# 実行構文：
# 入力値：
# 戻り値：
# 変更履歴：2014/11/28 (イードクトル木原) 新規作成

# 定数を定義する
[string]$extentionCsv = ".csv"
[string]$extentionCab = ".cab"

[int]$debug = 0

# 変数を設定する
[string]$connectionString = "Network=DBMSSOCN;Data Source=*************;failover partner=*************;Initial Catalog=Taikoban;Integrated Security=False;User ID=Taikoban;Password=************"   # 接続文字列
[string]$server = "localhost" # サーバー名
[string]$user = "Taikoban" # ユーザー名
[string]$password = "************" # パスワード
[string]$executionPath = "D:\eDoktor\Taikoban\Scripts\" # 実行パス
[string]$outputDirectoryBase = "D:\eDoktor\Taikoban\Output\"  # 出力フォルダ
[string]$outputFileBase = "TaikobanAuthlogBackup_"  # 出力ファイル名
[string]$delimiter = ","  # 区切り文字
[int]$statusRetentionPeriod = "30"  # ステータス保存期間（日数）
[string]$errorMessage = ""  # エラーメッセージ

# 関数を読み込む
try
{
    # 作業ディレクトリを移動する
    Set-Location $executionPath
    
    . ".\Common.ps1"
}
catch [Exception]
{
    # エラー通知に登録
    if ($_.exception -ne "") { $errorMessage = $_.exception }
    Notificate-Error 12 ([string]::Format("初期設定でエラーが発生しました: Exception=" + $errorMessage))
    
    return
}

# t_configsからログ保存期間を取得する
try
{
    $query = "SELECT TOP 1 * FROM t_configs ORDER BY id DESC"
    $dataTable = Get-DatabaseData $connectionString $query

    [int]$logRetentionPeriod = $dataTable.Rows[0]["log_retention_period"]
    [int]$logRetentionPeriodUnit = $dataTable.Rows[0]["log_retention_period_unit"]
    
    if (($logRetentionPeriod -lt 1) -or ($logRetentionPeriodUnit -lt 1) -or ($logRetentionPeriodUnit -gt 4))
    {
        # エラー通知に登録
        Notificate-Error 11 ([string]::Format("ログ保存期間の設定が不正です: ログ保存期間={0}, ログ保存期間の単位={1}", $logRetentionPeriod, $logRetentionPeriodUnit))
        
        return
    }
}
catch [Exception]
{
    # エラー通知に登録
    if ($_.exception -ne "") { $errorMessage = $_.exception }
    Notificate-Error 11 ([string]::Format("ログ保存期間の取得でエラーが発生しました: Exception={0}", $errorMessage))
    
    return
}
if ($debug -eq 1) { Add-Content ($outputDirectoryBase + "trace.log") ("1 " + $(Get-Date).ToString() + " ログ保存期間取得完了") }
if ($debug -eq 1) { Write-Host "ログ保存期間取得完了" }

# CSV出力フォルダを作成する
try
{
    $systemDate = Get-Date
    switch ($logRetentionPeriodUnit)
    {
        1 { $deleteDate = $systemDate.AddYears($logRetentionPeriod * -1) }
        2 { $deleteDate = $systemDate.AddMonths($logRetentionPeriod * -1) }
        3 { $deleteDate = $systemDate.AddDays($logRetentionPeriod * 7 * -1) }
        4 { $deleteDate = $systemDate.AddDays($logRetentionPeriod * -1) }
    }
    $outputDirectory = [string]::Format("{0}{1}\{2}\", $outputDirectoryBase, $deleteDate.AddDays(-1).Year, $deleteDate.AddDays(-1).Month)
    #$outputDirectory = $outputDirectoryBase
    $outputPathCsv = [string]::Format("{0}{1}{2}{3}", $outputDirectory, $outputFileBase, $deleteDate.AddDays(-1).ToString("yyyyMMdd_HHmmss"), $extentionCsv)
    $outputPathCab = [string]::Format("{0}{1}{2}{3}", $outputDirectory, $outputFileBase, $deleteDate.AddDays(-1).ToString("yyyyMMdd_HHmmss"), $extentionCab)

    if (-not(Test-Path $outputDirectory))
    {
        $result = New-Item $outputDirectory -itemType Directory
    }
}
catch [Exception]
{
    # エラー通知に登録
    if ($_.exception -ne "") { $errorMessage = $_.exception }
    Notificate-Error 11 ([string]::Format("CSVファイルの出力フォルダ作成でエラーが発生しました: Exception=" + $errorMessage))
    
    return
}
if ($debug -eq 1) { Add-Content ($outputDirectoryBase + "trace.log") ("2 " + $(Get-Date).ToString() + " CSV出力フォルダ作成完了") }
if ($debug -eq 1) { Write-Host "CSV出力フォルダ作成完了" }

# 削除ログをCSVファイルに出力する
try
{
    $query = "SELECT N'レコードID', N'ログ出力日時', N'端末名', N'リモート端末名', N'ログオンID', N'カード固有ID', N'カードタイプ', N'システムID', N'ログ項目', N'ログサブ項目', N'端末設定ID', N'メッセージ', N'ログ発生元'"
    $query += [string]::Format(" UNION ALL SELECT CONVERT(varchar, id) AS id, CONVERT(varchar, output_datetime, 121), terminal_name, remote_terminal_name, logon_id, card_id, CONVERT(varchar, card_type), CONVERT(varchar, system_key), CONVERT(varchar, log_type), CONVERT(varchar, log_sub_type), CONVERT(varchar, terminal_config_id), message, CONVERT(varchar, source) FROM [Taikoban].[dbo].[t_log] WHERE output_datetime < '{0}'", $deleteDate.ToString("yyyy-MM-dd"))
    $result = bcp $query queryout $outputPathCsv -S $server -U $user -P $password -t $delimiter -c -C 932
    #"result = " + $result
}
catch [Exception]
{
    # エラー通知に登録
    if ($_.exception -ne "") { $errorMessage = $_.exception }
    Notificate-Error 11 ([string]::Format("CSVファイルの出力でエラーが発生しました: Exception=" + $errorMessage))
    
    return
}
if ($debug -eq 1) { Add-Content ($outputDirectoryBase + "trace.log") ("3 " + $(Get-Date).ToString() +  " CSVファイル出力完了") }
if ($debug -eq 1) { Write-Host "CSVファイル出力完了" }

# CSVファイルをCABファイルに圧縮する
try
{
    $result = makecab $outputPathCsv $outputPathCab
}
catch [Exception]
{
    # エラー通知に登録
    if ($_.exception -ne "") { $errorMessage = $_.exception }
    Notificate-Error 11 ([string]::Format("CABファイルへの圧縮でエラーが発生しました: Exception=" + $errorMessage))
    
    return
}
if ($debug -eq 1) { Add-Content ($outputDirectoryBase + "trace.log") ("4 " + $(Get-Date).ToString() + " CAB圧縮完了") }
if ($debug -eq 1) { Write-Host "CAB圧縮完了" }

# CSVファイルを削除する
try
{
    $result = Remove-Item -Force $outputPathCsv
    #"result = " + $result
}
catch [Exception]
{
    # エラー通知に登録
    if ($_.exception -ne "") { $errorMessage = $_.exception }
    Notificate-Error 11 ([string]::Format("CSVファイルの削除でエラーが発生しました: Exception=" + $errorMessage))
    
    return
}
if ($debug -eq 1) { Add-Content ($outputDirectoryBase + "trace.log") ("5 " + $(Get-Date).ToString() + " CSVファイル削除完了") }
if ($debug -eq 1) { Write-Host "CSVファイル削除完了" }

# ログデータを削除する
try
{
    $query = [string]::Format("DELETE FROM [Taikoban].[dbo].[t_log] WHERE output_datetime < '{0}'", $deleteDate.ToString("yyyy-MM-dd"))
    
    $result = Invoke-DatabaseQuery $connectionString $query
    
    if ($result -lt 0)
    {
        Notificate-Error 11 "ログデータの削除に失敗しました"
        
        return
    }
}
catch [Exception]
{
    # エラー通知に登録
    if ($_.exception -ne "") { $errorMessage = $_.exception }
    Notificate-Error 11 ([string]::Format("ログデータの削除でエラーが発生しました: Exception=", $errorMessage))
    
    return
}
if ($debug -eq 1) { Add-Content ($outputDirectoryBase + "trace.log") ("6 " + $(Get-Date).ToString() + " ログ削除完了") }
if ($debug -eq 1) { Write-Host "ログ削除完了" }

# ステータス削除
try
{
    $query = [string]::Format("DELETE FROM [Taikoban].[dbo].[t_terminal_statuses] WHERE modification_datetime < DATEADD(day, {0}, '{1}')", $statusRetentionPeriod * -1, $systemDate.ToString("yyyy-MM-dd"))
    
    $result = Invoke-DatabaseQuery $connectionString $query
    
    if ($result -lt 0)
    {
        Notificate-Error 11 "ステータスデータの削除に失敗しました"
    }
}
catch [Exception]
{
    # エラー通知に登録
    if ($_.exception -ne "") { $errorMessage = $_.exception }
    Notificate-Error 11 ([string]::Format("ステータスデータの削除でエラーが発生しました: Exception=", $errorMessage))
    
    return
}
if ($debug -eq 1) { Add-Content ($outputDirectoryBase + "trace.log") ("7 " + $(Get-Date).ToString() + " ステータス削除完了") }
if ($debug -eq 1) { Write-Host "ステータス削除完了" }