﻿// UserConfirmationPage.cs

using System;
using System.Windows.Forms;

namespace eDoktor.AutoComplete.AuthUI
{
	using Common.EventExtensions;

	partial class UserConfirmationPage : PageBase
	{
		#region Enumerations
		enum AutoCloseModeType
		{
			No,
			Yes,
		}
		#endregion

		#region Events
		public event EventHandler YesButtonClick;
		public event EventHandler NoButtonClick;
		#endregion

		#region  Fields
		Timer _timer;
		#endregion

		#region Properties
		public string UserName { get; set; }
		public string DisplayName { get; set; }
		#endregion

		#region Constructors
		public UserConfirmationPage()
		{
			InitializeComponent();
			ClickThroughControls = new Control[] { this, messageLabel };
		}
		#endregion

		#region Protected Methods
		protected override void OnVisibleChanged(EventArgs e)
		{
			base.OnVisibleChanged(e);

			if (Visible)
			{
				messageLabel.Text = string.Format("{0}\r\nでログインしますか？", (string.IsNullOrWhiteSpace(DisplayName)) ? UserName : string.Format("{0}/{1}", UserName, DisplayName));
				yesButton.Focus();
				StartTimer();
			}
			else
			{
				DisposeTimer();
			}
		}

		protected override bool ProcessDialogKey(Keys keyData)
		{
			try
			{
				if (((keyData & Keys.KeyCode) == Keys.Enter) && ((keyData & (Keys.Alt | Keys.Control)) == Keys.None))
				{
					OnYesButtonClick(this, EventArgs.Empty);
					return true;
				}
				else if (((keyData & Keys.KeyCode) == Keys.Escape) && ((keyData & (Keys.Alt | Keys.Control)) == Keys.None))
				{
					OnNoButtonClick(this, EventArgs.Empty);
					return true;
				}
				else if (((keyData & Keys.KeyCode) == Keys.Space) && ((keyData & (Keys.Alt | Keys.Control)) == Keys.None) && (ActiveControl is IButtonControl))
				{
					(ActiveControl as IButtonControl).PerformClick();
					return true;
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}

			return base.ProcessDialogKey(keyData);
		}
		#endregion

		#region Private Methods
		private void DisposeTimer()
		{
			try
			{
				if (_timer != null)
				{
					_timer.Dispose();
					_timer = null;
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void StartTimer()
		{
			try
			{
				DisposeTimer();

				int interval = Common.Configuration.AppSetting("UserConfirmationAutoCloseTimeout", -1);

				if (interval < 0)
				{
					return;
				}

				_timer = new Timer() { Interval = interval };
				_timer.Tick += OnTimerTick;
				_timer.Start();
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnTimerTick(object sender, EventArgs e)
		{
			try
			{
				DisposeTimer();

				if (!Visible)
				{
					return;
				}

				var autoCloseMode = (AutoCloseModeType)Common.Configuration.AppSetting("UserConfirmationAutoCloseMode", (int)AutoCloseModeType.No);

				if (autoCloseMode == AutoCloseModeType.Yes)
				{
					OnYesButtonClick(this, EventArgs.Empty);
				}
				else // if (autoCloseMode == AutoCloseModeType.No)
				{
					OnNoButtonClick(this, EventArgs.Empty);
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnYesButtonClick(object sender, EventArgs e)
		{
			try
			{
				YesButtonClick.SafeInvoke(this, e);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnNoButtonClick(object sender, EventArgs e)
		{
			try
			{
				NoButtonClick.SafeInvoke(this, e);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion
	}
}
