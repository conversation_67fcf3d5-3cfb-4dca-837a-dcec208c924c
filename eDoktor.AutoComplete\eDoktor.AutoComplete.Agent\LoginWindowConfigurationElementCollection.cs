﻿// LoginWindowConfigurationElementCollection.cs

using System;
using System.Configuration;

namespace eDoktor.AutoComplete.Agent
{
	[ConfigurationCollection(typeof(LoginWindowConfigurationElement))]
	class LoginWindowConfigurationElementCollection : ConfigurationElementCollection
	{
		protected override object GetElementKey(ConfigurationElement element)
		{
			return ((LoginWindowConfigurationElement)element).Name;
		}

		protected override ConfigurationElement CreateNewElement()
		{
			return new LoginWindowConfigurationElement();
		}

		public void Add(LoginWindowConfigurationElement element)
		{
			BaseAdd(element);
		}

		public override ConfigurationElementCollectionType CollectionType
		{
			get { return ConfigurationElementCollectionType.BasicMap; }
		}

		public new LoginWindowConfigurationElement this[string name]
		{
			get { return (LoginWindowConfigurationElement)base.BaseGet(name); }
		}

		protected override string ElementName
		{
			get
			{
				return "loginWindow";
			}
		}
	}
}
