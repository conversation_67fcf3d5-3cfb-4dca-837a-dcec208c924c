﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
	<configSections>
		<sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
			<section name="eDoktor.Taikoban.TemporaryAllowOneFactorAuthentication.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
		</sectionGroup>
	</configSections>
	<applicationSettings>
		<eDoktor.Taikoban.TemporaryAllowOneFactorAuthentication.Properties.Settings>
			<setting name="ConnectionStringSettingsLabel" serializeAs="String">
				<value>Tai<PERSON>ban</value>
			</setting>
		</eDoktor.Taikoban.TemporaryAllowOneFactorAuthentication.Properties.Settings>
	</applicationSettings>
	<connectionStrings>
		<add name="Taikoban" connectionString="sOEqwRggeOs5J+mrA1PTQP03iS2JvKkkwovlMRimh7NIGBkA6vsDZOSli1ja2eqMXHwU1svdQpoPpl+cAiF71W5rxEQCPpq4d1gvrg7XgSWUOxpzgXIYISoUrCN+yEURta0qlL+UpBogoGF6fBt4Nmo40KsnimIn6wmRkbT0ScNmukl9T8237Gw5Z3A5jBlOw71SlKGl0maMbRVGzGH8J5zm175t4SnPg1pNJkVvp+0=" providerName="System.Data.SqlClient" />
	</connectionStrings>
	<appSettings>
		<!-- Trace -->
		<add key="EnableTrace" value="true" />
		<add key="EnableDebugTrace" value="true" />
		<add key="EnableDetailedExceptionTrace" value="true" />
		<add key="ContainingFolderPath" value="D:\eDoktor\Taikoban\Logs\TemporaryAllowOneFactorAuthentication\"/>
		<add key="TraceTermInDays" value="30" />

		<add key="Issue Term" value="2" />
		<add key="BeforeAccountGroupId" value="2"/>
		<add key="SystemId" value="/FRqtsXjPIeYpnoi/4A+iA=="/>
		<add key="SecurityKey"  value="33M2zH1m6Wu9pHapzvYoXMe8fTWOwuBE0wLf/FkOiZo="/>
		<!-- 年マスクスイッチ(true:マスクする false:マスクしない) -->
		<add key="YearMaskSw" value="true"/>
		<!-- 生年月日未登録時動作(0:エラーとしない 1:エラーとする) -->
		<add key="BirthdayNullAction" value="0"/>
		<!-- 固定貸出期間スイッチ(true:固定貸出期間(Issue Termの値) false:貸出期間は可変) 省略値=false-->
		<add key="IsFixedTerm" value="true"/>
		<!-- 貸出画面に貸出期間を表示するかどうかを示す。省略値=false-->
		<add key="RentalPeriodVisibility" value="false"/>

		<!-- 本人確認方法（0:なし 1:本人画像 2:ID/Pass入力 3:生年月日） -->
		<add key="IdentificationMethod" value="0"/>
		<!-- 本人画像の共有サーバーと共有名 -->
		<add key="FileServerNameOrAddress" value="\\*************\Shared"/>
		<!-- 共有サーバーへのログイン名(暗号化) -->
		<add key="NetworkAccountUserName" value="WNFY/8uidHezXc9o9TVEpSt3bPudWAGFRICoYwBtRXc="/>
		<!-- 共有サーバーへのログインパスワード(暗号化) -->
		<add key="NetworkAccountPassword" value="HT0MYszIsmXTT9IqOeOxNA=="/>
		<!-- 本人画像の共有フォルダパス -->
		<add key="SharedFolderPath" value="\Images\"/>
		<!-- 本人画像のファイル名フォーマット（%ID%は組織内IDで置換） -->
		<add key="FileNameFormat" value="%ID%.jpg"/>
		<!-- 本人画像取得失敗時の表示画像パス(exeと同じフォルダを想定) -->
		<add key="ErrorFilePath" value="ErrorImage.jpg"/>

	</appSettings>
</configuration>