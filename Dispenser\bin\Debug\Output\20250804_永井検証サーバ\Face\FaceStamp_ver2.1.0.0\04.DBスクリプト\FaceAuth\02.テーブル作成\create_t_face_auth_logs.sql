USE [$(Param1)]
GO

/****** Object:  Table [dbo].[t_face_auth_logs]    Script Date: 2021/07/07 9:40:02 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_face_auth_logs]') AND type in (N'U'))
DROP TABLE [dbo].[t_face_auth_logs]
GO

CREATE TABLE [dbo].[t_face_auth_logs](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[auth_datetime] [datetime2](7) NOT NULL,
	[ip_address] [nvarchar](32) NOT NULL,
	[is_auth_success] [bit] NOT NULL,
	[account_id] [int] NOT NULL,
	[template_id] [int] NOT NULL,
	[face_recognition_score] [float] NOT NULL,
	[template_quality] [float] NOT NULL,
	[mask_score] [float] NOT NULL,
	[log_contents] [nvarchar](max) NOT NULL,
	[thumbnail_data] [nvarchar](max) NOT NULL,
	[logon_id] [nvarchar](255) NOT NULL,
	[user_id] [int] NOT NULL,
	[organizational_id] [nvarchar](255) NOT NULL,
	[user_name] [nvarchar](255) NOT NULL,
	[auth_mode] [int] NOT NULL,
	[is_user_specified] [bit] NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[t_face_auth_logs] ADD  CONSTRAINT [DF_t_face_auth_logs_auth_datetime]  DEFAULT (getdate()) FOR [auth_datetime]
GO

ALTER TABLE [dbo].[t_face_auth_logs] ADD  CONSTRAINT [DF_t_face_auth_logs_ip_address]  DEFAULT ('') FOR [ip_address]
GO

ALTER TABLE [dbo].[t_face_auth_logs] ADD  CONSTRAINT [DF_t_face_auth_logs_is_auth_success]  DEFAULT ('false') FOR [is_auth_success]
GO

ALTER TABLE [dbo].[t_face_auth_logs] ADD  CONSTRAINT [DF_t_face_auth_logs_account_id]  DEFAULT ((0)) FOR [account_id]
GO

ALTER TABLE [dbo].[t_face_auth_logs] ADD  CONSTRAINT [DF_t_face_auth_logs_template_id]  DEFAULT ((0)) FOR [template_id]
GO

ALTER TABLE [dbo].[t_face_auth_logs] ADD  CONSTRAINT [DF_t_face_auth_logs_face_recognition_score]  DEFAULT ((0)) FOR [face_recognition_score]
GO

ALTER TABLE [dbo].[t_face_auth_logs] ADD  CONSTRAINT [DF_t_face_auth_logs_template_quality]  DEFAULT ((0)) FOR [template_quality]
GO

ALTER TABLE [dbo].[t_face_auth_logs] ADD  CONSTRAINT [DF_t_face_auth_logs_mask_score]  DEFAULT ((0)) FOR [mask_score]
GO

ALTER TABLE [dbo].[t_face_auth_logs] ADD  CONSTRAINT [DF_t_face_auth_logs_log_contents]  DEFAULT ('') FOR [log_contents]
GO

ALTER TABLE [dbo].[t_face_auth_logs] ADD  CONSTRAINT [DF_t_face_auth_logs_thumbnail_data]  DEFAULT ('') FOR [thumbnail_data]
GO

ALTER TABLE [dbo].[t_face_auth_logs] ADD  CONSTRAINT [DF_t_face_auth_logs_logon_id]  DEFAULT ('') FOR [logon_id]
GO

ALTER TABLE [dbo].[t_face_auth_logs] ADD  CONSTRAINT [DF_t_face_auth_logs_user_id]  DEFAULT ((0)) FOR [user_id]
GO

ALTER TABLE [dbo].[t_face_auth_logs] ADD  CONSTRAINT [DF_t_face_auth_logs_organizational_id]  DEFAULT ('') FOR [organizational_id]
GO

ALTER TABLE [dbo].[t_face_auth_logs] ADD  CONSTRAINT [DF_t_face_auth_logs_user_name]  DEFAULT ('') FOR [user_name]
GO

ALTER TABLE [dbo].[t_face_auth_logs] ADD  CONSTRAINT [DF_t_face_auth_logs_auth_mode]  DEFAULT ((0)) FOR [auth_mode]
GO

ALTER TABLE [dbo].[t_face_auth_logs] ADD  CONSTRAINT [DF_t_face_auth_logs_is_user_specified]  DEFAULT ('false') FOR [is_user_specified]
GO

ALTER TABLE [dbo].[t_face_auth_logs] ADD  CONSTRAINT [DF_t_face_auth_logs_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_logs', @level2type=N'COLUMN',@level2name=N'id'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'認証日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_logs', @level2type=N'COLUMN',@level2name=N'auth_datetime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'IPアドレス' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_logs', @level2type=N'COLUMN',@level2name=N'ip_address'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'認証結果' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_logs', @level2type=N'COLUMN',@level2name=N'is_auth_success'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'アカウントID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_logs', @level2type=N'COLUMN',@level2name=N'account_id'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'テンプレートID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_logs', @level2type=N'COLUMN',@level2name=N'template_id'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'顔照合スコア' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_logs', @level2type=N'COLUMN',@level2name=N'face_recognition_score'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'テンプレート評価値' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_logs', @level2type=N'COLUMN',@level2name=N'template_quality'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'マスク装着度' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_logs', @level2type=N'COLUMN',@level2name=N'mask_score'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ログ内容' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_logs', @level2type=N'COLUMN',@level2name=N'log_contents'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'サムネイルデータ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_logs', @level2type=N'COLUMN',@level2name=N'thumbnail_data'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ログオンID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_logs', @level2type=N'COLUMN',@level2name=N'logon_id'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ユーザID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_logs', @level2type=N'COLUMN',@level2name=N'user_id'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'組織内ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_logs', @level2type=N'COLUMN',@level2name=N'organizational_id'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ユーザ名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_logs', @level2type=N'COLUMN',@level2name=N'user_name'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'認証モード' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_logs', @level2type=N'COLUMN',@level2name=N'auth_mode'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ユーザ指定有無' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_logs', @level2type=N'COLUMN',@level2name=N'is_user_specified'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_logs', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO


