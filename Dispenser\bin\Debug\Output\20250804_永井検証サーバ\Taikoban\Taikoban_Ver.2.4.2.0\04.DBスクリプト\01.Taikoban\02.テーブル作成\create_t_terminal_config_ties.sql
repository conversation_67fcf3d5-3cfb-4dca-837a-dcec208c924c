USE [$(Param1)]
GO
/****** Object:  Table [dbo].[t_terminal_config_ties]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_terminal_config_ties]') AND type in (N'U'))
DROP TABLE [dbo].[t_terminal_config_ties]
GO

CREATE TABLE [dbo].[t_terminal_config_ties](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[registered_by] [int] NOT NULL,
	[modification_datetime] [datetime2](7) NOT NULL,
	[modified_by] [int] NOT NULL,
	[invalidated] [bit] NOT NULL,
	[invalidation_datetime] [datetime2](7) NULL,
	[invalidated_by] [int] NOT NULL,
	[logically_deleted] [bit] NOT NULL,
	[logical_deletion_datetime] [datetime2](7) NULL,
	[logically_deleted_by] [int] NOT NULL,
	[usage_start_datetime] [datetime2](7) NULL,
	[usage_end_datetime] [datetime2](7) NULL,
	[tie_type] [int] NOT NULL,
	[account_id] [int] NOT NULL,
	[account_group_id] [int] NOT NULL,
	[terminal_id] [int] NOT NULL,
	[terminal_group_id] [int] NOT NULL,
	[terminal_config_id] [int] NOT NULL,
	[notes] [nvarchar](255) NOT NULL,
 CONSTRAINT [PK_t_terminal_config_ties] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_terminal_config_ties] ADD  CONSTRAINT [DF_t_terminal_config_ties_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO
ALTER TABLE [dbo].[t_terminal_config_ties] ADD  CONSTRAINT [DF_t_terminal_config_ties_registered_by]  DEFAULT ((0)) FOR [registered_by]
GO
ALTER TABLE [dbo].[t_terminal_config_ties] ADD  CONSTRAINT [DF_t_terminal_config_ties_modification_datetime]  DEFAULT (getdate()) FOR [modification_datetime]
GO
ALTER TABLE [dbo].[t_terminal_config_ties] ADD  CONSTRAINT [DF_t_terminal_config_ties_modified_by]  DEFAULT ((0)) FOR [modified_by]
GO
ALTER TABLE [dbo].[t_terminal_config_ties] ADD  CONSTRAINT [DF_t_terminal_config_ties_invalidated]  DEFAULT ('false') FOR [invalidated]
GO
ALTER TABLE [dbo].[t_terminal_config_ties] ADD  CONSTRAINT [DF_t_terminal_config_ties_invalidated_by]  DEFAULT ((0)) FOR [invalidated_by]
GO
ALTER TABLE [dbo].[t_terminal_config_ties] ADD  CONSTRAINT [DF_t_terminal_config_ties_logically_deleted]  DEFAULT ('false') FOR [logically_deleted]
GO
ALTER TABLE [dbo].[t_terminal_config_ties] ADD  CONSTRAINT [DF_t_terminal_config_ties_logically_deleted_by]  DEFAULT ((0)) FOR [logically_deleted_by]
GO
ALTER TABLE [dbo].[t_terminal_config_ties] ADD  CONSTRAINT [DF_t_terminal_config_ties_tie_type]  DEFAULT ((0)) FOR [tie_type]
GO
ALTER TABLE [dbo].[t_terminal_config_ties] ADD  CONSTRAINT [DF_t_terminal_config_ties_account_id]  DEFAULT ((0)) FOR [account_id]
GO
ALTER TABLE [dbo].[t_terminal_config_ties] ADD  CONSTRAINT [DF_t_terminal_config_ties_account_group_id]  DEFAULT ((0)) FOR [account_group_id]
GO
ALTER TABLE [dbo].[t_terminal_config_ties] ADD  CONSTRAINT [DF_t_terminal_config_ties_terminal_id]  DEFAULT ((0)) FOR [terminal_id]
GO
ALTER TABLE [dbo].[t_terminal_config_ties] ADD  CONSTRAINT [DF_t_terminal_config_ties_terminal_group_id]  DEFAULT ((0)) FOR [terminal_group_id]
GO
ALTER TABLE [dbo].[t_terminal_config_ties] ADD  CONSTRAINT [DF_t_terminal_config_ties_terminal_config_id]  DEFAULT ((0)) FOR [terminal_config_id]
GO
ALTER TABLE [dbo].[t_terminal_config_ties] ADD  CONSTRAINT [DF_t_terminal_config_ties_notes]  DEFAULT ('') FOR [notes]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_config_ties', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_config_ties', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_config_ties', @level2type=N'COLUMN',@level2name=N'registered_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_config_ties', @level2type=N'COLUMN',@level2name=N'modification_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_config_ties', @level2type=N'COLUMN',@level2name=N'modified_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_config_ties', @level2type=N'COLUMN',@level2name=N'invalidated'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効化日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_config_ties', @level2type=N'COLUMN',@level2name=N'invalidation_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効化を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_config_ties', @level2type=N'COLUMN',@level2name=N'invalidated_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_config_ties', @level2type=N'COLUMN',@level2name=N'logically_deleted'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_config_ties', @level2type=N'COLUMN',@level2name=N'logical_deletion_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_config_ties', @level2type=N'COLUMN',@level2name=N'logically_deleted_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'有効期間開始日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_config_ties', @level2type=N'COLUMN',@level2name=N'usage_start_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'有効期間終了日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_config_ties', @level2type=N'COLUMN',@level2name=N'usage_end_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'紐付タイプ 0:未定義 1:端末グループ 2:端末 3:ユーザーグループ&端末グループ 4:ユーザーグループ&端末 5:ユーザー&端末グループ 6:ユーザー&端末' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_config_ties', @level2type=N'COLUMN',@level2name=N'tie_type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'アカウントID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_config_ties', @level2type=N'COLUMN',@level2name=N'account_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'アカウントグループID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_config_ties', @level2type=N'COLUMN',@level2name=N'account_group_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'端末ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_config_ties', @level2type=N'COLUMN',@level2name=N'terminal_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'端末グループID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_config_ties', @level2type=N'COLUMN',@level2name=N'terminal_group_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'動作ポリシーID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_config_ties', @level2type=N'COLUMN',@level2name=N'terminal_config_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'注記' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_config_ties', @level2type=N'COLUMN',@level2name=N'notes'
GO
