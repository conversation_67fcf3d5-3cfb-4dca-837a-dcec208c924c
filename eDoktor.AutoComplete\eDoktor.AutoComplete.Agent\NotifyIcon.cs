﻿// NotifyIcon.cs

namespace eDoktor.AutoComplete.Agent
{
	class NotifyIcon : System.IDisposable
	{
		#region Events
		public event System.EventHandler TerminalMenuClick;
		public event System.EventHandler AboutMenuClick;
		public event System.EventHandler ExitMenuClick;
		#endregion

		#region Fields
		private Common.NotifyIcon _notifyIcon;
		#endregion

		#region Constructors
		public NotifyIcon(System.Drawing.Icon icon)
		{
			_notifyIcon = new Common.NotifyIcon(icon, "eDoktor AutoComplete Agent", GetContextMenuItems());
			_notifyIcon.DoubleClick += new System.EventHandler(OnNotifyIconDoubleClick);
			_notifyIcon.Visible = true;
		}
		#endregion

		#region IDisposable
		~NotifyIcon()
		{
			Dispose(false);
		}

		public void Dispose()
		{
			Dispose(true);
			System.GC.SuppressFinalize(this);
		}

		protected virtual void Dispose(bool disposing)
		{
			if (_notifyIcon != null)
			{
				_notifyIcon.Visible = false;
				_notifyIcon.Dispose();
				_notifyIcon = null;
			}

			TerminalMenuClick = null;
			AboutMenuClick = null;
			ExitMenuClick = null;
		}
		#endregion

		#region Public Methods
		public void ShowBalloonTip(int timeout, string tipTitle, string tipText, System.Windows.Forms.ToolTipIcon tipIcon)
		{
			_notifyIcon.ShowBalloonTip(timeout, tipTitle, tipText, tipIcon);
		}
		#endregion

		#region Private Methods
		private System.Windows.Forms.ToolStripMenuItem[] GetContextMenuItems()
		{
			System.Collections.Generic.List<System.Windows.Forms.ToolStripMenuItem> menuItems = new System.Collections.Generic.List<System.Windows.Forms.ToolStripMenuItem>();

			System.Windows.Forms.ToolStripMenuItem menuItem = null;

			return menuItems.ToArray();
		}

		private void OnNotifyIconDoubleClick(object sender, System.EventArgs e)
		{
			OnTerminalMenuClick(sender, e);
		}

		private void OnTerminalMenuClick(object sender, System.EventArgs e)
		{
			try
			{
				System.EventHandler terminalMenuClick = TerminalMenuClick;

				if (terminalMenuClick != null)
				{
					terminalMenuClick(this, e);
				}
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnAboutMenuClick(object sender, System.EventArgs e)
		{
			try
			{
				System.EventHandler aboutMenuClick = AboutMenuClick;

				if (aboutMenuClick != null)
				{
					aboutMenuClick(sender, e);
				}
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnExitMenuClick(object sender, System.EventArgs e)
		{
			try
			{
				System.EventHandler exitMenuClick = ExitMenuClick;

				if (exitMenuClick != null)
				{
					exitMenuClick(sender, e);
				}
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion
	}
}
