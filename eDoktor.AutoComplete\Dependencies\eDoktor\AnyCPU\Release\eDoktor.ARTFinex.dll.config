﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <appSettings>
        <add key="Verbose" value="false" />
        <add key="Force8BytesUID" value="true" />
        <add key="Description" value="Silicon Labs CP210x USB to UART Bridge" />
        <add key="BaudRate" value="38400" />
        <!-- Parity: None = 0, Odd = 1, Even = 2, Mark = 3, Space = 4 -->
        <add key="Parity" value="0" />
        <add key="DataBits" value="8" />
        <!-- StopBits: None = 0, One = 1, Two = 2, OnePointFive = 3 -->
        <add key="StopBits" value="1" />
        <!-- Handshake: None = 0, XOnXOff = 1, RequestToSend = 2, RequestToSendXOnXOff = 3 -->
        <add key="Handshake" value="0" />
        <add key="DtrEnable" value="true" />
        <add key="RtsEnable" value="true" />
        <add key="ReadTimeout" value="5000" />
        <add key="WriteTimeout" value="5000" />
        <add key="PortSearchTimeout" value="5000" />
        <add key="ReceiveBufferSize" value="1024" />
        <add key="AutoReconnectInitialDueTime" value="5000" />
        <add key="AutoReconnectPeriod" value="1000" />
        <add key="TagDetectionTimeout" value="200"/>
    </appSettings>
</configuration>