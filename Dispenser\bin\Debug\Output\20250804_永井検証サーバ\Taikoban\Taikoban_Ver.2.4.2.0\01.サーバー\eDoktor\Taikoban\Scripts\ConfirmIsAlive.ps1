﻿# 定数を定義する
[int]$sourceNumber = 13
[int]$debug = 1
[string]$connectionString = "Network=DBMSSOCN;Data Source=*************;failover partner=*************;Initial Catalog=<PERSON><PERSON><PERSON>;Integrated Security=False;User ID=<PERSON><PERSON>ban;Password=************"   # 接続文字列
[string]$executionPath = "D:\eDoktor\Taikoban\Scripts\" # 実行パス
[string]$logPath = $executionPath + "Logs\confirm_alive.log" # ログファイルパス
[int]$statusRetentionPeriod = 180  # ステータス確認間隔（分）

# 変数を設定する
[string]$errorMessage = ""  # エラーメッセージ

if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " 処理開始") | Out-File $logPath }

# 関数を読み込む
try
{
    # 作業ディレクトリを移動する
    Set-Location $executionPath
    
    . ".\Common.ps1"
}
catch [Exception]
{
    # エラー通知に登録
    if ($_.exception -ne "") { $errorMessage = $_.exception }
    # Notificate-Error $sourceNumber ([string]::Format("初期設定でエラーが発生しました: Exception=" + $errorMessage))
    
    return
}

if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " 初期処理完了") | Out-File $logPath -Append }

# t_terminal_statusesから一定時間変更なしの端末リストを取得する
try
{

    $query = "SELECT * FROM t_terminal_statuses WHERE windows_session IN (0, 1, 2) AND authentication IN (0, 1) AND NOT (windows_session = 0 AND authentication = 0) AND DATEADD(minute, " + $statusRetentionPeriod * -1 + ", modification_datetime) <= GETDATE()"
    $dataTable = Get-DatabaseData $connectionString $query
}
catch [Exception]
{
    # エラー通知に登録
    if ($_.exception -ne "") { $errorMessage = $_.exception }
    # Notificate-Error $sourceNumber ([string]::Format("ログ保存期間の取得でエラーが発生しました: Exception=", $errorMessage))
    
    return
}

if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " 端末リスト取得完了") | Out-File $logPath -Append }

# 全端末の疎通確認を行いステータスを変更する
try
{
    <#$dataTable.Rows | Foreach-Object
    {
        # 疎通確認を行う
        [string]$terminalName = $_["terminal_name"]
        $result = Test-Connection $terminalName -Quiet
        
        if (-ne $result)
        {
            Host-Write ($result + "には疎通せず")
        }
    }#>
    foreach ($row in $dataTable.Rows)
    {
        [string]$terminalName = $row["ip_address"]
        [bool]$result = Test-Connection $terminalName -Quiet -Count 1
        
        if (-not $result)
        {
            #Write-Host ($terminalName + "には疎通せず")
            if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " " + $terminalName + "には疎通せず") | Out-File $logPath -Append }
            # ステータスを不明に変更する
            $query = "UPDATE t_terminal_statuses SET windows_session = 9, windows_session_change_datetime = GETDATE(), authentication = 9, authenticated_logon_id = '' WHERE ip_address = '" + $terminalName + "'"
            
            $result = Invoke-DatabaseQuery $connectionString $query
        }
    }
}
catch [Exception]
{
    # エラー通知に登録
    if ($_.exception -ne "") { $errorMessage = $_.exception }
    # Notificate-Error $sourceNumber ([string]::Format("CSVファイルの出力フォルダ作成でエラーが発生しました: Exception=" + $errorMessage))
    
    return
}

if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " 疎通確認完了") | Out-File $logPath -Append }
