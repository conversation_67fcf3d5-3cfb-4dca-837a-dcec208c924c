﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="UpdateValidPeriods.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <applicationSettings>
        <UpdateValidPeriods.Properties.Settings>
            <setting name="ConnectionStringSettingsLabel" serializeAs="String">
                <value>Taikoban</value>
            </setting>
        </UpdateValidPeriods.Properties.Settings>
    </applicationSettings>
  	<connectionStrings>
	    	<add name="Taikoban" connectionString="sOEqwRggeOs5J+mrA1PTQP03iS2JvKkkwovlMRimh7NIGBkA6vsDZOSli1ja2eqMXHwU1svdQpoPpl+cAiF71W5rxEQCPpq4d1gvrg7XgSWUOxpzgXIYISoUrCN+yEURta0qlL+UpBogoGF6fBt4Nmo40KsnimIn6wmRkbT0ScNmukl9T8237Gw5Z3A5jBlOw71SlKGl0maMbRVGzGH8J5zm175t4SnPg1pNJkVvp+0=" providerName="System.Data.SqlClient" />
	  </connectionStrings>

	  <appSettings>
		    <!-- Trace -->
	    	<add key="EnableTrace" value="true" />
	    	<add key="EnableDebugTrace" value="true" />
	    	<add key="EnableDetailedExceptionTrace" value="true" />
	    	<add key="ContainingFolderPath" value=".\Logs\"/>
	    	<add key="TraceTermInDays" value="30" />
	  </appSettings>
</configuration>