﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <connectionStrings>
    <add name="Taikoban" connectionString="sOEqwRggeOs5J+mrA1PTQP03iS2JvKkkwovlMRimh7NIGBkA6vsDZOSli1ja2eqMXHwU1svdQpoPpl+cAiF71W5rxEQCPpq4d1gvrg7XgSWUOxpzgXIYISoUrCN+yEURta0qlL+UpBogoGF6fBt4Nmo40KsnimIn6wmRkbT0ScNmukl9T8237Gw5Z3A5jBlOw71SlKGl0maMbRVGzGH8J5zm175t4SnPg1pNJkVvp+0=" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <appSettings>
    <add key="ConnectionStringName" value="Taikoban" />
    <!-- Trace -->
    <add key="EnableTrace" value="true" />
    <add key="EnableDebugTrace" value="true" />
    <add key="EnableDetailedExceptionTrace" value="true" />
    <add key="ContainingFolderPath" value=".\logs\" />
    <add key="TraceTermInDays" value="30" />
    <add key="SystemId" value="KmtYM5ZTllFZQmOlD5DEgg==" />
    <add key="SecurityKey" value="Lhh8Haj6u2Hqg+m8vQ6fDA==" />
  </appSettings>
</configuration>