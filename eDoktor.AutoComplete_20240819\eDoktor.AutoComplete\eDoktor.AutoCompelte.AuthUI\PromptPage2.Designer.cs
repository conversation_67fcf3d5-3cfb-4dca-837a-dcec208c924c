﻿namespace eDoktor.AutoComplete.AuthUI
{
	partial class PromptPage2
	{
		/// <summary> 
		/// 必要なデザイナー変数です。
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary> 
		/// 使用中のリソースをすべてクリーンアップします。
		/// </summary>
		/// <param name="disposing">マネージ リソースが破棄される場合 true、破棄されない場合は false です。</param>
		protected override void Dispose(bool disposing)
		{
			if (disposing && (components != null))
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		#region コンポーネント デザイナーで生成されたコード

		/// <summary> 
		/// デザイナー サポートに必要なメソッドです。このメソッドの内容を 
		/// コード エディターで変更しないでください。
		/// </summary>
		private void InitializeComponent()
		{
			this.messageLabel = new System.Windows.Forms.Label();
			this.startButton = new eDoktor.UI.ImageButton();
			this.closeButton = new eDoktor.UI.ImageButton();
			this.donotAutoLoginCheckBox = new System.Windows.Forms.CheckBox();
			this.SuspendLayout();
			// 
			// messageLabel
			// 
			this.messageLabel.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.messageLabel.Font = new System.Drawing.Font("Yu Gothic UI", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
			this.messageLabel.Location = new System.Drawing.Point(50, 70);
			this.messageLabel.Margin = new System.Windows.Forms.Padding(0);
			this.messageLabel.Name = "messageLabel";
			this.messageLabel.Size = new System.Drawing.Size(500, 220);
			this.messageLabel.TabIndex = 0;
			this.messageLabel.Text = "ICカードをかざすか\r\n認証開始ボタンを押下してください";
			this.messageLabel.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
			// 
			// startButton
			// 
			this.startButton.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.startButton.BackColor = System.Drawing.Color.Transparent;
			this.startButton.BackgroundImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_7;
			this.startButton.DefaultImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_7;
			this.startButton.DefaultTextColor = System.Drawing.Color.White;
			this.startButton.DialogResult = System.Windows.Forms.DialogResult.None;
			this.startButton.DisabledImage = null;
			this.startButton.DisabledTextColor = System.Drawing.Color.Empty;
			this.startButton.FocusImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_8;
			this.startButton.FocusTextColor = System.Drawing.Color.White;
			this.startButton.ForeColor = System.Drawing.SystemColors.ControlText;
			this.startButton.HoverImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_9;
			this.startButton.HoverTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(140)))), ((int)(((byte)(177)))));
			this.startButton.LabelText = "認証開始";
			this.startButton.Location = new System.Drawing.Point(80, 286);
			this.startButton.Margin = new System.Windows.Forms.Padding(0);
			this.startButton.Name = "startButton";
			this.startButton.PressedImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_9;
			this.startButton.PressedTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(140)))), ((int)(((byte)(177)))));
			this.startButton.Size = new System.Drawing.Size(195, 44);
			this.startButton.TabIndex = 1;
			this.startButton.TextFont = new System.Drawing.Font("Yu Gothic UI", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
			this.startButton.TextMargin = new System.Windows.Forms.Padding(0);
			this.startButton.Click += new System.EventHandler(this.OnStartButtonClick);
			// 
			// closeButton
			// 
			this.closeButton.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.closeButton.BackColor = System.Drawing.Color.Transparent;
			this.closeButton.BackgroundImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_7;
			this.closeButton.DefaultImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_7;
			this.closeButton.DefaultTextColor = System.Drawing.Color.White;
			this.closeButton.DialogResult = System.Windows.Forms.DialogResult.None;
			this.closeButton.DisabledImage = null;
			this.closeButton.DisabledTextColor = System.Drawing.Color.Empty;
			this.closeButton.FocusImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_8;
			this.closeButton.FocusTextColor = System.Drawing.Color.White;
			this.closeButton.ForeColor = System.Drawing.SystemColors.ControlText;
			this.closeButton.HoverImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_9;
			this.closeButton.HoverTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(140)))), ((int)(((byte)(177)))));
			this.closeButton.LabelText = "閉じる";
			this.closeButton.Location = new System.Drawing.Point(325, 286);
			this.closeButton.Margin = new System.Windows.Forms.Padding(0);
			this.closeButton.Name = "closeButton";
			this.closeButton.PressedImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_9;
			this.closeButton.PressedTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(140)))), ((int)(((byte)(177)))));
			this.closeButton.Size = new System.Drawing.Size(195, 44);
			this.closeButton.TabIndex = 4;
			this.closeButton.TextFont = new System.Drawing.Font("Yu Gothic UI", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
			this.closeButton.TextMargin = new System.Windows.Forms.Padding(0);
			this.closeButton.Click += new System.EventHandler(this.OnCloseButtonClick);
			// 
			// donotAutoLoginCheckBox
			// 
			this.donotAutoLoginCheckBox.AutoSize = true;
			this.donotAutoLoginCheckBox.Font = new System.Drawing.Font("Yu Gothic UI", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
			this.donotAutoLoginCheckBox.Location = new System.Drawing.Point(218, 253);
			this.donotAutoLoginCheckBox.Name = "donotAutoLoginCheckBox";
			this.donotAutoLoginCheckBox.Size = new System.Drawing.Size(156, 19);
			this.donotAutoLoginCheckBox.TabIndex = 5;
			this.donotAutoLoginCheckBox.Text = "手動でログインボタンを押す";
			this.donotAutoLoginCheckBox.UseVisualStyleBackColor = true;
			// 
			// PromptPage2
			// 
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.Controls.Add(this.donotAutoLoginCheckBox);
			this.Controls.Add(this.closeButton);
			this.Controls.Add(this.startButton);
			this.Controls.Add(this.messageLabel);
			this.Name = "PromptPage2";
			this.ResumeLayout(false);
			this.PerformLayout();

		}

        #endregion

        public System.Windows.Forms.Label messageLabel;
		public UI.ImageButton startButton;
        public UI.ImageButton closeButton;
        public System.Windows.Forms.CheckBox donotAutoLoginCheckBox;
    }
}
