﻿// ServiceInstaller.cs

using System;
using System.ComponentModel;
using System.ServiceProcess;

namespace eDoktor.Taikoban.ServerService
{
	[RunInstaller(true)]
	public class ServiceInstaller : Install.ServiceInstaller
	{
		#region Constructors
		public ServiceInstaller()
		{
			Description = "TAIKOBAN認証システムの代理ログインサーバーサービスです。";
			DisplayName = "eDoktor AutoComplete Server Service";
			ServiceName = "eDoktor.AutoComplete.ServerService";
			StartType = ServiceStartMode.Automatic;

			// 2016年1月現在Windows 10では.NETで開発されたサービスが「自動」で開始しないという問題が報告されている
			// 対策として「自動（遅延開始）」とすれば開始するとの情報があるが副作用については要検証
			/*
			if (Common.Environment.IsWindows10OrGreater)
			{
				DelayedAutoStart = true;
			}*/

			Account = ServiceAccount.LocalSystem;
			FirewallAuthorizedApplication = true;
			//InteractWithDesktop = true;
			StartServiceAfterInstallation = true;
		}
		#endregion
	}
}
