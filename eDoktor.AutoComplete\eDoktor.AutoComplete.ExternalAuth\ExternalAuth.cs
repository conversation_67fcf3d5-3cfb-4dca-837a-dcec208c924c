﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace eDoktor.AutoComplete.ExternalAuth
{
    public class ExternalAuth : IDisposable
    {
        #region Consts
        private const int ExternalAuthNoExecuting = 0;
        private const int ExternalAuthExecuting = 1;
        #endregion

        #region Fields
        private ExternalAuthClient _authClient = null;
        private bool _isCancel = false;
        #endregion

        #region Public Enum
        public enum AuthResult
        {
            Success = 0,
            Cancel = 1,
            UserNotExist = -2,
            SystemError = -99,
        }
        #endregion

        #region Constructors
        public ExternalAuth()
        {

        }
        #endregion

        #region IDisposable
        ~ExternalAuth()
        {
            Dispose(false);
        }

        public void Dispose()
        {
            Dispose(true);
            System.GC.SuppressFinalize(this);
        }

        private void Dispose(bool disposing)
        {
            if (_authClient != null)
            {
                _authClient.End();
                _authClient = null;
            }
        }
        #endregion

        #region Public Methods
        public AuthResult Authenticate(IntPtr hWnd, string userId)
        {
            try
            {
                //***debug***
                eDoktor.Common.Trace.OutputDebugTrace("Authenticate start [hWnd={0},userId={1}", hWnd, userId);
                //***debug***
                // 外部認証プログラムを起動する
                _authClient = new ExternalAuthClient();
                // メインディスプレイサイズ
                int h = System.Windows.Forms.Screen.PrimaryScreen.Bounds.Height;
                int w = System.Windows.Forms.Screen.PrimaryScreen.Bounds.Width;
                int result = _authClient.Start((int)hWnd, h, w, 10, 0);
                if (result != 1)
                {
                    return AuthResult.SystemError;
                }

                // 認証するユーザーが存在するかを確認する
                result = _authClient.QueryUserId(userId);
                if (result != 1)
                {
                    switch (result)
                    {
                        case 2:
                            return AuthResult.UserNotExist;
                        default:
                            return AuthResult.SystemError;
                    }
                }

                // 外部認証処理を開始する
                result = _authClient.PerformBioAuth();
                if (result != 1)
                {
                    return AuthResult.SystemError;
                }

                // 認証結果を待つ
                //var task = ExecStateCheck(_authClient);
                //result = task.Result;
                result = ExecStateCheck(_authClient);

                //結果を返す
                switch (result)
                {
                    case 2:
                        return AuthResult.Success;
                    case 3:
                        return AuthResult.Cancel;
                    default:
                        return AuthResult.SystemError;
                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);

                return AuthResult.SystemError;
            }
            finally
            {
                // 外部認証を終了する。
                _authClient.End();
                _authClient = null;
            }
        }

        public void CancelAuth()
        {
            try
            {
                // キャンセル
                _isCancel = true;
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }
        #endregion

        #region Private Methods
        private int ExecStateCheck(ExternalAuthClient client)
        {
            int result = -1;

            _isCancel = false;

            while (true)
            {
                if (_isCancel)
                {
                    result = 3; // Cancel

                    break;
                }

                int authState = client.GetExternalAuthState();

                if (authState != ExternalAuthExecuting && authState != ExternalAuthNoExecuting)
                {
                    result = authState;

                    break;
                }
                else
                {
                    // 少し待機
                    //await Task.Delay(100);
                    System.Threading.Thread.Sleep(100);
                    System.Windows.Forms.Application.DoEvents();
                }
            }

            return result;
        }
        #endregion
    }
}
