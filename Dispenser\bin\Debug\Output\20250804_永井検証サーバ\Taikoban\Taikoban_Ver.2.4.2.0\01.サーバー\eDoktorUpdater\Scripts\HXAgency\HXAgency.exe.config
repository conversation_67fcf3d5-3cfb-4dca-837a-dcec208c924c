﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
	<appSettings>
		<!-- Trace -->
		<add key="EnableTrace" value="true"/>
		<add key="EnableDebugTrace" value="true"/>
		<add key="EnableDetailedExceptionTrace" value="true"/>
		<add key="ContainingFolderPath" value="D:\eDoktor\Taikoban\Logs\HXAgency\"/>
		<add key="TraceTermInDays" value="30"/>

		<!-- ADFS -->
		<add key="ADFSProcessName" value="msedge"/>
		<add key="ADFSTargetURL" value="https://hx-nlbsp01c.hhx.local/adfs/"/>
		<add key="ADFSTargetWindowClassName" value=""/>
		<add key="ADFSTargetWindowCaption" value="Microsoft|Edge"/>
        <add key="ADFSUserElementId" value="UserName"/>
        <add key="ADFSPasswordElementId" value="Password"/>
        <add key="ADFSButtonElementId" value="submitButton"/>
        <add key="ADFSButtonElementClassName" value="submit"/>
		<add key="ADFSRetryCount" value="10"/>
		<add key="ADFSRetryWait" value="500"/>

		<!-- HXScreenSaver -->
		<add key="HXScreenSaverProcessName" value="Fujitsu.HOPE.EGMAINHX.Tools.HXScreenSaver"/>
		<add key="HXScreenSaverTargetWindowClassName" value=""/>
		<add key="HXScreenSaverTargetWindowCaption" value="Fujitsu.HOPE.EGMAINHX.HXScreenSaver.CertView"/>
		<add key="HXScreenSaverUserElementAutomationId" value="LoginText"/>
		<add key="HXScreenSaverPasswordElementAutomationId" value="PasswordText"/>
		<add key="HXScreenSaverButtonElementAutomationId" value="SignInButton"/>
		<add key="HXScreenSaverRetryCount" value="10"/>
		<add key="HXScreenSaverRetryWait" value="500"/>
		<add key="HXScreenSaverMoveDistance" value="101"/>
	</appSettings>
</configuration>