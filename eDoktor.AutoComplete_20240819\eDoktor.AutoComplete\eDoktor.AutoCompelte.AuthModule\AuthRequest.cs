﻿// AuthRequest.cs

using System;
using System.Windows.Forms;

namespace eDoktor.AutoComplete.AuthModule
{
	public class AuthRequest : MarshalByRefObject
	{
		#region Properties
		public AuthContextType Context { get; set; }
		public AuthModeType Mode { get; set; }
		public string UserName { get; set; }
		public System.IntPtr OwnerHandle { get; set; }
		public bool TopMost { get; set; }
		public DisplayPositionType DisplayPosition { get; set; }
		public Padding Margin { get; set; }
        public bool Draggable { get; set; }
        // ▼ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
        public string TopMessage { get; set; }
        // ▲ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
        // ▼ ADD ログインボタンを自動押下しない対応 2023/01/10 eDoktor Y.Kihara
        public bool EnableDonotAutoLoginCheckbox { get; set; }
        public string DonotAutoLoginCheckboxMessage { get; set; }
		// ▲ ADD ログインボタンを自動押下しない対応 2023/01/10 eDoktor Y.Kihara
		// ▼ ADD 認証開始ボタンの表示設定追加対応 2023/03/31 eDoktor Y.Kihara
		public bool NotAllowedOneFactorAuthentication { get; set; }
		// ▲ ADD 認証開始ボタンの表示設定追加対応 2023/03/31 eDoktor Y.Kihara

		// ▼ ADD 閉じるボタンの表示設定追加対応 2024/07/22 eDoktor Y.Kihara
		public bool HiddenCloseButton { get; set; }
		// ▲ ADD 閉じるボタンの表示設定追加対応 2024/07/22 eDoktor Y.Kihara
		#endregion

		#region Protected Methods
		public override object InitializeLifetimeService()
		{
			return null;
		}
		#endregion 
	}
}
