﻿// AuthFormBase.cs

using System;
using System.Runtime.InteropServices;

namespace eDoktor.AutoComplete.AuthModule
{
	using FormExtensions;

	public partial class AuthFormBase : UI.DraggableForm
	{
		#region Properties
		public AutoComplete.AuthModule.AuthRequest AuthRequest { get; set; }
		public AutoComplete.AuthModule.AuthResponse AuthResponse { get; set; }
		#endregion

		#region Constructors
		public AuthFormBase()
		{
			InitializeComponent();
		}
		#endregion

		#region Protected Methods
		protected override void OnLoad(EventArgs e)
		{
			try
			{
				base.OnLoad(e);
				var authRequest = AuthRequest;

				if (authRequest != null)
				{
					TopMost = authRequest.TopMost;
					Draggable = authRequest.Draggable;				
					this.SetLocationFromDisplayPosition();

					if (authRequest.OwnerHandle != IntPtr.Zero)
					{
						var result = SetWindowLongPtr(new HandleRef(this, Handle), GWLP_HWNDPARENT, authRequest.OwnerHandle);

						//Common.Trace.OutputDebugTrace("SetWindowLongPtr OwnerHandle={0} result={1}", authRequest.OwnerHandle.ToInt32(), result.ToInt32());
					}
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		protected static void ForceActivate(IntPtr handle)
		{
			if (IsIconic(handle))
			{
				ShowWindowAsync(handle, SW_RESTORE);
			}

			var currentTmeout = IntPtr.Zero;
			uint foregroundWindowThreadId = 0;
			uint targetWindowThreadId = 0;
			var foregroundWindowHandle = GetForegroundWindow();

			if (foregroundWindowHandle != handle)
			{
				uint processId;
				foregroundWindowThreadId = GetWindowThreadProcessId(foregroundWindowHandle, out processId);
				targetWindowThreadId = GetWindowThreadProcessId(handle, out processId);
				AttachThreadInput(targetWindowThreadId, foregroundWindowThreadId, true);
				var tempTimeout = IntPtr.Zero;
				// 起動中の他のアプリケーションにより本来の値とは違う値が現在値として取得される場合がある
				SystemParametersInfo(SPI_GETFOREGROUNDLOCKTIMEOUT, 0, currentTmeout, 0);
				SystemParametersInfo(SPI_SETFOREGROUNDLOCKTIMEOUT, 0, tempTimeout, 0);
			}

			SetForegroundWindow(handle);

			if (foregroundWindowHandle != handle)
			{
				// 本来の値と違う値を書き込んでしまう可能性があるためSPIF_UPDATEINIFILE | SPIF_SENDCHANGEは使用しない
				SystemParametersInfo(SPI_SETFOREGROUNDLOCKTIMEOUT, 0, currentTmeout, 0/*SPIF_UPDATEINIFILE | SPIF_SENDCHANGE*/);
				AttachThreadInput(targetWindowThreadId, foregroundWindowThreadId, false);
			}
		}
		#endregion

		#region P/Invoke
		private const int GWLP_HWNDPARENT = -8;

		private static IntPtr SetWindowLongPtr(HandleRef hWnd, int nIndex, IntPtr dwNewLong)
		{
			if (IntPtr.Size == 8)
			{
				return SetWindowLongPtr64(hWnd, nIndex, dwNewLong);
			}
			else
			{
				return new IntPtr(SetWindowLong32(hWnd, nIndex, dwNewLong.ToInt32()));
			}
		}

		const int SW_RESTORE = 9;
		const uint SPI_GETFOREGROUNDLOCKTIMEOUT = 0x2000;
		const uint SPI_SETFOREGROUNDLOCKTIMEOUT = 0x2001;

		[DllImport("user32.dll", EntryPoint = "SetWindowLong")]
		private static extern int SetWindowLong32(HandleRef hWnd, int nIndex, int dwNewLong);

		[DllImport("user32.dll", EntryPoint = "SetWindowLongPtr")]
		private static extern IntPtr SetWindowLongPtr64(HandleRef hWnd, int nIndex, IntPtr dwNewLong);

		[DllImport("user32.dll")]
		static extern bool IsIconic(IntPtr hWnd);

		[DllImport("user32.dll")]
		static extern bool ShowWindowAsync(IntPtr hWnd, int nCmdShow);

		[DllImport("user32.dll")]
		static extern IntPtr GetForegroundWindow();

		[DllImport("user32.dll")]
		[return: MarshalAs(UnmanagedType.Bool)]
		static extern bool SetForegroundWindow(IntPtr hWnd);

		[DllImport("user32.dll")]
		static extern bool AttachThreadInput(uint idAttach, uint idAttachTo, bool fAttach);

		[DllImport("user32.dll", SetLastError = true)]
		static extern bool SystemParametersInfo(uint uiAction, uint uiParam, IntPtr pvParam, uint fWinIni);

		[DllImport("user32.dll", SetLastError = true)]
		static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint lpdwProcessId);
		#endregion
	}
}
