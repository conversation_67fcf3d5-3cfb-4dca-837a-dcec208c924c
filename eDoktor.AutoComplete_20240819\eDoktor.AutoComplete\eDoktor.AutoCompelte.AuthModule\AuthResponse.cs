﻿// AuthResponse.cs

using System;

namespace eDoktor.AutoComplete.AuthModule
{
	public class AuthResponse : MarshalByRefObject
	{
		#region Properties
		public AuthResultType Result { get; set; }
		public AuthMethodTypes Methods { get; set; }
        // ▼ ADD ログインボタンを自動押下しない対応 2023/01/01 eDoktor Y.Kihara
        public bool DonotAutoLogin { get; set; }
        // ▲ ADD ログインボタンを自動押下しない対応 2023/01/01 eDoktor Y.Kihara
		public string UserName { get; set; }
		#endregion

		#region Protected Methods
		public override object InitializeLifetimeService()
		{
			return null;
		}
		#endregion
	}
}
