﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace eDoktor.Taikoban.AccountImporter.Model
{
    class User
    {
        #region Properties
        //項目
        public int Id { get; set; }
        public Boolean Invalidated { get; set; }
        public Boolean LogicallyDeleted { get; set; }
        public DateTime? UsageStartDatetime { get; set; }
        public DateTime? UsageEndDatetime { get; set; }
        public String OrganizationalId { get; set; }
        public String Name { get; set; }
        public String Kana { get; set; }
        public DateTime? Birthdate { get; set; }

        public int JobId { get; set; }

        //更新チェック用フラグ
        public Boolean IsInvalidatedChanged { get; set; }
        public Boolean IsLogicallyDeletedChanged { get; set; }
        #endregion

        #region Constructors
        public User()
        {
            Id = -1;
            Invalidated = false;
            LogicallyDeleted = false;
            UsageStartDatetime = null;
            UsageEndDatetime = null;
            OrganizationalId = string.Empty;
            Name = string.Empty;
            Kana = string.Empty;
            Birthdate = null;

            JobId = -1;

            IsInvalidatedChanged = false;
            IsLogicallyDeletedChanged = false;
        }

        public User(LineAnalyzer line, Job job)
        {
            Id = -1;
            Invalidated = line.Invalidated;
            LogicallyDeleted = line.LogicallyDeleted;
            UsageStartDatetime = line.UsageStartDatetime;
            UsageEndDatetime = line.UsageEndDatetime;
            OrganizationalId = line.OrganizationalId;
            Name = line.Name;
            Kana = line.Kana;
            Birthdate = line.Birthdate;

            JobId = (job == null) ? -1 : job.Id;

            IsInvalidatedChanged = false;
            IsLogicallyDeletedChanged = false;
        }
        #endregion
    }
}
