USE [$(Param1)]
GO

/****** Object:  Table [dbo].[t_face_auth_messages]    Script Date: 2021/07/07 9:40:02 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_face_auth_messages]') AND type in (N'U'))
DROP TABLE [dbo].[t_face_auth_messages]
GO

CREATE TABLE [dbo].[t_face_auth_messages](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[message_id] [nvarchar](255) NOT NULL,
	[message] [nvarchar](max) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[registered_by] [int] NOT NULL,
	[modification_datetime] [datetime2](7) NOT NULL,
	[modified_by] [int] NOT NULL,
 CONSTRAINT [PK_t_face_auth_messages] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [IX_t_face_auth_messages] UNIQUE NONCLUSTERED 
(
	[message_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[t_face_auth_messages] ADD  CONSTRAINT [DF_t_face_auth_messages_message_id]  DEFAULT ('') FOR [message_id]
GO

ALTER TABLE [dbo].[t_face_auth_messages] ADD  CONSTRAINT [DF_t_face_auth_messages_message]  DEFAULT ('') FOR [message]
GO

ALTER TABLE [dbo].[t_face_auth_messages] ADD  CONSTRAINT [DF_t_face_auth_messages_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO

ALTER TABLE [dbo].[t_face_auth_messages] ADD  CONSTRAINT [DF_t_face_auth_messages_registered_by]  DEFAULT ((0)) FOR [registered_by]
GO

ALTER TABLE [dbo].[t_face_auth_messages] ADD  CONSTRAINT [DF_t_face_auth_messages_modification_datetime]  DEFAULT (getdate()) FOR [modification_datetime]
GO

ALTER TABLE [dbo].[t_face_auth_messages] ADD  CONSTRAINT [DF_t_face_auth_messages_modified_by]  DEFAULT ((0)) FOR [modified_by]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_messages', @level2type=N'COLUMN',@level2name=N'id'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'メッセージID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_messages', @level2type=N'COLUMN',@level2name=N'message_id'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'メッセージ内容' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_messages', @level2type=N'COLUMN',@level2name=N'message'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_messages', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_messages', @level2type=N'COLUMN',@level2name=N'registered_by'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_messages', @level2type=N'COLUMN',@level2name=N'modification_datetime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_messages', @level2type=N'COLUMN',@level2name=N'modified_by'
GO


