USE [$(Param1)]
GO
/****** Object:  Table [dbo].[t_terminal_configs]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_terminal_configs]') AND type in (N'U'))
DROP TABLE [dbo].[t_terminal_configs]
GO

CREATE TABLE [dbo].[t_terminal_configs](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[registered_by] [int] NOT NULL,
	[modification_datetime] [datetime2](7) NOT NULL,
	[modified_by] [int] NOT NULL,
	[invalidated] [bit] NOT NULL,
	[invalidation_datetime] [datetime2](7) NULL,
	[invalidated_by] [int] NOT NULL,
	[logically_deleted] [bit] NOT NULL,
	[logical_deletion_datetime] [datetime2](7) NULL,
	[logically_deleted_by] [int] NOT NULL,
	[name] [nvarchar](255) NOT NULL,
	[description] [nvarchar](255) NOT NULL,
	[windows_logon_type] [int] NOT NULL,
	[auto_logon] [int] NOT NULL,
	[shared_account_user] [nvarchar](255) NOT NULL,
	[shared_account_password] [nvarchar](255) NOT NULL,
	[shared_account_domain] [nvarchar](255) NOT NULL,
	[bio_auth_skip] [bit] NOT NULL,
	[external_authentication_scheme_usage] [int] NOT NULL,
	[external_authentication_scheme] [int] NOT NULL,
	[ldap_url] [nvarchar](max) NOT NULL,
	[adsi_authentication_code] [nvarchar](max) NOT NULL,
	[search_filter] [nvarchar](max) NOT NULL,
	[dn_of_user_to_bind] [nvarchar](max) NOT NULL,
	[password_of_user_to_bind] [nvarchar](max) NOT NULL,
	[allow_logon_on_all_failure] [int] NOT NULL,
	[password_authentication] [int] NOT NULL,
	[use_authenticate_without_ic_card] [int] NOT NULL,
	[combination_key_authenticate_without_ic_card] [nvarchar](max) NOT NULL,
	[smartcard_removal_action_at_authentication] [int] NOT NULL,
	[manage_password_expiration] [int] NOT NULL,
	[allow_password_change_during_authentication] [int] NOT NULL,
	[smartcard_removal_action_at_password_update] [int] NOT NULL,
	[allow_logon_on_password_expiration] [int] NOT NULL,
	[command_to_execute_on_password_expiration] [nvarchar](max) NOT NULL,
	[display_go_to_desktop_link] [int] NOT NULL,
	[display_shutdown_link] [int] NOT NULL,
	[smartcard_removal_action_regarding_windows_session_at_desktop] [int] NOT NULL,
	[smartcard_removal_action_at_desktop] [int] NOT NULL,
	[duration_in_seconds_before_smartcard_removal_action] [int] NOT NULL,
	[seconds_to_display_countdown_before_smartcard_removal_action] [int] NOT NULL,
	[action_on_logout] [int] NOT NULL,
	[lock_key_combination] [nvarchar](max) NOT NULL,
	[duration_in_seconds_before_lock_because_of_inactivity] [int] NOT NULL,
	[duration_in_seconds_before_logoff_because_of_inactivity] [int] NOT NULL,
	[duration_in_seconds_before_logout_because_of_inactivity] [int] NOT NULL,
	[duration_in_seconds_before_logoff_after_lock] [int] NOT NULL,
	[duration_in_seconds_before_logout_after_lock] [int] NOT NULL,
	[action_on_sbc_roaming] [int] NOT NULL,
	[command_to_execute_on_sbc_roaming] [nvarchar](max) NOT NULL,
	[processes_to_end_on_sbc_roaming] [nvarchar](max) NOT NULL,
	[windows_to_close_on_sbc_roaming] [nvarchar](max) NOT NULL,
	[action_on_non_sbc_roaming] [int] NOT NULL,
	[command_to_execute_on_non_sbc_roaming] [nvarchar](max) NOT NULL,
	[processes_to_end_on_non_sbc_roaming] [nvarchar](max) NOT NULL,
	[windows_to_close_on_non_sbc_roaming] [nvarchar](max) NOT NULL,
	[command_to_execute_on_login] [nvarchar](max) NOT NULL,
	[command_to_execute_on_logout] [nvarchar](max) NOT NULL,
	[processes_to_end_on_logout] [nvarchar](max) NOT NULL,
	[windows_to_close_on_logout] [nvarchar](max) NOT NULL,
	[simultaneously_used_terminals_display_mode] [int] NOT NULL,
	[manage_ica_session] [int] NOT NULL,
	[notes] [nvarchar](255) NOT NULL,
	[admin_sas_key_combination] [nvarchar](max) NOT NULL,
	[default_sas_key_combination] [nvarchar](max) NOT NULL,
	[default_sas_one_factor_auth_policy] [int] NOT NULL,
	[default_sas2_key_combination] [nvarchar](max) NOT NULL,
	[default_sas2_one_factor_auth_policy] [int] NOT NULL,
	[smartcard_auth_policy] [int] NOT NULL,
	[action_on_ext_auth_error] [int] NOT NULL,
	[disable_leaving_seat_lock] [int] NOT NULL,
	[login_not_allowed] [int] NOT NULL,
 CONSTRAINT [PK_t_terminal_configs] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [IX_t_terminal_configs] UNIQUE NONCLUSTERED 
(
	[name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_registered_by]  DEFAULT ((0)) FOR [registered_by]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_modification_datetime]  DEFAULT (getdate()) FOR [modification_datetime]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_modified_by]  DEFAULT ((0)) FOR [modified_by]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_invalidated]  DEFAULT ('false') FOR [invalidated]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_invalidated_by]  DEFAULT ((0)) FOR [invalidated_by]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_logically_deleted]  DEFAULT ('false') FOR [logically_deleted]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_logically_deleted_by]  DEFAULT ((0)) FOR [logically_deleted_by]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_name]  DEFAULT ('') FOR [name]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_description]  DEFAULT ('') FOR [description]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_windows_logon_type]  DEFAULT ((0)) FOR [windows_logon_type]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_auto_logon]  DEFAULT ((1)) FOR [auto_logon]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_shared_account_user]  DEFAULT ('') FOR [shared_account_user]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_shared_account_password]  DEFAULT ('') FOR [shared_account_password]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_shared_account_domain]  DEFAULT ('') FOR [shared_account_domain]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_bio_auth_skip]  DEFAULT ('false') FOR [bio_auth_skip]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_external_authentication_scheme_usage]  DEFAULT ((2)) FOR [external_authentication_scheme_usage]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_external_authentication_scheme]  DEFAULT ((0)) FOR [external_authentication_scheme]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_ldap_url]  DEFAULT ('') FOR [ldap_url]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_adsi_authentication_code]  DEFAULT ('') FOR [adsi_authentication_code]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_search_filter]  DEFAULT ('') FOR [search_filter]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_dn_of_user_to_bind]  DEFAULT ('') FOR [dn_of_user_to_bind]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_password_of_user_to_bind]  DEFAULT ('') FOR [password_of_user_to_bind]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_allow_logon_on_all_failure]  DEFAULT ((1)) FOR [allow_logon_on_all_failure]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_password_authentication]  DEFAULT ((1)) FOR [password_authentication]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_use_authenticate_without_ic_card]  DEFAULT ((0)) FOR [use_authenticate_without_ic_card]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_combination_key_authenticate_without_ic_card]  DEFAULT ('') FOR [combination_key_authenticate_without_ic_card]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_smartcard_removal_action_at_authentication]  DEFAULT ((1)) FOR [smartcard_removal_action_at_authentication]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_manage_password_expiration]  DEFAULT ((1)) FOR [manage_password_expiration]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_allow_password_change_during_authentication]  DEFAULT ((0)) FOR [allow_password_change_during_authentication]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_smartcard_removal_action_at_password_update]  DEFAULT ((0)) FOR [smartcard_removal_action_at_password_update]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_allow_logon_on_password_expiration]  DEFAULT ((0)) FOR [allow_logon_on_password_expiration]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_command_to_execute_on_password_expiration]  DEFAULT ('') FOR [command_to_execute_on_password_expiration]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_display_go_to_desktop_link]  DEFAULT ((0)) FOR [display_go_to_desktop_link]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_display_shutdown_link]  DEFAULT ((0)) FOR [display_shutdown_link]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_smartcard_removal_action_regarding_windows_session_at_desktop]  DEFAULT ((1)) FOR [smartcard_removal_action_regarding_windows_session_at_desktop]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_smartcard_removal_action_at_desktop]  DEFAULT ((0)) FOR [smartcard_removal_action_at_desktop]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_duration_in_seconds_before_smartcard_removal_action]  DEFAULT ((0)) FOR [duration_in_seconds_before_smartcard_removal_action]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_seconds_to_display_countdown_before_smartcard_removal_action]  DEFAULT ((0)) FOR [seconds_to_display_countdown_before_smartcard_removal_action]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_action_on_logout]  DEFAULT ((0)) FOR [action_on_logout]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_lock_key_combination]  DEFAULT ('') FOR [lock_key_combination]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_duration_in_seconds_before_lock_because_of_inactivity]  DEFAULT ((0)) FOR [duration_in_seconds_before_lock_because_of_inactivity]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_duration_in_seconds_before_logoff_because_of_inactivity]  DEFAULT ((0)) FOR [duration_in_seconds_before_logoff_because_of_inactivity]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_duration_in_seconds_before_logout_because_of_inactivity]  DEFAULT ((0)) FOR [duration_in_seconds_before_logout_because_of_inactivity]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_duration_in_seconds_before_logoff_after_lock]  DEFAULT ((0)) FOR [duration_in_seconds_before_logoff_after_lock]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_duration_in_seconds_before_logout_after_lock]  DEFAULT ((0)) FOR [duration_in_seconds_before_logout_after_lock]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_action_on_sbc_roaming]  DEFAULT ((0)) FOR [action_on_sbc_roaming]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_command_to_execute_on_sbc_roaming]  DEFAULT ('') FOR [command_to_execute_on_sbc_roaming]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_processes_to_end_on_sbc_roaming]  DEFAULT ('') FOR [processes_to_end_on_sbc_roaming]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_windows_to_close_on_sbc_roaming]  DEFAULT ('') FOR [windows_to_close_on_sbc_roaming]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_action_on_non_sbc_roaming]  DEFAULT ((0)) FOR [action_on_non_sbc_roaming]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_command_to_execute_on_non_sbc_roaming]  DEFAULT ('') FOR [command_to_execute_on_non_sbc_roaming]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_processes_to_end_on_non_sbc_roaming]  DEFAULT ('') FOR [processes_to_end_on_non_sbc_roaming]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_windows_to_close_on_non_sbc_roaming]  DEFAULT ('') FOR [windows_to_close_on_non_sbc_roaming]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_command_to_execute_on_login]  DEFAULT ('') FOR [command_to_execute_on_login]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_command_to_execute_on_logout]  DEFAULT ('') FOR [command_to_execute_on_logout]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_processes_to_end_on_logout]  DEFAULT ('') FOR [processes_to_end_on_logout]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_windows_to_close_on_logout]  DEFAULT ('') FOR [windows_to_close_on_logout]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_simultaneously_used_terminals_display_mode]  DEFAULT ((0)) FOR [simultaneously_used_terminals_display_mode]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_manage_ica_session]  DEFAULT ((0)) FOR [manage_ica_session]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_notes]  DEFAULT ('') FOR [notes]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_admin_sas_key_combination]  DEFAULT ('') FOR [admin_sas_key_combination]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_default_sas_key_combination]  DEFAULT ('') FOR [default_sas_key_combination]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_default_sas_one_factor_auth_policy]  DEFAULT ((0)) FOR [default_sas_one_factor_auth_policy]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_default_sas2_key_combination]  DEFAULT ('ODQ=,NzM=,NzU=') FOR [default_sas2_key_combination]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_default_sas2_one_factor_auth_policy]  DEFAULT ((3)) FOR [default_sas2_one_factor_auth_policy]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_smartcard_auth_policy]  DEFAULT ((0)) FOR [smartcard_auth_policy]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_action_on_ext_auth_error]  DEFAULT ((0)) FOR [action_on_ext_auth_error]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_disable_leaving_seat_lock]  DEFAULT ((0)) FOR [disable_leaving_seat_lock]
GO
ALTER TABLE [dbo].[t_terminal_configs] ADD  CONSTRAINT [DF_t_terminal_configs_login_not_allowed]  DEFAULT ((0)) FOR [login_not_allowed]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'registered_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'modification_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'modified_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'invalidated'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効化日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'invalidation_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効化を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'invalidated_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'logically_deleted'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'logical_deletion_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'logically_deleted_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'動作ポリシー名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'説明' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'description'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Windowsログオン方法 0:共用ユーザ 1:個人ユーザ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'windows_logon_type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'自動ログオン 0:しない 1:する 2:起動直後のみ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'auto_logon'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'共用アカウントのユーザ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'shared_account_user'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'共用アカウントのユーザパスワード' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'shared_account_password'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'共用アカウントのドメイン' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'shared_account_domain'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'生体認証のスキップ可否' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'bio_auth_skip'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ユーザをTaikoban以外で認証する 0:無効 1:有効 2:障害時のみ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'external_authentication_scheme_usage'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Taikoban以外の認証方法 0:Windows 1:LDAP' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'external_authentication_scheme'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'LDAP　URL　文字列配列' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'ldap_url'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ADSI　認証コード　文字列配列' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'adsi_authentication_code'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'検索フィルター　文字列配列' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'search_filter'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'バインドするユーザのDN　文字列配列' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'dn_of_user_to_bind'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'バインドするユーザのパスワード　文字列配列' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'password_of_user_to_bind'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'障害時にTaikoban以外の認証がNGだった場合 0:ログインしない 1:ログインする' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'allow_logon_on_all_failure'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード認証 0:しない 1:する' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'password_authentication'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ICカード無し認証画面の利用 0:利用不可 1:利用可' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'use_authenticate_without_ic_card'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ICカード無し認証画面の呼出しコンビネーションキー　数値配列' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'combination_key_authenticate_without_ic_card'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'認証時にカード抜けが発生した場合の動作 0:キャンセルしない 1:キャンセルする' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'smartcard_removal_action_at_authentication'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード有効期限管理をする 0:しない 1:する' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'manage_password_expiration'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード変更画面の利用 0:利用不可 1:利用可' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'allow_password_change_during_authentication'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'有効期限切れ又は有効期限間近の際のパスワード変更画面でのカード抜け 0:キャンセルしない 1:キャンセルする' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'smartcard_removal_action_at_password_update'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード有効期限切れでのログイン 0:許可しない 1:許可する' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'allow_logon_on_password_expiration'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード有効期限切れの場合に実行するコマンド　文字列配列' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'command_to_execute_on_password_expiration'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'デスクトップへ移動ボタン表示 0:非表示 1:表示' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'display_go_to_desktop_link'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'シャットダウンボタン表示 0:非表示 1:表示' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'display_shutdown_link'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'カード抜け動作（Windowsセッション） 0:なにもしない 1:ロック 2:ログオフ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'smartcard_removal_action_regarding_windows_session_at_desktop'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'カード抜け動作（Taikoban認証） 0:なにもしない 1:ログアウト' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'smartcard_removal_action_at_desktop'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'カード抜けアクションの猶予時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'duration_in_seconds_before_smartcard_removal_action'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'カード抜けアクションの猶予時間の警告タイマー表示開始時間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'seconds_to_display_countdown_before_smartcard_removal_action'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ログアウト時の動作 0:なにもしない 1:ロックする' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'action_on_logout'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ロック操作ショートカットキー　数値配列' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'lock_key_combination'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'端末無操作時のロックまでの猶予時間（Windowsセッション）' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'duration_in_seconds_before_lock_because_of_inactivity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'端末無操作時ログオフまでの猶予時間（Windowsセッション）' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'duration_in_seconds_before_logoff_because_of_inactivity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'端末無操作時のログアウトまでの猶予時間（Taikoban認証）' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'duration_in_seconds_before_logout_because_of_inactivity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ロック後に自動ログオフするまでの猶予時間（Windowsセッション）' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'duration_in_seconds_before_logoff_after_lock'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ロック後に自動ログアウトするまでの猶予時間（Taikoban認証）' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'duration_in_seconds_before_logout_after_lock'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'SBC端末からの認証通知時の動作 0:なにもしない 1:ロック 2:Windowsのログオフ 3:Taikobanのみログアウト' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'action_on_sbc_roaming'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'SBC端末からの認証通知時の実行コマンド　文字列配列' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'command_to_execute_on_sbc_roaming'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'SBC端末からの認証通知時にKILLするプロセス　文字列配列' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'processes_to_end_on_sbc_roaming'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'SBC端末からの認証通知時にクローズするメインウィンドウ　文字列配列' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'windows_to_close_on_sbc_roaming'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'非SBC端末からの認証通知時の動作 0:なにもしない 1:ロック 2:Windowsのログオフ 3:Taikobanのみログアウト' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'action_on_non_sbc_roaming'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'非SBC端末からの認証通知時の実行コマンド　　文字列配列' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'command_to_execute_on_non_sbc_roaming'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'非SBC端末からの認証通知時にKILLするプロセス　文字列配列' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'processes_to_end_on_non_sbc_roaming'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'非SBC端末からの認証通知時にクローズするメインウィンドウ　文字列配列' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'windows_to_close_on_non_sbc_roaming'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Taikobanログイン時の実行コマンド　文字列配列' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'command_to_execute_on_login'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Taikobanログアウト時の実行コマンド　文字列配列' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'command_to_execute_on_logout'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Taikobanログアウト時にKILLするプロセス　文字列配列' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'processes_to_end_on_logout'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Taikobanログアウト時にクローズするメインウィンドウ　文字列配列' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'windows_to_close_on_logout'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'認証後に利用中の端末以外にログインしている端末を表示 0:非表示 1:表示 2:SBC端末のみの場合は非表示' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'simultaneously_used_terminals_display_mode'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Citrix連携 0:無効 1:有効' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'manage_ica_session'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'注記' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'notes'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'管理者SASキーコンビネーション' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'admin_sas_key_combination'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'標準SASキーコンビネーション' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'default_sas_key_combination'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'標準SAS発生時の一要素認証ポリシー 0:許可しない（2要素認証必須） 1:生体認証のみ 2:生体認証を優先するがパスワード認証も可 3:パスワードのみ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'default_sas_one_factor_auth_policy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'標準SASキーコンビネーション2' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'default_sas2_key_combination'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'標準SAS2発生時の一要素認証ポリシー 0:許可しない（2要素認証必須） 1:生体認証のみ 2:生体認証を優先するがパスワード認証も可 3:パスワードのみ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'default_sas2_one_factor_auth_policy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'スマートカード認証ポリシー 0:スマートカードのみ（1要素認証） 1:生体認証のみ 2:生体認証を優先するがパスワードも可 3:パスワードのみ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'smartcard_auth_policy'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'外部認証失敗時の動作 0:認証エラー 1:パスワード＋コメント入力 2:コメント入力' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'action_on_ext_auth_error'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'離席ロック画面の利用 0:利用する 1:利用しない' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'disable_leaving_seat_lock'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ログインを拒否するか: 0=許可 1=拒否' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'login_not_allowed'
GO
