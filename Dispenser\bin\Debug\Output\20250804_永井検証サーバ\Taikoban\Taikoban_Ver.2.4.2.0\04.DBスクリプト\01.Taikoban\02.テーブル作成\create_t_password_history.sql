USE [$(Param1)]
GO
/****** Object:  Table [dbo].[t_password_history]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_password_history]') AND type in (N'U'))
DROP TABLE [dbo].[t_password_history]
GO

CREATE TABLE [dbo].[t_password_history](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[logon_id] [nvarchar](255) NOT NULL,
	[password] [nvarchar](255) NOT NULL,
 CONSTRAINT [PK_t_password_history] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_password_history] ADD  CONSTRAINT [DF_t_password_history_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO
ALTER TABLE [dbo].[t_password_history] ADD  CONSTRAINT [DF_t_password_history_logon_id]  DEFAULT ('') FOR [logon_id]
GO
ALTER TABLE [dbo].[t_password_history] ADD  CONSTRAINT [DF_t_password_history]  DEFAULT ('') FOR [password]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_password_history', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_password_history', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ログオンID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_password_history', @level2type=N'COLUMN',@level2name=N'logon_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_password_history', @level2type=N'COLUMN',@level2name=N'password'
GO
