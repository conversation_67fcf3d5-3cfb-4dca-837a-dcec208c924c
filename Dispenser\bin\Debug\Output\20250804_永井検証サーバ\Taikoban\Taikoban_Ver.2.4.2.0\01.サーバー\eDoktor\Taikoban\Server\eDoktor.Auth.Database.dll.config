﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <connectionStrings>
        <!-- 別々のnameを指定する事で複数の接続文字列を定義できます。appSettingsのConnectionStringNameのvalueに実際に使用する接続文字列のnameを指定してください。 -->
        <add name ="Taikoban" connectionString="sOEqwRggeOs5J+mrA1PTQP03iS2JvKkkwovlMRimh7NIGBkA6vsDZOSli1ja2eqMXHwU1svdQpoPpl+cAiF71W5rxEQCPpq4d1gvrg7XgSWUOxpzgXIYISoUrCN+yEURta0qlL+UpBogoGF6fBt4Nmo40KsnimIn6wmRkbT0ScNmukl9T8237Gw5Z3A5jBlOw71SlKGl0maMbRVGzGH8J5zm175t4SnPg1pNJkVvp+0=" providerName="System.Data.SqlClient"/>
    </connectionStrings>
    <appSettings>
        <!-- EncryptedPasswords -->
        <add key="AccountPasswordEncryptionEnabled" value="true"/>
        <add key="SsoPasswordEncryptionEnabled" value="true"/>
        <add key="TerminalPasswordEncryptionEnabled" value="true"/>
        <add key="TerminalConfigPasswordEncryptionEnabled" value="true"/>
        <add key="CooperativePasswordEncryptionEnabled" value="false"/>
        <!-- Connection String -->
        <add key="ConnectionStringName" value="Taikoban"/>
        <!-- LDAP -->
        <add key="LdapServer" value=""/>
        <!-- <add key="LdapDomain" value="DC=edoktor,DC=local"/> -->
        <add key="LdapDomain" value=""/>
        <!-- 出退勤打刻 -->
        <add key="TimeClockEnabled" value="false"/>
    </appSettings>
</configuration>