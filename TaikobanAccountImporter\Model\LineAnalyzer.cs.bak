﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace eDoktor.Taikoban.AccountImporter.Model
{
    /// <summary>
    /// 連携データ解析クラス
    /// </summary>
    class LineAnalyzer
    {
        #region Fields
		/// <summary>無効フラグ</summary>
        public bool Invalidated { get; set; }
        /// <summary>削除フラグ</summary>
        public bool LogicallyDeleted { get; set; }
        /// <summary>有効開始日</summary>
        public DateTime? UsageStartDatetime { get; set; }
        /// <summary>有効終了日</summary>
        public DateTime? UsageEndDatetime { get; set; }
        /// <summary>組織内ID</summary>
        public string OrganizationalId { get; set; }
        /// <summary>氏名</summary>
        public string Name { get; set; }
        /// <summary>カナ氏名</summary>
		public string Kana { get; set; }
		/// <summary>職種コード</summary>
		public string JobCode { get; set; }
		/// <summary>職種名</summary>
		public string JobName { get; set; }
        /// <summary>生年月日</summary>
        public DateTime? Birthdate { get; set; }
        /// <summary>ログインID</summary>
        public string LogonId { get; set; }
        /// <summary>パスワード</summary>
        public string Password { get; set; }
        /// <summary>暗号化パスワード</summary>
		public string EncryptedPassword { get; set; }
		/// <summary>パスワード更新日</summary>
		public DateTime? PasswordUpdateDatetime { get; set; }
		/// <summary>カードID</summary>
		public string CardId { get; set; }
		/// <summary>カード種別</summary>
		public int CardType { get; set; }
		/// <summary>SSOログインID</summary>
		public string SsoLogonId { get; set; }
		/// <summary>SSOパスワード</summary>
		public string SsoPassword { get; set; }
		// AD
        public Dictionary<string, string> LdapKey { get; set; }
        public Dictionary<string, string> LdapProperty { get; set; }
        #endregion

        #region Constructors
        /// <summary>
        /// 連携データ解析クラス
        /// </summary>
        /// <param name="line">連携データ</param>
        public LineAnalyzer(string line)
        {
            bool result;
            string strTemp;
            DateTime dtTemp;

			//読み取った文字列をバイト配列に戻す
			byte[] bufferBytes = Configuration.Configuration.FileEncoding.GetBytes(line);

			// バイト配列長の確認
			if (bufferBytes.Length != Configuration.Configuration.LineLength)
			{
				throw new FormatException("1行のバイト数が不正です。");
			}

			// 組織内ID
			OrganizationalId = Configuration.Configuration.FileEncoding.GetString(bufferBytes, Configuration.Configuration.OrganizationalIdDataOffset, Configuration.Configuration.OrganizationalIdDataLength).Trim();
			if (string.IsNullOrWhiteSpace(OrganizationalId))
			{
				//組織内ID（利用者番号）が空の場合はエラー
				throw new FormatException("組織内IDが不正です。");
			}

			// 無効フラグ
			if (Configuration.Configuration.InvalidatedDataOffset >= 0)
			{
				Invalidated = Configuration.Configuration.FileEncoding.GetString(bufferBytes, Configuration.Configuration.InvalidatedDataOffset, Configuration.Configuration.InvalidatedDataLength).Trim().Equals("1");
			}
			else
			{
				Invalidated = false;
			}

			// 削除フラグ
			if (Configuration.Configuration.LogicallyDeletedDataOffset >= 0)
			{
				LogicallyDeleted = Configuration.Configuration.FileEncoding.GetString(bufferBytes, Configuration.Configuration.LogicallyDeletedDataOffset, Configuration.Configuration.LogicallyDeletedDataLength).Trim().Equals("1");
			}
			else
			{
				LogicallyDeleted = false;
			}

			// 有効開始日
			if (Configuration.Configuration.UsageStartDatetimeDataOffset >= 0)
			{
				strTemp = Configuration.Configuration.FileEncoding.GetString(bufferBytes, Configuration.Configuration.UsageStartDatetimeDataOffset, Configuration.Configuration.UsageStartDatetimeDataLength);
				// 特定文字列ならNULLと判断する
				if (Configuration.Configuration.UsageStartDatetimeDataNullList.Contains(strTemp))
				{
					UsageStartDatetime = null;
				}
				else
				{
					result = DateTime.TryParseExact(strTemp, Configuration.Configuration.UsageStartDatetimeDataFormat, null, System.Globalization.DateTimeStyles.None, out dtTemp);

					if (!result)
					{
						throw new FormatException(string.Format("有効開始日が不正です。[組織内ID={0}]", OrganizationalId));
					}

					UsageStartDatetime = dtTemp;
				}
			}
			else
			{
				UsageStartDatetime = null;
			}

			// 有効終了日
			if (Configuration.Configuration.UsageEndDatetimeDataOffset >= 0)
			{
				strTemp = Configuration.Configuration.FileEncoding.GetString(bufferBytes, Configuration.Configuration.UsageEndDatetimeDataOffset, Configuration.Configuration.UsageEndDatetimeDataLength);
				// 特定文字列ならNULLと判断する
				if (Configuration.Configuration.UsageEndDatetimeDataNullList.Contains(strTemp))
				{
					UsageEndDatetime = null;
				}
				else
				{
					result = DateTime.TryParseExact(strTemp, Configuration.Configuration.UsageEndDatetimeDataFormat, null, System.Globalization.DateTimeStyles.None, out dtTemp);

					if (!result)
					{
						throw new FormatException(string.Format("有効終了日が不正です。[組織内ID={0}]", OrganizationalId));
					}

					UsageEndDatetime = dtTemp.AddHours(23).AddMinutes(59).AddSeconds(59);
				}
			}
			else
			{
				UsageEndDatetime = null;
			}

			// 氏名
			Name = Configuration.Configuration.FileEncoding.GetString(bufferBytes, Configuration.Configuration.NameDataOffset, Configuration.Configuration.NameDataLength).Trim();
			if (Name.Equals(String.Empty))
			{
				//氏名が空の場合はエラー
				throw new FormatException(string.Format("利用者漢字氏名が不正です。[組織内ID={0}]", OrganizationalId));
			}

			// カナ
			if (Configuration.Configuration.KanaDataOffset >= 0)
			{
				Kana = Configuration.Configuration.FileEncoding.GetString(bufferBytes, Configuration.Configuration.KanaDataOffset, Configuration.Configuration.KanaDataLength).Trim();
			}
			else
			{
				Kana = string.Empty;
			}

			// 生年月日
			if (Configuration.Configuration.BirthdateDataOffset >= 0)
			{
				strTemp = Configuration.Configuration.FileEncoding.GetString(bufferBytes, Configuration.Configuration.BirthdateDataOffset, Configuration.Configuration.BirthdateDataLength);
				// 特定文字列ならNULLと判断する
				if (Configuration.Configuration.BirthdateDataNullList.Contains(strTemp))
				{
					Birthdate = null;
				}
				else
				{
					result = DateTime.TryParseExact(strTemp, Configuration.Configuration.BirthdateDataFormat, null, System.Globalization.DateTimeStyles.None, out dtTemp);

					if (!result)
					{
						throw new FormatException(string.Format("生年月日が不正です。[組織内ID={0}]", OrganizationalId));
					}

					Birthdate = dtTemp;
				}
			}
			else
			{
				Birthdate = null;
			}

			// 職種コード
			if (Configuration.Configuration.JobCodeDataOffset >= 0)
			{
				JobCode = Configuration.Configuration.FileEncoding.GetString(bufferBytes, Configuration.Configuration.JobCodeDataOffset, Configuration.Configuration.JobCodeDataLength).Trim();
			}
			else
			{
				JobCode = string.Empty;
			}
			// 職種名
			if (Configuration.Configuration.JobNameDataOffset >= 0)
			{
				JobName = Configuration.Configuration.FileEncoding.GetString(bufferBytes, Configuration.Configuration.JobNameDataOffset, Configuration.Configuration.JobNameDataLength).Trim();
			}
			else
			{
				JobName = string.Empty;
			}

			// ログインID
			LogonId = Configuration.Configuration.FileEncoding.GetString(bufferBytes, Configuration.Configuration.LogonIdDataOffset, Configuration.Configuration.LogonIdDataLength).Trim();
			if (string.IsNullOrWhiteSpace(LogonId))
			{
				//ログインID（利用者番号）が空の場合はエラー
				throw new FormatException(string.Format("ログオンIDが不正です。[組織内ID={0}]", OrganizationalId));
			}

			// パスワード、暗号化パスワード
			if (Configuration.Configuration.PasswordDataOffset >= 0)
			{
				strTemp = Configuration.Configuration.FileEncoding.GetString(bufferBytes, Configuration.Configuration.PasswordDataOffset, Configuration.Configuration.PasswordDataLength).Trim();
				if (string.IsNullOrWhiteSpace(strTemp))
				{
					// パスワードが空の場合はエラー
					throw new FormatException(string.Format("パスワードが不正です。[ログオンID={0}]", LogonId));
				}
				if (Configuration.Configuration.PasswordDataIsEncrypted)
				{
					if (Configuration.Configuration.PasswordDataDecryptMethod == Configuration.Configuration.PasswordDataDecryptMethodEnum.None)
					{
						Password = Configuration.Configuration.PasswordDataInitial;
						EncryptedPassword = strTemp;
					}
					else
					{
						// todo 復号処理
						Password = Configuration.Configuration.PasswordDataInitial;
						EncryptedPassword = strTemp;
					}
				}
				else
				{
					Password = strTemp;
					EncryptedPassword = string.Empty;
				}
			}
			else
			{
				Password = string.Empty;
				EncryptedPassword = string.Empty;
			}

			// パスワード更新日
			if (Configuration.Configuration.PasswordUpdateDatetimeDataOffset >= 0)
			{
				strTemp = Configuration.Configuration.FileEncoding.GetString(bufferBytes, Configuration.Configuration.PasswordUpdateDatetimeDataOffset, Configuration.Configuration.PasswordUpdateDatetimeDataLength);
				// 特定文字列ならNULLと判断する
				if (Configuration.Configuration.PasswordUpdateDatetimeDataNullList.Contains(strTemp))
				{
					PasswordUpdateDatetime = null;
				}
				else
				{
					result = DateTime.TryParseExact(strTemp, Configuration.Configuration.PasswordUpdateDatetimeDataFormat, null, System.Globalization.DateTimeStyles.None, out dtTemp);

					if (!result)
					{
						throw new FormatException(string.Format("更新日付、更新時間が不正です。[ID={0},DATA={1},FORMAT={2}]", OrganizationalId, strTemp, Configuration.Configuration.PasswordUpdateDatetimeDataFormat));
					}

					PasswordUpdateDatetime = dtTemp;
				}
			}
			else
			{
				PasswordUpdateDatetime = null;
			}
            
			// AD連携
			if (Configuration.Configuration.SyncronizeAd)
			{
				LdapKey = new Dictionary<string,string>();
				LdapProperty = new Dictionary<string,string>();

				foreach (eDoktor.Taikoban.AccountImporter.Configuration.ContentConfigurationElement content in Configuration.Configuration.Contents)
				{
					string temp = Configuration.Configuration.FileEncoding.GetString(bufferBytes, content.Offset, content.Lenght).Trim();
					temp = string.Format("{0}{1}{2}", content.Prefix, temp, content.Suffix);

					if (content.IsKey && string.IsNullOrWhiteSpace(temp))
					{
						throw new FormatException(string.Format("ADキーが不正です。({0})[ログオンID={1}]", content.Name, LogonId));
					}

					if (content.IsKey)
					{
						LdapKey.Add(content.PropertyName, temp);
					}
					else
					{
						LdapProperty.Add(content.PropertyName, temp);
					}
				}
			}
        }
		
        /// <summary>
        /// 連携データ解析クラス
        /// </summary>
        /// <param name="line">連携データ</param>
        public LineAnalyzer(string[] csv)
		{
			bool result;
			string strTemp;
			DateTime dtTemp;

			// 項目数の確認
			if (csv.Length != Configuration.Configuration.LineLength)
			{
				throw new FormatException("1行の項目数が不正です。");
			}

			// 組織内ID
			OrganizationalId = csv[Configuration.Configuration.OrganizationalIdDataOffset].Trim();
			if (string.IsNullOrWhiteSpace(OrganizationalId))
			{
				//組織内IDが空の場合はエラー
				throw new FormatException("組織内IDが不正です。");
			}

			// 無効フラグ
			if (Configuration.Configuration.InvalidatedDataOffset >= 0)
			{
				Invalidated = csv[Configuration.Configuration.InvalidatedDataOffset].Equals("1");
			}
			else
			{
				Invalidated = false;
			}

			// 削除フラグ
			if (Configuration.Configuration.LogicallyDeletedDataOffset >= 0)
			{	
				LogicallyDeleted = csv[Configuration.Configuration.LogicallyDeletedDataOffset].Equals("1");
			}
			else
			{
				LogicallyDeleted = false;
			}

			// 有効開始日
			if (Configuration.Configuration.UsageStartDatetimeDataOffset >= 0)
			{
				strTemp = csv[Configuration.Configuration.UsageStartDatetimeDataOffset];
				// 特定文字列ならNULLと判断する
				if (string.IsNullOrWhiteSpace(strTemp) || Configuration.Configuration.UsageStartDatetimeDataNullList.Contains(strTemp))
				{
					UsageStartDatetime = null;
				}
				else
				{
					result = DateTime.TryParseExact(strTemp, Configuration.Configuration.UsageStartDatetimeDataFormat, null, System.Globalization.DateTimeStyles.None, out dtTemp);

					if (!result)
					{
						throw new FormatException(string.Format("有効開始日が不正です。[組織内ID={0}]", OrganizationalId));
					}

					UsageStartDatetime = dtTemp;
				}
			}
			else
			{
				UsageStartDatetime = null;
			}

			// 有効終了日
			if (Configuration.Configuration.UsageEndDatetimeDataOffset >= 0)
			{
				strTemp = csv[Configuration.Configuration.UsageEndDatetimeDataOffset];
				// 特定文字列ならNULLと判断する
				if (string.IsNullOrWhiteSpace(strTemp) || Configuration.Configuration.UsageEndDatetimeDataNullList.Contains(strTemp))
				{
					UsageEndDatetime = null;
				}
				else
				{
					result = DateTime.TryParseExact(strTemp, Configuration.Configuration.UsageEndDatetimeDataFormat, null, System.Globalization.DateTimeStyles.None, out dtTemp);

					if (!result)
					{
						throw new FormatException(string.Format("有効終了日が不正です。[組織内ID={0}]", OrganizationalId));
					}

					UsageEndDatetime = dtTemp.AddHours(23).AddMinutes(59).AddSeconds(59);
				}
			}
			else
			{
				UsageEndDatetime = null;
			}

			// 氏名
			Name = csv[Configuration.Configuration.NameDataOffset].Trim();
			if (string.IsNullOrWhiteSpace(Name))
			{
				//氏名が空の場合はエラー
				throw new FormatException(string.Format("氏名が不正です。[組織内ID={0}]", OrganizationalId));
			}

			// カナ氏名
			if (Configuration.Configuration.KanaDataOffset >= 0)
			{
				Kana = csv[Configuration.Configuration.KanaDataOffset].Trim();
			}
			else
			{
				Kana = string.Empty;
			}

			// 生年月日
			if (Configuration.Configuration.BirthdateDataOffset >= 0)
			{
				strTemp = csv[Configuration.Configuration.BirthdateDataOffset];
				// 特定文字列ならNULLと判断する
				if (string.IsNullOrWhiteSpace(strTemp) || Configuration.Configuration.BirthdateDataNullList.Contains(strTemp))
				{
					Birthdate = null;
				}
				else
				{
					result = DateTime.TryParseExact(strTemp, Configuration.Configuration.BirthdateDataFormat, null, System.Globalization.DateTimeStyles.None, out dtTemp);

					if (!result)
					{
						throw new FormatException(string.Format("生年月日が不正です。[組織内ID={0}]", OrganizationalId));
					}

					Birthdate = dtTemp;
				}
			}
			else
			{
				Birthdate = null;
			}

			// 職種コード
			if (Configuration.Configuration.JobCodeDataOffset >= 0)
			{
				JobCode = csv[Configuration.Configuration.JobCodeDataOffset].Trim();
			}
			else
			{
				JobCode = string.Empty;
			}
			// 職種名
			if (Configuration.Configuration.JobNameDataOffset >= 0)
			{
				JobName = csv[Configuration.Configuration.JobNameDataOffset].Trim();
			}
			else
			{
				JobName = string.Empty;
			}

			// ログインID
			LogonId = csv[Configuration.Configuration.LogonIdDataOffset].Trim();
			if (string.IsNullOrWhiteSpace(LogonId))
			{
				//ログインID（利用者番号）が空の場合はエラー
				throw new FormatException(string.Format("ログオンIDが不正です。[組織内ID={0}]", OrganizationalId));
			}

			// パスワード、暗号化パスワード
			if (Configuration.Configuration.PasswordDataOffset >= 0)
			{
				strTemp = csv[Configuration.Configuration.PasswordDataOffset].Trim();
				if (string.IsNullOrWhiteSpace(strTemp))
				{
					// パスワードが空の場合はエラー
					throw new FormatException(string.Format("パスワードが不正です。[ログオンID={0}]", LogonId));
				}
				if (Configuration.Configuration.PasswordDataIsEncrypted)
				{
					if (Configuration.Configuration.PasswordDataDecryptMethod == Configuration.Configuration.PasswordDataDecryptMethodEnum.None)
					{
						Password = Configuration.Configuration.PasswordDataInitial;
						EncryptedPassword = strTemp;
					}
					else
					{
						// todo
						Password = Configuration.Configuration.PasswordDataInitial;
						EncryptedPassword = strTemp;
					}
				}
				else
				{
					Password = strTemp;
					EncryptedPassword = string.Empty;
				}
			}
			else
			{
				Password = string.Empty;
				EncryptedPassword = string.Empty;
			}

			// パスワード更新日
			if (Configuration.Configuration.PasswordUpdateDatetimeDataOffset >= 0)
			{
				strTemp = csv[Configuration.Configuration.PasswordUpdateDatetimeDataOffset];
				// 特定文字列ならNULLと判断する
				if (string.IsNullOrWhiteSpace(strTemp) || Configuration.Configuration.PasswordUpdateDatetimeDataNullList.Contains(strTemp))
				{
					PasswordUpdateDatetime = null;
				}
				else
				{
					result = DateTime.TryParseExact(strTemp, Configuration.Configuration.PasswordUpdateDatetimeDataFormat, null, System.Globalization.DateTimeStyles.None, out dtTemp);

					if (!result)
					{
						throw new FormatException(string.Format("更新日付、更新時間が不正です。[ログオンID={0}]", LogonId));
					}

					PasswordUpdateDatetime = dtTemp;
				}
			}
			else
			{
				PasswordUpdateDatetime = null;
			}

			// AD連携
			if (Configuration.Configuration.SyncronizeAd)
			{
				LdapKey = new Dictionary<string, string>();
				LdapProperty = new Dictionary<string, string>();

				foreach (eDoktor.Taikoban.AccountImporter.Configuration.ContentConfigurationElement content in Configuration.Configuration.Contents)
				{
					string temp = csv[content.Offset].Trim();
					temp = string.Format("{0}{1}{2}", content.Prefix, temp, content.Suffix);

					if (content.IsKey && string.IsNullOrWhiteSpace(temp))
					{
						throw new FormatException(string.Format("ADキーが不正です。({0})[ログオンID={1}]", content.Name, LogonId));
					}

					if (content.IsKey)
					{
						LdapKey.Add(content.PropertyName, temp);
					}
					else
					{
						LdapProperty.Add(content.PropertyName, temp);
					}
				}
			}
		}
        #endregion
    }
}
