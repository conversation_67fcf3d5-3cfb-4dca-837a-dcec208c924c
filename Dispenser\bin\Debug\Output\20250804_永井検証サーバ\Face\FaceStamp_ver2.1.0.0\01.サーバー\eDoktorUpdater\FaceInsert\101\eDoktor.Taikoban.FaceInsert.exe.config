﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
    </startup>
	<appSettings>
		<!-- Trace -->
		<add key="EnableTrace" value="true"/>
		<add key="EnableDebugTrace" value="true"/>
		<add key="EnableDetailedExceptionTrace" value="true"/>
		<add key="ContainingFolderPath" value=".\logs\"/>
		<add key="TraceTermInDays" value="30"/>
		<add key="StaffId" value="ABC12345"/>
		<!--<add key="SystemId" value="vYB9oqzZVDJ4VKw9Kcoadw==" />-->
		<add key="SystemId" value="tLH9NK2R2RSYIq+YVGCkJQ==" />
		<add key="SecurityKey" value="Lhh8Haj6u2Hqg+m8vQ6fDA==" />
		<!-- ファイル選択画面用ファイルパス -->
		<add key="ImageInputFilePath" value=".\" />
		<!-- 一括登録画面用ファイルパス -->
		<add key="CsvInputFilePath" value=".\" />
		<add key="CsvInputFileName" value="" />
		<!-- 一括登録画面CSV出力用ファイルパス -->
		<add key="CsvOutputFilePath" value=".\" />
		<add key="CsvOutputFileName" value="" />

		<!-- カメラ画像反転フラグ(true時反転) -->
		<add key="IsFlipHorizon" value="true"/>

		<!-- ▼ ADD 顔登録のなりすまし判定ONOFF対応 2023/11/20 whizz Y.Tani -->
		<!-- なりすまし判定フラグ(顔登録用) true : なりすまし判定あり、false : なりすまし判定なし -->
		<add key="IsLivenessCheckForFaceInsert" value="true"/>
		<!-- ▲ ADD 顔登録のなりすまし判定ONOFF対応 2023/11/20 whizz Y.Tani -->

		<!-- ▼ ADD 撮影範囲調整対応 2023/12/12 whizz Y.Tani -->
		<add key="PictureBoxLocationX" value="220"/>
		<add key="PictureBoxLocationY" value="169"/>
		<!-- ▲ ADD 撮影範囲調整対応 2023/12/12 whizz Y.Tani -->

		<!-- ▼ ADD なりすましエラーデザイン修正対応 2023/12/20 whizz Y.Tani -->
		<!-- なりすましエラーメッセージ表示フラグ true : 表示あり、false : 表示なし -->
		<add key="LivenessCheckErrMsgDisp" value="false"/>
		<!-- ▲ ADD なりすましエラーデザイン修正対応 2023/12/20 whizz Y.Tani -->
	</appSettings>
	<connectionStrings>
		<!-- 開発サーバー(起動時にSSOの確認が必要なため) -->
		<add name="Taikoban" connectionString="7vJjG5OBdr5/IOwdmd/cvEj0KqP2gqxynCRk5Zd70ydNZY2yLSwEzq4Wmp/qWXzyWQozc7ThPnKJyZBLdLSJHyPKUWuWM1rMN/YY+SEv9I4TIO64OC8Lkkg4GOxanE0u" providerName="System.Data.SqlClient"/>
		<!-- ローカルサーバー -->
		<!--<add name="Taikoban" connectionString="+ap8Z5YfhTkJu570YsTJa6gtpafcEBgnd/M0Un/T1MKoFiUSxlpMC4ovIfOMQGJjXFFaHRIKPCwY/kxgWXcHfFWfK6sPBjct9m6aKDHfUvwULV+KWqADRuPiK12tHNTp" providerName="System.Data.SqlClient"/>-->
	</connectionStrings>
	<runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.0.0.0" newVersion="5.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>