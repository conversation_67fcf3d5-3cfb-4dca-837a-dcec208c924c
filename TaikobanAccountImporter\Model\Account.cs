﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace eDoktor.Taikoban.AccountImporter.Model
{
    class Account
    {
        #region Properties
        //項目
        public int Id { get; set; }
        public bool Invalidated { get; set; }
        public bool LogicallyDeleted { get; set; }
        public DateTime? UsageStartDatetime { get; set; }
        public DateTime? UsageEndDatetime { get; set; }
        public string LogonId { get; set; }
        public string Password { get; set; }
        public string EncryptedPassword { get; set; }
        public string LogonTo { get; set; }
        public DateTime? PasswordUpdateDatetime { get; set; }
        public int UserId { get; set; }
        public int JobId { get; set; }
        public int GroupId { get; set; }
        public int PrivilegeTemplateId { get; set; }

        //更新チェック用フラグ
        public Boolean IsInvalidatedChanged { get; set; }
        public Boolean IsLogicallyDeletedChanged { get; set; }
        public Boolean IsPasswordChanged { get; set; }
        #endregion

        #region Constructors
        public Account()
        {
            Id = -1;
            
            Invalidated = false;
            LogicallyDeleted = false;
            UsageStartDatetime = null;
            UsageEndDatetime = null;
            LogonId = string.Empty;
            Password = string.Empty;
            EncryptedPassword = string.Empty;
            LogonTo = string.Empty;
            PasswordUpdateDatetime = null;
            UserId = -1;
            JobId = -1;
            GroupId = -1;
            PrivilegeTemplateId = -1;
        
            IsInvalidatedChanged = false;
            IsLogicallyDeletedChanged = false;
            IsPasswordChanged = false;
        }

        public Account(LineAnalyzer line, User user, Job job)
        {
            Id = -1;

            Invalidated = line.Invalidated;
            LogicallyDeleted = line.LogicallyDeleted;
            UsageStartDatetime = line.UsageStartDatetime;
            UsageEndDatetime = line.UsageEndDatetime;
            LogonId = line.LogonId;
            Password = line.Password;
            EncryptedPassword = line.EncryptedPassword;
            LogonTo = Configuration.Configuration.LogonTo;
            PasswordUpdateDatetime = line.PasswordUpdateDatetime;
            UserId = user.Id;
            JobId = (job == null) ? -1 : job.Id;
			GroupId = (job == null) ? Configuration.Configuration.DefaultAccountGroupID : job.AccountGroupId;
			PrivilegeTemplateId = (job == null) ? Configuration.Configuration.DefaultPrivilegeTemplateID : job.PrivilegeTemplateId;

            IsInvalidatedChanged = false;
            IsLogicallyDeletedChanged = false;
            IsPasswordChanged = false;
        }
        #endregion
    }
}
