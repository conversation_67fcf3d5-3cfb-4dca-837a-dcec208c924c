﻿// SystemFaultPage.cs

using System;
using System.Windows.Forms;

namespace eDoktor.AutoComplete.AuthUI
{
	using Common.EventExtensions;

	partial class SystemFaultPage : PageBase
	{
		#region Events
		public event EventHandler OKButtonClick;
		#endregion

		#region Constructors
		public SystemFaultPage()
		{
			InitializeComponent();
			ClickThroughControls = new Control[] { this, messageLabel };
		}
		#endregion

		#region Protected Methods
		protected override void OnVisibleChanged(EventArgs e)
		{
			base.OnVisibleChanged(e);

			if (Visible)
			{
				okButton.Focus();
			}
		}

		protected override bool ProcessDialogKey(Keys keyData)
		{
			try
			{
				if ((((keyData & Keys.KeyCode) == Keys.Return) || ((keyData & Keys.KeyCode) == Keys.Escape) || ((keyData & Keys.KeyCode) == Keys.Space)) && ((keyData & (Keys.Alt | Keys.Control)) == Keys.None) && (ActiveControl is IButtonControl))
				{
					(ActiveControl as IButtonControl).PerformClick();
					return true;
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}

			return base.ProcessDialogKey(keyData);
		}
		#endregion

		#region Private Methods
		private void OnOKButtonClick(object sender, EventArgs e)
		{
			try
			{
				OKButtonClick.SafeInvoke(this, e);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion
	}
}
