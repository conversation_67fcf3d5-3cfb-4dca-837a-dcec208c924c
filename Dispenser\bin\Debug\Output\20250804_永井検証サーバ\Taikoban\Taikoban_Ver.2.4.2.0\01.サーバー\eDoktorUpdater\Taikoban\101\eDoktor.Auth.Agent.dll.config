﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <appSettings>
        <!-- Shortcut Key Combinations -->
        <add key="LockShortcutKeyCombination" value=""/>
        <!-- Context Menu -->
        <add key="TerminalMenuEnabled" value="false"/>
        <add key="AboutMenuEnabled" value="false"/>
        <add key="ExitMenuEnabled" value="false"/>
        <!-- Secure Desktop  -->
        <add key="SecureDesktopEnabled" value="false"/>
        <add key="SecureDesktopMessage" value="デスクトップを準備しています。"/>
        <add key="SecureDesktopPersistenceTimeout" value="0"/>
        <!-- On-Screen Display -->
        <add key="OSDLockFormat" value="{0}秒後にロックされます。"/>
        <add key="OSDLogoffFormat" value="{0}秒後にログオフします。"/>
        <add key="OSDLogoutFormat" value="{0}秒後にログアウトされます。"/>
        <add key="SystemFaultRecoveryLockEnabled" value="false"/>
        <add key="SystemFaultRecoveryLockMessageFormat" value="障害復旧のため、{0}秒後にロックされます。"/>
        <add key="SystemFaultRecoveryLockCountingSeconds" value="61"/>
        <add key="SystemFaultRecoveryLockDisplaySeconds" value="60"/>
        <!-- IgnoringCardType 0:None 1:InvalidCard 2:InvalidOrOtherUsersCard -->
        <add key="IgnoringCardType" value="1"/>
        <!-- DurationInSecondsBeforeLogoutAfterLeavingSeatLock -1:Disabled -->
        <add key="DurationInSecondsBeforeLogoutAfterLeavingSeatLock" value="-1"/>
    </appSettings>
</configuration>