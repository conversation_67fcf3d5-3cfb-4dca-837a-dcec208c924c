USE [$(Param1)]
GO
/****** Object:  Table [dbo].[t_services_status]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_services_status]') AND type in (N'U'))
DROP TABLE [dbo].[t_services_status]
GO

CREATE TABLE [dbo].[t_services_status](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[registered_by] [int] NOT NULL,
	[modification_datetime] [datetime2](7) NOT NULL,
	[modified_by] [int] NOT NULL,
	[invalidated] [bit] NOT NULL,
	[invalidation_datetime] [datetime2](7) NULL,
	[invalidated_by] [int] NOT NULL,
	[logically_deleted] [bit] NOT NULL,
	[logical_deletion_datetime] [datetime2](7) NULL,
	[logically_deleted_by] [int] NOT NULL,
	[is_service] [int] NOT NULL,
	[name] [nvarchar](255) NOT NULL,
	[display_name] [nvarchar](255) NOT NULL,
	[terminal_name] [nvarchar](255) NOT NULL,
	[whether_to_restart] [int] NOT NULL,
	[whether_to_continue_restart] [int] NOT NULL,
	[service_for] [int] NOT NULL,
	[description] [nvarchar](255) NOT NULL,
	[status] [int] NOT NULL,
	[display_order] [int] NOT NULL,
	[notes] [nvarchar](255) NOT NULL,
 CONSTRAINT [PK_t_services_status] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [IX_t_services_status] UNIQUE NONCLUSTERED 
(
	[display_name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_services_status] ADD  CONSTRAINT [DF_t_services_status_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO
ALTER TABLE [dbo].[t_services_status] ADD  CONSTRAINT [DF_t_services_status_registered_by]  DEFAULT ((0)) FOR [registered_by]
GO
ALTER TABLE [dbo].[t_services_status] ADD  CONSTRAINT [DF_t_services_status_modification_datetime]  DEFAULT (getdate()) FOR [modification_datetime]
GO
ALTER TABLE [dbo].[t_services_status] ADD  CONSTRAINT [DF_t_services_status_modified_by]  DEFAULT ((0)) FOR [modified_by]
GO
ALTER TABLE [dbo].[t_services_status] ADD  CONSTRAINT [DF_t_service_statuses_invalidated]  DEFAULT ('false') FOR [invalidated]
GO
ALTER TABLE [dbo].[t_services_status] ADD  CONSTRAINT [DF_t_service_statuses_invalidated_by]  DEFAULT ((0)) FOR [invalidated_by]
GO
ALTER TABLE [dbo].[t_services_status] ADD  CONSTRAINT [DF_t_service_statuses_logically_deleted]  DEFAULT ('false') FOR [logically_deleted]
GO
ALTER TABLE [dbo].[t_services_status] ADD  CONSTRAINT [DF_t_service_statuses_logically_deleted_by]  DEFAULT ((0)) FOR [logically_deleted_by]
GO
ALTER TABLE [dbo].[t_services_status] ADD  CONSTRAINT [DF_t_services_status_is_service]  DEFAULT ((0)) FOR [is_service]
GO
ALTER TABLE [dbo].[t_services_status] ADD  CONSTRAINT [DF_t_services_status_name]  DEFAULT ('') FOR [name]
GO
ALTER TABLE [dbo].[t_services_status] ADD  CONSTRAINT [DF_t_services_status_display_name]  DEFAULT ('') FOR [display_name]
GO
ALTER TABLE [dbo].[t_services_status] ADD  CONSTRAINT [DF_t_services_status_terminal_name]  DEFAULT ('') FOR [terminal_name]
GO
ALTER TABLE [dbo].[t_services_status] ADD  CONSTRAINT [DF_t_services_status_whether_to_restart]  DEFAULT ((0)) FOR [whether_to_restart]
GO
ALTER TABLE [dbo].[t_services_status] ADD  CONSTRAINT [DF_t_services_status_whether_to_continue_restart]  DEFAULT ((0)) FOR [whether_to_continue_restart]
GO
ALTER TABLE [dbo].[t_services_status] ADD  CONSTRAINT [DF_t_services_status_service_for]  DEFAULT ((0)) FOR [service_for]
GO
ALTER TABLE [dbo].[t_services_status] ADD  CONSTRAINT [DF_t_services_status_description]  DEFAULT ('') FOR [description]
GO
ALTER TABLE [dbo].[t_services_status] ADD  CONSTRAINT [DF_t_services_status_status]  DEFAULT ((0)) FOR [status]
GO
ALTER TABLE [dbo].[t_services_status] ADD  CONSTRAINT [DF_t_services_status_display_order]  DEFAULT ((0)) FOR [display_order]
GO
ALTER TABLE [dbo].[t_services_status] ADD  CONSTRAINT [DF_t_services_status_notes]  DEFAULT ('') FOR [notes]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'registered_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'modification_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'modified_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'invalidated'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効化日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'invalidation_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効化を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'invalidated_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'logically_deleted'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'logical_deletion_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'logically_deleted_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'サービス／プロセス 0:サービス 1:プロセス' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'is_service'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'サービス／プロセス名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'サービス／プロセス表示名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'display_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'端末名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'terminal_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'再起動するか 0:しない 1:する' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'whether_to_restart'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'エラー発報後に再起動を続けるか 0:続けない 1:続ける' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'whether_to_continue_restart'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'どのサービス用か 0:監視サービス用 1:通知サービス用' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'service_for'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'説明' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'description'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'状態 0:STOP 1:RUNNING' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'表示順' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'display_order'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'注記' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_services_status', @level2type=N'COLUMN',@level2name=N'notes'
GO
