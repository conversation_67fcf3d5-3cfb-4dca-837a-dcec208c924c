USE [$(Param1)]
GO

/****** Object:  Table [dbo].[t_face_auth_templates]    Script Date: 2021/07/07 9:52:39 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_face_auth_templates]') AND type in (N'U'))
DROP TABLE [dbo].[t_face_auth_templates]
GO

CREATE TABLE [dbo].[t_face_auth_templates](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[account_id] [int] NOT NULL,
	[template_id] [int] NOT NULL,
	[template_data] [nvarchar](max) NOT NULL,
	[template_quality] [float] NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[registered_by] [int] NOT NULL,
	[modification_datetime] [datetime2](7) NOT NULL,
	[modified_by] [int] NOT NULL,
	[mask_score] [float] NOT NULL,
 CONSTRAINT [PK_t_face_auth_templates] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[t_face_auth_templates] ADD  CONSTRAINT [DF_t_face_auth_templates_account_id]  DEFAULT ((0)) FOR [account_id]
GO

ALTER TABLE [dbo].[t_face_auth_templates] ADD  CONSTRAINT [DF_t_face_auth_templates_template_id]  DEFAULT ((0)) FOR [template_id]
GO

ALTER TABLE [dbo].[t_face_auth_templates] ADD  CONSTRAINT [DF_t_face_auth_templates_template_quality]  DEFAULT ((0)) FOR [template_quality]
GO

ALTER TABLE [dbo].[t_face_auth_templates] ADD  CONSTRAINT [DF_t_face_auth_templates_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO

ALTER TABLE [dbo].[t_face_auth_templates] ADD  CONSTRAINT [DF_t_face_auth_templates_registered_by]  DEFAULT ((0)) FOR [registered_by]
GO

ALTER TABLE [dbo].[t_face_auth_templates] ADD  CONSTRAINT [DF_t_face_auth_templates_modification_datetime]  DEFAULT (getdate()) FOR [modification_datetime]
GO

ALTER TABLE [dbo].[t_face_auth_templates] ADD  CONSTRAINT [DF_t_face_auth_templates_modified_by]  DEFAULT ((0)) FOR [modified_by]
GO

ALTER TABLE [dbo].[t_face_auth_templates] ADD  CONSTRAINT [DF_t_face_auth_templates_mask_score]  DEFAULT ((0)) FOR [mask_score]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_templates', @level2type=N'COLUMN',@level2name=N'id'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'アカウントID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_templates', @level2type=N'COLUMN',@level2name=N'account_id'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'テンプレートID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_templates', @level2type=N'COLUMN',@level2name=N'template_id'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'テンプレートデータ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_templates', @level2type=N'COLUMN',@level2name=N'template_data'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'テンプレート評価値' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_templates', @level2type=N'COLUMN',@level2name=N'template_quality'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_templates', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_templates', @level2type=N'COLUMN',@level2name=N'registered_by'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_templates', @level2type=N'COLUMN',@level2name=N'modification_datetime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_templates', @level2type=N'COLUMN',@level2name=N'modified_by'
GO


