<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="eDoktor.Taikoban.AccountImporter.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
        </sectionGroup>
    </configSections>
    <appSettings>
      <!-- Trace -->
      <add key="EnableTrace" value="true"/>
      <add key="EnableDebugTrace" value="true"/>
      <add key="EnableDetailedExceptionTrace" value="true"/>
      <add key="ContainingFolderPath" value=".\Logs\"/>
      <add key="TraceTermInDays" value="30"/>
      
      <!-- ファイル取得 -->
      <!-- 取得方法 0=FTP 1=ローカル 2=共有フォルダ -->
      <add key="FileGetMethod" value="1"/>
      <add key="EncryptedConnectUser" value="Z8+CJkJ6ne6pu+aCouZdiw=="/>
      <add key="EncryptedConnectPassword" value="Z8+CJkJ6ne6pu+aCouZdiw=="/>
      <add key="SourceFileUri" value="ftp://localhost/account.txt"/>
      <add key="MaxRetryCount" value="3"/>
      <add key="RetryInterval" value="3000"/>
      <add key="LocalFilePath" value="D:\eDoktor\TaikobanIF\Temp\EYTBRY.txt"/>
      <add key="FileBackupDirectory" value="D:\eDoktor\TaikobanIF\Temp\Backup\"/>
      <add key="FileBackupName" value="EYTBRY_%DATETIME%.txt"/>
      <add key="FileTermInDays" value="30"/>
      
      <!-- ファイル内容 -->
      <add key="FileEncoding" value="Shift-JIS"/>
      <add key="LineLength" value="1143"/>
      <add key="InvalidatedDataOffset" value="1141"/>
      <add key="InvalidatedDataLength" value="1"/>
      <add key="LogicallyDeletedDataOffset" value="1142"/>
      <add key="LogicallyDeletedDataLength" value="1"/>
      <add key="UsageStartDatetimeDataOffset" value="1077"/>
      <add key="UsageStartDatetimeDataLength" value="8"/>
      <add key="UsageStartDatetimeDataFormat" value="yyyyMMdd"/>
      <add key="UsageStartDatetimeDataNullList" value="********"/>
      <add key="UsageEndDatetimeDataOffset" value="1085"/>
      <add key="UsageEndDatetimeDataLength" value="8"/>
      <add key="UsageEndDatetimeDataFormat" value="yyyyMMdd"/>
      <add key="UsageEndDatetimeDataNullList" value="99999999"/>
      <add key="OrganizationalIdDataOffset" value="0"/>
      <add key="OrganizationalIdDataLength" value="8"/>
      <add key="NameDataOffset" value="72"/>
      <add key="NameDataLength" value="20"/>
      <add key="KanaDataOffset" value="92"/>
      <add key="KanaDataLength" value="40"/>
      <add key="BirthdateDataOffset" value="172"/>
      <add key="BirthdateDataLength" value="8"/>
      <add key="BirthdateDataFormat" value="yyyyMMdd"/>
      <add key="BirthdateDataNullList" value=""/>
      <add key="LogonIdDataOffset" value="0"/>
      <add key="LogonIdDataLength" value="8"/>
      <add key="PasswordDataOffset" value="8"/>
      <add key="PasswordDataLength" value="64"/>
      <add key="PasswordDataIsEncrypted" value="true"/>
      <add key="PasswordUpdateDatetimeDataOffset" value="1101"/>
      <add key="PasswordUpdateDatetimeDataLength" value="16"/>
      <add key="PasswordUpdateDatetimeDataFormat" value="yyyyMMdd00HHmmss"/>
      <add key="PasswordUpdateDatetimeDataNullList" value=""/>
      <add key="PasswordUpdateDatetimeIsUsedForNewAccount" value="false"/>
      <add key="JobCodeDataOffset" value="281"/>
      <add key="JobCodeDataLength" value="3"/>
      <add key="JobNameDataOffset" value="284"/>
      <add key="JobNameDataLength" value="6"/>
      
      <!-- 固定登録 -->
      <add key="LogonTo" value="kainan-his"/>
      <add key="PasswordUpdateDatetimeToUseForNewAccount" value="1001-01-01"/>
      <add key="PractitionerID" value="1"/>
      <add key="DefaultAccountGroupID" value="1"/>
      <add key="DefaultPrivilegeTemplateID" value="1"/>
      <add key="ErrorSource" value="8"/>
      <add key="ErrorWhenJobInsert" value="false"/>
    </appSettings>
    <connectionStrings>
      <!-- 開発サーバー -->
      <add name="Taikoban_192_168_1_10" connectionString="gwfgQ0W5vk1ziBVLg2sp6txnAa4K45HiRnfZUEJ8TjTNM74RhfPnTgLb2pmS/+QSyGkfYEccnjKluKkb7joLjge+LV63IA3YQpng923z24wm3s33BB0NA3pwzCjbgyJ9HoKKJgfwYVBXWeYlrEjSg7/YN23Hs6E7EZoe10jCYP0NFmxAwfjtXMd+nlfHRO/Xw3352dQipWi3l0M4PQekhQ==" providerName="System.Data.SqlClient"/>
      <!--<add name="Taikoban" connectionString="gwfgQ0W5vk1ziBVLg2sp6qc55l3t+X+25DlKmddmxf9AkiOml+sRj17UShZcj0vynr74EgSM7ic9PHQ7m6aa3VFXCZwDuwYWYJ9eWnN2W6Slew0r60ASxWSlT8YzoDFZsOk4f9Wnx0g32fuUJrb//5lO6RXMBvwQznga2hshLSM=" providerName="System.Data.SqlClient" /> -->
      <!--<add name="Taikoban" connectionString="gwfgQ0W5vk1ziBVLg2sp6kolQQDUtnZfwiQMcQUL4TvQeH2uGdrBbOPMAqCcu73YYccBh6eL8+TJ1wRLkfnL44FpoSUTJd41G2g1kVC35Z7jcMH0cYjKRgoE5fPjRf0Roz8Lv6yQX7HP/urhRf714Lkzu0YxD1ByGB02+CWm550=" providerName="System.Data.SqlClient" /> -->
      <add name="Taikoban" connectionString="gwfgQ0W5vk1ziBVLg2sp6kQaf1h8rCH+iff39UGewcPXxkJAaSN/qzg/lk/wEmleJh8UzMiIyIyb4rXPAwl0tvOcTW1cQAtfrpokybLub+S0qM/5jRxIHVCxdTBNk8h4tC10irpPj0rX5pu2VIa4x7GoUJdUWVdSyOBVPjuw1ik=" providerName="System.Data.SqlClient"/>
      <add name="Taikoban_Test" connectionString="gwfgQ0W5vk1ziBVLg2sp6h70uykkrb0siW3s50qVkPhM3bHh+ztsyJUWD9b90Bx+LkraoqNJRw1tDoU7TRtLaSjiDj58PTTx134pnwH+0dmyW8p9VbfcD/QabN0bnTk41a+n+9P+GE4MYvaFcb5L4fpuYPnuEML3X+kyWVM+fMc=" providerName="System.Data.SqlClient"/>
    </connectionStrings>
    <applicationSettings>
        <eDoktor.Taikoban.AccountImporter.Properties.Settings>
            <setting name="ConnectionStringLabel" serializeAs="String">
                <value>Taikoban_Test</value>
            </setting>
        </eDoktor.Taikoban.AccountImporter.Properties.Settings>
    </applicationSettings>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.2"/></startup></configuration>
