USE [$(Param1)]
GO
/****** Object:  Table [dbo].[t_enumeration_types]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_enumeration_types]') AND type in (N'U'))
DROP TABLE [dbo].[t_enumeration_types]
GO

CREATE TABLE [dbo].[t_enumeration_types](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[registered_by] [int] NOT NULL,
	[modification_datetime] [datetime2](7) NOT NULL,
	[modified_by] [int] NOT NULL,
	[enum_type] [int] NOT NULL,
	[enum_id] [int] NOT NULL,
	[enum_desc] [nvarchar](255) NOT NULL,
 CONSTRAINT [PK_t_enumeration_types] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [IX_t_enumeration_types] UNIQUE NONCLUSTERED 
(
	[enum_type] ASC,
	[enum_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_enumeration_types] ADD  CONSTRAINT [DF_t_enumeration_types_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO
ALTER TABLE [dbo].[t_enumeration_types] ADD  CONSTRAINT [DF_t_enumeration_types_registered_by]  DEFAULT ((0)) FOR [registered_by]
GO
ALTER TABLE [dbo].[t_enumeration_types] ADD  CONSTRAINT [DF_t_enumeration_types_modification_datetime]  DEFAULT (getdate()) FOR [modification_datetime]
GO
ALTER TABLE [dbo].[t_enumeration_types] ADD  CONSTRAINT [DF_t_enumeration_types_modified_by]  DEFAULT ((0)) FOR [modified_by]
GO
ALTER TABLE [dbo].[t_enumeration_types] ADD  CONSTRAINT [DF_t_enumeration_types_enum_type]  DEFAULT ((0)) FOR [enum_type]
GO
ALTER TABLE [dbo].[t_enumeration_types] ADD  CONSTRAINT [DF_t_enumeration_types_enum_id]  DEFAULT ((0)) FOR [enum_id]
GO
ALTER TABLE [dbo].[t_enumeration_types] ADD  CONSTRAINT [DF_t_enumeration_types_enum_desc]  DEFAULT ('') FOR [enum_desc]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_enumeration_types', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_enumeration_types', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_enumeration_types', @level2type=N'COLUMN',@level2name=N'registered_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_enumeration_types', @level2type=N'COLUMN',@level2name=N'modification_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_enumeration_types', @level2type=N'COLUMN',@level2name=N'modified_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'列挙型種別:0=未定義,1=エラー発生元' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_enumeration_types', @level2type=N'COLUMN',@level2name=N'enum_type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'列挙型の識別子' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_enumeration_types', @level2type=N'COLUMN',@level2name=N'enum_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'列挙型の表示名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_enumeration_types', @level2type=N'COLUMN',@level2name=N'enum_desc'
GO
