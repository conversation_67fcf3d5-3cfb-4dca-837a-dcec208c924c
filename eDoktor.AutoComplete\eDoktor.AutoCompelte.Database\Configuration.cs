﻿// Configuration.cs

namespace eDoktor.AutoComplete.Database
{
	static class Configuration
	{
		#region Fields
		static System.Configuration.ConnectionStringSettings _connectionStringSettings;
		#endregion

		#region Properties
		public static System.Configuration.ConnectionStringSettings ConnectionStringSettings
		{
			get { return _connectionStringSettings; }
		}

		#endregion

		#region Constructors
		static Configuration()
		{
			try
			{
				string connectionStringName = Common.Configuration.AppSetting("ConnectionStringName");

				if (string.IsNullOrWhiteSpace(connectionStringName))
				{
					throw new System.ArgumentNullException("ConnectionStringName");
				}

				_connectionStringSettings = Common.Configuration.ConnectionStringSettings(connectionStringName);
				_connectionStringSettings.ConnectionString = Interprocess.Crypto.Decrypt(_connectionStringSettings.ConnectionString);
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion
	}
}
