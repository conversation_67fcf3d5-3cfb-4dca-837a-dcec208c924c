﻿// XenDesktopConnectionMonitor.cs

namespace eDoktor.AutoComplete.Interprocess
{
	using Common.EventExtensions;

	class XenDesktopConnectionMonitor : IConnectionMonitor
	{
		#region Events
		public event System.EventHandler<ConnectionStateEventArgs> StateChanged;
		#endregion

		#region Fields
		private static readonly ConnectionStateEventArgs EmptyConnectionStateEventArgs = new ConnectionStateEventArgs();
		private readonly object SyncRoot = new object();
		private Common.Polling _polling;
		private const int PollingInterval = 1000;
		private ConnectionStateEventArgs _connectionStateEventArgs;
		#endregion

		#region Properties
		public ConnectionStateEventArgs LastConnectionStateEventArgs { get { return _connectionStateEventArgs; } }
		#endregion

		#region Constructors
		private XenDesktopConnectionMonitor()
		{
			
		}

		private void Initialize()
		{
			_connectionStateEventArgs = EmptyConnectionStateEventArgs;
			UpdateHostNamesOrAddresses();
		}

		public static XenDesktopConnectionMonitor Create()
		{
			var temp = new XenDesktopConnectionMonitor();

			try
			{
				temp.Initialize();
				var self = temp;
				temp = null;
				return self;
			}
			finally
			{
				if (temp != null)
				{
					temp.Dispose();
				}
			}
		}
		#endregion

		#region IDisposable
		~XenDesktopConnectionMonitor()
		{
			Dispose(false);
		}

		public void Dispose()
		{
			Dispose(true);
			System.GC.SuppressFinalize(this);
		}

		protected virtual void Dispose(bool disposing)
		{
			try
			{
				using (Common.TimedLock.Lock(SyncRoot))
				{
					if (_polling != null)
					{
						_polling.Dispose();
						_polling = null;
					}
				}

				StateChanged = null;
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion

		#region Public Methods
		public void Start()
		{
			using (Common.TimedLock.Lock(SyncRoot))
			{
				if (_polling != null)
				{
					throw new System.InvalidOperationException();
				}

				_polling = new Common.Polling() { Interval = PollingInterval };
				_polling.Start(Poll, null);
			}
		}
		#endregion

		#region Private Methods
		private void Poll(object state)
		{
			try
			{
				UpdateHostNamesOrAddresses();
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void UpdateHostNamesOrAddresses()
		{
			ConnectionStateEventArgs connectionStateEventArgs = EmptyConnectionStateEventArgs;

			using (Common.TimedLock.Lock(SyncRoot))
			{
				string clientAddress = null;
				string clientName = null;
				//string subKeyName = @"Software\Citrix\Ica\Session";
				//string clientAddressValueName = "ClientAddress";
				//string clientNameValueName = "ClientName";

				int sessionId = Common.Environment.SessionId;
				string subKeyNameFormat = @"Software\Citrix\Ica\Session\{0}\Connection";
				string subKeyName = string.Format(subKeyNameFormat, sessionId);

				using (Microsoft.Win32.RegistryKey regkey = Common.Registry.OpenSubKey(Microsoft.Win32.RegistryHive.LocalMachine, Microsoft.Win32.RegistryView.Registry32, subKeyName, false))
				{
					if (regkey != null)
					{
						bool remote = true;
						bool connectable = true;
						clientAddress = regkey.GetValue("ClientAddress", string.Empty) as string;
						clientName = regkey.GetValue("ClientName", string.Empty) as string;

						if ((!string.IsNullOrWhiteSpace(clientAddress)) && (!string.IsNullOrWhiteSpace(clientName)))
						{
							// アドレスとマシン名の両方取れた場合以外接続不可
							connectionStateEventArgs = new ConnectionStateEventArgs() { Connectable = connectable, Remote = remote, ClientAddress = clientAddress, ClientName = clientName };
						}
					}
				}

				if ((_connectionStateEventArgs.Connectable == connectionStateEventArgs.Connectable) && (_connectionStateEventArgs.Remote == connectionStateEventArgs.Remote) && (_connectionStateEventArgs.ClientAddress == connectionStateEventArgs.ClientAddress))
				{
					return;
				}

				_connectionStateEventArgs = connectionStateEventArgs;
			}

			StateChanged.SafeInvoke(this, connectionStateEventArgs);
		}
		#endregion
	}
}
