<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="eDoktor.Taikoban.AccountImporter.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
    </sectionGroup>
    <section name="ldapSettings" type="eDoktor.Taikoban.AccountImporter.Configuration.LdapConfigurationSection,eDoktor.Taikoban.AccountImporter" />
  </configSections>
  <appSettings>
    <!-- Trace -->
    <add key="EnableTrace" value="true"/>
    <add key="EnableDebugTrace" value="true"/>
    <add key="EnableDetailedExceptionTrace" value="true"/>
    <add key="ContainingFolderPath" value=".\Logs\"/>
    <add key="TraceTermInDays" value="30"/>

    <!-- ファイル取得 -->
    <add key="FileGetMethod" value="3"/> <!-- 取得方法 0=FTP 1=共有フォルダ 2=ローカル -->
    <add key="EncryptedConnectUser" value="6Pt3jvUnqmIOZgLnD88WCA=="/>
    <add key="EncryptedConnectPassword" value="7ctpHRLn/TnmHMPF+xW00A=="/>
    <add key="SourceFileUri" value="ftp://192.168.1.183/full/PHRY.csv"/>
    <add key="SourceDirectoryPath" value="ftp://192.168.1.183/full/PHRY.csv"/>
    <add key="MaxRetryCount" value="3"/>
    <add key="RetryInterval" value="3000"/>
    <add key="LocalFilePath" value="D:\eDoktor\TaikobanIF\Temp\PHRY.csv"/>
    <add key="FileBackupDirectory" value="D:\eDoktor\TaikobanIF\Temp\Backup\"/>
    <add key="FileBackupName" value="PHRY_%DATETIME%.csv"/>
    <add key="FileTermInDays" value="30"/>

    <!-- ファイル内容 -->
    <add key="FileEncoding" value="Shift-JIS"/> <!-- ファイルエンコード UTF-8の場合はUTF8を設定 -->
		<add key="DataType" value="0"/> <!-- データ種別:0=固定長 1=可変長 -->
    <add key="LineLength" value="1143"/> <!-- 固定長の場合は1行のバイト数、可変長の場合は1行の項目数 -->
    <add key="Delimiter" value="," />
    <!-- 非アクティブ -->
		<add key="InvalidatedDataOffset" value="1141"/> <!-- 利用しない場合は -1 を設定 -->
    <add key="InvalidatedDataLength" value="1"/>
    <!-- 削除 -->
    <add key="LogicallyDeletedDataOffset" value="1142"/> <!-- 利用しない場合は -1 を設定 -->
    <add key="LogicallyDeletedDataLength" value="1"/>
    <!-- 有効開始日時 -->
    <add key="UsageStartDatetimeDataOffset" value="1077"/> <!-- 利用しない場合は -1 を設定 -->
    <add key="UsageStartDatetimeDataLength" value="8"/>
    <add key="UsageStartDatetimeDataFormat" value="yyyyMMdd"/>
    <add key="UsageStartDatetimeDataNullList" value="00000000"/>
    <!-- 有効終了日 -->
    <add key="UsageEndDatetimeDataOffset" value="1085"/> <!-- 利用しない場合は -1 を設定 -->
    <add key="UsageEndDatetimeDataLength" value="8"/>
    <add key="UsageEndDatetimeDataFormat" value="yyyyMMdd"/>
    <add key="UsageEndDatetimeDataNullList" value="99999999"/>
    <!-- 組織内ID-->
    <add key="OrganizationalIdDataOffset" value="0"/>
    <add key="OrganizationalIdDataLength" value="8"/>
    <!-- 氏名 -->
    <add key="NameDataOffset" value="72"/>
    <add key="NameDataLength" value="20"/>
    <!-- カナ氏名 -->
    <add key="KanaDataOffset" value="92"/> <!-- 利用しない場合は -1 を設定 -->
    <add key="KanaDataLength" value="40"/>
    <!-- 職種コード -->
    <add key="JobCodeDataOffset" value="281"/> <!-- 利用しない場合は -1 を設定 -->
    <add key="JobCodeDataLength" value="3"/>
    <!-- 職種名 -->
    <add key="JobNameDataOffset" value="284"/> <!-- 利用しない場合は -1 を設定 -->
    <add key="JobNameDataLength" value="6"/>
    <!-- 生年月日 -->
    <add key="BirthdateDataOffset" value="172"/> <!-- 利用しない場合は -1 を設定 -->
    <add key="BirthdateDataLength" value="8"/>
    <add key="BirthdateDataFormat" value="yyyyMMdd"/>
    <add key="BirthdateDataNullList" value="        |00000000|99999999"/>
    <!-- ログオンID -->
    <add key="LogonIdDataOffset" value="0"/>
    <add key="LogonIdDataLength" value="8"/>
    <!-- パスワード -->
    <add key="PasswordDataOffset" value="8"/> <!-- 利用しない場合は -1 を設定 -->
    <add key="PasswordDataLength" value="64"/>
    <add key="PasswordDataIsEncrypted" value="true"/> <!-- 暗号化されているか:false=されていない true=されている -->
    <add key="PasswordDataDecryptMethod" value="0"/> <!-- 復号化方式:0=復号化しない 1=GX -->
    <add key="PasswordDataInitial" value="7ctpHRLn/TnmHMPF+xW00A=="/> <!-- 初期パスワード(暗号化) -->
    <!-- パスワード更新日時 -->
    <add key="PasswordUpdateDatetimeDataOffset" value="1101"/> <!-- 利用しない場合は -1 を設定 -->
    <add key="PasswordUpdateDatetimeDataLength" value="16"/>
    <add key="PasswordUpdateDatetimeDataFormat" value="yyyyMMdd00HHmmss"/>
    <add key="PasswordUpdateDatetimeDataNullList" value=""/>
    <add key="PasswordUpdateDatetimeIsUsedForNewAccount" value="false"/> <!-- 新規アカウントの場合に固定の値を利用するか:true=利用する true=利用しない（電文値を利用） -->
    <!-- カードID(未実装) -->
    <add key="CardIdDataOffset" value="1101"/> <!-- 利用しない場合は -1 を設定 -->
    <add key="CardIdDataLength" value="16"/>
    <add key="CardIdWithHyphen" value="16"/> <!-- ハイフン付きか false=ハイフンなし。付与して登録 true=ハイフンあり。そのまま登録 -->
    <!-- SSOログオンID(未実装) -->
    <add key="SsoLogonIdDataOffset" value="0"/> <!-- 利用しない場合は -1 を設定 -->
    <add key="SsoLogonIdDataLength" value="8"/>
    <!-- SSOパスワード(未実装) -->
    <add key="SsoPasswordDataOffset" value="8"/> <!-- 利用しない場合は -1 を設定 -->
    <add key="SsoPasswordDataLength" value="64"/>
    <add key="SsoPasswordDataIsEncrypted" value="true"/> <!-- 暗号化されているか:false=されていない true=されている -->
    <add key="SsoPasswordDataDecryptMethod" value="0"/> <!-- 復号化方式:0=復号化しない 1=GX -->
    <add key="SsoPasswordDataInitial" value="7ctpHRLn/TnmHMPF+xW00A=="/> <!-- 初期パスワード(暗号化) -->

    <!-- 固定登録 -->
    <add key="LogonTo" value="edoktor"/>
    <add key="PasswordUpdateDatetimeToUseForNewAccount" value="1001-01-01"/> <!-- 初期パスワード更新日時 -->
    <add key="PractitionerID" value="********"/> <!-- 登録者更新者ID -->
    <add key="DefaultAccountGroupID" value="1"/> <!-- 新規登録時のアカウントグループID -->
    <add key="DefaultPrivilegeTemplateID" value="1"/> <!-- 新規登録時の権限テンプレートID -->
    <add key="DefaultPrivilegeTemplateID" value="1"/> <!-- 新規登録時の権限テンプレートID -->
    <add key="CardType" value="1"/> <!-- 登録時のカード種別：0=自動判定,1=FeliCa,2=MIFARE -->
    <add key="RegistErrorTable" value="false"/> <!-- エラー情報をエラーテーブル(t_errors)に登録するか:false=登録しない true=登録する -->
    <add key="ErrorSource" value="8"/> <!-- エラー時のソースID -->
    
    <!-- AD連携 -->
    <add key="SyncronizeAd" value="true" />
    <add key="LdapUrl" value="LDAP://192.168.1.170/OU=His-Users,DC=edoktor,DC=local" />
    <add key="EncryptedLdapUser" value="6Pt3jvUnqmIOZgLnD88WCA==" />
    <add key="EncryptedLdapPassword" value="7ctpHRLn/TnmHMPF+xW00A==" />
    <add key="LdapAuthenticationTypes" value="545" />
    <add key="SharePath" value="\\192.168.1.183\Share" />
    <add key="EncryptedShareUser" value="6Pt3jvUnqmIOZgLnD88WCA==" />
    <add key="EncryptedSharePassword" value="7ctpHRLn/TnmHMPF+xW00A==" />
    <add key="EncryptedInitialPassword" value="24zbpxH8NfrjjFWi+ilEzw==" />
    <add key="ShareLocalDrive" value="X:" />
    <add key="ShareRetryCount" value="3" />
    <add key="ShareRetryInterval" value="1000" />
    <add key="ShareDomain" value="edoktor.local" />
    <add key="ShareUnnecessaryUserList" value="Users" />
    <add key="ExcludedAccountList" value="SYSTEM" />
  </appSettings>
  <connectionStrings>
		<add name="AccountsSource" connectionString="mF87lF4gLh4Ly7I1PtcFlB+R1HC1ROQICYu67eVTwL4vowCdToeTkb7dPgX2MN9C0UF9nSll+B+8CCRlygJNhv2CaSxE5wv/yC1NNcEo45LGCmbrSD1yHRwxwH+2FHsphR85GTssYkhfecFVELcVmRQABwO56ZrJ1cEucVFhq7QwaCjdoWnkKP+HHzlDDYuJ" providerName="System.Data.SqlClient"/>
		<add name="Taikoban" connectionString="gwfgQ0W5vk1ziBVLg2sp6h70uykkrb0siW3s50qVkPhM3bHh+ztsyJUWD9b90Bx+LkraoqNJRw1tDoU7TRtLaSjiDj58PTTx134pnwH+0dmyW8p9VbfcD/QabN0bnTk41a+n+9P+GE4MYvaFcb5L4fpuYPnuEML3X+kyWVM+fMc=" providerName="System.Data.SqlClient"/>
  </connectionStrings>
  <!-- AD連携属性 -->
  <ldapSettings>
    <contents>
      <content name="cn" offset="0" length="8" propertyName="cn" prefix="" suffix="" isKey="true" />
      <content name="sAMAccountName" offset="0" propertyName="sAMAccountName" length="8" prefix="" suffix="" />
      <content name="displayName" offset="72" length="20" propertyName="displayName" prefix="" suffix="" />
      <content name="department" offset="325" length="10" propertyName="department" prefix="" suffix="" />
      <content name="mail" offset="181" length="80" propertyName="mail" prefix="" suffix="" />
      <content name="userPrincipalName" offset="0" length="8" propertyName="userPrincipalName" prefix="" suffix="@edoktor.com"  />
      <content name="postalCode" offset="0" length="0" propertyName="postalCode" prefix="" suffix="postalCode"  />
    </contents>
  </ldapSettings>
  <applicationSettings>
    <eDoktor.Taikoban.AccountImporter.Properties.Settings>
      <setting name="ConnectionStringLabel" serializeAs="String">
        <value>Taikoban</value>
      </setting>
    </eDoktor.Taikoban.AccountImporter.Properties.Settings>
  </applicationSettings>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.2"/>
  </startup>
</configuration>
