﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <section name="scriptConfiguration" type="eDoktor.Auth.Setup.Configuration.ScriptConfigurationSection,eDoktor.Auth.Setup"/>
    </configSections>
    <scriptConfiguration>
        <scripts>
            <!-- idは一意になるようにしてください。同じtypeの処理を複数追加できます。 -->
            <script id="1" type="PreInstall" command="" arguments="" asUser="false" createNoWindow="false" milliseconsTimeout="0" message=""/>
            <script id="2" type="PostInstall" command="" arguments="" asUser="false" createNoWindow="false" milliseconsTimeout="0" message=""/>
            <script id="3" type="PreUninstall" command="" arguments="" asUser="false" createNoWindow="false" milliseconsTimeout="0" message=""/>
            <script id="4" type="PostUninstall" command="" arguments="" asUser="false" createNoWindow="false" milliseconsTimeout="0" message=""/>
        </scripts>
    </scriptConfiguration>
    <appSettings>
        <add key="Version" value="1" />
        <add key="InstallPath" value="D:\eDoktor\Taikoban" />
        <add key="FileServerNameOrAddress" value="*************" />
        <add key="SharedFolderName" value="eDoktorUpdater" />
        <add key="ConfigurationName" value="Taikoban" />
        <add key="NetworkAccountUserName" value="NdAO6cCTFzm5bB3UD/xW8g==" />
        <add key="NetworkAccountPassword" value="ZlyUuSfbp/pLjuW4/u9Tww==" />
        <add key="DefaultUserName" value="lyeRubCQmo6r38utdiJmtQ==" />
        <add key="DefaultPassword" value="7ctpHRLn/TnmHMPF+xW00A==" />
        <add key="DefaultLogonTo" value="IISPqVAtKzsTtc/kgrvdFw==" />
        <add key="DefaultDomain" value="IISPqVAtKzsTtc/kgrvdFw==" />
        <add key="Enabled" value="true"/>
        <add key="AutoUpdate" value="true"/>
        <add key="CredentialProvider" value="false"/>
        <add key="SystemFaultMessage" value="システム障害が発生しています。" />
        <add key="BioAuthMaxRetryCount" value="2" />
        <add key="LogRotationFileSize" value="**********" />
        <add key="DevicesToBeExcludedFromVideoSources" value="The Imaging Source DFG/USB2pro|DFG/USB2pro unused IR receiver" />
        <add key="PlaySound" value="true" />
        <add key="ConnectSoundPath" value="C:\Windows\Media\Windows Background.wav" />
        <add key="DisconnectSoundPath" value="C:\Windows\Media\Windows Foreground.wav" />
        <add key="PasswordUpdateWaitingMilliseconds" value="10000" />
        <add key="CountdownCountingSeconds" value="31" />
        <add key="CountdownDisplaySeconds" value="30" />
        <!-- MaintenanceKey VK_LSHIFT(0xA0),Z key(0x5A),X key(0x58)  -->
        <add key="MaintenanceKeyCombination" value="160|90|88" />
        <add key="AgentName" value="eDoktor.FaceLink.Agent" />
        <add key="AgentFileName" value="eDoktor.FaceLink.Agent.exe" />
        <add key="ClientServiceName" value="eDoktor.FaceLink.ClientService" />
        <add key="ClientServiceFileName" value="eDoktor.FaceLink.ClientService.exe" />
        <add key="ApiComLocalServerFileName" value="FaceLinkAPI.exe" />
        <add key="InterfaceApiComLocalServerFileName" value="eDoktor.FaceLink.Interface.exe" />
        <add key="UpdateServiceName" value="eDoktor.FaceLink.UpdateService" />
        <add key="UpdateServiceFileName" value="eDoktor.FaceLink.UpdateService.exe" />
        <add key="SetupExeFileName" value="eDoktor.FaceLink.Setup.exe" />
    </appSettings>
</configuration>