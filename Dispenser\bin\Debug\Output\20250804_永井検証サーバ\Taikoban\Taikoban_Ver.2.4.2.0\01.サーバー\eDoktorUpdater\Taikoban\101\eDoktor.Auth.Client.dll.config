﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <appSettings>
        <!-- PC/SC START -->
        <add key="EnablePCSC" value="true"/>
        <add key="PCSCVerbose" value="false"/>
        <add key="PCSCWhiteReaderList" value="Sony FeliCa Port/PaSoRi 3.0|SONY FeliCa Port/PaSoRi 4.0"/>
        <add key="PCSCBlackReaderList" value=""/>
        <add key="PCSCMinimumPollingInterval" value="3000"/>
        <add key="PCSCAccessStartedEventMillisecondsTimeout" value="0"/>
        <!-- HPKI -->
        <add key="EnableHPKI" value="false"/>
        <add key="HPKIDataReadingDelayMilliseconds" value="1000"/>
        <!-- JPKI -->
        <add key="EnableJPKI" value="false"/>
        <add key="JPKIDataReadingDelayMilliseconds" value="1000"/>
        <!-- FeliCa -->
        <add key="FeliCaEnableDataReading" value="false"/>
        <!-- FeliCaDataReadingDelayType: 0:初回のみ 1:常時 -->
        <add key="FeliCaDataReadingDelayType" value="1"/>
        <add key="FeliCaDataReadingDelayMilliseconds" value="1000"/>
        <add key="FeliCaDataReadingMaxRetryCount" value="5"/>
        <add key="FeliCaEnableAutoRegistration" value="false"/>
        <!-- FeliCaAutoRegistrationDataType: 0:アカウント名 1:組織内ID -->
        <add key="FeliCaAutoRegistrationDataType" value="0"/>
        <add key="FeliCaConfirmServiceCodeValidity" value="false"/>
        <add key="FeliCaEncryptedSystemCode" value="UQZIV3V11pEyFqO04MMQqg=="/>
        <add key="FeliCaEncryptedServiceCode" value="5HP2sqMZAZli+MohCHtpmQ=="/>
        <add key="FeliCaEncryptedBlockIndex" value="jEvqnJPHAk2Hxp4N0a9I2w=="/>
        <add key="FeliCaEncryptedBlockCount" value="PDqHW/5rXFb8AbP6grPSjw=="/>
        <add key="FeliCaEncryptedDataOffset" value="kXt5sgfFrOCIEjdu5BzWzw=="/>
        <add key="FeliCaEncryptedDataLength" value="Oa5CDX2E2jnD/UesJFLciQ=="/>
        <!-- PC/SC END -->
        <add key="LockOnSystemFaultRecovery" value="false"/>
        <add key="RunLogoutCommandsInSession0IfNecessary" value="true"/>
        <add key="ConnectionTestMillisecondsTimeout" value="60000"/>
        <add key="InitialConnectionMillisecondsTimeout" value="10000"/>
        <add key="CredentialInformationCacheMillisecondsTimeout" value="86400000"/>
        <add key="AuthenticationInformationCacheMillisecondsTimeout" value="300000"/>
        <add key="SerializedSessionStateMillisecondsTimeout" value="300000"/>
        <!-- Session Update Timer -->
        <add key="SessionUpdateTimerInitialDelay" value="500"/>
        <add key="SessionUpdateTimerInterval" value="500"/>
        <add key="SessionUpdateTimerRepeatCount" value="5"/>
        <!-- PasswordUpdateLoginIdType: 0:UserName 1:OrganizationalId -->
        <add key="PasswordUpdateLoginIdType" value="0"/>
        <!-- SKYIF -->
        <add key="EnableSkyInterfaceLink" value="true"/>
        <!-- FJIF -->
        <add key="EnableFujitsuInterfaceLink" value="true"/>
        <!-- Terminal Config -->
        <!-- WindowsLogonType: 0:Shared 1:Personal -->
        <add key="WindowsLogonType" value="0"/>
        <!-- WindowsAutoLogonEnableDisableType: 0:Disabled 1:Enabled 2:EnabledOnStartupOnly -->
        <add key="WindowsAutoLogonEnableDisableType" value="1"/>
        <add key="DefaultUserName" value="lyeRubCQmo6r38utdiJmtQ=="/>
        <add key="DefaultPassword" value="7ctpHRLn/TnmHMPF+xW00A=="/>
        <add key="DefaultLogonTo" value="IISPqVAtKzsTtc/kgrvdFw=="/>
        <!-- AuthenticationSchemeEnableDisableType: 0:Disabled 1:Enabled 2:EnabledOnSystemFaultOnly -->
        <add key="AuthenticationSchemeEnableDisableType" value="2"/>
        <!-- AuthenticationSchemeType: 0:Windows 1:LDAP-->
        <add key="AuthenticationSchemeType" value="0"/>
        <add key="ManagePasswordExpiration" value="true"/>
        <add key="AllowLogonOnPasswordExpiration" value="false"/>
        <add key="CommandToExecuteOnPasswordExpiration" value=""/>
        <add key="DisplayGoToDesktopLink" value="true"/>
        <add key="DisplayShutdownLink" value="false"/>
        <add key="AllowLogonOnAllFailure" value="true"/>
        <add key="AllowPasswordChangeDuringAuthentication" value="false"/>
        <add key="AdminSasKeyCombination" value="0"/>
        <!-- DefaultSasKeyCombination 13:VK_RETURN(0x0D) -->
        <add key="DefaultSasKeyCombination" value="13"/>
        <!-- DefaultSasOneFactorAuthenticationPolicyType: 0:NotAllowed 1:BiometricAuthenticationOnly 2:PrioritizeBiometricAuthentication 3:PasswordOnly -->
        <add key="DefaultSasOneFactorAuthenticationPolicyType" value="3"/>
        <!-- SmartCardAuthenticationPolicyType: 0:SmartCardOnly 1:BiometricAuthenticationOnly 2:PrioritizeBiometricAuthentication 3:PasswordOnly -->
        <add key="SmartCardAuthenticationPolicyType" value="3"/>
        <!-- ActionRegardingWindowsSessionOnSmartCardRemovalAtDesktopType: 0:None 1:CancelAuthentication 2:CancelAuthenticationAndExpiringOrExpiredPasswordChange -->
        <add key="ActionOnSmartCardRemovalDuringAuthenticationType" value="2"/>
        <!-- ActionRegardingWindowsSessionOnSmartCardRemovalAtDesktopType: 0:None 1:Lock 2:Logoff -->
        <add key="ActionRegardingWindowsSessionOnSmartCardRemovalAtDesktopType" value="1"/>
        <!-- ActionRegardingAuthenticatedUserOnSmartCardRemovalAtDesktopType: 0:None 1:Logout -->
        <add key="ActionRegardingAuthenticatedUserOnSmartCardRemovalAtDesktopType" value="0"/>
        <add key="DurationInSecondsBeforeActionOnSmartCardRemoval" value="20"/>
        <add key="SecondsToDisplayCountdownBeforeActionOnSmartCardRemoval" value="20"/>
        <!-- ActionOnLogoutType: 0:None 1:Lock 2:Logoff -->
        <add key="ActionOnLogoutType" value="0"/>
        <!-- LockKeyCombination: 162:VK_LCONTROL(0xA2) | 91:VK_LWIN(0x5B) -->
        <add key="LockKeyCombination" value="162|91"/>
        <add key="DurationInSecondsBeforeLockBecauseOfInactivity" value="3600"/>
        <add key="DurationInSecondsBeforeLogoffBecauseOfInactivity" value="-1"/>
        <add key="DurationInSecondsBeforeLogoutBecauseOfInactivity" value="-1"/>
        <add key="DurationInSecondsBeforeLogoffAfterLock" value="-1"/>
        <add key="DurationInSecondsBeforeLogoutAfterLock" value="3600"/>
        <!-- ActionOnSbcRoamingType: 0:None 1:Lock 2:Logoff 3:Logout -->
        <add key="ActionOnSbcRoamingType" value="0"/>
        <add key="CommandToExecuteOnSbcRoaming" value=""/>
        <add key="ProcessesToEndOnSbcRoaming" value=""/>
        <add key="WindowsToCloseOnSbcRoaming" value=""/>
        <!-- ActionOnNonSbcRoamingType: 0:None 1:Lock 2:Logoff 3:Logout -->
        <add key="ActionOnNonSbcRoamingType" value="0"/>
        <add key="CommandToExecuteOnNonSBCRoaming" value=""/>
        <add key="ProcessesToEndOnNonSbcRoaming" value=""/>
        <add key="WindowsToCloseOnNonSbcRoaming" value=""/>
        <add key="CommandToExecuteOnLogin" value=""/>
        <add key="CommandToExecuteOnLogout" value=""/>
        <add key="ProcessesToEndOnLogout" value=""/>
        <add key="WindowsToCloseOnLogout" value=""/>
        <!-- SimultaneouslyUsedTerminalsDisplayModeType: 0:Disabled 1:Enabled 2:DoNotDisplayIfAllTerminalsAreSbc -->
        <add key="SimultaneouslyUsedTerminalsDisplayModeType" value="1"/>
        <add key="ManageIcaSession" value="false"/>
        <add key="EnablePasswordPolicyViolationCheck" value="true"/>
        <add key="EnableNetworkAdapterAvailabilityCheck" value="true"/>
        <!-- ExecutionRetryTimer -->
        <add key="ExecutionRetryTimerInitialDelay" value="500"/>
        <add key="ExecutionRetryTimerInterval" value="1000"/>
        <add key="ExecutionRetryTimerRepeatCount" value="60"/>
        <!-- ShutdownApp -->
        <add key="ShutdownAppFileName" value=""/>
        <add key="ShutdownAppArguments" value=""/>
        <add key="ShutdownAppCreateNoWindow" value="false"/>
        <!-- RunAsType: 0:System 1:User -->
        <add key="ShutdownAppRunAsType" value="0"/>
        <add key="ShutdownAppWaitForExit" value="false"/>
        <add key="ShutdownAppWaitForExitMillisecondsTimeout" value="10000"/>
        <!-- BiometricsType 1:指紋 2:顔 3:音声 4:静脈 5:虹彩 -->
        <add key="BiometricsType" value="2"/>
    </appSettings>
</configuration>