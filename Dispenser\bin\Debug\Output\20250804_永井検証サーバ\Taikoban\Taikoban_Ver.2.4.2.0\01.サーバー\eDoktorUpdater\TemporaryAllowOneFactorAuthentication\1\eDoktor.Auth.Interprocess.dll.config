﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <appSettings>
        <add key="ResponseWaitingTimeout" value="2000"/>
        <add key="ClientToServerAuthenticateUserTimeout" value="10000"/>
        <add key="ClientToServerCachedPasswordValidationTimeout" value="10000"/>
        <add key="ExtensionToClientLoginTimeout" value="20000"/>
        <add key="EnableVdiClientStatusCheck" value="false"/>
        <!-- MachineNameType 0:fqdn 1:NetBiosName 2:HostName -->
        <add key="MachineNameType" value="0"/>
        <!-- Server -->
        <add key="ServerAddressList" value="*************|*************"/>
        <add key="ServerPortNumber" value="49001"/>
        <add key="ServerBacklog" value="100"/>
        <add key="ServerMaxAcceptableClientCount" value="2500"/>
        <add key="ServerKeepAliveTime" value="60000"/>
        <!-- Client -->
        <add key="ClientPortNumber" value="49002"/>
        <add key="ClientBacklog" value="10"/>
        <add key="ClientMaxAcceptableClientCount" value="20"/>
        <add key="ClientKeepAliveTime" value="60000"/>
        <!-- Terminal Config -->
        <!-- DefaultLockType 1:UserSwitching 2:LeavingSeat -->
        <add key="DefaultLockType" value="2"/>
        <add key="TreatLeavingSeatLockAsUserSwitchingLock" value="true"/>
    </appSettings>
</configuration>