﻿// LoginWindow.cs

using System;
using System.Collections.Generic;

namespace eDoktor.AutoComplete.Agent
{
	class LoginWindow
	{
		#region Fields
		private string _targetWindowClassName;
		private string _targetWindowCaption;
		#endregion

		#region Properties
		public string Name { get; set; }
		public string ProcessName { get; set; }
		public string TargetWindowClassName { get { return _targetWindowClassName; } set { _targetWindowClassName = (string.IsNullOrWhiteSpace(value)) ? null : value; } }
		public string TargetWindowCaption { get { return _targetWindowCaption; } set { _targetWindowCaption = (string.IsNullOrWhiteSpace(value)) ? null : value; } }
		public int UserElementIndex { get; set; }
		public string UserElementAutomationId { get; set; }
		public string UserElementClassName { get; set; }
		public string UserElementCaption { get; set; }
		public int PasswordElementIndex { get; set; }
		public string PasswordElementAutomationId { get; set; }
		public string PasswordElementClassName { get; set; }
		public string PasswordElementCaption { get; set; }
		public bool PasswordElementIsPassword { get; set; }
		public int ButtonElementIndex { get; set; }
		public string ButtonElementAutomationId { get; set; }
		public string ButtonElementClassName { get; set; }
		public string ButtonElementCaption { get; set; }
		public ButtonClickMethodType ButtonClickMethod { get; set; }
		public int ButtonClickKeyCode { get; set; }
		public int ButtonClickX { get; set; }
		public int ButtonClickY { get; set; }
		public bool Variable { get; set; }
		#endregion
	}
}
