﻿// Database.cs
using eDoktor.AutoComplete.Interprocess;

namespace eDoktor.AutoComplete.Database
{
	using Interprocess.ObjectValidationExtensions;

	public class Database
	{
		#region Fields
		private static readonly string Separator = "|";
		private static readonly string[] Separators = new string[] { Separator };
		private static readonly string[] StringFromDatabaseSeparators = new string[] { "," };
		private static readonly string EncryptedEmptyString = Interprocess.Crypto.Encrypt(string.Empty);
		private const string RecordValidationFormat = "({0}.invalidated = 0) AND ({0}.logically_deleted = 0)";
		private const string RecordValidationFormatWithUsageStartEndCheck = "({0}.invalidated = 0) AND ({0}.logically_deleted = 0) AND (({0}.usage_start_datetime IS NULL) OR ({0}.usage_start_datetime <= GETDATE())) AND (({0}.usage_end_datetime IS NULL) OR ({0}.usage_end_datetime >= GETDATE()))";
		private const string RecordValidationFormatWithUsageEndCheck = "({0}.invalidated = 0) AND ({0}.logically_deleted = 0) AND (({0}.usage_end_datetime IS NULL) OR ({0}.usage_end_datetime >= GETDATE()))";
		private static readonly string ApplicationRecordValidationSql;
        // ▼ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
        private static readonly string AutoCompleteInfoRecordValidationSql;
        // ▲ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
        // ▼ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
        private static readonly string AutoCompleteInfoQuerySqlFormat;
        // ▲ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
        // ▼ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
        private static readonly Interprocess.AutoCompleteInfo EmptyAutoCompleteInfo = new Interprocess.AutoCompleteInfo();
        // ▲ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
		private Common.Database _database;
		private System.Text.Encoding _encoding = System.Text.Encoding.GetEncoding("Shift_JIS");	// 管理画面から設定されるbase64文字列のデコード時に使用するエンコーディング
		#endregion

		#region Enumerations
		#endregion

		#region Constructors
		static Database()
		{
			try
			{
				ApplicationRecordValidationSql = string.Format(RecordValidationFormat, "t_applications");
                // ▼ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
                AutoCompleteInfoRecordValidationSql = string.Format(RecordValidationFormat, "t_autocomplete_settings");
                // ▲ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara

                // ▼ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
                AutoCompleteInfoQuerySqlFormat = string.Format(
@"SELECT t_autocomplete_settings.*, t_applications.name, t_applications.system_key, t_applications.security_key FROM (SELECT * FROM t_autocomplete_settings WHERE {0}) AS t_autocomplete_settings
LEFT JOIN (SELECT * FROM t_applications WHERE {1}) AS t_applications ON t_autocomplete_settings.application_id = t_applications.id
ORDER BY t_autocomplete_settings.id",
                AutoCompleteInfoRecordValidationSql,
                ApplicationRecordValidationSql);
                // ▲ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		public Database()
		{
			try
			{
				_database = new Common.Database(Configuration.ConnectionStringSettings);
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion

		#region Public Methods
        // ▼ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
        public Interprocess.AutoCompleteInfo QueryAutoCompleteInfo()
        {
            try
            {
                Interprocess.AutoCompleteInfo autoCompleteInfo = new Interprocess.AutoCompleteInfo();

                System.Collections.Generic.List<Interprocess.AutoCompleteSetting> autoCompleteSettings = new System.Collections.Generic.List<Interprocess.AutoCompleteSetting>();
 
                string commandText = AutoCompleteInfoQuerySqlFormat;

                Common.Trace.OutputDebugTrace("commandText={0}", commandText);

                _database.ExecuteQuery(commandText, delegate(System.Data.Common.DbDataReader reader)
                {
                    Interprocess.AutoCompleteSetting autoCompleteSetting = new Interprocess.AutoCompleteSetting();
                    autoCompleteSetting.Name = Common.Database.GetString(reader, "name", string.Empty);
					autoCompleteSetting.SystemKey = Common.Database.GetInt32(reader, "system_key", 0);
					autoCompleteSetting.SecurityKey = Common.Database.GetString(reader, "security_key", string.Empty);
                    autoCompleteSetting.ProcessName = Common.Database.GetString(reader, "process_name", string.Empty);
                    autoCompleteSetting.TargetWindowClassName = Common.Database.GetString(reader, "target_window_class_name", string.Empty);
                    autoCompleteSetting.TargetWindowCaption = Common.Database.GetString(reader, "target_window_caption", string.Empty);
                    autoCompleteSetting.UserElementIndex = Common.Database.GetInt32(reader, "user_element_index", -1);
                    autoCompleteSetting.UserElementAutomationId = Common.Database.GetString(reader, "user_element_automation_id", string.Empty);
                    autoCompleteSetting.UserElementClassName = Common.Database.GetString(reader, "user_element_class_name", string.Empty);
                    autoCompleteSetting.UserElementCaption = Common.Database.GetString(reader, "user_element_caption", string.Empty);
                    autoCompleteSetting.PasswordElementIndex = Common.Database.GetInt32(reader, "password_element_index", -1);
                    autoCompleteSetting.PasswordElementAutomationId = Common.Database.GetString(reader, "password_element_automation_id", string.Empty);
                    autoCompleteSetting.PasswordElementClassName = Common.Database.GetString(reader, "password_element_class_name", string.Empty);
                    autoCompleteSetting.PasswordElementCaption = Common.Database.GetString(reader, "password_element_caption", string.Empty);
                    autoCompleteSetting.PasswordElementIsPassword = Common.Database.GetBoolean(reader, "password_element_is_password", false);
					// ▼ ADD パスワード入力方式追加対応 2024/08/07 eDoktor Y.Kihara
					autoCompleteSetting.PasswordInputMethod = (Interprocess.PasswordInputMethodType)Common.Database.GetInt32(reader, "password_input_method", 0);
					autoCompleteSetting.PasswordFocusingWait = Common.Database.GetInt32(reader, "password_focusing_wait", 0);
					autoCompleteSetting.PasswordInputInterval = Common.Database.GetInt32(reader, "password_input_interval", 0);
					// ▲ ADD パスワード入力方式追加対応 2024/08/07 eDoktor Y.Kihara
					autoCompleteSetting.ButtonElementIndex = Common.Database.GetInt32(reader, "button_element_index", -1);
                    autoCompleteSetting.ButtonElementAutomationId = Common.Database.GetString(reader, "button_element_automation_id", string.Empty);
                    autoCompleteSetting.ButtonElementClassName = Common.Database.GetString(reader, "button_element_class_name", string.Empty);
                    autoCompleteSetting.ButtonElementCaption = Common.Database.GetString(reader, "button_element_caption", string.Empty);
                    autoCompleteSetting.ButtonClickMethod = (Interprocess.ButtonClickMethodType)Common.Database.GetInt32(reader, "button_click_method", 0);
                    autoCompleteSetting.ButtonClickKeyCode = Common.Database.GetInt32(reader, "button_click_key_code", 0);
                    autoCompleteSetting.ButtonClickX = Common.Database.GetInt32(reader, "button_click_x", 0);
                    autoCompleteSetting.ButtonClickY = Common.Database.GetInt32(reader, "button_click_y", 0);
                    autoCompleteSetting.ActionOnExit = (Interprocess.ActionOnExitType)Common.Database.GetInt32(reader, "action_on_exit", 0);
                    // ▼ ADD ログインボタンを自動押下しない対応 2023/01/10 eDoktor Y.Kihara
                    autoCompleteSetting.EnableDonotAutoLoginCheckbox = Common.Database.GetBoolean(reader, "enable_donot_auto_login_checkbox", false);
                    autoCompleteSetting.DonotAutoLoginCheckboxMessage = Common.Database.GetString(reader, "donot_auto_login_checkbox_message", string.Empty);
					// ▲ ADD ログインボタンを自動押下しない対応 2023/01/10 eDoktor Y.Kihara
					// ▼ ADD 認証開始ボタンの表示設定追加対応 2024/01/07 eDoktor Y.Kihara
					autoCompleteSetting.NotAllowedOneFactorAuthentication = Common.Database.GetBoolean(reader, "not_allowed_one_factor_authentication", false);
					// ▲ ADD 認証開始ボタンの表示設定追加対応 2024/01/07 eDoktor Y.Kihara
					// ▼ ADD 閉じるボタンの表示設定追加対応 2024/07/22 eDoktor Y.Kihara
					autoCompleteSetting.HiddenCloseButton = Common.Database.GetBoolean(reader, "hidden_close_button", false);
					// ▲ ADD 閉じるボタンの表示設定追加対応 2024/07/22 eDoktor Y.Kihara
					autoCompleteSetting.Variable = Common.Database.GetBoolean(reader, "variable", false);
                    autoCompleteSettings.Add(autoCompleteSetting);
                });

                return new Interprocess.AutoCompleteInfo() { AutoCompleteSettings = (autoCompleteSettings.Count > 0) ? autoCompleteSettings.ToArray() : null };
            }
            catch (System.Exception ex)
            {
                Common.Trace.OutputExceptionTrace(ex);
            }

            return null;
        }
        // ▲ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
		#endregion

		#region Private Methods
		private string ConvertArrayToCsv(string[] array)
		{
			var csvBuilder = new System.Text.StringBuilder();

			for (int i = 0; i < array.Length; i++)
			{
				if (csvBuilder.Length != 0)
				{
					csvBuilder.Append(",");
				}

				csvBuilder.Append(System.Convert.ToBase64String(_encoding.GetBytes(array[i])));
			}

			return csvBuilder.ToString();
		}

		private string[] ConvertStringFromDatabaseToStringArray(string stringFromDatabase)
		{
			if (string.IsNullOrWhiteSpace(stringFromDatabase))
			{
				return null;
			}

			// 管理画面の実装に合わせた処理
			var stringList = new System.Collections.Generic.List<string>();
			string[] base64StringArray = stringFromDatabase.Split(StringFromDatabaseSeparators, System.StringSplitOptions.RemoveEmptyEntries);

			foreach (string base64String in base64StringArray)
			{
				string result = _encoding.GetString(System.Convert.FromBase64String(base64String));

				if (!string.IsNullOrWhiteSpace(result))
				{
					stringList.Add(result);
				}
			}

			return stringList.ToArray();
		}

		private int[] ConvertStringFromDatabaseToIntArray(string stringFromDatabase)
		{
			if (string.IsNullOrWhiteSpace(stringFromDatabase))
			{
				return null;
			}

			// 管理画面の実装に合わせた処理
			var intList = new System.Collections.Generic.List<int>();
			string[] base64StringArray = stringFromDatabase.Split(StringFromDatabaseSeparators, System.StringSplitOptions.RemoveEmptyEntries);

			foreach (string base64String in base64StringArray)
			{
				int result = 0;

				if ((int.TryParse(_encoding.GetString(System.Convert.FromBase64String(base64String)), out result)) && (result != 0))
				{
					// todo 有効な値の範囲チェック
					intList.Add(result);
				}
			}

			return intList.ToArray();
		}
		#endregion

		#region P/Invoke
		[System.Runtime.InteropServices.DllImport("netapi32.dll", CharSet = System.Runtime.InteropServices.CharSet.Unicode, CallingConvention = System.Runtime.InteropServices.CallingConvention.StdCall, SetLastError = true)]
		private static extern uint NetUserChangePassword(
			[System.Runtime.InteropServices.MarshalAs(System.Runtime.InteropServices.UnmanagedType.LPWStr)] string domainName,
			[System.Runtime.InteropServices.MarshalAs(System.Runtime.InteropServices.UnmanagedType.LPWStr)] string userName,
			[System.Runtime.InteropServices.MarshalAs(System.Runtime.InteropServices.UnmanagedType.LPWStr)] string oldPassword,
			[System.Runtime.InteropServices.MarshalAs(System.Runtime.InteropServices.UnmanagedType.LPWStr)] string newPassword
		);
		private const uint NERR_Success = 0;
		private const uint NERR_BASE = 2100;
		private const uint NERR_UserNotFound = NERR_BASE + 121;
		private const uint ERROR_INVALID_PASSWORD = 86;
		#endregion
	}
}
