﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <!-- UserConfirmation -->
    <add key="UserConfirmationAutoCloseTimeout" value="5000" />
    <!-- AutoCloseMode 0:No 1:Yes -->
    <add key="UserConfirmationAutoCloseMode" value="1" />
    <!-- Shortcut Key Combinations -->
    <!-- 162:VK_LCONTROL 91:VK_LWIN -->
    <add key="SasKeyCodes" value="162|91" />
    <!-- Auth Module -->
    <add key="AuthModuleAssemblyNameOrPath" value="eDoktor.FaceAuth.AuthModule, Version=*******, Culture=neutral, PublicKeyToken=0ff7bf58c14a5b51" />
    <add key="AuthModuleTypeName" value="eDoktor.FaceAuth.AuthModule.FaceAuthModule" />
    <add key="ClientSettingsProvider.ServiceUri" value="" />
  </appSettings>
  <system.web>
    <membership defaultProvider="ClientAuthenticationMembershipProvider">
      <providers>
        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
      </providers>
    </membership>
    <roleManager defaultProvider="ClientRoleProvider" enabled="true">
      <providers>
        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
      </providers>
    </roleManager>
  </system.web>
</configuration>