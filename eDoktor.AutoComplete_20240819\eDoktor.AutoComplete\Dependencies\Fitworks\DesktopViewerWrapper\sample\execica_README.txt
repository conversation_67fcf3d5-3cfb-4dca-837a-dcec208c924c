﻿*************************************************************************
* Copyright (C) 2014,2015 Fitworks All Rights Reserved.
*
* README file for execica.exe
*
* Revision History
* ------------------------------------------------------------------
*  Rev  | 日付       | コメント
* ------+------------+----------------------------------------------
*  1.0  | 2014/12/14 | 初回リリース(StoreFront2.6専用です）
* ------+------------+----------------------------------------------
*  1.1  | 2014/12/30 | 以下の引数を追加
*       |            | - /up : ユーザーIDの前に付与する文字列
*       |            | - /us : ユーザーIDの後に付与する文字列
* ------+------------+----------------------------------------------
*  1.2  | 2015/03/11 | 以下の引数を追加
*       |            | - /wfcrun : ICAファイルの起動をWfcrun32.exeで実行
* ------+------------+----------------------------------------------
*  1.3  | 2015/03/16 | 以下の引数を追加
*       |            | - /postexec : ICA接続処理完了後に起動するプログラムを指定
*       |            | - /postexecargs : postexecへの引数
* ------+------------+----------------------------------------------
*  1.4  | 2015/04/16 | .net4.0対応と微調整
*       |            | - 実行時に「ログイン」、「閉じる」をクリックできない様に変更
* ------------------------------------------------------------------
*************************************************************************

【概要】
　・XenAppの公開アプリケーションを起動します。
　
【実行/引数】
	execica.exe [/u userid] [/up useridprefix] [/us useridsuffix]
	            [/p password] [/autoconnect] [/autoclose] [/wfcrun]
	            [/postexec path\to\exe] [/postexecargs ]
	            [/a publishedAppName] [/r args for published app]
	/u: ユーザーID
	/up: ユーザーIDの前に付与する文字列
	/us: ユーザーIDの後に付与する文字列
	/p: パスワード
	/autoconnect: EXE起動後に自動ログインをする場合
	/autoclose: XenApp接続後に自動終了します。
	/wfcrun: ICAファイルの起動をWfcrun32.exeで実行
	/postexec: ICA接続処理完了後に起動するプログラムを指定
	/postexecargs: postexecへの引数
	    ※$USERNAME$ $PASSWORD$はフォームで入力された値で置換する
	/a: 公開アプリケーション名
	/r: 公開アプリケーションへの引数
	　　※この引数は引数全体の最後に設定してください。他の引数を
	　　※公開アプリケーション側の設定で引数に「%**」が設定されている
	　　　場合に有効です。


【アプリケーション設定ファイル】
    <!-- フォームタイトル -->
    <add key="Title" value="インターネットSBC" />

    <!-- フォームメッセージ -->
    <add key="Message" value="ユーザーIDとパスワードを入力してください。" />

    <!-- StoreFront -->
    <add key="StoreFront" value="http://172.16.2.38/Citrix/TestStoreWeb/" />

    <!-- 起動アプリ名（デフォルト） -->
    <add key="AppName" value="iexp" />
    ※AppNameは、引数/aが未指定の場合に利用されます。
    

【検証済み環境】
　・サーバー　　：XenApp7.6(StoreFront2.6)
　・クライアント：Receiver3.4/4.0/4.2

 
【制限事項/既知の不具合】
　・ユーザーIDとパスワードが不正な場合も「認証エラー」としてハンドリング
　　できていないため、「アプリケーションの起動に失敗しました。」のメッセージ
　　を表示します。
　・セッションローミングに時間がかかる様ですが、放置していると公開アプリ
　　が表示されます。

EOF.