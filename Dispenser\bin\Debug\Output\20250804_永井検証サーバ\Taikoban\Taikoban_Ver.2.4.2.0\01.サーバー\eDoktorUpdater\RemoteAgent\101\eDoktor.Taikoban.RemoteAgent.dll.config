﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <!-- メッセージ表示モジュール名 -->
    <add key="Messenger" value="eDoktor.Taikoban.Messenger.exe" />
    <!-- 画面キャプチャ―取得モジュール名 -->
    <add key="CaptureScreen" value="eDoktor.Taikoban.CaptureScreen.exe" />
    <!-- 画面キャプチャ―の一時保存領域 -->
    <add key="CaptureTempFolder" value="..\Capture\" />
    <!-- 端末操作モジュール名 -->
    <add key="TerminalOperator" value="eDoktor.Taikoban.TerminalOperator.exe" />

    <!-- 起動後最初の更新チェックまでの時間(ms)-->
    <add key="DueTime" value="60000" />
    <!-- 更新チェック間隔(ms)-->
    <add key="PeriodTime" value="0" />
  </appSettings>
</configuration>