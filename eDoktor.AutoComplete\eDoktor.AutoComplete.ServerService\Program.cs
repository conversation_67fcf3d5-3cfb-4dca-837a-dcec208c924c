﻿// Program.cs

using System;
using System.IO;
using System.ServiceProcess;

namespace eDoktor.Taikoban.ServerService
{
	static class Program
	{
		#region Main
		static void Main()
		{
			try
			{
				string traceContainingFolderPath = Common.Configuration.AppSetting("TraceContainingFolderPath");

				if (!string.IsNullOrWhiteSpace(traceContainingFolderPath))
				{
					if (!Directory.Exists(traceContainingFolderPath))
					{
						Directory.CreateDirectory(traceContainingFolderPath);
					}

					Common.Trace.ContainingFolderPath = traceContainingFolderPath;
				}

				Common.Trace.EnableDebugTrace = Common.Configuration.AppSetting("EnableDebugTrace", false);
				Common.Trace.EnableDetailedExceptionTrace = Common.Configuration.AppSetting("EnableDetailedExceptionTrace", false);
				Common.Trace.TraceTerm = TimeSpan.FromDays(Common.Configuration.AppSetting("TraceTermInDays", Common.Trace.DefaultTraceTerm.Days));
				Common.Trace.SafeEnableTrace = Common.Configuration.AppSetting("EnableTrace", false);
				AppDomain.CurrentDomain.UnhandledException += OnUnhandledExceptionEvent;
				ServiceBase.Run(new ServiceBase[] { new ServerService() });
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion

		#region Private Methods
		private static void OnUnhandledExceptionEvent(object sender, UnhandledExceptionEventArgs e)
		{
			try
			{
				var ex = e.ExceptionObject as Exception;

				Common.Trace.OutputExceptionTrace(ex);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion
	}
}
