<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="eDoktor.Taikoban.AccountImporter.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
    </sectionGroup>
    <section name="ldapSettings" type="eDoktor.Taikoban.AccountImporter.Configuration.LdapConfigurationSection,eDoktor.Taikoban.AccountImporter" />
  </configSections>
  <appSettings>
    <!-- Trace -->
    <add key="EnableTrace" value="true"/>
    <add key="EnableDebugTrace" value="true"/>
    <add key="EnableDetailedExceptionTrace" value="true"/>
    <add key="ContainingFolderPath" value=".\Logs\"/>
    <add key="TraceTermInDays" value="30"/>

    <!-- ファイル取得 -->
    <!-- 取得方法 0=FTP 1=ローカル 2=共有フォルダ -->
    <add key="FileGetMethod" value="1"/>
    <add key="EncryptedConnectUser" value="rCvUcDWSjlPCC87M0dvM/Q=="/>
    <add key="EncryptedConnectPassword" value="rCvUcDWSjlPCC87M0dvM/Q=="/>
    <add key="SourceFileUri" value="D:\eDoktor\Taikoban\AccountImporterLocal\eybry.csv"/>
    <add key="MaxRetryCount" value="3"/>
    <add key="RetryInterval" value="3000"/>
    <add key="LocalFilePath" value="D:\eDoktor\TaikobanIF\Temp\eybry.csv"/>
    <add key="FileBackupDirectory" value="D:\eDoktor\TaikobanIF\Temp\Backup\"/>
    <add key="FileBackupName" value="eybry_%DATETIME%.csv"/>
    <add key="FileTermInDays" value="30"/>

    <!-- ファイル内容 -->
    <add key="FileEncoding" value="Shift-JIS"/>
    <add key="LineLength" value="1338"/>
    <add key="InvalidatedDataOffset" value="1332"/>
    <add key="InvalidatedDataLength" value="1"/>
    <add key="LogicallyDeletedDataOffset" value="1336"/>
    <add key="LogicallyDeletedDataLength" value="1"/>
    <add key="UsageStartDatetimeDataOffset" value="1310"/>
    <add key="UsageStartDatetimeDataLength" value="8"/>
    <add key="UsageStartDatetimeDataFormat" value="yyyyMMdd"/>
    <add key="UsageStartDatetimeDataNullList" value="********"/>
    <add key="UsageEndDatetimeDataOffset" value="1321"/>
    <add key="UsageEndDatetimeDataLength" value="8"/>
    <add key="UsageEndDatetimeDataFormat" value="yyyyMMdd"/>
    <add key="UsageEndDatetimeDataNullList" value="99999999"/>
    <add key="OrganizationalIdDataOffset" value="1"/>
    <add key="OrganizationalIdDataLength" value="8"/>
    <add key="NameDataOffset" value="207"/>
    <add key="NameDataLength" value="20"/>
    <add key="KanaDataOffset" value="230"/>
    <add key="KanaDataLength" value="40"/>
    <add key="BirthdateDataOffset" value="316"/>
    <add key="BirthdateDataLength" value="8"/>
    <add key="BirthdateDataFormat" value="yyyyMMdd"/>
    <add key="BirthdateDataNullList" value="        |********|99999999"/>
    <add key="LogonIdDataOffset" value="1"/>
    <add key="LogonIdDataLength" value="8"/>
    <add key="PasswordDataOffset" value="12"/>
    <add key="PasswordDataLength" value="64"/>
    <add key="PasswordDataIsEncrypted" value="true"/>
    <add key="PasswordUpdateDatetimeDataOffset" value="1336"/>
    <add key="PasswordUpdateDatetimeDataLength" value="1"/>
    <add key="PasswordUpdateDatetimeDataFormat" value="yyyyMMdd00HHmmss"/>
    <add key="PasswordUpdateDatetimeDataNullList" value="0|1| "/>
    <add key="PasswordUpdateDatetimeIsUsedForNewAccount" value="false"/>
    <add key="JobCodeDataOffset" value="437"/>
    <add key="JobCodeDataLength" value="3"/>
    <add key="JobNameDataOffset" value="437"/>
    <add key="JobNameDataLength" value="3"/>

    <!-- 固定登録 -->
    <add key="LogonTo" value="hhx"/>
    <add key="PasswordUpdateDatetimeToUseForNewAccount" value="2023-01-01"/>
    <add key="PractitionerID" value="********"/>
    <add key="DefaultAccountGroupID" value="1"/>
    <add key="DefaultPrivilegeTemplateID" value="1"/>
    <add key="ErrorSource" value="8"/>
    <add key="ErrorWhenJobInsert" value="false"/>
    
    <!-- AD連携 -->
    <add key="SyncronizeAd" value="false" />
    <add key="LdapUrl" value="LDAP://172.17.200.31/OU=HIS_USER,OU=02.Users,DC=kainan-his,DC=local" />
    <add key="EncryptedLdapUser" value="6Pt3jvUnqmIOZgLnD88WCA==" />
    <add key="EncryptedLdapPassword" value="IErkYW7lG+jIRjxjXuTh8w==" />
    <add key="LdapAuthenticationTypes" value="545" />
    <add key="SharePath" value="\\172.17.201.175\個人フォルダ" />
    <add key="EncryptedShareUser" value="rCsFR+dPYWZtjzfszbcAfffMNjj+DmKGBBm0H8qR/9U=" />
    <add key="EncryptedSharePassword" value="IErkYW7lG+jIRjxjXuTh8w==" />
    <add key="EncryptedInitialPassword" value="24zbpxH8NfrjjFWi+ilEzw==" />
    <add key="ShareLocalDrive" value="X:" />
    <add key="ShareRetryCount" value="3" />
    <add key="ShareRetryInterval" value="1000" />
    <add key="ShareDomain" value="mit.local" />
    <add key="ShareUnnecessaryUserList" value="Users" />
    <add key="ExcludedAccountList" value="SYSTEM" />
  </appSettings>
  <connectionStrings>
    <!-- Taikobanサーバー -->
    <add name="Taikoban" connectionString="sOEqwRggeOs5J+mrA1PTQJHv+yDr+eSUxm5V9RWyjvi+aPWRWhUWhsN/I1J5Kz2S0R23UO9ZGEPnKiNSSJFDNuI575blkps0DMXCwrHxuP7Xrq0WQ4Z/1FO/gOY/6c6f7CtxnlmuaobnSQR52dOpgrCAcb7CgiysLPu8PQJBnQ3IE2984ThUacvxR+feZzRGT0cEuMnffO4owJCKRn1XZXGLizfjWIuR8GbZUqwyjLg=" providerName="System.Data.SqlClient"/>
  </connectionStrings>
  <!-- AD連携属性 -->
  <ldapSettings>
    <contents>
      <content name="cn" offset="0" length="8" propertyName="cn" prefix="" suffix="" isKey="true" />
      <content name="sAMAccountName" offset="0" length="8" propertyName="sAMAccountName" prefix="" suffix="" />
      <content name="displayName" offset="72" length="20" propertyName="displayName" prefix="" suffix="" />
      <content name="department" offset="325" length="10" propertyName="department" prefix="" suffix="" />
      <content name="mail" offset="181" length="80" propertyName="mail" prefix="" suffix="" />
      <content name="userPrincipalName" offset="0" length="8" propertyName="userPrincipalName" prefix="" suffix="@kainan.jaaikosei.or.jp"  />
      <content name="postalCode" offset="0" length="0" propertyName="postalCode" prefix="" suffix="0"  />
    </contents>
  </ldapSettings>
  <applicationSettings>
    <eDoktor.Taikoban.AccountImporter.Properties.Settings>
      <setting name="ConnectionStringLabel" serializeAs="String">
        <value>Taikoban</value>
      </setting>
    </eDoktor.Taikoban.AccountImporter.Properties.Settings>
  </applicationSettings>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.2"/>
  </startup>
</configuration>
