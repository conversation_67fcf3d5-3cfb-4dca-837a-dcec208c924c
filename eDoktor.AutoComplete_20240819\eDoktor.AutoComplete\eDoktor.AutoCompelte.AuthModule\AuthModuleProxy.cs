﻿// AuthModuleProxy.cs

using System;
using System.IO;
using System.Reflection;

namespace eDoktor.AutoComplete.AuthModule
{
	class AuthModuleProxy : MarshalByRefObject
	{
		// 特定のインターフェイスを実装させるなどの改修を行えないアセンブリに対してもプロキシを用意すればアクセスできる場合がある
		// またAppDomainを超えられないデータをやり取りする場合もプロキシ内で処理する事で対応できる場合がある
		#region Fields
		private readonly object SyncRoot = new object();
		private dynamic _instance;
		private volatile bool _abortRequested;
		#endregion

		#region Public Methods
		public AuthResponse PerformAuthentication(string assemblyNameOrPath, string typeName, AuthRequest authRequest)
		{
			try
			{
				using (Common.TimedLock.Lock(SyncRoot))
				{
					if (_instance != null)
					{
						throw new InvalidOperationException();
					}

					if (_abortRequested)
					{
						return new AuthResponse() { Result = AuthResultType.CallerAborted };
					}

					var assembly = (File.Exists(assemblyNameOrPath)) ? Assembly.LoadFile(assemblyNameOrPath) : Assembly.Load(assemblyNameOrPath);
					var type = assembly.GetType(typeName);
					_instance = Activator.CreateInstance(type);
				}

				return _instance.PerformAuthentication(authRequest);
			}
			finally
			{
				_abortRequested = false;
				_instance = null;
			}
		}

		public void Abort()
		{
			using (Common.TimedLock.Lock(SyncRoot))
			{
				if (_instance != null)
				{
					_instance.Abort();
				}
				else
				{
					_abortRequested = true;
				}
			}
		}
		#endregion

		#region Protected Methods
		public override object InitializeLifetimeService()
		{
			return null;
		}
		#endregion
	}
}
