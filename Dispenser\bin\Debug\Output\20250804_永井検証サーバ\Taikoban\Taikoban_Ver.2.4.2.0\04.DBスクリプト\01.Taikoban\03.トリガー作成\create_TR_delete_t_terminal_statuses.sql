USE [$(Param1)]
GO

/****** Object:  Trigger [dbo].[TR_delete_t_terminal_statuses]    Script Date: 01/07/2015 13:22:57 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[TR_delete_t_terminal_statuses]') AND type in (N'TR'))
DROP TRIGGER [dbo].[TR_delete_t_terminal_statuses]
GO

CREATE TRIGGER [dbo].[TR_delete_t_terminal_statuses] ON [dbo].[t_terminal_statuses]
FOR DELETE
AS 
BEGIN

INSERT INTO t_deleted_terminal_statuses
(
deletion_datetime
, registration_datetime
, modification_datetime
, terminal_name
, ip_address
, windows_session
, windows_session_change_datetime
, authentication
, authenticated_logon_id
, os_version
, installed_package_version
, package_installation_datetime
, package_installation_status
, authentication_enabled
, connection_status
)
SELECT 
GETDATE()
, registration_datetime
, modification_datetime
, terminal_name
, ip_address
, windows_session
, windows_session_change_datetime
, authentication
, authenticated_logon_id
, os_version
, installed_package_version
, package_installation_datetime
, package_installation_status
, authentication_enabled
, connection_status
FROM deleted

END
RETURN

GO