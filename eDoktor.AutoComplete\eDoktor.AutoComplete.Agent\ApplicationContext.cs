﻿// ApplicationContext.cs

namespace eDoktor.AutoComplete.Agent
{
	using Interprocess.ObjectValidationExtensions;
	using Interprocess.TcpClientExtensions;

	public class ApplicationContext : System.Windows.Forms.ApplicationContext
	{
		#region Fields
		private readonly object SyncRoot = new object();
		private static readonly System.TimeSpan InactivityLockMuteTimeSpan = System.TimeSpan.FromSeconds(10);
        // ▼ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
        private Interprocess.AutoCompleteInfo _autoCompleteInfo;
        // ▲ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
		private NotifyIcon _notifyIcon;
		private Interprocess.Client _client;
		private System.Threading.Timer _timer;
		private System.DateTime _timerActionMutingDue = System.DateTime.MinValue;
		private static readonly System.TimeSpan TimerActionMutingTimeSpan = System.TimeSpan.FromSeconds(5);	// 連続で要求しないように時間を取る
		private LoginWindowMonitor _loginWindowMonitor = null;
		// ▼ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
		private readonly string AutoCompleteInfoDataStoreDirectoryPath = eDoktor.Common.Configuration.AppSetting("DataStoreDirectoryPath");
		private const string AutoCompleteInfoDataStoreFileName = "AutoCompleteInfo.xml";
		// ▲ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
		// ▼ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
		private static Common.PersistentDataStore<Interprocess.AutoCompleteInfo> _autoCompleteInfoDataStore;
		// ▲ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
		#endregion

		#region Properties

		// ▼ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
		public Interprocess.AutoCompleteInfo DefaultAutoCompleteInfo
		{
			get
			{
				using (Common.TimedLock.Lock(SyncRoot))
				{
					if (_autoCompleteInfoDataStore == null)
					{
						//***debug***
						eDoktor.Common.Trace.OutputDebugTrace("AutoCompleteInfoDataStoreDirectoryPath={0}", AutoCompleteInfoDataStoreDirectoryPath);

						if (string.IsNullOrWhiteSpace(AutoCompleteInfoDataStoreDirectoryPath))
						{
							//***debug***
							eDoktor.Common.Trace.OutputDebugTrace("AutoCompleteInfoDataStoreDirectoryPath is null or space");
							_autoCompleteInfoDataStore = new Common.PersistentDataStore<Interprocess.AutoCompleteInfo>(System.IO.Path.Combine(Common.Environment.GetExecutingAssemblyDirectoryName(), AutoCompleteInfoDataStoreFileName), null, true);
						}
						else
						{
							if (!System.IO.Directory.Exists(AutoCompleteInfoDataStoreDirectoryPath))
							{
								//***debug***
								eDoktor.Common.Trace.OutputDebugTrace("AutoCompleteInfoDataStoreDirectoryPath is not exists");
								System.IO.Directory.CreateDirectory(AutoCompleteInfoDataStoreDirectoryPath);
							}

							_autoCompleteInfoDataStore = new Common.PersistentDataStore<Interprocess.AutoCompleteInfo>(System.IO.Path.Combine(AutoCompleteInfoDataStoreDirectoryPath, AutoCompleteInfoDataStoreFileName), null, true);
						}
					}

					return _autoCompleteInfoDataStore.Value;
				}
			}

			set
			{
				using (Common.TimedLock.Lock(SyncRoot))
				{
					if (!value.IsValid())
					{
						return;
					}

					var autoCompleteInfo = DefaultAutoCompleteInfo;

					if (!autoCompleteInfo.Equals(value))
					{
						_autoCompleteInfoDataStore.Value = value;
						_autoCompleteInfoDataStore.Serialize();
					}
				}
			}
		}
		// ▲ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
		#endregion

		#region Constructors
		private ApplicationContext()
		{
		}

		public static ApplicationContext Create(System.Drawing.Icon icon)
		{
			var temp = new ApplicationContext();

			try
			{
				temp.Initialize(icon);
				var self = temp;
				temp = null;

				return self;
			}
			finally
			{
				if (temp != null)
				{
					temp.Dispose();
				}
			}
		}

		private void Initialize(System.Drawing.Icon icon)
		{
			_notifyIcon = new NotifyIcon(icon);
			_notifyIcon.TerminalMenuClick += OnTerminalMenuClick;
			_notifyIcon.AboutMenuClick += OnAboutMenuClick;
			_notifyIcon.ExitMenuClick += OnExitMenuClick;

			_client = new Interprocess.Client(Interprocess.ConnectionType.ToRemoteServer, true);
			_client.SocketConnected += OnSocketConnected;
			_client.SocketDisconnected += OnSocketDisconnected;
			_client.PacketReceived += OnPacketReceived;
			_client.Start();

            // ▼ DELETE 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
            //_loginWindowMonitor = new LoginWindowMonitor();
            //_loginWindowMonitor.Start();
            // ▲ DELETE 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
		}
		#endregion

		#region IDisposable
		protected override void Dispose(bool disposing)
		{
			try
			{
				Common.Trace.OutputDebugTrace("disposing={0}", disposing);

				if (_client != null)
				{
					_client.Dispose();
					_client = null;
				}

				if (_timer != null)
				{
					_timer.Dispose();
					_timer = null;
				}

				if (_loginWindowMonitor != null)
				{
					_loginWindowMonitor.Dispose();
					_loginWindowMonitor = null;
				}

				if (_notifyIcon != null)
				{
					_notifyIcon.Dispose();
					_notifyIcon = null;
				}

				base.Dispose(disposing);
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion

		#region Protected Methods
		protected override void ExitThreadCore()
		{
			try
			{
				base.ExitThreadCore();
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion

		#region Private Methods

		private void OnSocketConnected(object sender, System.EventArgs e)
		{
			try
			{
				var client = sender as Interprocess.TcpClient;
				var responseData = client.Transceive(new Interprocess.ClientToServerAutoCompleteSettingQueryRequestData() {  });

				using (Common.TimedLock.Lock(SyncRoot))
				{
					if (responseData != null)
					{
						// ▼ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
						DefaultAutoCompleteInfo = responseData.AutoCompleteInfo;
                        // ▲ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
					}

                    // ▼ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
                    if (_loginWindowMonitor == null)
                    {
                        _loginWindowMonitor = new LoginWindowMonitor(DefaultAutoCompleteInfo);
                        _loginWindowMonitor.Start();
                    }
                    // ▲ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
				}
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnSocketDisconnected(object sender, System.EventArgs e)
		{
			try
			{
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnPacketReceived(object sender, Common.PacketEventArgs e)
		{
			try
			{
				var client = sender as Interprocess.TcpClient;
				var packet = e.Packet as Interprocess.Packet;

				Common.Trace.OutputDebugTrace("command={0}", packet.Command);

				switch (packet.Command)
				{
					default:
						break;
				}
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnTerminalMenuClick(object sender, System.EventArgs e)
		{
			try
			{

			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnAboutMenuClick(object sender, System.EventArgs e)
		{
			try
			{

			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnExitMenuClick(object sender, System.EventArgs e)
		{
			try
			{
				Common.Trace.OutputWarningTrace("Exiting");

				ExitThread();
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion
	}
}
