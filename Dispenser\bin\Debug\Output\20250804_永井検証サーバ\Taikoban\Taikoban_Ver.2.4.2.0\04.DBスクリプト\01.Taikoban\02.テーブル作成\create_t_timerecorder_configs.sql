USE [$(Param1)]
GO
/****** Object:  Table [dbo].[t_timerecorder_configs]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_timerecorder_configs]') AND type in (N'U'))
DROP TABLE [dbo].[t_timerecorder_configs]
GO

CREATE TABLE [dbo].[t_timerecorder_configs](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[registered_by] [int] NOT NULL,
	[modification_datetime] [datetime2](7) NOT NULL,
	[modified_by] [int] NOT NULL,
	[require_password] [int] NOT NULL,
	[seconds_to_display_completion] [int] NOT NULL,
	[require_clock_in] [int] NOT NULL,
	[allow_clock_in_out_at_all_terminals] [int] NOT NULL,
 CONSTRAINT [PK_t_timerecorder_configs] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_timerecorder_configs] ADD  CONSTRAINT [DF_t_timerecorder_configs_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO
ALTER TABLE [dbo].[t_timerecorder_configs] ADD  CONSTRAINT [DF_t_timerecorder_configs_registered_by]  DEFAULT ((0)) FOR [registered_by]
GO
ALTER TABLE [dbo].[t_timerecorder_configs] ADD  CONSTRAINT [DF_t_timerecorder_configs_modification_datetime]  DEFAULT (getdate()) FOR [modification_datetime]
GO
ALTER TABLE [dbo].[t_timerecorder_configs] ADD  CONSTRAINT [DF_t_timerecorder_configs_modified_by]  DEFAULT ((0)) FOR [modified_by]
GO
ALTER TABLE [dbo].[t_timerecorder_configs] ADD  CONSTRAINT [DF_t_timerecorder_configs_require_password]  DEFAULT ((0)) FOR [require_password]
GO
ALTER TABLE [dbo].[t_timerecorder_configs] ADD  CONSTRAINT [DF_t_timerecorder_configs_seconds_to_display_completion]  DEFAULT ((0)) FOR [seconds_to_display_completion]
GO
ALTER TABLE [dbo].[t_timerecorder_configs] ADD  CONSTRAINT [DF_t_timerecorder_configs_require_clock_in]  DEFAULT ((0)) FOR [require_clock_in]
GO
ALTER TABLE [dbo].[t_timerecorder_configs] ADD  CONSTRAINT [DF_t_timerecorder_configs_allow_clock_in_out_at_all_terminals]  DEFAULT ((0)) FOR [allow_clock_in_out_at_all_terminals]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_timerecorder_configs', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_timerecorder_configs', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_timerecorder_configs', @level2type=N'COLUMN',@level2name=N'registered_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_timerecorder_configs', @level2type=N'COLUMN',@level2name=N'modification_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_timerecorder_configs', @level2type=N'COLUMN',@level2name=N'modified_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'出退勤時にパスワード入力が必要 0:不要 1:必要' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_timerecorder_configs', @level2type=N'COLUMN',@level2name=N'require_password'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'出退勤打刻の完了画面を表示する時間（秒）' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_timerecorder_configs', @level2type=N'COLUMN',@level2name=N'seconds_to_display_completion'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'認証を行う前に出勤操作が必要か 0:不要 1:必要' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_timerecorder_configs', @level2type=N'COLUMN',@level2name=N'require_clock_in'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'全端末での出勤操作を許可するか 0:しない 1:する' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_timerecorder_configs', @level2type=N'COLUMN',@level2name=N'allow_clock_in_out_at_all_terminals'
GO
