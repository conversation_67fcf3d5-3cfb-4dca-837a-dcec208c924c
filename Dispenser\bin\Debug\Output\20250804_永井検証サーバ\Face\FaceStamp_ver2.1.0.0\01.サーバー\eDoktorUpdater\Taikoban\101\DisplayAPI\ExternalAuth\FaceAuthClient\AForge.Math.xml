<?xml version="1.0"?>
<doc>
    <assembly>
        <name>AForge.Math</name>
    </assembly>
    <members>
        <member name="T:AForge.Math.Tools">
            <summary>
            Set of tool functions.
            </summary>
            
            <remarks>The class contains different utility functions.</remarks>
            
        </member>
        <member name="M:AForge.Math.Tools.Pow2(System.Int32)">
            <summary>
            Calculates power of 2.
            </summary>
            
            <param name="power">Power to raise in.</param>
            
            <returns>Returns specified power of 2 in the case if power is in the range of
            [0, 30]. Otherwise returns 0.</returns>
            
        </member>
        <member name="M:AForge.Math.Tools.IsPowerOf2(System.Int32)">
            <summary>
            Checks if the specified integer is power of 2.
            </summary>
            
            <param name="x">Integer number to check.</param>
            
            <returns>Returns <b>true</b> if the specified number is power of 2.
            Otherwise returns <b>false</b>.</returns>
            
        </member>
        <member name="M:AForge.Math.Tools.Log2(System.Int32)">
            <summary>
            Get base of binary logarithm.
            </summary>
            
            <param name="x">Source integer number.</param>
            
            <returns>Power of the number (base of binary logarithm).</returns>
            
        </member>
        <member name="T:AForge.Math.Metrics.IDistance">
             <summary>
             Interface for distance metric algorithms.
             </summary>
             
             <remarks><para>The interface defines a set of methods implemented
             by distance metric algorithms. These algorithms typically take a set of points and return a 
             distance measure of the x and y coordinates. In this case, the points are represented by two vectors.</para>
             
             <para>Distance metric algorithms are used in many machine learning algorithms e.g K-nearest neighbor
             and K-means clustering.</para>
            
             <para>For additional details about distance metrics, documentation of the
             particular algorithms should be studied.</para>
             </remarks>
             
        </member>
        <member name="M:AForge.Math.Metrics.IDistance.GetDistance(System.Double[],System.Double[])">
            <summary>
            Returns distance between two N-dimensional double vectors.
            </summary>
            
            <param name="p">1st point vector.</param>
            <param name="q">2nd point vector.</param>
            
            <returns>Returns distance measurement determined by the given algorithm.</returns>
            
        </member>
        <member name="T:AForge.Math.Histogram">
             <summary>
             Histogram for discrete random values.
             </summary>
             
             <remarks><para>The class wraps histogram for discrete stochastic function, which is represented
             by integer array, where indexes of the array are treated as values of the stochastic function,
             but array values are treated as "probabilities" (total amount of hits).
             </para>
             
             <para>Sample usage:</para>
             <code>
             // create histogram
             Histogram histogram = new Histogram( new int[10] { 0, 0, 1, 3, 6, 8, 11, 0, 0, 0 } );
             // get mean and standard deviation values
             Console.WriteLine( "mean = " + histogram.Mean + ", std.dev = " + histogram.StdDev );
             </code>
             </remarks>
            
        </member>
        <member name="M:AForge.Math.Histogram.#ctor(System.Int32[])">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Histogram"/> class.
            </summary>
            
            <param name="values">Values of the histogram.</param>
            
            <remarks><para>Indexes of the input array are treated as values of stochastic function,
            but array values are treated as "probabilities" (total amount of hits).
            </para></remarks>
            
        </member>
        <member name="M:AForge.Math.Histogram.GetRange(System.Double)">
            <summary>
            Get range around median containing specified percentage of values.
            </summary>
            
            <param name="percent">Values percentage around median.</param>
            
            <returns>Returns the range which containes specifies percentage of values.</returns>
            
            <remarks><para>The method calculates range of stochastic variable, which summary probability
            comprises the specified percentage of histogram's hits.</para>
            
            <para>Sample usage:</para>
            <code>
            // create histogram
            Histogram histogram = new Histogram( new int[10] { 0, 0, 1, 3, 6, 8, 11, 0, 0, 0 } );
            // get 50% range
            IntRange range = histogram.GetRange( 0.5 );
            // show the range ([4, 6])
            Console.WriteLine( "50% range = [" + range.Min + ", " + range.Max + "]" );
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Histogram.Update">
            <summary>
            Update statistical value of the histogram.
            </summary>
            
            <remarks>The method recalculates statistical values of the histogram, like mean,
            standard deviation, etc., in the case if histogram's values were changed directly.
            The method should be called only in the case if histogram's values were retrieved
            through <see cref="P:AForge.Math.Histogram.Values"/> property and updated after that.
            </remarks>
            
        </member>
        <member name="P:AForge.Math.Histogram.Values">
            <summary>
            Values of the histogram.
            </summary>
            
            <remarks><para>Indexes of this array are treated as values of stochastic function,
            but array values are treated as "probabilities" (total amount of hits).
            </para></remarks>
            
        </member>
        <member name="P:AForge.Math.Histogram.Mean">
            <summary>
            Mean value.
            </summary>
            
            <remarks><para>The property allows to retrieve mean value of the histogram.</para>
            
            <para>Sample usage:</para>
            <code>
            // create histogram
            Histogram histogram = new Histogram( new int[10] { 0, 0, 1, 3, 6, 8, 11, 0, 0, 0 } );
            // get mean value (= 4.862)
            Console.WriteLine( "mean = " + histogram.Mean.ToString( "F3" ) );
            </code>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.Histogram.StdDev">
            <summary>
            Standard deviation.
            </summary>
            
            <remarks><para>The property allows to retrieve standard deviation value of the histogram.</para>
            
            <para>Sample usage:</para>
            <code>
            // create histogram
            Histogram histogram = new Histogram( new int[10] { 0, 0, 1, 3, 6, 8, 11, 0, 0, 0 } );
            // get std.dev. value (= 1.136)
            Console.WriteLine( "std.dev. = " + histogram.StdDev.ToString( "F3" ) );
            </code>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.Histogram.Median">
            <summary>
            Median value.
            </summary>
            
            <remarks><para>The property allows to retrieve median value of the histogram.</para>
            
            <para>Sample usage:</para>
            <code>
            // create histogram
            Histogram histogram = new Histogram( new int[10] { 0, 0, 1, 3, 6, 8, 11, 0, 0, 0 } );
            // get median value (= 5)
            Console.WriteLine( "median = " + histogram.Median );
            </code>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.Histogram.Min">
            <summary>
            Minimum value.
            </summary>
            
            <remarks><para>The property allows to retrieve minimum value of the histogram with non zero
            hits count.</para>
            
            <para>Sample usage:</para>
            <code>
            // create histogram
            Histogram histogram = new Histogram( new int[10] { 0, 0, 1, 3, 6, 8, 11, 0, 0, 0 } );
            // get min value (= 2)
            Console.WriteLine( "min = " + histogram.Min );
            </code>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.Histogram.Max">
            <summary>
            Maximum value.
            </summary>
            
            <remarks><para>The property allows to retrieve maximum value of the histogram with non zero
            hits count.</para>
            
            <para>Sample usage:</para>
            <code>
            // create histogram
            Histogram histogram = new Histogram( new int[10] { 0, 0, 1, 3, 6, 8, 11, 0, 0, 0 } );
            // get max value (= 6)
            Console.WriteLine( "max = " + histogram.Max );
            </code>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.Histogram.TotalCount">
             <summary>
             Total count of values.
             </summary>
             
             <remarks><para>The property represents total count of values contributed to the histogram, which is
             essentially sum of the <see cref="P:AForge.Math.Histogram.Values"/> array.</para>
            
             <para>Sample usage:</para>
             <code>
             // create histogram
             Histogram histogram = new Histogram( new int[10] { 0, 0, 1, 3, 6, 8, 11, 0, 0, 0 } );
             // get total value (= 29)
             Console.WriteLine( "total = " + histogram.TotalCount );
             </code>
             </remarks>
             
        </member>
        <member name="T:AForge.Math.Statistics">
            <summary>
            Set of statistics functions.
            </summary>
            
            <remarks>The class represents collection of simple functions used
            in statistics.</remarks>
            
        </member>
        <member name="M:AForge.Math.Statistics.Mean(System.Int32[])">
            <summary>
            Calculate mean value.
            </summary>
            
            <param name="values">Histogram array.</param>
            
            <returns>Returns mean value.</returns>
            
            <remarks><para>The input array is treated as histogram, i.e. its
            indexes are treated as values of stochastic function, but
            array values are treated as "probabilities" (total amount of
            hits).</para>
            
            <para>Sample usage:</para>
            <code>
            // create histogram array
            int[] histogram = new int[] { 1, 1, 2, 3, 6, 8, 11, 12, 7, 3 };
            // calculate mean value
            double mean = Statistics.Mean( histogram );
            // output it (5.759)
            Console.WriteLine( "mean = " + mean.ToString( "F3" ) );
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Statistics.StdDev(System.Int32[])">
            <summary>
            Calculate standard deviation.
            </summary>
            
            <param name="values">Histogram array.</param>
            
            <returns>Returns value of standard deviation.</returns>
            
            <remarks><para>The input array is treated as histogram, i.e. its
            indexes are treated as values of stochastic function, but
            array values are treated as "probabilities" (total amount of
            hits).</para>
            
            <para>Sample usage:</para>
            <code>
            // create histogram array
            int[] histogram = new int[] { 1, 1, 2, 3, 6, 8, 11, 12, 7, 3 };
            // calculate standard deviation value
            double stdDev = Statistics.StdDev( histogram );
            // output it (1.999)
            Console.WriteLine( "std.dev. = " + stdDev.ToString( "F3" ) );
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Statistics.StdDev(System.Int32[],System.Double)">
            <summary>
            Calculate standard deviation.
            </summary>
            
            <param name="values">Histogram array.</param>
            <param name="mean">Mean value of the histogram.</param>
            
            <returns>Returns value of standard deviation.</returns>
            
            <remarks><para>The input array is treated as histogram, i.e. its
            indexes are treated as values of stochastic function, but
            array values are treated as "probabilities" (total amount of
            hits).</para>
            
            <para>The method is an equevalent to the <see cref="M:AForge.Math.Statistics.StdDev(System.Int32[])"/> method,
            but it relieas on the passed mean value, which is previously calculated
            using <see cref="M:AForge.Math.Statistics.Mean(System.Int32[])"/> method.</para>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Statistics.Median(System.Int32[])">
            <summary>
            Calculate median value.
            </summary>
            
            <param name="values">Histogram array.</param>
            
            <returns>Returns value of median.</returns>
            
            <remarks>
            <para>The input array is treated as histogram, i.e. its
            indexes are treated as values of stochastic function, but
            array values are treated as "probabilities" (total amount of
            hits).</para>
            
            <para><note>The median value is calculated accumulating histogram's
            values starting from the <b>left</b> point until the sum reaches 50% of
            histogram's sum.</note></para>
            
            <para>Sample usage:</para>
            <code>
            // create histogram array
            int[] histogram = new int[] { 1, 1, 2, 3, 6, 8, 11, 12, 7, 3 };
            // calculate median value
            int median = Statistics.Median( histogram );
            // output it (6)
            Console.WriteLine( "median = " + median );
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Statistics.GetRange(System.Int32[],System.Double)">
            <summary>
            Get range around median containing specified percentage of values.
            </summary>
            
            <param name="values">Histogram array.</param>
            <param name="percent">Values percentage around median.</param>
            
            <returns>Returns the range which containes specifies percentage
            of values.</returns>
            
            <remarks>
            <para>The input array is treated as histogram, i.e. its
            indexes are treated as values of stochastic function, but
            array values are treated as "probabilities" (total amount of
            hits).</para>
            
            <para>The method calculates range of stochastic variable, which summary probability
            comprises the specified percentage of histogram's hits.</para>
            
            <para>Sample usage:</para>
            <code>
            // create histogram array
            int[] histogram = new int[] { 1, 1, 2, 3, 6, 8, 11, 12, 7, 3 };
            // get 75% range around median
            IntRange range = Statistics.GetRange( histogram, 0.75 );
            // output it ([4, 8])
            Console.WriteLine( "range = [" + range.Min + ", " + range.Max + "]" );
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Statistics.Entropy(System.Int32[])">
            <summary>
            Calculate entropy value.
            </summary>
            
            <param name="values">Histogram array.</param>
            
            <returns>Returns entropy value of the specified histagram array.</returns>
            
            <remarks><para>The input array is treated as histogram, i.e. its
            indexes are treated as values of stochastic function, but
            array values are treated as "probabilities" (total amount of
            hits).</para>
            
            <para>Sample usage:</para>
            <code>
            // create histogram array with 2 values of equal probabilities
            int[] histogram1 = new int[2] { 3, 3 };
            // calculate entropy
            double entropy1 = Statistics.Entropy( histogram1 );
            // output it (1.000)
            Console.WriteLine( "entropy1 = " + entropy1.ToString( "F3" ) );
            
            // create histogram array with 4 values of equal probabilities
            int[] histogram2 = new int[4] { 1, 1, 1, 1 };
            // calculate entropy
            double entropy2 = Statistics.Entropy( histogram2 );
            // output it (2.000)
            Console.WriteLine( "entropy2 = " + entropy2.ToString( "F3" ) );
            
            // create histogram array with 4 values of different probabilities
            int[] histogram3 = new int[4] { 1, 2, 3, 4 };
            // calculate entropy
            double entropy3 = Statistics.Entropy( histogram3 );
            // output it (1.846)
            Console.WriteLine( "entropy3 = " + entropy3.ToString( "F3" ) );
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Statistics.Mode(System.Int32[])">
             <summary>
             Calculate mode value.
             </summary>
             
             <param name="values">Histogram array.</param>
             
             <returns>Returns mode value of the histogram array.</returns>
             
             <remarks>
             <para>The input array is treated as histogram, i.e. its
             indexes are treated as values of stochastic function, but
             array values are treated as "probabilities" (total amount of
             hits).</para>
             
             <para><note>Returns the minimum mode value if the specified histogram is multimodal.</note></para>
            
             <para>Sample usage:</para>
             <code>
             // create array
             int[] values = new int[] { 1, 1, 2, 3, 6, 8, 11, 12, 7, 3 };
             // calculate mode value
             int mode = Statistics.Mode( values );
             // output it (7)
             Console.WriteLine( "mode = " + mode );
             </code>
             </remarks>
             
        </member>
        <member name="T:AForge.Math.Random.GaussianGenerator">
            <summary>
            Gaussian random numbers generator.
            </summary>
            
            <remarks><para>The random number generator generates gaussian
            random numbers with specified mean and standard deviation values.</para>
            
            <para>The generator uses <see cref="T:AForge.Math.Random.StandardGenerator"/> generator as base
            to generate random numbers.</para>
            
            <para>Sample usage:</para>
            <code>
            // create instance of random generator
            IRandomNumberGenerator generator = new GaussianGenerator( 5.0, 1.5 );
            // generate random number
            float randomNumber = generator.Next( );
            </code>
            </remarks>
            
        </member>
        <member name="T:AForge.Math.Random.IRandomNumberGenerator">
            <summary>
            Interface for random numbers generators.
            </summary>
            
            <remarks><para>The interface defines set of methods and properties, which should
            be implemented by different algorithms for random numbers generatation.</para>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Random.IRandomNumberGenerator.Next">
            <summary>
            Generate next random number.
            </summary>
            
            <returns>Returns next random number.</returns>
            
        </member>
        <member name="M:AForge.Math.Random.IRandomNumberGenerator.SetSeed(System.Int32)">
            <summary>
            Set seed of the random numbers generator.
            </summary>
            
            <param name="seed">Seed value.</param>
            
        </member>
        <member name="P:AForge.Math.Random.IRandomNumberGenerator.Mean">
            <summary>
            Mean value of generator.
            </summary>
            
        </member>
        <member name="P:AForge.Math.Random.IRandomNumberGenerator.Variance">
            <summary>
            Variance value of generator.
            </summary>
            
        </member>
        <member name="M:AForge.Math.Random.GaussianGenerator.#ctor(System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Random.GaussianGenerator"/> class.
            </summary>
            
            <param name="mean">Mean value.</param>
            <param name="stdDev">Standard deviation value.</param>
            
        </member>
        <member name="M:AForge.Math.Random.GaussianGenerator.#ctor(System.Single,System.Single,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Random.GaussianGenerator"/> class.
            </summary>
            
            <param name="mean">Mean value.</param>
            <param name="stdDev">Standard deviation value.</param>
            <param name="seed">Seed value to initialize random numbers generator.</param>
            
        </member>
        <member name="M:AForge.Math.Random.GaussianGenerator.Next">
            <summary>
            Generate next random number.
            </summary>
            
            <returns>Returns next random number.</returns>
            
        </member>
        <member name="M:AForge.Math.Random.GaussianGenerator.SetSeed(System.Int32)">
            <summary>
            Set seed of the random numbers generator.
            </summary>
            
            <param name="seed">Seed value.</param>
            
            <remarks>Resets random numbers generator initializing it with
            specified seed value.</remarks>
            
        </member>
        <member name="P:AForge.Math.Random.GaussianGenerator.Mean">
             <summary>
             Mean value of the generator.
             </summary>
            
        </member>
        <member name="P:AForge.Math.Random.GaussianGenerator.Variance">
             <summary>
             Variance value of the generator.
             </summary>
            
        </member>
        <member name="P:AForge.Math.Random.GaussianGenerator.StdDev">
             <summary>
             Standard deviation value.
             </summary>
            
        </member>
        <member name="T:AForge.Math.Geometry.LineSegment">
            <summary>
            The class encapsulates 2D line segment and provides some tool methods related to lines.
            </summary>
            
            <remarks><para>The class provides some methods which are related to line segments:
            distance to point, finding intersection point, etc.
            </para>
            
            <para>A line segment may be converted to a <see cref="T:AForge.Math.Geometry.Line"/>.</para>
            
            <para>Sample usage:</para>
            <code>
            // create a segment
            LineSegment segment = new LineSegment( new Point( 0, 0 ), new Point( 3, 4 ) );
            // get segment's length
            float length = segment.Length;
            
            // get intersection point with a line
            Point? intersection = segment.GetIntersectionWith(
                new Line( new Point( -3, 8 ), new Point( 0, 4 ) ) );
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Geometry.LineSegment.#ctor(AForge.Point,AForge.Point)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Geometry.LineSegment"/> class.
            </summary>
            
            <param name="start">Segment's start point.</param>
            <param name="end">Segment's end point.</param>
            
            <exception cref="T:System.ArgumentException">Thrown if the two points are the same.</exception>
            
        </member>
        <member name="M:AForge.Math.Geometry.LineSegment.op_Explicit(AForge.Math.Geometry.LineSegment)~AForge.Math.Geometry.Line">
            <summary>
            Converts this <see cref="T:AForge.Math.Geometry.LineSegment"/> to a <see cref="T:AForge.Math.Geometry.Line"/> by discarding
            its endpoints and extending it infinitely in both directions.
            </summary>
            
            <param name="segment">The segment to convert to a <see cref="T:AForge.Math.Geometry.Line"/>.</param>
            
            <returns>Returns a <see cref="T:AForge.Math.Geometry.Line"/> that contains this <paramref name="segment"/>.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.LineSegment.DistanceToPoint(AForge.Point)">
            <summary>
            Calculate Euclidean distance between a point and a finite line segment.
            </summary>
            
            <param name="point">The point to calculate the distance to.</param>
            
            <returns>Returns the Euclidean distance between this line segment and the specified point. Unlike
            <see cref="M:AForge.Math.Geometry.Line.DistanceToPoint(AForge.Point)"/>, this returns the distance from the finite segment. (0,0) is 5 units
            from the segment (0,5)-(0,8), but is 0 units from the line through those points.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.LineSegment.GetIntersectionWith(AForge.Math.Geometry.LineSegment)">
            <summary>
            Finds, provided it exists, the intersection point with the specified <see cref="T:AForge.Math.Geometry.LineSegment"/>.
            </summary>
            
            <param name="other"><see cref="T:AForge.Math.Geometry.LineSegment"/> to find intersection with.</param>
            
            <returns>Returns intersection point with the specified <see cref="T:AForge.Math.Geometry.LineSegment"/>, or <see langword="null"/>, if
            the two segments do not intersect.</returns>
            
            <remarks><para>If the two segments do not intersect, the method returns <see langword="null"/>. If the two
            segments share multiple points, this throws an <see cref="T:System.InvalidOperationException"/>.
            </para></remarks>
            
            <exception cref="T:System.InvalidOperationException">Thrown if the segments overlap - if they have
            multiple points in common.</exception>
            
        </member>
        <member name="M:AForge.Math.Geometry.LineSegment.GetIntersectionWith(AForge.Math.Geometry.Line)">
            <summary>
            Finds, provided it exists, the intersection point with the specified <see cref="T:AForge.Math.Geometry.Line"/>.
            </summary>
            
            <param name="other"><see cref="T:AForge.Math.Geometry.Line"/> to find intersection with.</param>
            
            <returns>Returns intersection point with the specified <see cref="T:AForge.Math.Geometry.Line"/>, or <see langword="null"/>, if
            the line does not intersect with this segment.</returns>
            
            <remarks><para>If the line and the segment do not intersect, the method returns <see langword="null"/>. If the line
            and the segment share multiple points, the method throws an <see cref="T:System.InvalidOperationException"/>.
            </para></remarks>
            
            <exception cref="T:System.InvalidOperationException">Thrown if this segment is a portion of
            <paramref name="other"/> line.</exception>
            
        </member>
        <member name="M:AForge.Math.Geometry.LineSegment.op_Equality(AForge.Math.Geometry.LineSegment,AForge.Math.Geometry.LineSegment)">
             <summary>
             Equality operator - checks if two line segments have equal parameters.
             </summary>
             
             <param name="line1">First line segment to check.</param>
             <param name="line2">Second line segment to check.</param>
             
             <returns>Returns <see langword="true"/> if parameters of specified
             line segments are equal.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.LineSegment.op_Inequality(AForge.Math.Geometry.LineSegment,AForge.Math.Geometry.LineSegment)">
             <summary>
             Inequality operator - checks if two lines have different parameters.
             </summary>
             
             <param name="line1">First line segment to check.</param>
             <param name="line2">Second line segment to check.</param>
             
             <returns>Returns <see langword="true"/> if parameters of specified
             line segments are not equal.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.LineSegment.Equals(System.Object)">
            <summary>
            Check if this instance of <see cref="T:AForge.Math.Geometry.LineSegment"/> equals to the specified one.
            </summary>
            
            <param name="obj">Another line segment to check equalty to.</param>
            
            <returns>Return <see langword="true"/> if objects are equal.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.LineSegment.GetHashCode">
            <summary>
            Get hash code for this instance.
            </summary>
            
            <returns>Returns the hash code for this instance.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.LineSegment.ToString">
             <summary>
             Get string representation of the class.
             </summary>
             
             <returns>Returns string, which contains values of the like in readable form.</returns>
            
        </member>
        <member name="P:AForge.Math.Geometry.LineSegment.Start">
            <summary>
            Start point of the line segment.
            </summary>
        </member>
        <member name="P:AForge.Math.Geometry.LineSegment.End">
            <summary>
            End point of the line segment.
            </summary>
        </member>
        <member name="P:AForge.Math.Geometry.LineSegment.Length">
            <summary>
            Get segment's length - Euclidean distance between its <see cref="P:AForge.Math.Geometry.LineSegment.Start"/> and <see cref="P:AForge.Math.Geometry.LineSegment.End"/> points.
            </summary>
        </member>
        <member name="T:AForge.Math.Gaussian">
            <summary>
            Gaussian function.
            </summary>
            
            <remarks><para>The class is used to calculate 1D and 2D Gaussian functions for
            specified <see cref="P:AForge.Math.Gaussian.Sigma"/> (s) value:</para>
            
            <code lang="none">
            1-D: f(x) = exp( x * x / ( -2 * s * s ) ) / ( s * sqrt( 2 * PI ) )
            
            2-D: f(x, y) = exp( x * x + y * y / ( -2 * s * s ) ) / ( s * s * 2 * PI )
            </code>
            
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Gaussian.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Gaussian"/> class.
            </summary>
            
        </member>
        <member name="M:AForge.Math.Gaussian.#ctor(System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Gaussian"/> class.
            </summary>
            
            <param name="sigma">Sigma value.</param>
            
        </member>
        <member name="M:AForge.Math.Gaussian.Function(System.Double)">
            <summary>
            1-D Gaussian function.
            </summary>
            
            <param name="x">x value.</param>
            
            <returns>Returns function's value at point <paramref name="x"/>.</returns>
            
            <remarks><para>The function calculates 1-D Gaussian function:</para>
            
            <code lang="none">
            f(x) = exp( x * x / ( -2 * s * s ) ) / ( s * sqrt( 2 * PI ) )
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Gaussian.Function2D(System.Double,System.Double)">
            <summary>
            2-D Gaussian function.
            </summary>
            
            <param name="x">x value.</param>
            <param name="y">y value.</param>
            
            <returns>Returns function's value at point (<paramref name="x"/>, <paramref name="y"/>).</returns>
            
            <remarks><para>The function calculates 2-D Gaussian function:</para>
            
            <code lang="none">
            f(x, y) = exp( x * x + y * y / ( -2 * s * s ) ) / ( s * s * 2 * PI )
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Gaussian.Kernel(System.Int32)">
            <summary>
            1-D Gaussian kernel.
            </summary>
            
            <param name="size">Kernel size (should be odd), [3, 101].</param>
            
            <returns>Returns 1-D Gaussian kernel of the specified size.</returns>
            
            <remarks><para>The function calculates 1-D Gaussian kernel, which is array
            of Gaussian function's values in the [-r, r] range of x value, where
            r=floor(<paramref name="size"/>/2).
            </para></remarks>
            
            <exception cref="T:System.ArgumentException">Wrong kernel size.</exception>
            
        </member>
        <member name="M:AForge.Math.Gaussian.Kernel2D(System.Int32)">
            <summary>
            2-D Gaussian kernel.
            </summary>
            
            <param name="size">Kernel size (should be odd), [3, 101].</param>
            
            <returns>Returns 2-D Gaussian kernel of specified size.</returns>
            
            <remarks><para>The function calculates 2-D Gaussian kernel, which is array
            of Gaussian function's values in the [-r, r] range of x,y values, where
            r=floor(<paramref name="size"/>/2).
            </para></remarks>
            
            <exception cref="T:System.ArgumentException">Wrong kernel size.</exception>
            
        </member>
        <member name="P:AForge.Math.Gaussian.Sigma">
            <summary>
            Sigma value.
            </summary>
            
            <remarks><para>Sigma property of Gaussian function.</para>
            
            <para>Default value is set to <b>1</b>. Minimum allowed value is <b>0.00000001</b>.</para>
            </remarks>
            
        </member>
        <member name="T:AForge.Math.FourierTransform">
            <summary>
            Fourier transformation.
            </summary>
            
            <remarks>The class implements one dimensional and two dimensional
            Discrete and Fast Fourier Transformation.</remarks>
            
        </member>
        <member name="M:AForge.Math.FourierTransform.DFT(AForge.Math.Complex[],AForge.Math.FourierTransform.Direction)">
            <summary>
            One dimensional Discrete Fourier Transform.
            </summary>
            
            <param name="data">Data to transform.</param>
            <param name="direction">Transformation direction.</param>
            
        </member>
        <member name="M:AForge.Math.FourierTransform.DFT2(AForge.Math.Complex[0:,0:],AForge.Math.FourierTransform.Direction)">
            <summary>
            Two dimensional Discrete Fourier Transform.
            </summary>
            
            <param name="data">Data to transform.</param>
            <param name="direction">Transformation direction.</param>
            
        </member>
        <member name="M:AForge.Math.FourierTransform.FFT(AForge.Math.Complex[],AForge.Math.FourierTransform.Direction)">
            <summary>
            One dimensional Fast Fourier Transform.
            </summary>
            
            <param name="data">Data to transform.</param>
            <param name="direction">Transformation direction.</param>
            
            <remarks><para><note>The method accepts <paramref name="data"/> array of 2<sup>n</sup> size
            only, where <b>n</b> may vary in the [1, 14] range.</note></para></remarks>
            
            <exception cref="T:System.ArgumentException">Incorrect data length.</exception>
            
        </member>
        <member name="M:AForge.Math.FourierTransform.FFT2(AForge.Math.Complex[0:,0:],AForge.Math.FourierTransform.Direction)">
            <summary>
            Two dimensional Fast Fourier Transform.
            </summary>
            
            <param name="data">Data to transform.</param>
            <param name="direction">Transformation direction.</param>
            
            <remarks><para><note>The method accepts <paramref name="data"/> array of 2<sup>n</sup> size
            only in each dimension, where <b>n</b> may vary in the [1, 14] range. For example, 16x16 array
            is valid, but 15x15 is not.</note></para></remarks>
            
            <exception cref="T:System.ArgumentException">Incorrect data length.</exception>
            
        </member>
        <member name="T:AForge.Math.FourierTransform.Direction">
            <summary>
            Fourier transformation direction.
            </summary>
        </member>
        <member name="F:AForge.Math.FourierTransform.Direction.Forward">
            <summary>
            Forward direction of Fourier transformation.
            </summary>
        </member>
        <member name="F:AForge.Math.FourierTransform.Direction.Backward">
            <summary>
            Backward direction of Fourier transformation.
            </summary>
        </member>
        <member name="T:AForge.Math.Geometry.Line">
            <summary>
            The class encapsulates 2D line and provides some tool methods related to lines.
            </summary>
            
            <remarks><para>The class provides some methods which are related to lines:
            angle between lines, distance to point, finding intersection point, etc.
            </para>
            
            <para>Generally, the equation of the line is y = <see cref="P:AForge.Math.Geometry.Line.Slope"/> * x + 
            <see cref="P:AForge.Math.Geometry.Line.Intercept"/>. However, when <see cref="P:AForge.Math.Geometry.Line.Slope"/> is an Infinity,
            <see name="Intercept"/> would normally be meaningless, and it would be
            impossible to distinguish the line x = 5 from the line x = -5. Therefore,
            if <see cref="P:AForge.Math.Geometry.Line.Slope"/> is <see cref="F:System.Single.PositiveInfinity"/> or
            <see cref="F:System.Single.NegativeInfinity"/>, the line's equation is instead 
            x = <see cref="P:AForge.Math.Geometry.Line.Intercept"/>.</para>
            
            <para>Sample usage:</para>
            <code>
            // create a line
            Line line = Line.FromPoints( new Point( 0, 0 ), new Point( 3, 4 ) );
            // check if it is vertical or horizontal
            if ( line.IsVertical || line.IsHorizontal )
            {
                // ...
            }
            
            // get intersection point with another line
            Point intersection = line.GetIntersectionWith(
                Line.FromPoints( new Point( 3, 0 ), new Point( 0, 4 ) ) );
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Geometry.Line.FromPoints(AForge.Point,AForge.Point)">
            <summary>
            Creates a <see cref="T:AForge.Math.Geometry.Line"/>  that goes through the two specified points.
            </summary>
            
            <param name="point1">One point on the line.</param>
            <param name="point2">Another point on the line.</param>
            
            <returns>Returns a <see cref="T:AForge.Math.Geometry.Line"/> representing the line between <paramref name="point1"/>
            and <paramref name="point2"/>.</returns>
            
            <exception cref="T:System.ArgumentException">Thrown if the two points are the same.</exception>
            
        </member>
        <member name="M:AForge.Math.Geometry.Line.FromSlopeIntercept(System.Single,System.Single)">
            <summary>
            Creates a <see cref="T:AForge.Math.Geometry.Line"/> with the specified slope and intercept.
            </summary>
            
            <param name="slope">The slope of the line</param>
            <param name="intercept">The Y-intercept of the line, unless the slope is an
            infinity, in which case the line's equation is "x = intercept" instead.</param>
            
            <returns>Returns a <see cref="T:AForge.Math.Geometry.Line"/> representing the specified line.</returns>
            
            <remarks><para>The construction here follows the same rules as for the rest of this class.
            Most lines are expressed as y = slope * x + intercept. Vertical lines, however, are 
            x = intercept. This is indicated by <see cref="P:AForge.Math.Geometry.Line.IsVertical"/> being true or by 
            <see cref="P:AForge.Math.Geometry.Line.Slope"/> returning <see cref="F:System.Single.PositiveInfinity"/> or 
            <see cref="F:System.Single.NegativeInfinity"/>.</para></remarks>
            
        </member>
        <member name="M:AForge.Math.Geometry.Line.FromRTheta(System.Single,System.Single)">
            <summary>
            Constructs a <see cref="T:AForge.Math.Geometry.Line"/> from a radius and an angle (in degrees).
            </summary>
            
            <param name="radius">The minimum distance from the line to the origin.</param>
            <param name="theta">The angle of the vector from the origin to the line.</param>
            
            <returns>Returns a <see cref="T:AForge.Math.Geometry.Line"/> representing the specified line.</returns>
            
            <remarks><para><paramref name="radius"/> is the minimum distance from the origin
            to the line, and <paramref name="theta"/> is the counterclockwise rotation from
            the positive X axis to the vector through the origin and normal to the line.</para>
            <para>This means that if <paramref name="theta"/> is in [0,180), the point on the line
            closest to the origin is on the positive X or Y axes, or in quadrants I or II. Likewise,
            if <paramref name="theta"/> is in [180,360), the point on the line closest to the
            origin is on the negative X or Y axes, or in quadrants III or IV.</para></remarks>
            
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown if radius is negative.</exception>
            
        </member>
        <member name="M:AForge.Math.Geometry.Line.FromPointTheta(AForge.Point,System.Single)">
            <summary>
            Constructs a <see cref="T:AForge.Math.Geometry.Line"/> from a point and an angle (in degrees).
            </summary>
            
            <param name="point">The minimum distance from the line to the origin.</param>
            <param name="theta">The angle of the normal vector from the origin to the line.</param>
            
            <remarks><para><paramref name="theta"/> is the counterclockwise rotation from
            the positive X axis to the vector through the origin and normal to the line.</para>
            <para>This means that if <paramref name="theta"/> is in [0,180), the point on the line
            closest to the origin is on the positive X or Y axes, or in quadrants I or II. Likewise,
            if <paramref name="theta"/> is in [180,360), the point on the line closest to the
            origin is on the negative X or Y axes, or in quadrants III or IV.</para></remarks>
            
            <returns>Returns a <see cref="T:AForge.Math.Geometry.Line"/> representing the specified line.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.Line.GetAngleBetweenLines(AForge.Math.Geometry.Line)">
            <summary>
            Calculate minimum angle between this line and the specified line measured in [0, 90] degrees range.
            </summary>
            
            <param name="secondLine">The line to find angle between.</param>
            
            <returns>Returns minimum angle between lines.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.Line.GetIntersectionWith(AForge.Math.Geometry.Line)">
            <summary>
            Finds intersection point with the specified line.
            </summary>
            
            <param name="secondLine">Line to find intersection with.</param>
            
            <returns>Returns intersection point with the specified line, or 
            <see langword="null"/> if the lines are parallel and distinct.</returns>
            
            <exception cref="T:System.InvalidOperationException">Thrown if the specified line is the same line as this line.</exception>
            
        </member>
        <member name="M:AForge.Math.Geometry.Line.GetIntersectionWith(AForge.Math.Geometry.LineSegment)">
            <summary>
            Finds, provided it exists, the intersection point with the specified <see cref="T:AForge.Math.Geometry.LineSegment"/>.
            </summary>
            
            <param name="other"><see cref="T:AForge.Math.Geometry.LineSegment"/> to find intersection with.</param>
            
            <returns>Returns intersection point with the specified <see cref="T:AForge.Math.Geometry.LineSegment"/>, or <see langword="null"/>,
            if this line does not intersect with the segment.</returns>
            
            <remarks><para>If the line and segment do not intersect, the method returns <see langword="null"/>.
            If the line and segment share multiple points, the method throws an <see cref="T:System.InvalidOperationException"/>.
            </para></remarks>
            
            <exception cref="T:System.InvalidOperationException">Thrown if <paramref name="other"/> is a portion
            of this line.</exception>
            
        </member>
        <member name="M:AForge.Math.Geometry.Line.DistanceToPoint(AForge.Point)">
            <summary>
            Calculate Euclidean distance between a point and a line.
            </summary>
            
            <param name="point">The point to calculate distance to.</param>
            
            <returns>Returns the Euclidean distance between this line and the specified point. Unlike
            <see cref="M:AForge.Math.Geometry.LineSegment.DistanceToPoint(AForge.Point)"/>, this returns the distance from the infinite line. (0,0) is 0 units
            from the line defined by (0,5) and (0,8), but is 5 units from the segment with those endpoints.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.Line.op_Equality(AForge.Math.Geometry.Line,AForge.Math.Geometry.Line)">
             <summary>
             Equality operator - checks if two lines have equal parameters.
             </summary>
             
             <param name="line1">First line to check.</param>
             <param name="line2">Second line to check.</param>
             
             <returns>Returns <see langword="true"/> if parameters of specified
             lines are equal.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.Line.op_Inequality(AForge.Math.Geometry.Line,AForge.Math.Geometry.Line)">
             <summary>
             Inequality operator - checks if two lines have different parameters.
             </summary>
             
             <param name="line1">First line to check.</param>
             <param name="line2">Second line to check.</param>
             
             <returns>Returns <see langword="true"/> if parameters of specified
             lines are not equal.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.Line.Equals(System.Object)">
            <summary>
            Check if this instance of <see cref="T:AForge.Math.Geometry.Line"/> equals to the specified one.
            </summary>
            
            <param name="obj">Another line to check equalty to.</param>
            
            <returns>Return <see langword="true"/> if objects are equal.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.Line.GetHashCode">
            <summary>
            Get hash code for this instance.
            </summary>
            
            <returns>Returns the hash code for this instance.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.Line.ToString">
             <summary>
             Get string representation of the class.
             </summary>
             
             <returns>Returns string, which contains values of the like in readable form.</returns>
            
        </member>
        <member name="P:AForge.Math.Geometry.Line.IsVertical">
             <summary>
             Checks if the line vertical or not.
             </summary>
            
        </member>
        <member name="P:AForge.Math.Geometry.Line.IsHorizontal">
            <summary>
            Checks if the line horizontal or not.
            </summary>
        </member>
        <member name="P:AForge.Math.Geometry.Line.Slope">
            <summary>
            Gets the slope of the line.
            </summary>
        </member>
        <member name="P:AForge.Math.Geometry.Line.Intercept">
            <summary>
            If not <see cref="P:AForge.Math.Geometry.Line.IsVertical"/>, gets the Line's Y-intercept.
            If <see cref="P:AForge.Math.Geometry.Line.IsVertical"/> gets the line's X-intercept.
            </summary>
        </member>
        <member name="T:AForge.Math.Metrics.JaccardDistance">
            <summary>
            Jaccard distance metric. 
            </summary>
            
            <remarks><para>This class represents the 
            <a href="http://en.wikipedia.org/wiki/Jaccard_distance">Jaccard distance metric</a>.</para>
            
            <para>Sample usage:</para>
            <code>
            // instantiate new distance class
            JaccardDistance dist = new JaccardDistance( ); 
            // create two vectors for inputs
            double[] p = new double[] { 2.5, 3.5, 3.0, 3.5, 2.5, 3.0 };
            double[] q = new double[] { 3.0, 3.5, 1.5, 5.0, 3.5, 3.0 };
            // get distance between the two vectors
            double distance = dist.GetDistance( p, q );
            </code>   
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Metrics.JaccardDistance.GetDistance(System.Double[],System.Double[])">
            <summary>
            Returns distance between two N-dimensional double vectors.
            </summary>
            
            <param name="p">1st point vector.</param>
            <param name="q">2nd point vector.</param>
            
            <returns>Returns Jaccard distance between two supplied vectors.</returns>
            
            <exception cref="T:System.ArgumentException">Thrown if the two vectors are of different dimensions (if specified
            array have different length).</exception>
            
        </member>
        <member name="T:AForge.Math.Geometry.GeometryTools">
            <summary>
            Collection of some gemetry tool methods.
            </summary>
            
        </member>
        <member name="M:AForge.Math.Geometry.GeometryTools.GetAngleBetweenVectors(AForge.Point,AForge.Point,AForge.Point)">
            <summary>
            Calculate angle between to vectors measured in [0, 180] degrees range.
            </summary>
            
            <param name="startPoint">Starting point of both vectors.</param>
            <param name="vector1end">Ending point of the first vector.</param>
            <param name="vector2end">Ending point of the second vector.</param>
            
            <returns>Returns angle between specified vectors measured in degrees.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.GeometryTools.GetAngleBetweenLines(AForge.Point,AForge.Point,AForge.Point,AForge.Point)">
            <summary>
            Calculate minimum angle between two lines measured in [0, 90] degrees range.
            </summary>
            
            <param name="a1">A point on the first line.</param>
            <param name="a2">Another point on the first line.</param>
            <param name="b1">A point on the second line.</param>
            <param name="b2">Another point on the second line.</param>
            
            <returns>Returns minimum angle between two lines.</returns>
            
            <remarks><para><note>It is preferred to use <see cref="M:AForge.Math.Geometry.Line.GetAngleBetweenLines(AForge.Math.Geometry.Line)"/> if it is required to calculate angle
            multiple times for one of the lines.</note></para></remarks>
            
            <exception cref="T:System.ArgumentException"><paramref name="a1"/> and <paramref name="a2"/> are the same,
            -OR- <paramref name="b1"/> and <paramref name="b2"/> are the same.</exception>
            
        </member>
        <member name="T:AForge.Math.Geometry.ClosePointsMergingOptimizer">
            <summary>
            Shape optimizer, which merges points within close distance to each other.
            </summary>
            
            <remarks><para>This shape optimizing algorithm checks all points of a shape
            and merges any two points which are within <see cref="P:AForge.Math.Geometry.ClosePointsMergingOptimizer.MaxDistanceToMerge">specified distance</see>
            to each other. Two close points are replaced by a single point, which has
            mean coordinates of the removed points.</para>
            
            <para><note>Because of the fact that the algorithm performs points merging
            while it goes through a shape, it may merge several points (more than 2) into a
            single point, where distance between extreme points may be bigger
            than the <see cref="P:AForge.Math.Geometry.ClosePointsMergingOptimizer.MaxDistanceToMerge">specified limit</see>. For example, suppose
            a case with 3 points, where 1st and 2nd points are close enough to be merged, but the
            3rd point is a little bit further. During merging of 1st and 2nd points, it may
            happen that the new point with mean coordinates will get closer to the 3rd point,
            so they will be merged also on next iteration of the algorithm.</note></para>
            
            <para>
            For example, the below circle shape comprised of 65 points, can be optimized to 8 points
            by setting <see cref="P:AForge.Math.Geometry.ClosePointsMergingOptimizer.MaxDistanceToMerge"/> to 28.<br/>
            <img src="img/math/close_points_merging_optimizer.png" width="268" height="238"/>
            </para>
            </remarks>
            
        </member>
        <member name="T:AForge.Math.Geometry.IShapeOptimizer">
             <summary>
             Interface for shape optimizing algorithms.
             </summary>
             
             <remarks><para>The interface defines set of methods, which should be implemented
             by shape optimizing algorithms. These algorithms take input shape, which is defined
             by a set of points (corners of convex hull, etc.), and remove some insignificant points from it,
             which has little influence on the final shape's look.</para>
             
             <para>The shape optimizing algorithms can be useful in conjunction with such algorithms
             like convex hull searching, which usually may provide many hull points, where some
             of them are insignificant and could be removed.</para>
            
             <para>For additional details about shape optimizing algorithms, documentation of
             particular algorithm should be studied.</para>
             </remarks>
             
        </member>
        <member name="M:AForge.Math.Geometry.IShapeOptimizer.OptimizeShape(System.Collections.Generic.List{AForge.IntPoint})">
            <summary>
            Optimize specified shape.
            </summary>
            
            <param name="shape">Shape to be optimized.</param>
            
            <returns>Returns final optimized shape, which may have reduced amount of points.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.ClosePointsMergingOptimizer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Geometry.ClosePointsMergingOptimizer"/> class.
            </summary>
            
        </member>
        <member name="M:AForge.Math.Geometry.ClosePointsMergingOptimizer.#ctor(System.Single)">
             <summary>
             Initializes a new instance of the <see cref="T:AForge.Math.Geometry.ClosePointsMergingOptimizer"/> class.
             </summary>
             
             <param name="maxDistanceToMerge">Maximum allowed distance between points, which are
             merged during optimization (see <see cref="P:AForge.Math.Geometry.ClosePointsMergingOptimizer.MaxDistanceToMerge"/>).</param>
            
        </member>
        <member name="M:AForge.Math.Geometry.ClosePointsMergingOptimizer.OptimizeShape(System.Collections.Generic.List{AForge.IntPoint})">
            <summary>
            Optimize specified shape.
            </summary>
            
            <param name="shape">Shape to be optimized.</param>
            
            <returns>Returns final optimized shape, which may have reduced amount of points.</returns>
            
        </member>
        <member name="P:AForge.Math.Geometry.ClosePointsMergingOptimizer.MaxDistanceToMerge">
            <summary>
            Maximum allowed distance between points, which are merged during optimization, [0, ∞).
            </summary>
            
            <remarks><para>The property sets maximum allowed distance between two points of
            a shape, which are replaced by single point with mean coordinates.</para>
            
            <para>Default value is set to <b>10</b>.</para></remarks>
            
        </member>
        <member name="T:AForge.Math.Random.StandardGenerator">
            <summary>
            Standard random numbers generator.
            </summary>
            
            <remarks><para>The random number generator generates gaussian
            random numbers with zero mean and standard deviation of one. The generator
            implements polar form of the Box-Muller transformation.</para>
            
            <para>The generator uses <see cref="T:AForge.Math.Random.UniformOneGenerator"/> generator as a base
            to generate random numbers.</para>
            
            <para>Sample usage:</para>
            <code>
            // create instance of random generator
            IRandomNumberGenerator generator = new StandardGenerator( );
            // generate random number
            float randomNumber = generator.Next( );
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Random.StandardGenerator.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Random.StandardGenerator"/> class.
            </summary>
            
        </member>
        <member name="M:AForge.Math.Random.StandardGenerator.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Random.StandardGenerator"/> class.
            </summary>
            
            <param name="seed">Seed value to initialize random numbers generator.</param>
            
        </member>
        <member name="M:AForge.Math.Random.StandardGenerator.Next">
            <summary>
            Generate next random number.
            </summary>
            
            <returns>Returns next random number.</returns>
            
        </member>
        <member name="M:AForge.Math.Random.StandardGenerator.SetSeed(System.Int32)">
            <summary>
            Set seed of the random numbers generator.
            </summary>
            
            <param name="seed">Seed value.</param>
            
            <remarks>Resets random numbers generator initializing it with
            specified seed value.</remarks>
            
        </member>
        <member name="P:AForge.Math.Random.StandardGenerator.Mean">
            <summary>
            Mean value of the generator.
            </summary>
            
        </member>
        <member name="P:AForge.Math.Random.StandardGenerator.Variance">
             <summary>
             Variance value of the generator.
             </summary>
            
        </member>
        <member name="T:AForge.Math.Geometry.CoplanarPosit">
             <summary>
             3D pose estimation algorithm (coplanar case).
             </summary>
            
             <remarks><para>The class implements an algorithm for 3D object's pose estimation from it's
             2D coordinates obtained by perspective projection, when the object is described coplanar points.
             The idea of the implemented math and algorithm is described in "Iterative Pose Estimation using
             Coplanar Feature Points" paper written by Oberkampf, Daniel DeMenthon and Larry Davis
             (the implementation of the algorithm is very close translation of the pseudo code given by the
             paper, so should be easy to follow).</para>
             
             <para><note>At this point the implementation works only with models described by 4 points, which is
             the minimum number of points enough for 3D pose estimation.</note></para>
             
             <para><note>The 4 model's point are supposed to be coplanar, i.e. supposed to reside all within
             same planer. See <see cref="T:AForge.Math.Geometry.Posit"/> for none coplanar case.</note></para>
             
             <para>Read <a href="http://www.aforgenet.com/articles/posit/">3D Pose Estimation</a> article for
             additional information and samples.</para>
             
             <para>Sample usage:</para>
             <code>
             // points of real object - model
             Vector3[] copositObject = new Vector3[4]
             { 
                 new Vector3( -56.5f, 0,  56.5f ),
                 new Vector3(  56.5f, 0,  56.5f ),
                 new Vector3(  56.5f, 0, -56.5f ),
                 new Vector3( -56.5f, 0, -56.5f ),
             };
             // focal length of camera used to capture the object
             float focalLength = 640; // depends on your camera or projection system
             // initialize CoPOSIT object
             CoplanarPosit coposit = new CoplanarPosit( copositObject, focalLength );
             
             // 2D points of te object - projection
             AForge.Point[] projectedPoints = new AForge.Point[4]
             {
                 new AForge.Point( -77,  48 ),
                 new AForge.Point(  44,  66 ),
                 new AForge.Point(  75, -36 ),
                 new AForge.Point( -61, -58 ),
             };
             // estimate pose
             Matrix3x3 rotationMatrix;
             Vector3 translationVector;
             coposit.EstimatePose( projectedPoints,
                 out rotationMatrix, out translationVector );
             </code>
             </remarks>
             
             <seealso cref="T:AForge.Math.Geometry.Posit"/>
            
        </member>
        <member name="M:AForge.Math.Geometry.CoplanarPosit.#ctor(AForge.Math.Vector3[],System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Geometry.Posit"/> class.
            </summary>
            
            <param name="model">Array of vectors containing coordinates of four real model's point.</param>
            <param name="focalLength">Effective focal length of the camera used to capture the model.</param>
            
            <exception cref="T:System.ArgumentException">The model must have 4 points.</exception>
            
        </member>
        <member name="M:AForge.Math.Geometry.CoplanarPosit.EstimatePose(AForge.Point[],AForge.Math.Matrix3x3@,AForge.Math.Vector3@)">
            <summary>
            Estimate pose of a model from it's projected 2D coordinates.
            </summary>
            
            <param name="points">4 2D points of the <see cref="P:AForge.Math.Geometry.CoplanarPosit.Model">model's</see> projection.</param>
            <param name="rotation">Gets best estimation of object's rotation.</param>
            <param name="translation">Gets best estimation of object's translation.</param>
            
            <exception cref="T:System.ArgumentException">4 points must be be given for pose estimation.</exception>
            
            <remarks><para>Because of the Coplanar POSIT algorithm's nature, it provides two pose estimations,
            which are valid from the algorithm's math point of view. For each pose an error is calculated,
            which specifies how good estimation fits to the specified real 2D coordinated. The method
            provides the best estimation through its output parameters <paramref name="rotation"/> and
            <paramref name="translation"/>. This may be enough for many of the pose estimation application.
            For those, who require checking the alternate pose estimation, it can be obtained using
            <see cref="P:AForge.Math.Geometry.CoplanarPosit.AlternateEstimatedRotation"/> and <see cref="P:AForge.Math.Geometry.CoplanarPosit.AlternateEstimatedTranslation"/> properties.
            The calculated error is provided for both estimations through <see cref="P:AForge.Math.Geometry.CoplanarPosit.BestEstimationError"/> and
            <see cref="P:AForge.Math.Geometry.CoplanarPosit.AlternateEstimationError"/> properties.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.Geometry.CoplanarPosit.BestEstimatedRotation">
            <summary>
            Best estimated pose recently found.
            </summary>
            
            <remarks><para>The property keeps best estimated pose found by the latest call to <see cref="M:AForge.Math.Geometry.CoplanarPosit.EstimatePose(AForge.Point[],AForge.Math.Matrix3x3@,AForge.Math.Vector3@)"/>.
            The same estimated pose is provided by that method also and can be accessed through this property
            for convenience.</para>
            
            <para>See also <see cref="P:AForge.Math.Geometry.CoplanarPosit.BestEstimatedTranslation"/> and <see cref="P:AForge.Math.Geometry.CoplanarPosit.BestEstimationError"/>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.Geometry.CoplanarPosit.BestEstimatedTranslation">
            <summary>
            Best estimated translation recently found.
            </summary>
            
            <remarks><para>The property keeps best estimated translation found by the latest call to <see cref="M:AForge.Math.Geometry.CoplanarPosit.EstimatePose(AForge.Point[],AForge.Math.Matrix3x3@,AForge.Math.Vector3@)"/>.
            The same estimated translation is provided by that method also and can be accessed through this property
            for convenience.</para>
            
            <para>See also <see cref="P:AForge.Math.Geometry.CoplanarPosit.BestEstimatedRotation"/> and <see cref="P:AForge.Math.Geometry.CoplanarPosit.BestEstimationError"/>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.Geometry.CoplanarPosit.BestEstimationError">
            <summary>
            Error of the best pose estimation.
            </summary>
            
            <remarks><para>The property keeps error of the best pose estimation, which is calculated as average
            error between real angles of the specified quadrilateral and angles of the quadrilateral which
            is a projection of the best pose estimation. The error is measured degrees in (angle).</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.Geometry.CoplanarPosit.AlternateEstimatedRotation">
            <summary>
            Alternate estimated pose recently found.
            </summary>
            
            <remarks><para>The property keeps alternate estimated pose found by the latest call to <see cref="M:AForge.Math.Geometry.CoplanarPosit.EstimatePose(AForge.Point[],AForge.Math.Matrix3x3@,AForge.Math.Vector3@)"/>.</para>
            
            <para>See also <see cref="P:AForge.Math.Geometry.CoplanarPosit.AlternateEstimatedTranslation"/> and <see cref="P:AForge.Math.Geometry.CoplanarPosit.AlternateEstimationError"/>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.Geometry.CoplanarPosit.AlternateEstimatedTranslation">
            <summary>
            Alternated estimated translation recently found.
            </summary>
            
            <remarks><para>The property keeps alternate estimated translation found by the latest call to <see cref="M:AForge.Math.Geometry.CoplanarPosit.EstimatePose(AForge.Point[],AForge.Math.Matrix3x3@,AForge.Math.Vector3@)"/>.</para>
            
            <para>See also <see cref="P:AForge.Math.Geometry.CoplanarPosit.AlternateEstimatedRotation"/> and <see cref="P:AForge.Math.Geometry.CoplanarPosit.AlternateEstimationError"/>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.Geometry.CoplanarPosit.AlternateEstimationError">
            <summary>
            Error of the alternate pose estimation.
            </summary>
            
            <remarks><para>The property keeps error of the alternate pose estimation, which is calculated as average
            error between real angles of the specified quadrilateral and angles of the quadrilateral which
            is a projection of the alternate pose estimation. The error is measured in degrees (angle).</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.Geometry.CoplanarPosit.Model">
            <summary>
            Coordinates of the model points which pose should be estimated.
            </summary>
        </member>
        <member name="P:AForge.Math.Geometry.CoplanarPosit.FocalLength">
            <summary>
            Effective focal length of the camera used to capture the model.
            </summary>
        </member>
        <member name="T:AForge.Math.Metrics.PearsonCorrelation">
            <summary>
            Pearson correlation metric. 
            </summary>
            
            <remarks><para>This class represents the 
            <a href="http://en.wikipedia.org/wiki/Pearson_correlation">Pearson correlation metric</a>.</para>
            
            <para>Sample usage:</para>
            <code>
            // instantiate new pearson correlation class
            PearsonCorrelation cor = new PearsonCorrelation( ); 
            // create two vectors for inputs
            double[] p = new double[] { 2.5, 3.5, 3.0, 3.5, 2.5, 3.0 };
            double[] q = new double[] { 3.0, 3.5, 1.5, 5.0, 3.5, 3.0 };
            // get correlation between the two vectors
            double correlation = cor.GetSimilarityScore( p, q );
            </code>    
            </remarks>
            
        </member>
        <member name="T:AForge.Math.Metrics.ISimilarity">
             <summary>
             Interface for similarity algorithms.
             </summary>
             
             <remarks><para>The interface defines a set of methods implemented
             by similarity and correlation algorithms. These algorithms typically take a set of points and return a 
             similarity score for the two vectors.</para>
             
             <para>Similarity and correlation algorithms are used in many machine learning and collaborative
             filtering algorithms.</para>
            
             <para>For additional details about similarity metrics, documentation of the
             particular algorithms should be studied.</para>
             </remarks>
             
        </member>
        <member name="M:AForge.Math.Metrics.ISimilarity.GetSimilarityScore(System.Double[],System.Double[])">
            <summary>
            Returns similarity score for two N-dimensional double vectors. 
            </summary>
            
            <param name="p">1st point vector.</param>
            <param name="q">2nd point vector.</param>
            
            <returns>Returns similarity score determined by the given algorithm.</returns>
            
        </member>
        <member name="M:AForge.Math.Metrics.PearsonCorrelation.GetSimilarityScore(System.Double[],System.Double[])">
            <summary>
            Returns the pearson correlation for two N-dimensional double vectors. 
            </summary>
            
            <param name="p">1st point vector.</param>
            <param name="q">2nd point vector.</param>
            
            <returns>Returns Pearson correlation between two supplied vectors.</returns>
            
            <exception cref="T:System.ArgumentException">Thrown if the two vectors are of different dimensions (if specified
            array have different length).</exception>
            
        </member>
        <member name="T:AForge.Math.Geometry.SimpleShapeChecker">
             <summary>
             A class for checking simple geometrical shapes.
             </summary>
             
             <remarks><para>The class performs checking/detection of some simple geometrical
             shapes for provided set of points (shape's edge points). During the check
             the class goes through the list of all provided points and checks how accurately
             they fit into assumed shape.</para>
             
             <para>All the shape checks allow some deviation of
             points from the shape with assumed parameters. In other words it is allowed
             that specified set of points may form a little bit distorted shape, which may be
             still recognized. The allowed amount of distortion is controlled by two
             properties (<see cref="P:AForge.Math.Geometry.SimpleShapeChecker.MinAcceptableDistortion"/> and <see cref="P:AForge.Math.Geometry.SimpleShapeChecker.RelativeDistortionLimit"/>),
             which allow higher distortion level for bigger shapes and smaller amount of
             distortion for smaller shapes. Checking specified set of points, the class
             calculates mean distance between specified set of points and edge of the assumed
             shape. If the mean distance is equal to or less than maximum allowed distance,
             then a shape is recognized. The maximum allowed distance is calculated as:
             <code lang="none">
             maxDistance = max( minAcceptableDistortion, relativeDistortionLimit * ( width + height ) / 2 )
             </code>
             , where <b>width</b> and <b>height</b> is the size of bounding rectangle for the
             specified points.
             </para>
             
             <para>See also <see cref="P:AForge.Math.Geometry.SimpleShapeChecker.AngleError"/> and <see cref="P:AForge.Math.Geometry.SimpleShapeChecker.LengthError"/> properties,
             which set acceptable errors for polygon sub type checking done by
             <see cref="M:AForge.Math.Geometry.SimpleShapeChecker.CheckPolygonSubType(System.Collections.Generic.List{AForge.IntPoint})"/> method.</para>
             
             <para><note>See the next article for details about the implemented algorithms:
             <a href="http://www.aforgenet.com/articles/shape_checker/">Detecting some simple shapes in images</a>.
             </note></para>
             
             <para>Sample usage:</para>
             <code>
             private List&lt;IntPoint&gt; idealCicle = new List&lt;IntPoint&gt;( );
             private List&lt;IntPoint&gt; distorredCircle = new List&lt;IntPoint&gt;( );
             System.Random rand = new System.Random( );
            
             // generate sample circles
             float radius = 100;
            
             for ( int i = 0; i &lt; 360; i += 10 )
             {
                 float angle = (float) ( (float) i / 180 * System.Math.PI );
             
                 // add point to ideal circle
                 idealCicle.Add( new IntPoint(
                     (int) ( radius * System.Math.Cos( angle ) ),
                     (int) ( radius * System.Math.Sin( angle ) ) ) );
             
                 // add a bit distortion for distorred cirlce
                 float distorredRadius = radius + rand.Next( 7 ) - 3;
             
                 distorredCircle.Add( new IntPoint(
                     (int) ( distorredRadius * System.Math.Cos( angle ) ),
                     (int) ( distorredRadius * System.Math.Sin( angle ) ) ) );
             }
             
             // check shape
             SimpleShapeChecker shapeChecker = new SimpleShapeChecker( );
             
             if ( shapeChecker.IsCircle( idealCicle ) )
             {
                 // ...
             }
             
             if ( shapeChecker.CheckShapeType( distorredCircle ) == ShapeType.Circle )
             {
                 // ...
             }
             </code>
             </remarks>
             
        </member>
        <member name="M:AForge.Math.Geometry.SimpleShapeChecker.CheckShapeType(System.Collections.Generic.List{AForge.IntPoint})">
            <summary>
            Check type of the shape formed by specified points.
            </summary>
            
            <param name="edgePoints">Shape's points to check.</param>
            
            <returns>Returns type of the detected shape.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.SimpleShapeChecker.IsCircle(System.Collections.Generic.List{AForge.IntPoint})">
            <summary>
            Check if the specified set of points form a circle shape.
            </summary>
            
            <param name="edgePoints">Shape's points to check.</param>
            
            <returns>Returns <see langword="true"/> if the specified set of points form a
            circle shape or <see langword="false"/> otherwise.</returns>
            
            <remarks><para><note>Circle shape must contain at least 8 points to be recognized.
            The method returns <see langword="false"/> always, of number of points in the specified
            shape is less than 8.</note></para></remarks>
            
        </member>
        <member name="M:AForge.Math.Geometry.SimpleShapeChecker.IsCircle(System.Collections.Generic.List{AForge.IntPoint},AForge.Point@,System.Single@)">
            <summary>
            Check if the specified set of points form a circle shape.
            </summary>
            
            <param name="edgePoints">Shape's points to check.</param>
            <param name="center">Receives circle's center on successful return.</param>
            <param name="radius">Receives circle's radius on successful return.</param>
            
            <returns>Returns <see langword="true"/> if the specified set of points form a
            circle shape or <see langword="false"/> otherwise.</returns>
            
            <remarks><para><note>Circle shape must contain at least 8 points to be recognized.
            The method returns <see langword="false"/> always, of number of points in the specified
            shape is less than 8.</note></para></remarks>
            
        </member>
        <member name="M:AForge.Math.Geometry.SimpleShapeChecker.IsQuadrilateral(System.Collections.Generic.List{AForge.IntPoint})">
            <summary>
            Check if the specified set of points form a quadrilateral shape.
            </summary>
            
            <param name="edgePoints">Shape's points to check.</param>
            
            <returns>Returns <see langword="true"/> if the specified set of points form a
            quadrilateral shape or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.SimpleShapeChecker.IsQuadrilateral(System.Collections.Generic.List{AForge.IntPoint},System.Collections.Generic.List{AForge.IntPoint}@)">
            <summary>
            Check if the specified set of points form a quadrilateral shape.
            </summary>
            
            <param name="edgePoints">Shape's points to check.</param>
            <param name="corners">List of quadrilateral corners on successful return.</param>
            
            <returns>Returns <see langword="true"/> if the specified set of points form a
            quadrilateral shape or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.SimpleShapeChecker.IsTriangle(System.Collections.Generic.List{AForge.IntPoint})">
            <summary>
            Check if the specified set of points form a triangle shape.
            </summary>
            
            <param name="edgePoints">Shape's points to check.</param>
            
            <returns>Returns <see langword="true"/> if the specified set of points form a
            triangle shape or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.SimpleShapeChecker.IsTriangle(System.Collections.Generic.List{AForge.IntPoint},System.Collections.Generic.List{AForge.IntPoint}@)">
            <summary>
            Check if the specified set of points form a triangle shape.
            </summary>
            
            <param name="edgePoints">Shape's points to check.</param>
            <param name="corners">List of triangle corners on successful return.</param>
            
            <returns>Returns <see langword="true"/> if the specified set of points form a
            triangle shape or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.SimpleShapeChecker.IsConvexPolygon(System.Collections.Generic.List{AForge.IntPoint},System.Collections.Generic.List{AForge.IntPoint}@)">
            <summary>
            Check if the specified set of points form a convex polygon shape.
            </summary>
            
            <param name="edgePoints">Shape's points to check.</param>
            <param name="corners">List of polygon corners on successful return.</param>
            
            <returns>Returns <see langword="true"/> if the specified set of points form a
            convex polygon shape or <see langword="false"/> otherwise.</returns>
            
            <remarks><para><note>The method is able to detect only triangles and quadrilaterals
            for now. Check number of detected corners to resolve type of the detected polygon.
            </note></para></remarks>
            
        </member>
        <member name="M:AForge.Math.Geometry.SimpleShapeChecker.CheckPolygonSubType(System.Collections.Generic.List{AForge.IntPoint})">
            <summary>
            Check sub type of a convex polygon.
            </summary>
            
            <param name="corners">Corners of the convex polygon to check.</param>
            
            <returns>Return detected sub type of the specified shape.</returns>
            
            <remarks><para>The method check corners of a convex polygon detecting
            its subtype. Polygon's corners are usually retrieved using <see cref="M:AForge.Math.Geometry.SimpleShapeChecker.IsConvexPolygon(System.Collections.Generic.List{AForge.IntPoint},System.Collections.Generic.List{AForge.IntPoint}@)"/>
            method, but can be any list of 3-4 points (only sub types of triangles and
            quadrilateral are checked).</para>
            
            <para>See <see cref="P:AForge.Math.Geometry.SimpleShapeChecker.AngleError"/> and <see cref="P:AForge.Math.Geometry.SimpleShapeChecker.LengthError"/> properties,
            which set acceptable errors for polygon sub type checking.</para>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Geometry.SimpleShapeChecker.CheckIfPointsFitShape(System.Collections.Generic.List{AForge.IntPoint},System.Collections.Generic.List{AForge.IntPoint})">
             <summary>
             Check if a shape specified by the set of points fits a convex polygon
             specified by the set of corners.
             </summary>
             
             <param name="edgePoints">Shape's points to check.</param>
             <param name="corners">Corners of convex polygon to check fitting into.</param>
            
             <returns>Returns <see langword="true"/> if the specified shape fits
             the specified convex polygon or <see langword="false"/> otherwise.</returns>
             
             <remarks><para>The method checks if the set of specified points form the same shape
             as the set of provided corners.</para></remarks>
             
        </member>
        <member name="P:AForge.Math.Geometry.SimpleShapeChecker.MinAcceptableDistortion">
            <summary>
            Minimum value of allowed shapes' distortion.
            </summary>
            
            <remarks><para>The property sets minimum value for allowed shapes'
            distortion (in pixels). See documentation to <see cref="T:AForge.Math.Geometry.SimpleShapeChecker"/>
            class for more details about this property.</para>
            
            <para>Default value is set to <b>0.5</b>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.Geometry.SimpleShapeChecker.RelativeDistortionLimit">
            <summary>
            Maximum value of allowed shapes' distortion, [0, 1].
            </summary>
            
            <remarks><para>The property sets maximum value for allowed shapes'
            distortion. The value is measured in [0, 1] range, which corresponds
            to [0%, 100%] range, which means that maximum allowed shapes'
            distortion is calculated relatively to shape's size. This results to
            higher allowed distortion level for bigger shapes and smaller allowed
            distortion for smaller shapers. See documentation to <see cref="T:AForge.Math.Geometry.SimpleShapeChecker"/>
            class for more details about this property.</para>
            
            <para>Default value is set to <b>0.03</b> (3%).</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.Geometry.SimpleShapeChecker.AngleError">
            <summary>
            Maximum allowed angle error in degrees, [0, 20].
            </summary>
            
            <remarks><para>The value sets maximum allowed difference between two angles to
            treat them as equal. It is used by <see cref="M:AForge.Math.Geometry.SimpleShapeChecker.CheckPolygonSubType(System.Collections.Generic.List{AForge.IntPoint})"/> method to
            check for parallel lines and angles of triangles and quadrilaterals.
            For example, if angle between two lines equals 5 degrees and this properties value
            is set to 7, then two compared lines are treated as parallel.</para>
            
            <para>Default value is set to <b>7</b>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.Geometry.SimpleShapeChecker.LengthError">
             <summary>
             Maximum allowed difference in sides' length (relative to shapes' size), [0, 1].
             </summary>
            
             <remarks><para>The values sets maximum allowed difference between two sides' length
             to treat them as equal. The error value is set relative to shapes size and measured
             in [0, 1] range, which corresponds to [0%, 100%] range. Absolute length error in pixels
             is calculated as:
             <code lang="none">
             LengthError * ( width + height ) / 2
             </code>
             , where <b>width</b> and <b>height</b> is the size of bounding rectangle for the
             specified shape.
             </para>
             
             <para>Default value is set to <b>0.1</b> (10%).</para>
             </remarks>
            
        </member>
        <member name="T:AForge.Math.Geometry.GrahamConvexHull">
            <summary>
            Graham scan algorithm for finding convex hull.
            </summary>
            
            <remarks><para>The class implements
            <a href="http://en.wikipedia.org/wiki/Graham_scan">Graham scan</a> algorithm for finding convex hull
            of a given set of points.</para>
            
            <para>Sample usage:</para>
            <code>
            // generate some random points
            Random rand = new Random( );
            List&lt;IntPoint&gt; points = new List&lt;IntPoint&gt;( );
            
            for ( int i = 0; i &lt; 10; i++ )
            {
                points.Add( new IntPoint(
                       rand.Next( 200 ) - 100,
                       rand.Next( 200 ) - 100 ) );
            }
            
            // find the convex hull
            IConvexHullAlgorithm hullFinder = new GrahamConvexHull( );
            List&lt;IntPoint&gt; hull = hullFinder.FindHull( points );
            </code>
            </remarks>
            
        </member>
        <member name="T:AForge.Math.Geometry.IConvexHullAlgorithm">
            <summary>
            Interface defining methods for algorithms, which search for convex hull of the specified points' set.
            </summary>
            
            <remarks><para>The interface defines a method, which should be implemented by different classes
            performing convex hull search for specified set of points.</para>
            
            <para><note>All algorithms, implementing this interface, should follow two rules for the found convex hull:
            <list type="bullet">
            <item>the first point in the returned list is the point with lowest X coordinate (and with lowest Y if
            there are several points with the same X value);</item>
            <item>points in the returned list are given in counter clockwise order
            (<a href="http://en.wikipedia.org/wiki/Cartesian_coordinate_system">Cartesian
            coordinate system</a>).</item>
            </list>
            </note></para>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Geometry.IConvexHullAlgorithm.FindHull(System.Collections.Generic.List{AForge.IntPoint})">
            <summary>
            Find convex hull for the given set of points.
            </summary>
            
            <param name="points">Set of points to search convex hull for.</param>
            
            <returns>Returns set of points, which form a convex hull for the given <paramref name="points"/>.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.GrahamConvexHull.FindHull(System.Collections.Generic.List{AForge.IntPoint})">
            <summary>
            Find convex hull for the given set of points.
            </summary>
            
            <param name="points">Set of points to search convex hull for.</param>
            
            <returns>Returns set of points, which form a convex hull for the given <paramref name="points"/>.
            The first point in the list is the point with lowest X coordinate (and with lowest Y if there are
            several points with the same X value). Points are provided in counter clockwise order
            (<a href="http://en.wikipedia.org/wiki/Cartesian_coordinate_system">Cartesian
            coordinate system</a>).</returns>
            
        </member>
        <member name="T:AForge.Math.Metrics.EuclideanDistance">
            <summary>
            Euclidean distance metric.
            </summary>
            
            <remarks><para>This class represents the 
            <a href="http://en.wikipedia.org/wiki/Euclidean_distance">Euclidean distance metric.</a></para>
            
            <para>Sample usage:</para>
            <code>
            // instantiate new distance class
            EuclideanDistance dist = new EuclideanDistance( );
            // create two vectors for inputs
            double[] p = new double[] { 2.5, 3.5, 3.0, 3.5, 2.5, 3.0 };
            double[] q = new double[] { 3.0, 3.5, 1.5, 5.0, 3.5, 3.0 };
            // get distance between the two vectors
            double distance = dist.GetDistance( p, q );
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Metrics.EuclideanDistance.GetDistance(System.Double[],System.Double[])">
            <summary>
            Returns distance between two N-dimensional double vectors.
            </summary>
            
            <param name="p">1st point vector.</param>
            <param name="q">2nd point vector.</param>
            
            <returns>Returns Euclidean distance between two supplied vectors.</returns>
            
            <exception cref="T:System.ArgumentException">Thrown if the two vectors are of different dimensions (if specified
            array have different length).</exception>
            
        </member>
        <member name="T:AForge.Math.Geometry.LineStraighteningOptimizer">
             <summary>
             Shape optimizer, which removes points within close range to shapes' body.
             </summary>
             
             <remarks><para>This shape optimizing algorithm checks all points of the shape and
             removes those of them, which are in a certain distance to a line connecting previous and
             the next points. In other words, it goes through all adjacent edges of a shape and checks
             what is the distance between the corner formed by these two edges and a possible edge, which
             could be used as substitution of these edges. If the distance is equal or smaller than
             the <see cref="P:AForge.Math.Geometry.LineStraighteningOptimizer.MaxDistanceToRemove">specified value</see>, then the point is removed,
             so the two edges are substituted by a single one. When optimization process is done,
             the new shape has reduced amount of points and none of the removed points are further away
             from the new shape than the specified limit.</para>
             
             <para>The shape optimizer does not optimize shapes to less than 3 points, so optimized
             shape always will have at least 3 points.</para>
            
             <para>
             For example, the below circle shape comprised of 65 points, can be optimized to 8 points
             by setting <see cref="P:AForge.Math.Geometry.LineStraighteningOptimizer.MaxDistanceToRemove"/> to 10.<br/>
             <img src="img/math/line_straightening_optimizer.png" width="268" height="238"/>
             </para>
             </remarks>
             
        </member>
        <member name="M:AForge.Math.Geometry.LineStraighteningOptimizer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Geometry.LineStraighteningOptimizer"/> class.
            </summary>
            
        </member>
        <member name="M:AForge.Math.Geometry.LineStraighteningOptimizer.#ctor(System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Geometry.LineStraighteningOptimizer"/> class.
            </summary>
            
            <param name="maxDistanceToRemove">Maximum allowed distance between removed points
            and optimized shape (see <see cref="P:AForge.Math.Geometry.LineStraighteningOptimizer.MaxDistanceToRemove"/>).</param>
            
        </member>
        <member name="M:AForge.Math.Geometry.LineStraighteningOptimizer.OptimizeShape(System.Collections.Generic.List{AForge.IntPoint})">
            <summary>
            Optimize specified shape.
            </summary>
            
            <param name="shape">Shape to be optimized.</param>
            
            <returns>Returns final optimized shape, which may have reduced amount of points.</returns>
            
        </member>
        <member name="P:AForge.Math.Geometry.LineStraighteningOptimizer.MaxDistanceToRemove">
            <summary>
            Maximum allowed distance between removed points and optimized shape, [0, ∞).
            </summary>
            
            <remarks><para>The property sets maximum allowed distance between points removed from original
            shape and optimized shape - none of the removed points are further away
            from the new shape than the specified limit.
            </para>
            
            <para>Default value is set to <b>5</b>.</para></remarks>
            
        </member>
        <member name="T:AForge.Math.Geometry.FlatAnglesOptimizer">
             <summary>
             Shape optimizer, which removes obtuse angles (close to flat) from a shape.
             </summary>
             
             <remarks><para>This shape optimizing algorithm checks all adjacent edges of a shape
             and substitutes any 2 edges with a single edge if angle between them is greater than
             <see cref="P:AForge.Math.Geometry.FlatAnglesOptimizer.MaxAngleToKeep"/>. The algorithm makes sure there are not obtuse angles in
             a shape, which are very close to flat line.</para>
             
             <para>The shape optimizer does not optimize shapes to less than 3 points, so optimized
             shape always will have at least 3 points.</para>
            
             <para>
             For example, the below circle shape comprised of 65 points, can be optimized to 10 points
             by setting <see cref="P:AForge.Math.Geometry.FlatAnglesOptimizer.MaxAngleToKeep"/> to 160.<br/>
             <img src="img/math/flat_angles_optimizer.png" width="268" height="238"/>
             </para>
             </remarks>
             
        </member>
        <member name="M:AForge.Math.Geometry.FlatAnglesOptimizer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Geometry.FlatAnglesOptimizer"/> class.
            </summary>
            
        </member>
        <member name="M:AForge.Math.Geometry.FlatAnglesOptimizer.#ctor(System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Geometry.FlatAnglesOptimizer"/> class.
            </summary>
            
            <param name="maxAngleToKeep">Maximum acceptable angle between two edges of a shape (see <see cref="P:AForge.Math.Geometry.FlatAnglesOptimizer.MaxAngleToKeep"/>).</param>
            
        </member>
        <member name="M:AForge.Math.Geometry.FlatAnglesOptimizer.OptimizeShape(System.Collections.Generic.List{AForge.IntPoint})">
            <summary>
            Optimize specified shape.
            </summary>
            
            <param name="shape">Shape to be optimized.</param>
            
            <returns>Returns final optimized shape, which may have reduced amount of points.</returns>
            
        </member>
        <member name="P:AForge.Math.Geometry.FlatAnglesOptimizer.MaxAngleToKeep">
            <summary>
            Maximum angle between adjacent edges to keep in a shape, [140, 180].
            </summary>
            
            <remarks><para>The property sets maximum angle between adjacent edges, which is kept
            during optimization. All edges, which have a greater angle between them, are substituted
            by a single edge.</para>
            
            <para>Default value is set to <b>160</b>.</para></remarks>
            
        </member>
        <member name="T:AForge.Math.Random.UniformGenerator">
            <summary>
            Uniform random numbers generator.
            </summary>
            
            <remarks><para>The random numbers generator generates uniformly
            distributed numbers in the <see cref="P:AForge.Math.Random.UniformGenerator.Range">specified range</see> - values
            are greater or equal to minimum range's value and less than maximum range's
            value.</para>
            
            <para>The generator uses <see cref="T:AForge.Math.Random.UniformOneGenerator"/> generator
            to generate random numbers.</para>
            
            <para>Sample usage:</para>
            <code>
            // create instance of random generator
            IRandomNumberGenerator generator = new UniformGenerator( new Range( 50, 100 ) );
            // generate random number
            float randomNumber = generator.Next( );
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Random.UniformGenerator.#ctor(AForge.Range)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Random.UniformGenerator"/> class.
            </summary>
            
            <param name="range">Random numbers range.</param>
            
            <remarks>Initializes random numbers generator with zero seed.</remarks>
            
        </member>
        <member name="M:AForge.Math.Random.UniformGenerator.#ctor(AForge.Range,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Random.UniformGenerator"/> class.
            </summary>
            
            <param name="range">Random numbers range.</param>
            <param name="seed">Seed value to initialize random numbers generator.</param>
            
        </member>
        <member name="M:AForge.Math.Random.UniformGenerator.Next">
            <summary>
            Generate next random number.
            </summary>
            
            <returns>Returns next random number.</returns>
            
        </member>
        <member name="M:AForge.Math.Random.UniformGenerator.SetSeed(System.Int32)">
            <summary>
            Set seed of the random numbers generator.
            </summary>
            
            <param name="seed">Seed value.</param>
            
            <remarks>Resets random numbers generator initializing it with
            specified seed value.</remarks>
            
        </member>
        <member name="P:AForge.Math.Random.UniformGenerator.Mean">
             <summary>
             Mean value of the generator.
             </summary>
            
        </member>
        <member name="P:AForge.Math.Random.UniformGenerator.Variance">
             <summary>
             Variance value of the generator.
             </summary>
            
        </member>
        <member name="P:AForge.Math.Random.UniformGenerator.Range">
            <summary>
            Random numbers range.
            </summary>
            
            <remarks><para>Range of random numbers to generate. Generated numbers are
            greater or equal to minimum range's value and less than maximum range's
            value.</para>
            </remarks>
            
        </member>
        <member name="T:AForge.Math.Metrics.ManhattanDistance">
            <summary>
            Manhattan distance metric. 
            </summary>
            
            <remarks><para>This class represents the 
            <a href="http://en.wikipedia.org/wiki/Manhattan_distance">Manhattan distance metric</a>
            (aka City Block and Taxi Cab distance).</para>
            
            <para>Sample usage:</para>
            <code>
            // instantiate new distance class
            ManhattanDistance dist = new ManhattanDistance( ); 
            // create two vectors for inputs
            double[] p = new double[] { 2.5, 3.5, 3.0, 3.5, 2.5, 3.0 };
            double[] q = new double[] { 3.0, 3.5, 1.5, 5.0, 3.5, 3.0 };
            // get distance between the two vectors
            double distance = dist.GetDistance( p, q );
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Metrics.ManhattanDistance.GetDistance(System.Double[],System.Double[])">
            <summary>
            Returns distance between two N-dimensional double vectors.
            </summary>
            
            <param name="p">1st point vector.</param>
            <param name="q">2nd point vector.</param>
            
            <returns>Returns Manhattan distance between two supplied vectors.</returns>
            
            <exception cref="T:System.ArgumentException">Thrown if the two vectors are of different dimensions (if specified
            array have different length).</exception>
            
        </member>
        <member name="T:AForge.Math.Vector4">
            <summary>
            4D Vector structure with X, Y, Z and W coordinates.
            </summary>
            
            <remarks><para>The structure incapsulates X, Y, Z and W coordinates of a 4D vector and
            provides some operations with it.</para></remarks>
            
        </member>
        <member name="F:AForge.Math.Vector4.X">
            <summary>
            X coordinate of the vector.
            </summary>
        </member>
        <member name="F:AForge.Math.Vector4.Y">
            <summary>
            Y coordinate of the vector.
            </summary>
        </member>
        <member name="F:AForge.Math.Vector4.Z">
            <summary>
            Z coordinate of the vector.
            </summary>
        </member>
        <member name="F:AForge.Math.Vector4.W">
            <summary>
            W coordinate of the vector.
            </summary>
        </member>
        <member name="M:AForge.Math.Vector4.#ctor(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Vector4"/> structure.
            </summary>
            
            <param name="x">X coordinate of the vector.</param>
            <param name="y">Y coordinate of the vector.</param>
            <param name="z">Z coordinate of the vector.</param>
            <param name="w">W coordinate of the vector.</param>
            
        </member>
        <member name="M:AForge.Math.Vector4.#ctor(System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Vector4"/> structure.
            </summary>
            
            <param name="value">Value, which is set to all 4 coordinates of the vector.</param>
            
        </member>
        <member name="M:AForge.Math.Vector4.ToString">
            <summary>
            Returns a string representation of this object.
            </summary>
            
            <returns>A string representation of this object.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.ToArray">
            <summary>
            Returns array representation of the vector.
            </summary>
            
            <returns>Array with 4 values containing X/Y/Z/W coordinates.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.op_Addition(AForge.Math.Vector4,AForge.Math.Vector4)">
             <summary>
             Adds corresponding coordinates of two vectors.
             </summary>
             
             <param name="vector1">The vector to add to.</param>
             <param name="vector2">The vector to add to the first vector.</param>
             
             <returns>Returns a vector which coordinates are equal to sum of corresponding
             coordinates of the two specified vectors.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.Add(AForge.Math.Vector4,AForge.Math.Vector4)">
             <summary>
             Adds corresponding coordinates of two vectors.
             </summary>
             
             <param name="vector1">The vector to add to.</param>
             <param name="vector2">The vector to add to the first vector.</param>
             
             <returns>Returns a vector which coordinates are equal to sum of corresponding
             coordinates of the two specified vectors.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.op_Addition(AForge.Math.Vector4,System.Single)">
            <summary>
            Adds a value to all coordinates of the specified vector.
            </summary>
            
            <param name="vector">Vector to add the specified value to.</param>
            <param name="value">Value to add to all coordinates of the vector.</param>
            
            <returns>Returns new vector with all coordinates increased by the specified value.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.Add(AForge.Math.Vector4,System.Single)">
            <summary>
            Adds a value to all coordinates of the specified vector.
            </summary>
            
            <param name="vector">Vector to add the specified value to.</param>
            <param name="value">Value to add to all coordinates of the vector.</param>
            
            <returns>Returns new vector with all coordinates increased by the specified value.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.op_Subtraction(AForge.Math.Vector4,AForge.Math.Vector4)">
             <summary>
             Subtracts corresponding coordinates of two vectors.
             </summary>
             
             <param name="vector1">The vector to subtract from.</param>
             <param name="vector2">The vector to subtract from the first vector.</param>
             
             <returns>Returns a vector which coordinates are equal to difference of corresponding
             coordinates of the two specified vectors.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.Subtract(AForge.Math.Vector4,AForge.Math.Vector4)">
             <summary>
             Subtracts corresponding coordinates of two vectors.
             </summary>
             
             <param name="vector1">The vector to subtract from.</param>
             <param name="vector2">The vector to subtract from the first vector.</param>
             
             <returns>Returns a vector which coordinates are equal to difference of corresponding
             coordinates of the two specified vectors.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.op_Subtraction(AForge.Math.Vector4,System.Single)">
            <summary>
            Subtracts a value from all coordinates of the specified vector.
            </summary>
            
            <param name="vector">Vector to subtract the specified value from.</param>
            <param name="value">Value to subtract from all coordinates of the vector.</param>
            
            <returns>Returns new vector with all coordinates decreased by the specified value.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.Subtract(AForge.Math.Vector4,System.Single)">
            <summary>
            Subtracts a value from all coordinates of the specified vector.
            </summary>
            
            <param name="vector">Vector to subtract the specified value from.</param>
            <param name="value">Value to subtract from all coordinates of the vector.</param>
            
            <returns>Returns new vector with all coordinates decreased by the specified value.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.op_Multiply(AForge.Math.Vector4,AForge.Math.Vector4)">
             <summary>
             Multiplies corresponding coordinates of two vectors.
             </summary>
             
             <param name="vector1">The first vector to multiply.</param>
             <param name="vector2">The second vector to multiply.</param>
             
             <returns>Returns a vector which coordinates are equal to multiplication of corresponding
             coordinates of the two specified vectors.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.Multiply(AForge.Math.Vector4,AForge.Math.Vector4)">
             <summary>
             Multiplies corresponding coordinates of two vectors.
             </summary>
             
             <param name="vector1">The first vector to multiply.</param>
             <param name="vector2">The second vector to multiply.</param>
             
             <returns>Returns a vector which coordinates are equal to multiplication of corresponding
             coordinates of the two specified vectors.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.op_Multiply(AForge.Math.Vector4,System.Single)">
             <summary>
             Multiplies coordinates of the specified vector by the specified factor.
             </summary>
             
             <param name="vector">Vector to multiply coordinates of.</param>
             <param name="factor">Factor to multiple coordinates of the specified vector by.</param>
             
             <returns>Returns new vector with all coordinates multiplied by the specified factor.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.Multiply(AForge.Math.Vector4,System.Single)">
             <summary>
             Multiplies coordinates of the specified vector by the specified factor.
             </summary>
             
             <param name="vector">Vector to multiply coordinates of.</param>
             <param name="factor">Factor to multiple coordinates of the specified vector by.</param>
             
             <returns>Returns new vector with all coordinates multiplied by the specified factor.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.op_Division(AForge.Math.Vector4,AForge.Math.Vector4)">
             <summary>
             Divides corresponding coordinates of two vectors.
             </summary>
             
             <param name="vector1">The first vector to divide.</param>
             <param name="vector2">The second vector to devide.</param>
             
             <returns>Returns a vector which coordinates are equal to coordinates of the first vector divided by
             corresponding coordinates of the second vector.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.Divide(AForge.Math.Vector4,AForge.Math.Vector4)">
             <summary>
             Divides corresponding coordinates of two vectors.
             </summary>
             
             <param name="vector1">The first vector to divide.</param>
             <param name="vector2">The second vector to devide.</param>
             
             <returns>Returns a vector which coordinates are equal to coordinates of the first vector divided by
             corresponding coordinates of the second vector.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.op_Division(AForge.Math.Vector4,System.Single)">
             <summary>
             Divides coordinates of the specified vector by the specified factor.
             </summary>
             
             <param name="vector">Vector to divide coordinates of.</param>
             <param name="factor">Factor to divide coordinates of the specified vector by.</param>
             
             <returns>Returns new vector with all coordinates divided by the specified factor.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.Divide(AForge.Math.Vector4,System.Single)">
             <summary>
             Divides coordinates of the specified vector by the specified factor.
             </summary>
             
             <param name="vector">Vector to divide coordinates of.</param>
             <param name="factor">Factor to divide coordinates of the specified vector by.</param>
             
             <returns>Returns new vector with all coordinates divided by the specified factor.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.op_Equality(AForge.Math.Vector4,AForge.Math.Vector4)">
            <summary>
            Tests whether two specified vectors are equal.
            </summary>
            
            <param name="vector1">The left-hand vector.</param>
            <param name="vector2">The right-hand vector.</param>
            
            <returns>Returns <see langword="true"/> if the two vectors are equal or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.op_Inequality(AForge.Math.Vector4,AForge.Math.Vector4)">
            <summary>
            Tests whether two specified vectors are not equal.
            </summary>
            
            <param name="vector1">The left-hand vector.</param>
            <param name="vector2">The right-hand vector.</param>
            
            <returns>Returns <see langword="true"/> if the two vectors are not equal or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.Equals(AForge.Math.Vector4)">
            <summary>
            Tests whether the vector equals to the specified one.
            </summary>
            
            <param name="vector">The vector to test equality with.</param>
            
            <returns>Returns <see langword="true"/> if the two vectors are equal or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.Equals(System.Object)">
            <summary>
            Tests whether the vector equals to the specified object.
            </summary>
            
            <param name="obj">The object to test equality with.</param>
            
            <returns>Returns <see langword="true"/> if the vector equals to the specified object or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.GetHashCode">
            <summary>
            Returns the hashcode for this instance.
            </summary>
            
            <returns>A 32-bit signed integer hash code.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.Normalize">
             <summary>
             Normalizes the vector by dividing it’s all coordinates with the vector's norm.
             </summary>
             
             <returns>Returns the value of vectors’ norm before normalization.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.Inverse">
             <summary>
             Inverse the vector.
             </summary>
             
             <returns>Returns a vector with all coordinates equal to 1.0 divided by the value of corresponding coordinate
             in this vector (or equal to 0.0 if this vector has corresponding coordinate also set to 0.0).</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.Abs">
            <summary>
            Calculate absolute values of the vector.
            </summary>
            
            <returns>Returns a vector with all coordinates equal to absolute values of this vector's coordinates.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.Dot(AForge.Math.Vector4,AForge.Math.Vector4)">
            <summary>
            Calculates dot product of two vectors.
            </summary>
            
            <param name="vector1">First vector to use for dot product calculation.</param>
            <param name="vector2">Second vector to use for dot product calculation.</param>
            
            <returns>Returns dot product of the two specified vectors.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector4.ToVector3">
            <summary>
            Converts the vector to a 3D vector.
            </summary>
            
            <returns>Returns 3D vector which has X/Y/Z coordinates equal to X/Y/Z coordinates
            of this vector divided by <see cref="F:AForge.Math.Vector4.W"/>.</returns>
            
        </member>
        <member name="P:AForge.Math.Vector4.Max">
             <summary>
             Returns maximum value of the vector.
             </summary>
            
             <remarks><para>Returns maximum value of all 4 vector's coordinates.</para></remarks>
            
        </member>
        <member name="P:AForge.Math.Vector4.Min">
             <summary>
             Returns minimum value of the vector.
             </summary>
            
             <remarks><para>Returns minimum value of all 4 vector's coordinates.</para></remarks>
            
        </member>
        <member name="P:AForge.Math.Vector4.MaxIndex">
             <summary>
             Returns index of the coordinate with maximum value.
             </summary>
            
             <remarks><para>Returns index of the coordinate, which has the maximum value - 0 for X,
             1 for Y, 2 for Z or 3 for W.</para>
             
             <para><note>If there are multiple coordinates which have the same maximum value, the
             property returns smallest index.</note></para>
             </remarks>
            
        </member>
        <member name="P:AForge.Math.Vector4.MinIndex">
             <summary>
             Returns index of the coordinate with minimum value.
             </summary>
            
             <remarks><para>Returns index of the coordinate, which has the minimum value - 0 for X,
             1 for Y, 2 for Z or 3 for W.</para>
             
             <para><note>If there are multiple coordinates which have the same minimum value, the
             property returns smallest index.</note></para>
             </remarks>
            
        </member>
        <member name="P:AForge.Math.Vector4.Norm">
            <summary>
            Returns vector's norm.
            </summary>
            
            <remarks><para>Returns Euclidean norm of the vector, which is a
            square root of the sum: X<sup>2</sup>+Y<sup>2</sup>+Z<sup>2</sup>+W<sup>2</sup>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.Vector4.Square">
            <summary>
            Returns square of the vector's norm.
            </summary>
            
            <remarks><para>Return X<sup>2</sup>+Y<sup>2</sup>+Z<sup>2</sup>+W<sup>2</sup>, which is
            a square of <see cref="P:AForge.Math.Vector4.Norm">vector's norm</see> or a <see cref="M:AForge.Math.Vector4.Dot(AForge.Math.Vector4,AForge.Math.Vector4)">dot product</see> of this vector
            with itself.</para></remarks>
            
        </member>
        <member name="T:AForge.Math.Random.UniformOneGenerator">
            <summary>
            Uniform random numbers generator in the range of [0, 1).
            </summary>
            
            <remarks><para>The random number generator generates uniformly
            distributed numbers in the range of [0, 1) - greater or equal to 0.0
            and less than 1.0.</para>
            
            <para><note>At this point the generator is based on the
            internal .NET generator, but may be rewritten to
            use faster generation algorithm.</note></para>
            
            <para>Sample usage:</para>
            <code>
            // create instance of random generator
            IRandomNumberGenerator generator = new UniformOneGenerator( );
            // generate random number
            float randomNumber = generator.Next( );
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Random.UniformOneGenerator.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Random.UniformOneGenerator"/> class.
            </summary>
            
            <remarks>Initializes random numbers generator with zero seed.</remarks>
            
        </member>
        <member name="M:AForge.Math.Random.UniformOneGenerator.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Random.UniformOneGenerator"/> class.
            </summary>
            
            <param name="seed">Seed value to initialize random numbers generator.</param>
            
        </member>
        <member name="M:AForge.Math.Random.UniformOneGenerator.Next">
            <summary>
            Generate next random number.
            </summary>
            
            <returns>Returns next random number.</returns>
            
        </member>
        <member name="M:AForge.Math.Random.UniformOneGenerator.SetSeed(System.Int32)">
            <summary>
            Set seed of the random numbers generator.
            </summary>
            
            <param name="seed">Seed value.</param>
            
            <remarks>Resets random numbers generator initializing it with
            specified seed value.</remarks>
            
        </member>
        <member name="P:AForge.Math.Random.UniformOneGenerator.Mean">
             <summary>
             Mean value of the generator.
             </summary>
            
        </member>
        <member name="P:AForge.Math.Random.UniformOneGenerator.Variance">
             <summary>
             Variance value of the generator.
             </summary>
            
        </member>
        <member name="T:AForge.Math.Random.ExponentialGenerator">
            <summary>
            Exponential random numbers generator.
            </summary>
            
            <remarks><para>The random number generator generates exponential
            random numbers with specified rate value (lambda).</para>
            
            <para>The generator uses <see cref="T:AForge.Math.Random.UniformOneGenerator"/> generator as a base
            to generate random numbers.</para>
            
            <para>Sample usage:</para>
            <code>
            // create instance of random generator
            IRandomNumberGenerator generator = new ExponentialGenerator( 5 );
            // generate random number
            float randomNumber = generator.Next( );
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Random.ExponentialGenerator.#ctor(System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Random.ExponentialGenerator"/> class.
            </summary>
            
            <param name="rate">Rate value.</param>
            
            <exception cref="T:System.ArgumentException">Rate value should be greater than zero.</exception>
            
        </member>
        <member name="M:AForge.Math.Random.ExponentialGenerator.#ctor(System.Single,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Random.ExponentialGenerator"/> class.
            </summary>
            
            <param name="rate">Rate value (inverse mean).</param>
            <param name="seed">Seed value to initialize random numbers generator.</param>
            
            <exception cref="T:System.ArgumentException">Rate value should be greater than zero.</exception>
            
        </member>
        <member name="M:AForge.Math.Random.ExponentialGenerator.Next">
            <summary>
            Generate next random number
            </summary>
            
            <returns>Returns next random number.</returns>
            
        </member>
        <member name="M:AForge.Math.Random.ExponentialGenerator.SetSeed(System.Int32)">
            <summary>
            Set seed of the random numbers generator.
            </summary>
            
            <param name="seed">Seed value.</param>
            
            <remarks>Resets random numbers generator initializing it with
            specified seed value.</remarks>
            
        </member>
        <member name="P:AForge.Math.Random.ExponentialGenerator.Rate">
            <summary>
            Rate value (inverse mean).
            </summary>
            
            <remarks>The rate value should be positive and non zero.</remarks>
            
        </member>
        <member name="P:AForge.Math.Random.ExponentialGenerator.Mean">
            <summary>
            Mean value of the generator.
            </summary>
            
        </member>
        <member name="P:AForge.Math.Random.ExponentialGenerator.Variance">
             <summary>
             Variance value of the generator.
             </summary>
            
        </member>
        <member name="T:AForge.Math.Matrix4x4">
            <summary>
            A structure representing 4x4 matrix.
            </summary>
            
            <remarks><para>The structure incapsulates elements of a 4x4 matrix and
            provides some operations with it.</para></remarks>
            
        </member>
        <member name="F:AForge.Math.Matrix4x4.V00">
            <summary>
            Row 0 column 0 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix4x4.V01">
            <summary>
            Row 0 column 1 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix4x4.V02">
            <summary>
            Row 0 column 2 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix4x4.V03">
            <summary>
            Row 0 column 3 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix4x4.V10">
            <summary>
            Row 1 column 0 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix4x4.V11">
            <summary>
            Row 1 column 1 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix4x4.V12">
            <summary>
            Row 1 column 2 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix4x4.V13">
            <summary>
            Row 1 column 3 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix4x4.V20">
            <summary>
            Row 2 column 0 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix4x4.V21">
            <summary>
            Row 2 column 1 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix4x4.V22">
            <summary>
            Row 2 column 2 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix4x4.V23">
            <summary>
            Row 2 column 3 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix4x4.V30">
            <summary>
            Row 3 column 0 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix4x4.V31">
            <summary>
            Row 3 column 1 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix4x4.V32">
            <summary>
            Row 3 column 2 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix4x4.V33">
            <summary>
            Row 3 column 3 element of the matrix.
            </summary>
        </member>
        <member name="M:AForge.Math.Matrix4x4.ToArray">
            <summary>
            Returns array representation of the matrix.
            </summary>
            
            <returns>Returns array which contains all elements of the matrix in the row-major order.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.CreateRotationY(System.Single)">
            <summary>
            Creates rotation matrix around Y axis.
            </summary>
            
            <param name="radians">Rotation angle around Y axis in radians.</param>
            
            <returns>Returns rotation matrix to rotate an object around Y axis.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.CreateRotationX(System.Single)">
            <summary>
            Creates rotation matrix around X axis.
            </summary>
            
            <param name="radians">Rotation angle around X axis in radians.</param>
            
            <returns>Returns rotation matrix to rotate an object around X axis.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.CreateRotationZ(System.Single)">
            <summary>
            Creates rotation matrix around Z axis.
            </summary>
            
            <param name="radians">Rotation angle around Z axis in radians.</param>
            
            <returns>Returns rotation matrix to rotate an object around Z axis.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.CreateFromYawPitchRoll(System.Single,System.Single,System.Single)">
            <summary>
            Creates rotation matrix to rotate an object around X, Y and Z axes.
            </summary>
            
            <param name="yaw">Rotation angle around Y axis in radians.</param>
            <param name="pitch">Rotation angle around X axis in radians.</param>
            <param name="roll">Rotation angle around Z axis in radians.</param>
            
            <returns>Returns rotation matrix to rotate an object around all 3 axes.</returns>
            
            <remarks>
            <para><note>The routine assumes roll-pitch-yaw rotation order, when creating rotation
            matrix, i.e. an object is first rotated around Z axis, then around X axis and finally around
            Y axis.</note></para>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.ExtractYawPitchRoll(System.Single@,System.Single@,System.Single@)">
            <summary>
            Extract rotation angles from the rotation matrix.
            </summary>
            
            <param name="yaw">Extracted rotation angle around Y axis in radians.</param>
            <param name="pitch">Extracted rotation angle around X axis in radians.</param>
            <param name="roll">Extracted rotation angle around Z axis in radians.</param>
            
            <remarks><para><note>The routine assumes roll-pitch-yaw rotation order when extracting rotation angle.
            Using extracted angles with the <see cref="M:AForge.Math.Matrix4x4.CreateFromYawPitchRoll(System.Single,System.Single,System.Single)"/> should provide same rotation matrix.
            </note></para>
            
            <para><note>The method assumes the provided matrix represent valid rotation matrix.</note></para>
            
            <para>Sample usage:</para>
            <code>
            // assume we have a rotation matrix created like this
            float yaw   = 10.0f / 180 * Math.PI;
            float pitch = 30.0f / 180 * Math.PI;
            float roll  = 45.0f / 180 * Math.PI;
            
            Matrix4x4 rotationMatrix = Matrix3x3.CreateFromYawPitchRoll( yaw, pitch, roll );
            // ...
            
            // now somewhere in the code you may want to get rotation
            // angles back from a matrix assuming same rotation order
            float extractedYaw;
            float extractedPitch;
            float extractedRoll;
            
            rotation.ExtractYawPitchRoll( out extractedYaw, out extractedPitch, out extractedRoll );
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.CreateFromRotation(AForge.Math.Matrix3x3)">
            <summary>
            Creates 4x4 tranformation matrix from 3x3 rotation matrix.
            </summary>
            
            <param name="rotationMatrix">Source 3x3 rotation matrix.</param>
            
            <returns>Returns 4x4 rotation matrix.</returns>
            
            <remarks><para>The source 3x3 rotation matrix is copied into the top left corner of the result 4x4 matrix,
            i.e. it represents 0th, 1st and 2nd row/column. The <see cref="F:AForge.Math.Matrix4x4.V33"/> element is set to 1 and the rest
            elements of 3rd row and 3rd column are set to zeros.</para></remarks>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.CreateTranslation(AForge.Math.Vector3)">
            <summary>
            Creates translation matrix for the specified movement amount.
            </summary>
            
            <param name="position">Vector which set direction and amount of movement.</param>
            
            <returns>Returns translation matrix.</returns>
            
            <remarks><para>The specified vector is copied to the 3rd column of the result matrix.
            All diagonal elements are set to 1. The rest of matrix is initialized with zeros.</para></remarks>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.CreateLookAt(AForge.Math.Vector3,AForge.Math.Vector3)">
            <summary>
            Creates a view matrix for the specified camera position and target point.
            </summary>
            
            <param name="cameraPosition">Position of camera.</param>
            <param name="cameraTarget">Target point towards which camera is pointing.</param>
            
            <returns>Returns a view matrix.</returns>
            
            <remarks><para>Camera's "up" vector is supposed to be (0, 1, 0).</para></remarks>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.CreatePerspective(System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Creates a perspective projection matrix.
            </summary>
            
            <param name="width">Width of the view volume at the near view plane.</param>
            <param name="height">Height of the view volume at the near view plane.</param>
            <param name="nearPlaneDistance">Distance to the near view plane.</param>
            <param name="farPlaneDistance">Distance to the far view plane.</param>
            
            <returns>Return a perspective projection matrix.</returns>
            
            <exception cref="T:System.ArgumentOutOfRangeException">Both near and far view planes' distances must be greater than zero.</exception>
            <exception cref="T:System.ArgumentException">Near plane must be closer than the far plane.</exception>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.CreateFromRows(AForge.Math.Vector4,AForge.Math.Vector4,AForge.Math.Vector4,AForge.Math.Vector4)">
            <summary>
            Creates a matrix from 4 rows specified as vectors.
            </summary>
            
            <param name="row0">First row of the matrix to create.</param>
            <param name="row1">Second row of the matrix to create.</param>
            <param name="row2">Third row of the matrix to create.</param>
            <param name="row3">Fourth row of the matrix to create.</param>
            
            <returns>Returns a matrix from specified rows.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.CreateFromColumns(AForge.Math.Vector4,AForge.Math.Vector4,AForge.Math.Vector4,AForge.Math.Vector4)">
            <summary>
            Creates a matrix from 4 columns specified as vectors.
            </summary>
            
            <param name="column0">First column of the matrix to create.</param>
            <param name="column1">Second column of the matrix to create.</param>
            <param name="column2">Third column of the matrix to create.</param>
            <param name="column3">Fourth column of the matrix to create.</param>
            
            <returns>Returns a matrix from specified columns.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.CreateDiagonal(AForge.Math.Vector4)">
            <summary>
            Creates a diagonal matrix using the specified vector as diagonal elements.
            </summary>
            
            <param name="vector">Vector to use for diagonal elements of the matrix.</param>
            
            <returns>Returns a diagonal matrix.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.GetRow(System.Int32)">
            <summary>
            Get row of the matrix.
            </summary>
            
            <param name="index">Row index to get, [0, 3].</param>
            
            <returns>Returns specified row of the matrix as a vector.</returns>
            
            <exception cref="T:System.ArgumentException">Invalid row index was specified.</exception>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.GetColumn(System.Int32)">
            <summary>
            Get column of the matrix.
            </summary>
            
            <param name="index">Column index to get, [0, 3].</param>
            
            <returns>Returns specified column of the matrix as a vector.</returns>
            
            <exception cref="T:System.ArgumentException">Invalid column index was specified.</exception>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.op_Multiply(AForge.Math.Matrix4x4,AForge.Math.Matrix4x4)">
            <summary>
            Multiplies two specified matrices.
            </summary>
            
            <param name="matrix1">Matrix to multiply.</param>
            <param name="matrix2">Matrix to multiply by.</param>
            
            <returns>Return new matrix, which the result of multiplication of the two specified matrices.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.Multiply(AForge.Math.Matrix4x4,AForge.Math.Matrix4x4)">
            <summary>
            Multiplies two specified matrices.
            </summary>
            
            <param name="matrix1">Matrix to multiply.</param>
            <param name="matrix2">Matrix to multiply by.</param>
            
            <returns>Return new matrix, which the result of multiplication of the two specified matrices.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.op_Addition(AForge.Math.Matrix4x4,AForge.Math.Matrix4x4)">
             <summary>
             Adds corresponding components of two matrices.
             </summary>
             
             <param name="matrix1">The matrix to add to.</param>
             <param name="matrix2">The matrix to add to the first matrix.</param>
             
             <returns>Returns a matrix which components are equal to sum of corresponding
             components of the two specified matrices.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.Add(AForge.Math.Matrix4x4,AForge.Math.Matrix4x4)">
             <summary>
             Adds corresponding components of two matrices.
             </summary>
             
             <param name="matrix1">The matrix to add to.</param>
             <param name="matrix2">The matrix to add to the first matrix.</param>
             
             <returns>Returns a matrix which components are equal to sum of corresponding
             components of the two specified matrices.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.op_Subtraction(AForge.Math.Matrix4x4,AForge.Math.Matrix4x4)">
             <summary>
             Subtracts corresponding components of two matrices.
             </summary>
             
             <param name="matrix1">The matrix to subtract from.</param>
             <param name="matrix2">The matrix to subtract from the first matrix.</param>
             
             <returns>Returns a matrix which components are equal to difference of corresponding
             components of the two specified matrices.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.Subtract(AForge.Math.Matrix4x4,AForge.Math.Matrix4x4)">
             <summary>
             Subtracts corresponding components of two matrices.
             </summary>
             
             <param name="matrix1">The matrix to subtract from.</param>
             <param name="matrix2">The matrix to subtract from the first matrix.</param>
             
             <returns>Returns a matrix which components are equal to difference of corresponding
             components of the two specified matrices.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.op_Multiply(AForge.Math.Matrix4x4,AForge.Math.Vector4)">
             <summary>
             Multiplies specified matrix by the specified vector.
             </summary>
             
             <param name="matrix">Matrix to multiply by vector.</param>
             <param name="vector">Vector to multiply matrix by.</param>
             
             <returns>Returns new vector which is the result of multiplication of the specified matrix
             by the specified vector.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.Multiply(AForge.Math.Matrix4x4,AForge.Math.Vector4)">
             <summary>
             Multiplies specified matrix by the specified vector.
             </summary>
             
             <param name="matrix">Matrix to multiply by vector.</param>
             <param name="vector">Vector to multiply matrix by.</param>
             
             <returns>Returns new vector which is the result of multiplication of the specified matrix
             by the specified vector.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.op_Equality(AForge.Math.Matrix4x4,AForge.Math.Matrix4x4)">
            <summary>
            Tests whether two specified matrices are equal.
            </summary>
            
            <param name="matrix1">The left-hand matrix.</param>
            <param name="matrix2">The right-hand matrix.</param>
            
            <returns>Returns <see langword="true"/> if the two matrices are equal or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.op_Inequality(AForge.Math.Matrix4x4,AForge.Math.Matrix4x4)">
            <summary>
            Tests whether two specified matrices are not equal.
            </summary>
            
            <param name="matrix1">The left-hand matrix.</param>
            <param name="matrix2">The right-hand matrix.</param>
            
            <returns>Returns <see langword="true"/> if the two matrices are not equal or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.Equals(AForge.Math.Matrix4x4)">
            <summary>
            Tests whether the matrix equals to the specified one.
            </summary>
            
            <param name="matrix">The matrix to test equality with.</param>
            
            <returns>Returns <see langword="true"/> if the two matrices are equal or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.Equals(System.Object)">
            <summary>
            Tests whether the matrix equals to the specified object.
            </summary>
            
            <param name="obj">The object to test equality with.</param>
            
            <returns>Returns <see langword="true"/> if the matrix equals to the specified object or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix4x4.GetHashCode">
            <summary>
            Returns the hashcode for this instance.
            </summary>
            
            <returns>A 32-bit signed integer hash code.</returns>
            
        </member>
        <member name="P:AForge.Math.Matrix4x4.Identity">
            <summary>
            Provides an identity matrix with all diagonal elements set to 1.
            </summary>
        </member>
        <member name="T:AForge.Math.Geometry.ShapeType">
            <summary>
            Enumeration of some basic shape types.
            </summary>
        </member>
        <member name="F:AForge.Math.Geometry.ShapeType.Unknown">
            <summary>
            Unknown shape type.
            </summary>
        </member>
        <member name="F:AForge.Math.Geometry.ShapeType.Circle">
            <summary>
            Circle shape.
            </summary>
        </member>
        <member name="F:AForge.Math.Geometry.ShapeType.Triangle">
            <summary>
            Triangle shape.
            </summary>
        </member>
        <member name="F:AForge.Math.Geometry.ShapeType.Quadrilateral">
            <summary>
            Quadrilateral shape.
            </summary>
        </member>
        <member name="T:AForge.Math.Geometry.PolygonSubType">
            <summary>
            Some common sub types of some basic shapes.
            </summary>
        </member>
        <member name="F:AForge.Math.Geometry.PolygonSubType.Unknown">
            <summary>
            Unrecognized sub type of a shape (generic shape which does not have
            any specific sub type).
            </summary>
        </member>
        <member name="F:AForge.Math.Geometry.PolygonSubType.Trapezoid">
            <summary>
            Quadrilateral with one pair of parallel sides.
            </summary>
        </member>
        <member name="F:AForge.Math.Geometry.PolygonSubType.Parallelogram">
            <summary>
            Quadrilateral with two pairs of parallel sides.
            </summary>
        </member>
        <member name="F:AForge.Math.Geometry.PolygonSubType.Rectangle">
            <summary>
            Parallelogram with perpendicular adjacent sides.
            </summary>
        </member>
        <member name="F:AForge.Math.Geometry.PolygonSubType.Rhombus">
            <summary>
            Parallelogram with all sides equal.
            </summary>
        </member>
        <member name="F:AForge.Math.Geometry.PolygonSubType.Square">
            <summary>
            Rectangle with all sides equal.
            </summary>
        </member>
        <member name="F:AForge.Math.Geometry.PolygonSubType.EquilateralTriangle">
            <summary>
            Triangle with all sides/angles equal.
            </summary>
        </member>
        <member name="F:AForge.Math.Geometry.PolygonSubType.IsoscelesTriangle">
            <summary>
            Triangle with two sides/angles equal.
            </summary>
        </member>
        <member name="F:AForge.Math.Geometry.PolygonSubType.RectangledTriangle">
            <summary>
            Triangle with a 90 degrees angle.
            </summary>
        </member>
        <member name="F:AForge.Math.Geometry.PolygonSubType.RectangledIsoscelesTriangle">
            <summary>
            Triangle with a 90 degrees angle and other two angles are equal.
            </summary>
        </member>
        <member name="T:AForge.Math.Geometry.PointsCloud">
            <summary>
            Set of tools for processing collection of points in 2D space.
            </summary>
            
            <remarks><para>The static class contains set of routines, which provide different
            operations with collection of points in 2D space. For example, finding the
            furthest point from a specified point or line.</para>
            
            <para>Sample usage:</para>
            <code>
            // create points' list
            List&lt;IntPoint&gt; points = new List&lt;IntPoint&gt;( );
            points.Add( new IntPoint( 10, 10 ) );
            points.Add( new IntPoint( 20, 15 ) );
            points.Add( new IntPoint( 15, 30 ) );
            points.Add( new IntPoint( 40, 12 ) );
            points.Add( new IntPoint( 30, 20 ) );
            // get furthest point from the specified point
            IntPoint p1 = PointsCloud.GetFurthestPoint( points, new IntPoint( 15, 15 ) );
            Console.WriteLine( p1.X + ", " + p1.Y );
            // get furthest point from line
            IntPoint p2 = PointsCloud.GetFurthestPointFromLine( points,
                new IntPoint( 50, 0 ), new IntPoint( 0, 50 ) );
            Console.WriteLine( p2.X + ", " + p2.Y );
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Geometry.PointsCloud.Shift(System.Collections.Generic.IList{AForge.IntPoint},AForge.IntPoint)">
            <summary>
            Shift cloud by adding specified value to all points in the collection.
            </summary>
            
            <param name="cloud">Collection of points to shift their coordinates.</param>
            <param name="shift">Point to shift by.</param>
            
        </member>
        <member name="M:AForge.Math.Geometry.PointsCloud.GetBoundingRectangle(System.Collections.Generic.IEnumerable{AForge.IntPoint},AForge.IntPoint@,AForge.IntPoint@)">
            <summary>
            Get bounding rectangle of the specified list of points.
            </summary>
            
            <param name="cloud">Collection of points to get bounding rectangle for.</param>
            <param name="minXY">Point comprised of smallest X and Y coordinates.</param>
            <param name="maxXY">Point comprised of biggest X and Y coordinates.</param>
            
        </member>
        <member name="M:AForge.Math.Geometry.PointsCloud.GetCenterOfGravity(System.Collections.Generic.IEnumerable{AForge.IntPoint})">
            <summary>
            Get center of gravity for the specified list of points.
            </summary>
            
            <param name="cloud">List of points to calculate center of gravity for.</param>
            
            <returns>Returns center of gravity (mean X-Y values) for the specified list of points.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.PointsCloud.GetFurthestPoint(System.Collections.Generic.IEnumerable{AForge.IntPoint},AForge.IntPoint)">
            <summary>
            Find furthest point from the specified point.
            </summary>
            
            <param name="cloud">Collection of points to search furthest point in.</param>
            <param name="referencePoint">The point to search furthest point from.</param>
            
            <returns>Returns a point, which is the furthest away from the <paramref name="referencePoint"/>.</returns>
            
        </member>
        <member name="M:AForge.Math.Geometry.PointsCloud.GetFurthestPointsFromLine(System.Collections.Generic.IEnumerable{AForge.IntPoint},AForge.IntPoint,AForge.IntPoint,AForge.IntPoint@,AForge.IntPoint@)">
            <summary>
            Find two furthest points from the specified line.
            </summary>
            
            <param name="cloud">Collection of points to search furthest points in.</param>
            <param name="linePoint1">First point forming the line.</param>
            <param name="linePoint2">Second point forming the line.</param>
            <param name="furthestPoint1">First found furthest point.</param>
            <param name="furthestPoint2">Second found furthest point (which is on the
            opposite side from the line compared to the <paramref name="furthestPoint1"/>);</param>
            
            <remarks><para>The method finds two furthest points from the specified line,
            where one point is on one side from the line and the second point is on
            another side from the line.</para></remarks>
            
        </member>
        <member name="M:AForge.Math.Geometry.PointsCloud.GetFurthestPointsFromLine(System.Collections.Generic.IEnumerable{AForge.IntPoint},AForge.IntPoint,AForge.IntPoint,AForge.IntPoint@,System.Single@,AForge.IntPoint@,System.Single@)">
             <summary>
             Find two furthest points from the specified line.
             </summary>
             
             <param name="cloud">Collection of points to search furthest points in.</param>
             <param name="linePoint1">First point forming the line.</param>
             <param name="linePoint2">Second point forming the line.</param>
             <param name="furthestPoint1">First found furthest point.</param>
             <param name="distance1">Distance between the first found point and the given line.</param>
             <param name="furthestPoint2">Second found furthest point (which is on the
             opposite side from the line compared to the <paramref name="furthestPoint1"/>);</param>
             <param name="distance2">Distance between the second found point and the given line.</param>
             
             <remarks><para>The method finds two furthest points from the specified line,
             where one point is on one side from the line and the second point is on
             another side from the line.</para></remarks>
            
        </member>
        <member name="M:AForge.Math.Geometry.PointsCloud.GetFurthestPointFromLine(System.Collections.Generic.IEnumerable{AForge.IntPoint},AForge.IntPoint,AForge.IntPoint)">
             <summary>
             Find the furthest point from the specified line.
             </summary>
             
             <param name="cloud">Collection of points to search furthest point in.</param>
             <param name="linePoint1">First point forming the line.</param>
             <param name="linePoint2">Second point forming the line.</param>
             
             <returns>Returns a point, which is the furthest away from the
             specified line.</returns>
             
             <remarks><para>The method finds the furthest point from the specified line.
             Unlike the <see cref="M:AForge.Math.Geometry.PointsCloud.GetFurthestPointsFromLine(System.Collections.Generic.IEnumerable{AForge.IntPoint},AForge.IntPoint,AForge.IntPoint,AForge.IntPoint@,AForge.IntPoint@)"/>
             method, this method find only one point, which is the furthest away from the line
             regardless of side from the line.</para></remarks>
            
        </member>
        <member name="M:AForge.Math.Geometry.PointsCloud.GetFurthestPointFromLine(System.Collections.Generic.IEnumerable{AForge.IntPoint},AForge.IntPoint,AForge.IntPoint,System.Single@)">
             <summary>
             Find the furthest point from the specified line.
             </summary>
             
             <param name="cloud">Collection of points to search furthest points in.</param>
             <param name="linePoint1">First point forming the line.</param>
             <param name="linePoint2">Second point forming the line.</param>
             <param name="distance">Distance between the furthest found point and the given line.</param>
             
             <returns>Returns a point, which is the furthest away from the
             specified line.</returns>
             
             <remarks><para>The method finds the furthest point from the specified line.
             Unlike the <see cref="M:AForge.Math.Geometry.PointsCloud.GetFurthestPointsFromLine(System.Collections.Generic.IEnumerable{AForge.IntPoint},AForge.IntPoint,AForge.IntPoint,AForge.IntPoint@,System.Single@,AForge.IntPoint@,System.Single@)"/>
             method, this method find only one point, which is the furthest away from the line
             regardless of side from the line.</para></remarks>
            
        </member>
        <member name="M:AForge.Math.Geometry.PointsCloud.FindQuadrilateralCorners(System.Collections.Generic.IEnumerable{AForge.IntPoint})">
            <summary>
            Find corners of quadrilateral or triangular area, which contains the specified collection of points.
            </summary>
            
            <param name="cloud">Collection of points to search quadrilateral for.</param>
            
            <returns>Returns a list of 3 or 4 points, which are corners of the quadrilateral or
            triangular area filled by specified collection of point. The first point in the list
            is the point with lowest X coordinate (and with lowest Y if there are several points
            with the same X value). The corners are provided in counter clockwise order
            (<a href="http://en.wikipedia.org/wiki/Cartesian_coordinate_system">Cartesian
            coordinate system</a>).</returns>
            
            <remarks><para>The method makes an assumption that the specified collection of points
            form some sort of quadrilateral/triangular area. With this assumption it tries to find corners
            of the area.</para>
            
            <para><note>The method does not search for <b>bounding</b> quadrilateral/triangular area,
            where all specified points are <b>inside</b> of the found quadrilateral/triangle. Some of the
            specified points potentially may be outside of the found quadrilateral/triangle, since the
            method takes corners only from the specified collection of points, but does not calculate such
            to form true bounding quadrilateral/triangle.</note></para>
            
            <para>See <see cref="P:AForge.Math.Geometry.PointsCloud.QuadrilateralRelativeDistortionLimit"/> property for additional information.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.Geometry.PointsCloud.QuadrilateralRelativeDistortionLimit">
            <summary>
            Relative distortion limit allowed for quadrilaterals, [0.0, 0.25].
            </summary>
            
            <remarks><para>The value of this property is used to calculate distortion limit used by
            <see cref="M:AForge.Math.Geometry.PointsCloud.FindQuadrilateralCorners(System.Collections.Generic.IEnumerable{AForge.IntPoint})"/>, when processing potential corners and making decision
            if the provided points form a quadrilateral or a triangle. The distortion limit is
            calculated as:
            <code lang="none">
            distrtionLimit = RelativeDistortionLimit * ( W * H ) / 2,
            </code>
            where <b>W</b> and <b>H</b> are width and height of the "points cloud" passed to the
            <see cref="M:AForge.Math.Geometry.PointsCloud.FindQuadrilateralCorners(System.Collections.Generic.IEnumerable{AForge.IntPoint})"/>.
            </para>
            
            <para>To explain the idea behind distortion limit, let’s suppose that quadrilateral finder routine found
            the next candidates for corners:<br/>
            <img src="img/math/potential_corners.png" width="151" height="128"/><br/>
            As we can see on the above picture, the shape there potentially can be a triangle, but not quadrilateral
            (suppose that points list comes from a hand drawn picture or acquired from camera, so some
            inaccuracy may exist). It may happen that the <b>D</b> point is just a distortion (noise, etc).
            So the <see cref="M:AForge.Math.Geometry.PointsCloud.FindQuadrilateralCorners(System.Collections.Generic.IEnumerable{AForge.IntPoint})"/> check what is the distance between a potential corner
            (D in this case) and a line connecting two adjacent points (AB in this case). If the distance is smaller
            then the distortion limit, then the point may be rejected, so the shape turns into triangle.
            </para>
            
            <para>An exception is the case when both <b>C</b> and <b>D</b> points are very close to the <b>AB</b> line,
            so both their distances are less than distortion limit. In this case both points will be accepted as corners -
            the shape is just a flat quadrilateral.</para>
            
            <para>Default value is set to <b>0.1</b>.</para>
            </remarks>
            
        </member>
        <member name="T:AForge.Math.Vector3">
            <summary>
            3D Vector structure with X, Y and Z coordinates.
            </summary>
            
            <remarks><para>The structure incapsulates X, Y and Z coordinates of a 3D vector and
            provides some operations with it.</para></remarks>
            
        </member>
        <member name="F:AForge.Math.Vector3.X">
            <summary>
            X coordinate of the vector.
            </summary>
        </member>
        <member name="F:AForge.Math.Vector3.Y">
            <summary>
            Y coordinate of the vector.
            </summary>
        </member>
        <member name="F:AForge.Math.Vector3.Z">
            <summary>
            Z coordinate of the vector.
            </summary>
        </member>
        <member name="M:AForge.Math.Vector3.#ctor(System.Single,System.Single,System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Vector3"/> structure.
            </summary>
            
            <param name="x">X coordinate of the vector.</param>
            <param name="y">Y coordinate of the vector.</param>
            <param name="z">Z coordinate of the vector.</param>
            
        </member>
        <member name="M:AForge.Math.Vector3.#ctor(System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Vector3"/> structure.
            </summary>
            
            <param name="value">Value, which is set to all 3 coordinates of the vector.</param>
            
        </member>
        <member name="M:AForge.Math.Vector3.ToString">
            <summary>
            Returns a string representation of this object.
            </summary>
            
            <returns>A string representation of this object.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.ToArray">
            <summary>
            Returns array representation of the vector.
            </summary>
            
            <returns>Array with 3 values containing X/Y/Z coordinates.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.op_Addition(AForge.Math.Vector3,AForge.Math.Vector3)">
             <summary>
             Adds corresponding coordinates of two vectors.
             </summary>
             
             <param name="vector1">The vector to add to.</param>
             <param name="vector2">The vector to add to the first vector.</param>
             
             <returns>Returns a vector which coordinates are equal to sum of corresponding
             coordinates of the two specified vectors.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.Add(AForge.Math.Vector3,AForge.Math.Vector3)">
             <summary>
             Adds corresponding coordinates of two vectors.
             </summary>
             
             <param name="vector1">The vector to add to.</param>
             <param name="vector2">The vector to add to the first vector.</param>
             
             <returns>Returns a vector which coordinates are equal to sum of corresponding
             coordinates of the two specified vectors.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.op_Addition(AForge.Math.Vector3,System.Single)">
            <summary>
            Adds a value to all coordinates of the specified vector.
            </summary>
            
            <param name="vector">Vector to add the specified value to.</param>
            <param name="value">Value to add to all coordinates of the vector.</param>
            
            <returns>Returns new vector with all coordinates increased by the specified value.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.Add(AForge.Math.Vector3,System.Single)">
            <summary>
            Adds a value to all coordinates of the specified vector.
            </summary>
            
            <param name="vector">Vector to add the specified value to.</param>
            <param name="value">Value to add to all coordinates of the vector.</param>
            
            <returns>Returns new vector with all coordinates increased by the specified value.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.op_Subtraction(AForge.Math.Vector3,AForge.Math.Vector3)">
             <summary>
             Subtracts corresponding coordinates of two vectors.
             </summary>
             
             <param name="vector1">The vector to subtract from.</param>
             <param name="vector2">The vector to subtract from the first vector.</param>
             
             <returns>Returns a vector which coordinates are equal to difference of corresponding
             coordinates of the two specified vectors.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.Subtract(AForge.Math.Vector3,AForge.Math.Vector3)">
             <summary>
             Subtracts corresponding coordinates of two vectors.
             </summary>
             
             <param name="vector1">The vector to subtract from.</param>
             <param name="vector2">The vector to subtract from the first vector.</param>
             
             <returns>Returns a vector which coordinates are equal to difference of corresponding
             coordinates of the two specified vectors.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.op_Subtraction(AForge.Math.Vector3,System.Single)">
            <summary>
            Subtracts a value from all coordinates of the specified vector.
            </summary>
            
            <param name="vector">Vector to subtract the specified value from.</param>
            <param name="value">Value to subtract from all coordinates of the vector.</param>
            
            <returns>Returns new vector with all coordinates decreased by the specified value.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.Subtract(AForge.Math.Vector3,System.Single)">
            <summary>
            Subtracts a value from all coordinates of the specified vector.
            </summary>
            
            <param name="vector">Vector to subtract the specified value from.</param>
            <param name="value">Value to subtract from all coordinates of the vector.</param>
            
            <returns>Returns new vector with all coordinates decreased by the specified value.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.op_Multiply(AForge.Math.Vector3,AForge.Math.Vector3)">
             <summary>
             Multiplies corresponding coordinates of two vectors.
             </summary>
             
             <param name="vector1">The first vector to multiply.</param>
             <param name="vector2">The second vector to multiply.</param>
             
             <returns>Returns a vector which coordinates are equal to multiplication of corresponding
             coordinates of the two specified vectors.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.Multiply(AForge.Math.Vector3,AForge.Math.Vector3)">
             <summary>
             Multiplies corresponding coordinates of two vectors.
             </summary>
             
             <param name="vector1">The first vector to multiply.</param>
             <param name="vector2">The second vector to multiply.</param>
             
             <returns>Returns a vector which coordinates are equal to multiplication of corresponding
             coordinates of the two specified vectors.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.op_Multiply(AForge.Math.Vector3,System.Single)">
             <summary>
             Multiplies coordinates of the specified vector by the specified factor.
             </summary>
             
             <param name="vector">Vector to multiply coordinates of.</param>
             <param name="factor">Factor to multiple coordinates of the specified vector by.</param>
             
             <returns>Returns new vector with all coordinates multiplied by the specified factor.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.Multiply(AForge.Math.Vector3,System.Single)">
             <summary>
             Multiplies coordinates of the specified vector by the specified factor.
             </summary>
             
             <param name="vector">Vector to multiply coordinates of.</param>
             <param name="factor">Factor to multiple coordinates of the specified vector by.</param>
             
             <returns>Returns new vector with all coordinates multiplied by the specified factor.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.op_Division(AForge.Math.Vector3,AForge.Math.Vector3)">
             <summary>
             Divides corresponding coordinates of two vectors.
             </summary>
             
             <param name="vector1">The first vector to divide.</param>
             <param name="vector2">The second vector to devide.</param>
             
             <returns>Returns a vector which coordinates are equal to coordinates of the first vector divided by
             corresponding coordinates of the second vector.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.Divide(AForge.Math.Vector3,AForge.Math.Vector3)">
             <summary>
             Divides corresponding coordinates of two vectors.
             </summary>
             
             <param name="vector1">The first vector to divide.</param>
             <param name="vector2">The second vector to devide.</param>
             
             <returns>Returns a vector which coordinates are equal to coordinates of the first vector divided by
             corresponding coordinates of the second vector.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.op_Division(AForge.Math.Vector3,System.Single)">
             <summary>
             Divides coordinates of the specified vector by the specified factor.
             </summary>
             
             <param name="vector">Vector to divide coordinates of.</param>
             <param name="factor">Factor to divide coordinates of the specified vector by.</param>
             
             <returns>Returns new vector with all coordinates divided by the specified factor.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.Divide(AForge.Math.Vector3,System.Single)">
             <summary>
             Divides coordinates of the specified vector by the specified factor.
             </summary>
             
             <param name="vector">Vector to divide coordinates of.</param>
             <param name="factor">Factor to divide coordinates of the specified vector by.</param>
             
             <returns>Returns new vector with all coordinates divided by the specified factor.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.op_Equality(AForge.Math.Vector3,AForge.Math.Vector3)">
            <summary>
            Tests whether two specified vectors are equal.
            </summary>
            
            <param name="vector1">The left-hand vector.</param>
            <param name="vector2">The right-hand vector.</param>
            
            <returns>Returns <see langword="true"/> if the two vectors are equal or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.op_Inequality(AForge.Math.Vector3,AForge.Math.Vector3)">
            <summary>
            Tests whether two specified vectors are not equal.
            </summary>
            
            <param name="vector1">The left-hand vector.</param>
            <param name="vector2">The right-hand vector.</param>
            
            <returns>Returns <see langword="true"/> if the two vectors are not equal or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.Equals(AForge.Math.Vector3)">
            <summary>
            Tests whether the vector equals to the specified one.
            </summary>
            
            <param name="vector">The vector to test equality with.</param>
            
            <returns>Returns <see langword="true"/> if the two vectors are equal or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.Equals(System.Object)">
            <summary>
            Tests whether the vector equals to the specified object.
            </summary>
            
            <param name="obj">The object to test equality with.</param>
            
            <returns>Returns <see langword="true"/> if the vector equals to the specified object or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.GetHashCode">
            <summary>
            Returns the hashcode for this instance.
            </summary>
            
            <returns>A 32-bit signed integer hash code.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.Normalize">
             <summary>
             Normalizes the vector by dividing it’s all coordinates with the vector's norm.
             </summary>
             
             <returns>Returns the value of vectors’ norm before normalization.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.Inverse">
             <summary>
             Inverse the vector.
             </summary>
             
             <returns>Returns a vector with all coordinates equal to 1.0 divided by the value of corresponding coordinate
             in this vector (or equal to 0.0 if this vector has corresponding coordinate also set to 0.0).</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.Abs">
            <summary>
            Calculate absolute values of the vector.
            </summary>
            
            <returns>Returns a vector with all coordinates equal to absolute values of this vector's coordinates.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.Cross(AForge.Math.Vector3,AForge.Math.Vector3)">
            <summary>
            Calculates cross product of two vectors.
            </summary>
            
            <param name="vector1">First vector to use for cross product calculation.</param>
            <param name="vector2">Second vector to use for cross product calculation.</param>
            
            <returns>Returns cross product of the two specified vectors.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.Dot(AForge.Math.Vector3,AForge.Math.Vector3)">
            <summary>
            Calculates dot product of two vectors.
            </summary>
            
            <param name="vector1">First vector to use for dot product calculation.</param>
            <param name="vector2">Second vector to use for dot product calculation.</param>
            
            <returns>Returns dot product of the two specified vectors.</returns>
            
        </member>
        <member name="M:AForge.Math.Vector3.ToVector4">
            <summary>
            Converts the vector to a 4D vector.
            </summary>
            
            <returns>Returns 4D vector which is an extension of the 3D vector.</returns>
            
            <remarks><para>The method returns a 4D vector which has X, Y and Z coordinates equal to the
            coordinates of this 3D vector and <see cref="F:AForge.Math.Vector4.W">W</see> coordinate set to 1.0.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.Vector3.Max">
             <summary>
             Returns maximum value of the vector.
             </summary>
            
             <remarks><para>Returns maximum value of all 3 vector's coordinates.</para></remarks>
            
        </member>
        <member name="P:AForge.Math.Vector3.Min">
             <summary>
             Returns minimum value of the vector.
             </summary>
            
             <remarks><para>Returns minimum value of all 3 vector's coordinates.</para></remarks>
            
        </member>
        <member name="P:AForge.Math.Vector3.MaxIndex">
             <summary>
             Returns index of the coordinate with maximum value.
             </summary>
            
             <remarks><para>Returns index of the coordinate, which has the maximum value - 0 for X,
             1 for Y or 2 for Z.</para>
             
             <para><note>If there are multiple coordinates which have the same maximum value, the
             property returns smallest index.</note></para>
             </remarks>
            
        </member>
        <member name="P:AForge.Math.Vector3.MinIndex">
             <summary>
             Returns index of the coordinate with minimum value.
             </summary>
            
             <remarks><para>Returns index of the coordinate, which has the minimum value - 0 for X,
             1 for Y or 2 for Z.</para>
             
             <para><note>If there are multiple coordinates which have the same minimum value, the
             property returns smallest index.</note></para>
             </remarks>
            
        </member>
        <member name="P:AForge.Math.Vector3.Norm">
            <summary>
            Returns vector's norm.
            </summary>
            
            <remarks><para>Returns Euclidean norm of the vector, which is a
            square root of the sum: X<sup>2</sup>+Y<sup>2</sup>+Z<sup>2</sup>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.Vector3.Square">
            <summary>
            Returns square of the vector's norm.
            </summary>
            
            <remarks><para>Return X<sup>2</sup>+Y<sup>2</sup>+Z<sup>2</sup>, which is
            a square of <see cref="P:AForge.Math.Vector3.Norm">vector's norm</see> or a <see cref="M:AForge.Math.Vector3.Dot(AForge.Math.Vector3,AForge.Math.Vector3)">dot product</see> of this vector
            with itself.</para></remarks>
            
        </member>
        <member name="T:AForge.Math.Metrics.CosineSimilarity">
            <summary>
            Cosine similarity metric. 
            </summary>
            
            <remarks><para>This class represents the 
            <a href="http://en.wikipedia.org/wiki/Cosine_similarity">Cosine Similarity metric</a>.</para>
            
            <para>Sample usage:</para>
            <code>
            // instantiate new similarity class
            CosineSimilarity sim = new CosineSimilarity( ); 
            // create two vectors for inputs
            double[] p = new double[] { 2.5, 3.5, 3.0, 3.5, 2.5, 3.0 };
            double[] q = new double[] { 3.0, 3.5, 1.5, 5.0, 3.5, 3.0 };
            // get similarity between the two vectors
            double similarityScore = sim.GetSimilarityScore( p, q );
            </code>    
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Metrics.CosineSimilarity.GetSimilarityScore(System.Double[],System.Double[])">
            <summary>
            Returns similarity score for two N-dimensional double vectors. 
            </summary>
            
            <param name="p">1st point vector.</param>
            <param name="q">2nd point vector.</param>
            
            <returns>Returns Cosine similarity between two supplied vectors.</returns>
            
            <exception cref="T:System.ArgumentException">Thrown if the two vectors are of different dimensions (if specified
            array have different length).</exception>
            
        </member>
        <member name="T:AForge.Math.Geometry.Posit">
            <summary>
            3D pose estimation algorithm.
            </summary>
            
            <remarks><para>The class implements an algorithm for 3D object's pose estimation from it's
            2D coordinates obtained by perspective projection, when the object is described none coplanar points.
            The idea of the implemented math and algorithm is described in "Model-Based Object Pose in 25
            Lines of Code" paper written by Daniel F. DeMenthon and Larry S. Davis (the implementation of
            the algorithm is almost 1 to 1 translation of the pseudo code given by the paper, so should
            be easy to follow).</para>
            
            <para><note>At this point the implementation works only with models described by 4 points, which is
            the minimum number of points enough for 3D pose estimation.</note></para>
            
            <para><note>The 4 model's point <b>must not be coplanar</b>, i.e. must not reside all within
            same planer. See <see cref="T:AForge.Math.Geometry.CoplanarPosit"/> for coplanar case.</note></para>
            
            <para>Read <a href="http://www.aforgenet.com/articles/posit/">3D Pose Estimation</a> article for
            additional information and samples.</para>
            
            <para>Sample usage:</para>
            <code>
            // points of real object - model
            Vector3[] positObject = new Vector3[4]
            { 
                new Vector3(  28,  28, -28 ),
                new Vector3( -28,  28, -28 ),
                new Vector3(  28, -28, -28 ),
                new Vector3(  28,  28,  28 ),
            };
            // focal length of camera used to capture the object
            float focalLength = 640; // depends on your camera or projection system
            // initialize POSIT object
            Posit posit = new Posit( positObject, focalLength );
            
            // 2D points of te object - projection
            AForge.Point[] projectedPoints = new AForge.Point[4]
            {
                new AForge.Point(   -4,   29 ),
                new AForge.Point( -180,   86 ),
                new AForge.Point(   -5, -102 ),
                new AForge.Point(   76,  137 ),
            };
            // estimate pose
            Matrix3x3 rotationMatrix;
            Vector3 translationVector;
            posit.EstimatePose( projectedPoints,
                out rotationMatrix, out translationVector );
            </code>
            </remarks>
            
            <seealso cref="T:AForge.Math.Geometry.CoplanarPosit"/>
            
        </member>
        <member name="M:AForge.Math.Geometry.Posit.#ctor(AForge.Math.Vector3[],System.Single)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Geometry.Posit"/> class.
            </summary>
            
            <param name="model">Array of vectors containing coordinates of four real model's point (points
            must not be on the same plane).</param>
            <param name="focalLength">Effective focal length of the camera used to capture the model.</param>
            
            <exception cref="T:System.ArgumentException">The model must have 4 points.</exception>
            
        </member>
        <member name="M:AForge.Math.Geometry.Posit.EstimatePose(AForge.Point[],AForge.Math.Matrix3x3@,AForge.Math.Vector3@)">
            <summary>
            Estimate pose of a model from it's projected 2D coordinates.
            </summary>
            
            <param name="points">4 2D points of the <see cref="P:AForge.Math.Geometry.Posit.Model">model's</see> projection.</param>
            <param name="rotation">Gets object's rotation.</param>
            <param name="translation">Gets object's translation.</param>
            
            <exception cref="T:System.ArgumentException">4 points must be be given for pose estimation.</exception>
            
        </member>
        <member name="P:AForge.Math.Geometry.Posit.Model">
            <summary>
            Coordinates of the model points which pose should be estimated.
            </summary>
        </member>
        <member name="P:AForge.Math.Geometry.Posit.FocalLength">
            <summary>
            Effective focal length of the camera used to capture the model.
            </summary>
        </member>
        <member name="T:AForge.Math.ContinuousHistogram">
            <summary>
            Histogram for continuous random values.
            </summary>
            
            <remarks><para>The class wraps histogram for continuous stochastic function, which is represented
            by integer array and range of the function. Values of the integer array are treated
            as total amount of hits on the corresponding subranges, which are calculated by splitting the
            specified range into required amount of consequent ranges.</para>
            
            <para>For example, if the integer array is equal to { 1, 2, 4, 8, 16 } and the range is set
            to [0, 1], then the histogram consists of next subranges:
            <list type="bullet">
            <item>[0.0, 0.2] - 1 hit;</item>
            <item>[0.2, 0.4] - 2 hits;</item>
            <item>[0.4, 0.6] - 4 hits;</item>
            <item>[0.6, 0.8] - 8 hits;</item>
            <item>[0.8, 1.0] - 16 hits.</item>
            </list>
            </para>
            
            <para>Sample usage:</para>
            <code>
            // create histogram
            ContinuousHistogram histogram = new ContinuousHistogram(
                new int[] { 0, 0, 8, 4, 2, 4, 7, 1, 0 }, new Range( 0.0f, 1.0f ) );
            // get mean and standard deviation values
            Console.WriteLine( "mean = " + histogram.Mean + ", std.dev = " + histogram.StdDev );
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.ContinuousHistogram.#ctor(System.Int32[],AForge.Range)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.ContinuousHistogram"/> class.
            </summary>
            
            <param name="values">Values of the histogram.</param>
            <param name="range">Range of random values.</param>
            
            <remarks>Values of the integer array are treated as total amount of hits on the
            corresponding subranges, which are calculated by splitting the specified range into
            required amount of consequent ranges (see <see cref="T:AForge.Math.ContinuousHistogram"/> class
            description for more information).
            </remarks>
            
        </member>
        <member name="M:AForge.Math.ContinuousHistogram.GetRange(System.Single)">
            <summary>
            Get range around median containing specified percentage of values.
            </summary>
            
            <param name="percent">Values percentage around median.</param>
            
            <returns>Returns the range which containes specifies percentage of values.</returns>
            
            <remarks><para>The method calculates range of stochastic variable, which summary probability
            comprises the specified percentage of histogram's hits.</para>
            
            <para>Sample usage:</para>
            <code>
            // create histogram
            ContinuousHistogram histogram = new ContinuousHistogram(
                new int[] { 0, 0, 8, 4, 2, 4, 7, 1, 0 }, new Range( 0.0f, 1.0f ) );
            // get 50% range
            Range range = histogram.GetRange( 0.5f );
            // show the range ([0.25, 0.75])
            Console.WriteLine( "50% range = [" + range.Min + ", " + range.Max + "]" );
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.ContinuousHistogram.Update">
            <summary>
            Update statistical value of the histogram.
            </summary>
            
            <remarks>The method recalculates statistical values of the histogram, like mean,
            standard deviation, etc. The method should be called only in the case if histogram
            values were retrieved through <see cref="P:AForge.Math.ContinuousHistogram.Values"/> property and updated after that.
            </remarks>
            
        </member>
        <member name="P:AForge.Math.ContinuousHistogram.Values">
            <summary>
            Values of the histogram.
            </summary>
            
        </member>
        <member name="P:AForge.Math.ContinuousHistogram.Range">
            <summary>
            Range of random values.
            </summary>
            
        </member>
        <member name="P:AForge.Math.ContinuousHistogram.Mean">
            <summary>
            Mean value.
            </summary>
            
            <remarks><para>The property allows to retrieve mean value of the histogram.</para>
            
            <para>Sample usage:</para>
            <code>
            // create histogram
            ContinuousHistogram histogram = new ContinuousHistogram(
                new int[] { 0, 0, 8, 4, 2, 4, 7, 1, 0 }, new Range( 0.0f, 1.0f ) );
            // get mean value (= 0.505 )
            Console.WriteLine( "mean = " + histogram.Mean.ToString( "F3" ) );
            </code>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.ContinuousHistogram.StdDev">
            <summary>
            Standard deviation.
            </summary>
            
            <remarks><para>The property allows to retrieve standard deviation value of the histogram.</para>
            
            <para>Sample usage:</para>
            <code>
            // create histogram
            ContinuousHistogram histogram = new ContinuousHistogram(
                new int[] { 0, 0, 8, 4, 2, 4, 7, 1, 0 }, new Range( 0.0f, 1.0f ) );
            // get std.dev. value (= 0.215)
            Console.WriteLine( "std.dev. = " + histogram.StdDev.ToString( "F3" ) );
            </code>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.ContinuousHistogram.Median">
            <summary>
            Median value.
            </summary>
            
            <remarks><para>The property allows to retrieve median value of the histogram.</para>
            
            <para>Sample usage:</para>
            <code>
            // create histogram
            ContinuousHistogram histogram = new ContinuousHistogram(
                new int[] { 0, 0, 8, 4, 2, 4, 7, 1, 0 }, new Range( 0.0f, 1.0f ) );
            // get median value (= 0.500)
            Console.WriteLine( "median = " + histogram.Median.ToString( "F3" ) );
            </code>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.ContinuousHistogram.Min">
            <summary>
            Minimum value.
            </summary>
            
            <remarks><para>The property allows to retrieve minimum value of the histogram with non zero
            hits count.</para>
            
            <para>Sample usage:</para>
            <code>
            // create histogram
            ContinuousHistogram histogram = new ContinuousHistogram(
                new int[] { 0, 0, 8, 4, 2, 4, 7, 1, 0 }, new Range( 0.0f, 1.0f ) );
            // get min value (= 0.250)
            Console.WriteLine( "min = " + histogram.Min.ToString( "F3" ) );
            </code>
            </remarks>
        </member>
        <member name="P:AForge.Math.ContinuousHistogram.Max">
            <summary>
            Maximum value.
            </summary>
            
            <remarks><para>The property allows to retrieve maximum value of the histogram with non zero
            hits count.</para>
            
            <para>Sample usage:</para>
            <code>
            // create histogram
            ContinuousHistogram histogram = new ContinuousHistogram(
                new int[] { 0, 0, 8, 4, 2, 4, 7, 1, 0 }, new Range( 0.0f, 1.0f ) );
            // get max value (= 0.875)
            Console.WriteLine( "max = " + histogram.Max.ToString( "F3" ) );
            </code>
            </remarks>
            
        </member>
        <member name="T:AForge.Math.Matrix3x3">
            <summary>
            A structure representing 3x3 matrix.
            </summary>
            
            <remarks><para>The structure incapsulates elements of a 3x3 matrix and
            provides some operations with it.</para></remarks>
            
        </member>
        <member name="F:AForge.Math.Matrix3x3.V00">
            <summary>
            Row 0 column 0 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix3x3.V01">
            <summary>
            Row 0 column 1 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix3x3.V02">
            <summary>
            Row 0 column 2 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix3x3.V10">
            <summary>
            Row 1 column 0 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix3x3.V11">
            <summary>
            Row 1 column 1 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix3x3.V12">
            <summary>
            Row 1 column 2 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix3x3.V20">
            <summary>
            Row 2 column 0 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix3x3.V21">
            <summary>
            Row 2 column 1 element of the matrix.
            </summary>
        </member>
        <member name="F:AForge.Math.Matrix3x3.V22">
            <summary>
            Row 2 column 2 element of the matrix.
            </summary>
        </member>
        <member name="M:AForge.Math.Matrix3x3.ToArray">
            <summary>
            Returns array representation of the matrix.
            </summary>
            
            <returns>Returns array which contains all elements of the matrix in the row-major order.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.CreateRotationY(System.Single)">
            <summary>
            Creates rotation matrix around Y axis.
            </summary>
            
            <param name="radians">Rotation angle around Y axis in radians.</param>
            
            <returns>Returns rotation matrix to rotate an object around Y axis.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.CreateRotationX(System.Single)">
            <summary>
            Creates rotation matrix around X axis.
            </summary>
            
            <param name="radians">Rotation angle around X axis in radians.</param>
            
            <returns>Returns rotation matrix to rotate an object around X axis.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.CreateRotationZ(System.Single)">
            <summary>
            Creates rotation matrix around Z axis.
            </summary>
            
            <param name="radians">Rotation angle around Z axis in radians.</param>
            
            <returns>Returns rotation matrix to rotate an object around Z axis.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.CreateFromYawPitchRoll(System.Single,System.Single,System.Single)">
            <summary>
            Creates rotation matrix to rotate an object around X, Y and Z axes.
            </summary>
            
            <param name="yaw">Rotation angle around Y axis in radians.</param>
            <param name="pitch">Rotation angle around X axis in radians.</param>
            <param name="roll">Rotation angle around Z axis in radians.</param>
            
            <returns>Returns rotation matrix to rotate an object around all 3 axes.</returns>
            
            <remarks>
            <para><note>The routine assumes roll-pitch-yaw rotation order, when creating rotation
            matrix, i.e. an object is first rotated around Z axis, then around X axis and finally around
            Y axis.</note></para>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.ExtractYawPitchRoll(System.Single@,System.Single@,System.Single@)">
            <summary>
            Extract rotation angles from the rotation matrix.
            </summary>
            
            <param name="yaw">Extracted rotation angle around Y axis in radians.</param>
            <param name="pitch">Extracted rotation angle around X axis in radians.</param>
            <param name="roll">Extracted rotation angle around Z axis in radians.</param>
            
            <remarks><para><note>The routine assumes roll-pitch-yaw rotation order when extracting rotation angle.
            Using extracted angles with the <see cref="M:AForge.Math.Matrix3x3.CreateFromYawPitchRoll(System.Single,System.Single,System.Single)"/> should provide same rotation matrix.
            </note></para>
            
            <para><note>The method assumes the provided matrix represent valid rotation matrix.</note></para>
            
            <para>Sample usage:</para>
            <code>
            // assume we have a rotation matrix created like this
            float yaw   = 10.0f / 180 * Math.PI;
            float pitch = 30.0f / 180 * Math.PI;
            float roll  = 45.0f / 180 * Math.PI;
            
            Matrix3x3 rotationMatrix = Matrix3x3.CreateFromYawPitchRoll( yaw, pitch, roll );
            // ...
            
            // now somewhere in the code you may want to get rotation
            // angles back from a matrix assuming same rotation order
            float extractedYaw;
            float extractedPitch;
            float extractedRoll;
            
            rotation.ExtractYawPitchRoll( out extractedYaw, out extractedPitch, out extractedRoll );
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.CreateFromRows(AForge.Math.Vector3,AForge.Math.Vector3,AForge.Math.Vector3)">
            <summary>
            Creates a matrix from 3 rows specified as vectors.
            </summary>
            
            <param name="row0">First row of the matrix to create.</param>
            <param name="row1">Second row of the matrix to create.</param>
            <param name="row2">Third row of the matrix to create.</param>
            
            <returns>Returns a matrix from specified rows.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.CreateFromColumns(AForge.Math.Vector3,AForge.Math.Vector3,AForge.Math.Vector3)">
            <summary>
            Creates a matrix from 3 columns specified as vectors.
            </summary>
            
            <param name="column0">First column of the matrix to create.</param>
            <param name="column1">Second column of the matrix to create.</param>
            <param name="column2">Third column of the matrix to create.</param>
            
            <returns>Returns a matrix from specified columns.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.CreateDiagonal(AForge.Math.Vector3)">
            <summary>
            Creates a diagonal matrix using the specified vector as diagonal elements.
            </summary>
            
            <param name="vector">Vector to use for diagonal elements of the matrix.</param>
            
            <returns>Returns a diagonal matrix.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.GetRow(System.Int32)">
            <summary>
            Get row of the matrix.
            </summary>
            
            <param name="index">Row index to get, [0, 2].</param>
            
            <returns>Returns specified row of the matrix as a vector.</returns>
            
            <exception cref="T:System.ArgumentException">Invalid row index was specified.</exception>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.GetColumn(System.Int32)">
            <summary>
            Get column of the matrix.
            </summary>
            
            <param name="index">Column index to get, [0, 2].</param>
            
            <returns>Returns specified column of the matrix as a vector.</returns>
            
            <exception cref="T:System.ArgumentException">Invalid column index was specified.</exception>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.op_Multiply(AForge.Math.Matrix3x3,AForge.Math.Matrix3x3)">
            <summary>
            Multiplies two specified matrices.
            </summary>
            
            <param name="matrix1">Matrix to multiply.</param>
            <param name="matrix2">Matrix to multiply by.</param>
            
            <returns>Return new matrix, which the result of multiplication of the two specified matrices.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.Multiply(AForge.Math.Matrix3x3,AForge.Math.Matrix3x3)">
            <summary>
            Multiplies two specified matrices.
            </summary>
            
            <param name="matrix1">Matrix to multiply.</param>
            <param name="matrix2">Matrix to multiply by.</param>
            
            <returns>Return new matrix, which the result of multiplication of the two specified matrices.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.op_Addition(AForge.Math.Matrix3x3,AForge.Math.Matrix3x3)">
             <summary>
             Adds corresponding components of two matrices.
             </summary>
             
             <param name="matrix1">The matrix to add to.</param>
             <param name="matrix2">The matrix to add to the first matrix.</param>
             
             <returns>Returns a matrix which components are equal to sum of corresponding
             components of the two specified matrices.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.Add(AForge.Math.Matrix3x3,AForge.Math.Matrix3x3)">
             <summary>
             Adds corresponding components of two matrices.
             </summary>
             
             <param name="matrix1">The matrix to add to.</param>
             <param name="matrix2">The matrix to add to the first matrix.</param>
             
             <returns>Returns a matrix which components are equal to sum of corresponding
             components of the two specified matrices.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.op_Subtraction(AForge.Math.Matrix3x3,AForge.Math.Matrix3x3)">
             <summary>
             Subtracts corresponding components of two matrices.
             </summary>
             
             <param name="matrix1">The matrix to subtract from.</param>
             <param name="matrix2">The matrix to subtract from the first matrix.</param>
             
             <returns>Returns a matrix which components are equal to difference of corresponding
             components of the two specified matrices.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.Subtract(AForge.Math.Matrix3x3,AForge.Math.Matrix3x3)">
             <summary>
             Subtracts corresponding components of two matrices.
             </summary>
             
             <param name="matrix1">The matrix to subtract from.</param>
             <param name="matrix2">The matrix to subtract from the first matrix.</param>
             
             <returns>Returns a matrix which components are equal to difference of corresponding
             components of the two specified matrices.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.op_Multiply(AForge.Math.Matrix3x3,AForge.Math.Vector3)">
             <summary>
             Multiplies specified matrix by the specified vector.
             </summary>
             
             <param name="matrix">Matrix to multiply by vector.</param>
             <param name="vector">Vector to multiply matrix by.</param>
             
             <returns>Returns new vector which is the result of multiplication of the specified matrix
             by the specified vector.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.Multiply(AForge.Math.Matrix3x3,AForge.Math.Vector3)">
             <summary>
             Multiplies specified matrix by the specified vector.
             </summary>
             
             <param name="matrix">Matrix to multiply by vector.</param>
             <param name="vector">Vector to multiply matrix by.</param>
             
             <returns>Returns new vector which is the result of multiplication of the specified matrix
             by the specified vector.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.op_Multiply(AForge.Math.Matrix3x3,System.Single)">
            <summary>
            Multiplies matrix by the specified factor.
            </summary>
            
            <param name="matrix">Matrix to multiply.</param>
            <param name="factor">Factor to multiple the specified matrix by.</param>
            
            <returns>Returns new matrix with all components equal to corresponding components of the
            specified matrix multiples by the specified factor.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.Multiply(AForge.Math.Matrix3x3,System.Single)">
            <summary>
            Multiplies matrix by the specified factor.
            </summary>
            
            <param name="matrix">Matrix to multiply.</param>
            <param name="factor">Factor to multiple the specified matrix by.</param>
            
            <returns>Returns new matrix with all components equal to corresponding components of the
            specified matrix multiples by the specified factor.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.op_Addition(AForge.Math.Matrix3x3,System.Single)">
            <summary>
            Adds specified value to all components of the specified matrix.
            </summary>
            
            <param name="matrix">Matrix to add value to.</param>
            <param name="value">Value to add to all components of the specified matrix.</param>
            
            <returns>Returns new matrix with all components equal to corresponding components of the
            specified matrix increased by the specified value.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.Add(AForge.Math.Matrix3x3,System.Single)">
            <summary>
            Adds specified value to all components of the specified matrix.
            </summary>
            
            <param name="matrix">Matrix to add value to.</param>
            <param name="value">Value to add to all components of the specified matrix.</param>
            
            <returns>Returns new matrix with all components equal to corresponding components of the
            specified matrix increased by the specified value.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.op_Equality(AForge.Math.Matrix3x3,AForge.Math.Matrix3x3)">
            <summary>
            Tests whether two specified matrices are equal.
            </summary>
            
            <param name="matrix1">The left-hand matrix.</param>
            <param name="matrix2">The right-hand matrix.</param>
            
            <returns>Returns <see langword="true"/> if the two matrices are equal or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.op_Inequality(AForge.Math.Matrix3x3,AForge.Math.Matrix3x3)">
            <summary>
            Tests whether two specified matrices are not equal.
            </summary>
            
            <param name="matrix1">The left-hand matrix.</param>
            <param name="matrix2">The right-hand matrix.</param>
            
            <returns>Returns <see langword="true"/> if the two matrices are not equal or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.Equals(AForge.Math.Matrix3x3)">
            <summary>
            Tests whether the matrix equals to the specified one.
            </summary>
            
            <param name="matrix">The matrix to test equality with.</param>
            
            <returns>Returns <see langword="true"/> if the two matrices are equal or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.Equals(System.Object)">
            <summary>
            Tests whether the matrix equals to the specified object.
            </summary>
            
            <param name="obj">The object to test equality with.</param>
            
            <returns>Returns <see langword="true"/> if the matrix equals to the specified object or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.GetHashCode">
            <summary>
            Returns the hashcode for this instance.
            </summary>
            
            <returns>A 32-bit signed integer hash code.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.Transpose">
            <summary>
            Transpose the matrix, A<sup>T</sup>.
            </summary>
            
            <returns>Return a matrix which equals to transposition of this matrix.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.MultiplySelfByTranspose">
             <summary>
             Multiply the matrix by its transposition, A*A<sup>T</sup>.
             </summary>
             
             <returns>Returns a matrix which is the result of multiplying this matrix by its transposition.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.MultiplyTransposeBySelf">
             <summary>
             Multiply transposition of this matrix by itself, A<sup>T</sup>*A.
             </summary>
             
             <returns>Returns a matrix which is the result of multiplying this matrix's transposition by itself.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.Adjugate">
            <summary>
            Calculate adjugate of the matrix, adj(A).
            </summary>
            
            <returns>Returns adjugate of the matrix.</returns>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.Inverse">
            <summary>
            Calculate inverse of the matrix, A<sup>-1</sup>.
            </summary>
            
            <returns>Returns inverse of the matrix.</returns>
            
            <exception cref="T:System.ArgumentException">Cannot calculate inverse of the matrix since it is singular.</exception>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.PseudoInverse">
            <summary>
            Calculate pseudo inverse of the matrix, A<sup>+</sup>.
            </summary>
            
            <returns>Returns pseudo inverse of the matrix.</returns>
            
            <remarks><para>The pseudo inverse of the matrix is calculate through its <see cref="M:AForge.Math.Matrix3x3.SVD(AForge.Math.Matrix3x3@,AForge.Math.Vector3@,AForge.Math.Matrix3x3@)"/>
            as V*E<sup>+</sup>*U<sup>T</sup>.</para></remarks>
            
        </member>
        <member name="M:AForge.Math.Matrix3x3.SVD(AForge.Math.Matrix3x3@,AForge.Math.Vector3@,AForge.Math.Matrix3x3@)">
            <summary>
            Calculate Singular Value Decomposition (SVD) of the matrix, such as A=U*E*V<sup>T</sup>.
            </summary>
            
            <param name="u">Output parameter which gets 3x3 U matrix.</param>
            <param name="e">Output parameter which gets diagonal elements of the E matrix.</param>
            <param name="v">Output parameter which gets 3x3 V matrix.</param>
            
            <remarks><para>Having components U, E and V the source matrix can be reproduced using below code:
            <code>
            Matrix3x3 source = u * Matrix3x3.Diagonal( e ) * v.Transpose( );
            </code>
            </para></remarks>
            
        </member>
        <member name="P:AForge.Math.Matrix3x3.Identity">
            <summary>
            Provides an identity matrix with all diagonal elements set to 1.
            </summary>
        </member>
        <member name="P:AForge.Math.Matrix3x3.Determinant">
            <summary>
            Calculates determinant of the matrix.
            </summary>
        </member>
        <member name="T:AForge.Math.PerlinNoise">
            <summary>
            Perlin noise function.
            </summary>
            
            <remarks><para>The class implements 1-D and 2-D Perlin noise functions, which represent
            sum of several smooth noise functions with different frequency and amplitude. The description
            of Perlin noise function and its calculation may be found on
            <a href="http://freespace.virgin.net/hugo.elias/models/m_perlin.htm" target="_blank">Hugo Elias's page</a>.
            </para>
            
            <para>The number of noise functions, which comprise the resulting Perlin noise function, is
            set by <see cref="P:AForge.Math.PerlinNoise.Octaves"/> property. Amplitude and frequency values for each octave
            start from values, which are set by <see cref="P:AForge.Math.PerlinNoise.InitFrequency"/> and <see cref="P:AForge.Math.PerlinNoise.InitAmplitude"/>
            properties.</para>
            
            <para>Sample usage (clouds effect):</para>
            <code>
            // create Perlin noise function
            PerlinNoise noise = new PerlinNoise( 8, 0.5, 1.0 / 32 );
            // generate clouds effect
            float[,] texture = new float[height, width];
            
            for ( int y = 0; y &lt; height; y++ )
            {
            	for ( int x = 0; x &lt; width; x++ )
            	{
            		texture[y, x] = 
            			Math.Max( 0.0f, Math.Min( 1.0f,
            				(float) noise.Function2D( x, y ) * 0.5f + 0.5f
            			) );
            	}
            }
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.PerlinNoise.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.PerlinNoise"/> class.
            </summary>
            
        </member>
        <member name="M:AForge.Math.PerlinNoise.#ctor(System.Int32,System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.PerlinNoise"/> class.
            </summary>
            
            <param name="octaves">Number of octaves (see <see cref="P:AForge.Math.PerlinNoise.Octaves"/> property).</param>
            <param name="persistence">Persistence value (see <see cref="P:AForge.Math.PerlinNoise.Persistence"/> property).</param>
            
        </member>
        <member name="M:AForge.Math.PerlinNoise.#ctor(System.Int32,System.Double,System.Double,System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.PerlinNoise"/> class.
            </summary>
            
            <param name="octaves">Number of octaves (see <see cref="P:AForge.Math.PerlinNoise.Octaves"/> property).</param>
            <param name="persistence">Persistence value (see <see cref="P:AForge.Math.PerlinNoise.Persistence"/> property).</param>
            <param name="initFrequency">Initial frequency (see <see cref="P:AForge.Math.PerlinNoise.InitFrequency"/> property).</param>
            <param name="initAmplitude">Initial amplitude (see <see cref="P:AForge.Math.PerlinNoise.InitAmplitude"/> property).</param>
            
        </member>
        <member name="M:AForge.Math.PerlinNoise.Function(System.Double)">
            <summary>
            1-D Perlin noise function.
            </summary>
            
            <param name="x">x value.</param>
            
            <returns>Returns function's value at point <paramref name="x"/>.</returns>
            
        </member>
        <member name="M:AForge.Math.PerlinNoise.Function2D(System.Double,System.Double)">
            <summary>
            2-D Perlin noise function.
            </summary>
            
            <param name="x">x value.</param>
            <param name="y">y value.</param>
            
            <returns>Returns function's value at point (<paramref name="x"/>, <paramref name="y"/>).</returns>
            
        </member>
        <member name="M:AForge.Math.PerlinNoise.Noise(System.Int32)">
            <summary>
            Ordinary noise function
            </summary>
        </member>
        <member name="M:AForge.Math.PerlinNoise.SmoothedNoise(System.Double)">
            <summary>
            Smoothed noise.
            </summary>
        </member>
        <member name="M:AForge.Math.PerlinNoise.CosineInterpolate(System.Double,System.Double,System.Double)">
            <summary>
            Cosine interpolation.
            </summary>
        </member>
        <member name="P:AForge.Math.PerlinNoise.InitFrequency">
            <summary>
            Initial frequency.
            </summary>
            
            <remarks><para>The property sets initial frequency of the first octave. Frequencies for
            next octaves are calculated using the next equation:<br/>
            frequency<sub>i</sub> = <see cref="P:AForge.Math.PerlinNoise.InitFrequency"/> * 2<sup>i</sup>,
            where i = [0, <see cref="P:AForge.Math.PerlinNoise.Octaves"/>).
            </para>
            
            <para>Default value is set to <b>1</b>.</para>
            </remarks>
            
        </member>
        <member name="P:AForge.Math.PerlinNoise.InitAmplitude">
             <summary>
             Initial amplitude.
             </summary>
             
             <remarks><para>The property sets initial amplitude of the first octave. Amplitudes for
             next octaves are calculated using the next equation:<br/>
             amplitude<sub>i</sub> = <see cref="P:AForge.Math.PerlinNoise.InitAmplitude"/> * <see cref="P:AForge.Math.PerlinNoise.Persistence"/><sup>i</sup>,
             where i = [0, <see cref="P:AForge.Math.PerlinNoise.Octaves"/>).
             </para>
             
             <para>Default value is set to <b>1</b>.</para>
             </remarks>
            
        </member>
        <member name="P:AForge.Math.PerlinNoise.Persistence">
             <summary>
             Persistence value.
             </summary>
            
             <remarks><para>The property sets so called persistence value, which controls the way
             how <see cref="P:AForge.Math.PerlinNoise.InitAmplitude">amplitude</see> is calculated for each octave comprising
             the Perlin noise function.</para>
             
             <para>Default value is set to <b>0.65</b>.</para>
             </remarks>
            
        </member>
        <member name="P:AForge.Math.PerlinNoise.Octaves">
            <summary>
            Number of octaves, [1, 32].
            </summary>
            
            <remarks><para>The property sets the number of noise functions, which sum up the resulting
            Perlin noise function.</para>
            
            <para>Default value is set to <b>4</b>.</para>
            </remarks>
            
        </member>
        <member name="T:AForge.Math.Metrics.CosineDistance">
            <summary>
            Cosine distance metric. 
            </summary>
            
            <remarks><para>This class represents the cosine distance metric (1 - cosine similarity)
            <a href="http://en.wikipedia.org/wiki/Cosine_similarity"></a>.
            </para>
            
            <para>Sample usage:</para>
            <code>
            // instantiate new distance class
            CosineDistance dist = new CosineDistance(); 
            // create two vectors for inputs
            double[] p = new double[] { 2.5, 3.5, 3.0, 3.5, 2.5, 3.0 };
            double[] q = new double[] { 3.0, 3.5, 1.5, 5.0, 3.5, 3.0 };
            // get distance between the two vectors
            double distance = dist.GetDistance( p, q );
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Metrics.CosineDistance.GetDistance(System.Double[],System.Double[])">
            <summary>
            Returns distance between two N-dimensional double vectors.
            </summary>
            
            <param name="p">1st point vector.</param>
            <param name="q">2nd point vector.</param>
            
            <returns>Returns Cosine distance between two supplied vectors.</returns>
            
            <exception cref="T:System.ArgumentException">Thrown if the two vectors are of different dimensions (if specified
            array have different length).</exception>
            
        </member>
        <member name="T:AForge.Math.Complex">
            <summary>
            Complex number wrapper class.
            </summary>
            
            <remarks><para>The class encapsulates complex number and provides
            set of different operators to manipulate it, lake adding, subtractio,
            multiplication, etc.</para>
            
            <para>Sample usage:</para>
            <code>
            // define two complex numbers
            Complex c1 = new Complex( 3, 9 );
            Complex c2 = new Complex( 8, 3 );
            // sum
            Complex s1 = Complex.Add( c1, c2 );
            Complex s2 = c1 + c2;
            Complex s3 = c1 + 5;
            // difference
            Complex d1 = Complex.Subtract( c1, c2 );
            Complex d2 = c1 - c2;
            Complex d3 = c1 - 2;
            </code>
            </remarks>
            
        </member>
        <member name="F:AForge.Math.Complex.Re">
            <summary>
            Real part of the complex number.
            </summary>
        </member>
        <member name="F:AForge.Math.Complex.Im">
            <summary>
            Imaginary part of the complex number.
            </summary>
        </member>
        <member name="F:AForge.Math.Complex.Zero">
            <summary>
             A double-precision complex number that represents zero.
            </summary>
        </member>
        <member name="F:AForge.Math.Complex.One">
            <summary>
             A double-precision complex number that represents one.
            </summary>
        </member>
        <member name="F:AForge.Math.Complex.I">
            <summary>
             A double-precision complex number that represents the squere root of (-1).
            </summary>
        </member>
        <member name="M:AForge.Math.Complex.#ctor(System.Double,System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Complex"/> class.
            </summary>
            
            <param name="re">Real part.</param>
            <param name="im">Imaginary part.</param>
            
        </member>
        <member name="M:AForge.Math.Complex.#ctor(AForge.Math.Complex)">
            <summary>
            Initializes a new instance of the <see cref="T:AForge.Math.Complex"/> class.
            </summary>
            
            <param name="c">Source complex number.</param>
            
        </member>
        <member name="M:AForge.Math.Complex.Add(AForge.Math.Complex,AForge.Math.Complex)">
            <summary>
            Adds two complex numbers.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="b">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the sum of specified
            complex numbers.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.Add(AForge.Math.Complex,System.Double)">
            <summary>
            Adds scalar value to a complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="s">A scalar value.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the sum of specified
            complex number and scalar value.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.Add(AForge.Math.Complex,AForge.Math.Complex,AForge.Math.Complex@)">
            <summary>
            Adds two complex numbers and puts the result into the third complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="b">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="result">A <see cref="T:AForge.Math.Complex"/> instance to hold the result.</param>
            
        </member>
        <member name="M:AForge.Math.Complex.Add(AForge.Math.Complex,System.Double,AForge.Math.Complex@)">
            <summary>
            Adds scalar value to a complex number and puts the result into another complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="s">A scalar value.</param>
            <param name="result">A <see cref="T:AForge.Math.Complex"/> instance to hold the result.</param>
            
        </member>
        <member name="M:AForge.Math.Complex.Subtract(AForge.Math.Complex,AForge.Math.Complex)">
            <summary>
            Subtracts one complex number from another.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance to subtract from.</param>
            <param name="b">A <see cref="T:AForge.Math.Complex"/> instance to be subtracted.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the subtraction result (<b>a - b</b>).</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.Subtract(AForge.Math.Complex,System.Double)">
            <summary>
            Subtracts a scalar from a complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance to subtract from.</param>
            <param name="s">A scalar value to be subtracted.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the subtraction result (<b>a - s</b>).</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.Subtract(System.Double,AForge.Math.Complex)">
            <summary>
            Subtracts a complex number from a scalar value.
            </summary>
            
            <param name="s">A scalar value to subtract from.</param>
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance to be subtracted.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the subtraction result (<b>s - a</b>).</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.Subtract(AForge.Math.Complex,AForge.Math.Complex,AForge.Math.Complex@)">
            <summary>
            Subtracts one complex number from another and puts the result in the third complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance to subtract from.</param>
            <param name="b">A <see cref="T:AForge.Math.Complex"/> instance to be subtracted.</param>
            <param name="result">A <see cref="T:AForge.Math.Complex"/> instance to hold the result.</param>
            
        </member>
        <member name="M:AForge.Math.Complex.Subtract(AForge.Math.Complex,System.Double,AForge.Math.Complex@)">
            <summary>
            Subtracts a scalar value from a complex number and puts the result into another complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance to subtract from.</param>
            <param name="s">A scalar value to be subtracted.</param>
            <param name="result">A <see cref="T:AForge.Math.Complex"/> instance to hold the result.</param>
            
        </member>
        <member name="M:AForge.Math.Complex.Subtract(System.Double,AForge.Math.Complex,AForge.Math.Complex@)">
            <summary>
            Subtracts a complex number from a scalar value and puts the result into another complex number.
            </summary>
            
            <param name="s">A scalar value to subtract from.</param>
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance to be subtracted.</param>
            <param name="result">A <see cref="T:AForge.Math.Complex"/> instance to hold the result.</param>
            
        </member>
        <member name="M:AForge.Math.Complex.Multiply(AForge.Math.Complex,AForge.Math.Complex)">
            <summary>
            Multiplies two complex numbers.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="b">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the result of multiplication.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.Multiply(AForge.Math.Complex,System.Double)">
            <summary>
            Multiplies a complex number by a scalar value.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="s">A scalar value.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the result of multiplication.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.Multiply(AForge.Math.Complex,AForge.Math.Complex,AForge.Math.Complex@)">
            <summary>
            Multiplies two complex numbers and puts the result in a third complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="b">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="result">A <see cref="T:AForge.Math.Complex"/> instance to hold the result.</param>
            
        </member>
        <member name="M:AForge.Math.Complex.Multiply(AForge.Math.Complex,System.Double,AForge.Math.Complex@)">
            <summary>
            Multiplies a complex number by a scalar value and puts the result into another complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="s">A scalar value.</param>
            <param name="result">A <see cref="T:AForge.Math.Complex"/> instance to hold the result.</param>
            
        </member>
        <member name="M:AForge.Math.Complex.Divide(AForge.Math.Complex,AForge.Math.Complex)">
            <summary>
            Divides one complex number by another complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="b">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the result.</returns>
            
            <exception cref="T:System.DivideByZeroException">Can not divide by zero.</exception>
            
        </member>
        <member name="M:AForge.Math.Complex.Divide(AForge.Math.Complex,System.Double)">
            <summary>
            Divides a complex number by a scalar value.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="s">A scalar value.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the result.</returns>
            
            <exception cref="T:System.DivideByZeroException">Can not divide by zero.</exception>
            
        </member>
        <member name="M:AForge.Math.Complex.Divide(System.Double,AForge.Math.Complex)">
            <summary>
            Divides a scalar value by a complex number.
            </summary>
            
            <param name="s">A scalar value.</param>
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the result.</returns>
            
            <exception cref="T:System.DivideByZeroException">Can not divide by zero.</exception>
            
        </member>
        <member name="M:AForge.Math.Complex.Divide(AForge.Math.Complex,AForge.Math.Complex,AForge.Math.Complex@)">
            <summary>
            Divides one complex number by another complex number and puts the result in a third complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="b">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="result">A <see cref="T:AForge.Math.Complex"/> instance to hold the result.</param>
            
            <exception cref="T:System.DivideByZeroException">Can not divide by zero.</exception>
            
        </member>
        <member name="M:AForge.Math.Complex.Divide(AForge.Math.Complex,System.Double,AForge.Math.Complex@)">
            <summary>
            Divides a complex number by a scalar value and puts the result into another complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="s">A scalar value.</param>
            <param name="result">A <see cref="T:AForge.Math.Complex"/> instance to hold the result.</param>
            
            <exception cref="T:System.DivideByZeroException">Can not divide by zero.</exception>
            
        </member>
        <member name="M:AForge.Math.Complex.Divide(System.Double,AForge.Math.Complex,AForge.Math.Complex@)">
            <summary>
            Divides a scalar value by a complex number and puts the result into another complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="s">A scalar value.</param>
            <param name="result">A <see cref="T:AForge.Math.Complex"/> instance to hold the result.</param>
            
            <exception cref="T:System.DivideByZeroException">Can not divide by zero.</exception>
            
        </member>
        <member name="M:AForge.Math.Complex.Negate(AForge.Math.Complex)">
            <summary>
            Negates a complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the negated values.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.ApproxEqual(AForge.Math.Complex,AForge.Math.Complex)">
            <summary>
            Tests whether two complex numbers are approximately equal using default tolerance value.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="b">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            
            <returns>Return <see langword="true"/> if the two vectors are approximately equal or <see langword="false"/> otherwise.</returns>
            
            <remarks><para>The default tolerance value, which is used for the test, equals to 8.8817841970012523233891E-16.</para></remarks>
            
        </member>
        <member name="M:AForge.Math.Complex.ApproxEqual(AForge.Math.Complex,AForge.Math.Complex,System.Double)">
            <summary>
            Tests whether two complex numbers are approximately equal given a tolerance value.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="b">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="tolerance">The tolerance value used to test approximate equality.</param>
            
            <remarks><para>The default tolerance value, which is used for the test, equals to 8.8817841970012523233891E-16.</para></remarks>
            
        </member>
        <member name="M:AForge.Math.Complex.Parse(System.String)">
            <summary>
            Converts the specified string to its <see cref="T:AForge.Math.Complex"/> equivalent.
            </summary>
            
            <param name="s">A string representation of a complex number.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance that represents the complex number
            specified by the <paramref name="s"/> parameter.</returns>
            
            <exception cref="T:System.FormatException">String representation of the complex number is not correctly formatted.</exception>
            
        </member>
        <member name="M:AForge.Math.Complex.TryParse(System.String,AForge.Math.Complex@)">
            <summary>
            Try to convert the specified string to its <see cref="T:AForge.Math.Complex"/> equivalent.
            </summary>
            
            <param name="s">A string representation of a complex number.</param>
            
            <param name="result"><see cref="T:AForge.Math.Complex"/> instance to output the result to.</param>
            
            <returns>Returns boolean value that indicates if the parse was successful or not.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.Sqrt(AForge.Math.Complex)">
            <summary>
            Calculates square root of a complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the square root of the specified
            complex number.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.Log(AForge.Math.Complex)">
            <summary>
            Calculates natural (base <b>e</b>) logarithm of a complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the natural logarithm of the specified
            complex number.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.Exp(AForge.Math.Complex)">
            <summary>
            Calculates exponent (<b>e</b> raised to the specified power) of a complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the exponent of the specified
            complex number.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.Sin(AForge.Math.Complex)">
            <summary>
            Calculates Sine value of the complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the Sine value of the specified
            complex number.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.Cos(AForge.Math.Complex)">
            <summary>
            Calculates Cosine value of the complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the Cosine value of the specified
            complex number.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.Tan(AForge.Math.Complex)">
            <summary>
            Calculates Tangent value of the complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the Tangent value of the specified
            complex number.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.GetHashCode">
            <summary>
            Returns the hashcode for this instance.
            </summary>
            
            <returns>A 32-bit signed integer hash code.</returns>
        </member>
        <member name="M:AForge.Math.Complex.Equals(System.Object)">
            <summary>
            Returns a value indicating whether this instance is equal to the specified object.
            </summary>
            
            <param name="obj">An object to compare to this instance.</param>
            
            <returns>Returns <see langword="true"/> if <paramref name="obj"/> is a <see cref="T:AForge.Math.Complex"/> and has the same values as this instance or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.ToString">
            <summary>
            Returns a string representation of this object.
            </summary>
            
            <returns>A string representation of this object.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.op_Equality(AForge.Math.Complex,AForge.Math.Complex)">
            <summary>
            Tests whether two specified complex numbers are equal.
            </summary>
            
            <param name="u">The left-hand complex number.</param>
            <param name="v">The right-hand complex number.</param>
            
            <returns>Returns <see langword="true"/> if the two complex numbers are equal or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.op_Inequality(AForge.Math.Complex,AForge.Math.Complex)">
            <summary>
            Tests whether two specified complex numbers are not equal.
            </summary>
            
            <param name="u">The left-hand complex number.</param>
            <param name="v">The right-hand complex number.</param>
            
            <returns>Returns <see langword="true"/> if the two complex numbers are not equal or <see langword="false"/> otherwise.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.op_UnaryNegation(AForge.Math.Complex)">
            <summary>
            Negates the complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/>  instance.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the negated values.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.op_Addition(AForge.Math.Complex,AForge.Math.Complex)">
            <summary>
            Adds two complex numbers.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="b">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the sum.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.op_Addition(AForge.Math.Complex,System.Double)">
            <summary>
            Adds a complex number and a scalar value.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="s">A scalar value.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the sum.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.op_Addition(System.Double,AForge.Math.Complex)">
            <summary>
            Adds a complex number and a scalar value.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="s">A scalar value.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the sum.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.op_Subtraction(AForge.Math.Complex,AForge.Math.Complex)">
            <summary>
            Subtracts one complex number from another complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="b">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the difference.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.op_Subtraction(AForge.Math.Complex,System.Double)">
            <summary>
            Subtracts a scalar value from a complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="s">A scalar value.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the difference.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.op_Subtraction(System.Double,AForge.Math.Complex)">
            <summary>
            Subtracts a complex number from a scalar value.
            </summary>
            
            <param name="s">A scalar value.</param>
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the difference.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.op_Multiply(AForge.Math.Complex,AForge.Math.Complex)">
            <summary>
            Multiplies two complex numbers.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="b">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the result of multiplication.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.op_Multiply(System.Double,AForge.Math.Complex)">
            <summary>
            Multiplies a complex number by a scalar value.
            </summary>
            
            <param name="s">A scalar value.</param>
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the result of multiplication.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.op_Multiply(AForge.Math.Complex,System.Double)">
            <summary>
            Multiplies a complex number by a scalar value.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="s">A scalar value.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the result of multiplication.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.op_Division(AForge.Math.Complex,AForge.Math.Complex)">
            <summary>
            Divides one complex number by another complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="b">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            
            <returns>A new Complex instance containing the result.</returns>
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the result of division.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.op_Division(AForge.Math.Complex,System.Double)">
            <summary>
            Divides a complex number by a scalar value.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="s">A scalar value.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the result of division.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.op_Division(System.Double,AForge.Math.Complex)">
            <summary>
            Divides a scalar value by a complex number.
            </summary>
            
            <param name="a">A <see cref="T:AForge.Math.Complex"/> instance.</param>
            <param name="s">A scalar value.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing the result of division.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.op_Explicit(System.Single)~AForge.Math.Complex">
            <summary>
            Converts from a single-precision real number to a complex number. 
            </summary>
            
            <param name="value">Single-precision real number to convert to complex number.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing complex number with
            real part initialized to the specified value.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.op_Explicit(System.Double)~AForge.Math.Complex">
            <summary>
            Converts from a double-precision real number to a complex number. 
            </summary>
            
            <param name="value">Double-precision real number to convert to complex number.</param>
            
            <returns>Returns new <see cref="T:AForge.Math.Complex"/> instance containing complex number with
            real part initialized to the specified value.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.System#ICloneable#Clone">
            <summary>
            Creates an exact copy of this <see cref="T:AForge.Math.Complex"/> object.
            </summary>
            
            <returns>Returns clone of the complex number.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.Clone">
            <summary>
            Creates an exact copy of this <see cref="T:AForge.Math.Complex"/> object.
            </summary>
            
            <returns>Returns clone of the complex number.</returns>
            
        </member>
        <member name="M:AForge.Math.Complex.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo"/> with the data needed to serialize the target object.
            </summary>
            
            <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> to populate with data. </param>
            <param name="context">The destination (see <see cref="T:System.Runtime.Serialization.StreamingContext"/>) for this serialization.</param>
            
        </member>
        <member name="P:AForge.Math.Complex.Magnitude">
            <summary>
            Magnitude value of the complex number.
            </summary>
            
            <remarks><para>Magnitude of the complex number, which equals to <b>Sqrt( Re * Re + Im * Im )</b>.</para></remarks>
            
        </member>
        <member name="P:AForge.Math.Complex.Phase">
            <summary>
            Phase value of the complex number.
            </summary>
            
            <remarks><para>Phase of the complex number, which equals to <b>Atan( Im / Re )</b>.</para></remarks>
            
        </member>
        <member name="P:AForge.Math.Complex.SquaredMagnitude">
            <summary>
            Squared magnitude value of the complex number.
            </summary>
        </member>
        <member name="T:AForge.Math.Metrics.HammingDistance">
            <summary>
            Hamming distance metric. 
            </summary>
            
            <remarks><para>This class represents the 
            <a href="http://en.wikipedia.org/wiki/Hamming_Distance">Hamming distance metric</a>.</para>
            
            <para>Sample usage:</para>
            <code>
            // instantiate new distance class
            HammingDistance dist = new HammingDistance( ); 
            // create two vectors for inputs
            double[] p = new double[] { 2.5, 3.5, 3.0, 3.5, 2.5, 3.0 };
            double[] q = new double[] { 3.0, 3.5, 1.5, 5.0, 3.5, 3.0 };
            // get distance between the two vectors
            double distance = dist.GetDistance( p, q );
            </code>
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Metrics.HammingDistance.GetDistance(System.Double[],System.Double[])">
            <summary>
            Returns distance between two N-dimensional double vectors.
            </summary>
            
            <param name="p">1st point vector.</param>
            <param name="q">2nd point vector.</param>
            
            <returns>Returns Hamming distance between two supplied vectors.</returns>
            
            <exception cref="T:System.ArgumentException">Thrown if the two vectors are of different dimensions (if specified
            array have different length).</exception>
            
        </member>
        <member name="T:AForge.Math.Metrics.EuclideanSimilarity">
            <summary>
            Euclidean similarity metric. 
            </summary>
            
            <remarks><para>This class represents the 
            <a href="http://en.wikipedia.org/wiki/Euclidean_distance">Euclidean Similarity metric</a>,
            which is calculated as 1.0 / ( 1.0 + EuclideanDistance ).</para>
            
            <para>Sample usage:</para>
            <code>
            // instantiate new similarity class
            EuclideanSimilarity sim = new EuclideanSimilarity( ); 
            // create two vectors for inputs
            double[] p = new double[] { 2.5, 3.5, 3.0, 3.5, 2.5, 3.0 };
            double[] q = new double[] { 3.0, 3.5, 1.5, 5.0, 3.5, 3.0 };
            // get simirarity between the two vectors
            double similarityScore = sim.GetSimilarityScore( p, q );
            </code>    
            </remarks>
            
        </member>
        <member name="M:AForge.Math.Metrics.EuclideanSimilarity.GetSimilarityScore(System.Double[],System.Double[])">
            <summary>
            Returns similarity score for two N-dimensional double vectors. 
            </summary>
            
            <param name="p">1st point vector.</param>
            <param name="q">2nd point vector.</param>
            
            <returns>Returns Euclidean similarity between two supplied vectors.</returns>
            
            <exception cref="T:System.ArgumentException">Thrown if the two vectors are of different dimensions (if specified
            array have different length).</exception>
            
        </member>
    </members>
</doc>
