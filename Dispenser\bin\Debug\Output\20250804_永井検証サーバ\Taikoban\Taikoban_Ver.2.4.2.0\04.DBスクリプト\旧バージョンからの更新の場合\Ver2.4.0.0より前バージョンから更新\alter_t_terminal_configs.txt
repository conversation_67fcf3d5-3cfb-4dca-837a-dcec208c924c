ALTER TABLE [dbo].[t_terminal_configs]
ADD 
	[action_on_ext_auth_error] [int] NOT NULL CONSTRAINT [DF_t_terminal_configs_action_on_ext_auth_error]  DEFAULT ((0)),
	[default_sas2_key_combination] [nvarchar](max) NOT NULL CONSTRAINT [DF_t_terminal_configs_default_sas2_key_combination]  DEFAULT ('ODQ=,NzM=,NzU='),
	[default_sas2_one_factor_auth_policy] [int] NOT NULL CONSTRAINT [DF_t_terminal_configs_default_sas2_one_factor_auth_policy]  DEFAULT ((3))
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'外部認証失敗時の動作 0:認証エラー 1:パスワード＋コメント入力 2:コメント入力' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'action_on_ext_auth_error'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'標準SASキーコンビネーション2' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'default_sas2_key_combination'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'標準SAS2発生時の一要素認証ポリシー 0:許可しない（2要素認証必須） 1:生体認証のみ 2:生体認証を優先するがパスワード認証も可 3:パスワードのみ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminal_configs', @level2type=N'COLUMN',@level2name=N'default_sas2_one_factor_auth_policy'
GO


