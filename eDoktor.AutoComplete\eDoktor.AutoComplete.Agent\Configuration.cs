﻿// ApplicationSettings.cs

namespace eDoktor.AutoComplete.Agent
{
	static class Configuration
	{
		#region Fields
		private const int DefaultMuteTimeout = 5000;
		#endregion

		#region Properties
		public static System.TimeSpan LoginWindowMonitorMuteTimeout { get; set; }
		#endregion

		#region Constructors
		static Configuration()
		{
			try
			{
				LoginWindowMonitorMuteTimeout = System.TimeSpan.FromMilliseconds(Common.Configuration.AppSetting("LoginWindowMonitorMuteTimeout", DefaultMuteTimeout));
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion

		#region Private Methods
		private static bool VerifyShortcutKeyCombination(int[] shortcutKeys)
		{
			if ((shortcutKeys == null) || (shortcutKeys.Length == 0))
			{
				return false;
			}

			foreach (int shortcutKey in shortcutKeys)
			{
				if (shortcutKey != 0)
				{
					return true;
				}
			}

			return false;
		}
		#endregion
	}
}
