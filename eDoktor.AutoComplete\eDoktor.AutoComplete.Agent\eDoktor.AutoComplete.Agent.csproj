﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{840131F0-9341-4F52-877A-C78EBD4D391E}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>eDoktor.AutoComplete.Agent</RootNamespace>
    <AssemblyName>eDoktor.AutoComplete.Agent</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>None</ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\bin\$(Platform)\$(Configuration)\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>..\bin\$(Platform)\$(Configuration)\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>false</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <Win32Resource>app.res</Win32Resource>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>edoktor.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="eDoktor.Auth.Interprocess">
      <HintPath>..\Dependencies\eDoktor\x86\Release\eDoktor.Auth.Interprocess.dll</HintPath>
    </Reference>
    <Reference Include="eDoktor.Common, Version=1.0.0.0, Culture=neutral, PublicKeyToken=0ff7bf58c14a5b51, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\eDoktor\$(Platform)\$(Configuration)\eDoktor.Common.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="UIAutomationClient" />
    <Reference Include="UIAutomationTypes" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ApplicationContext.cs" />
    <Compile Include="AutomationElementScope.cs" />
    <Compile Include="AutomationHelper.cs" />
    <Compile Include="ButtonClickMethodType.cs" />
    <Compile Include="Configuration.cs" />
    <Compile Include="LoginWindow.cs" />
    <Compile Include="LoginWindowConfigurationElement.cs" />
    <Compile Include="LoginWindowConfigurationElementCollection.cs" />
    <Compile Include="LoginWindowMonitor.cs" />
    <Compile Include="NotifyIcon.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="SsoConfigurationSection.cs" />
    <None Include="app.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="app.manifest" />
    <None Include="app.res" />
    <None Include="edoktor.snk" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\eDoktor.AutoCompelte.AuthModule\eDoktor.AutoComplete.AuthModule.csproj">
      <Project>{1d4f08d7-c6d0-4e27-b336-95896bf6ce8b}</Project>
      <Name>eDoktor.AutoComplete.AuthModule</Name>
    </ProjectReference>
    <ProjectReference Include="..\eDoktor.AutoComplete.Interprocess\eDoktor.AutoComplete.Interprocess.csproj">
      <Project>{82fcc6ff-259c-4cb3-b77c-b71557afb297}</Project>
      <Name>eDoktor.AutoComplete.Interprocess</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Resources\taikoban16.ico" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>