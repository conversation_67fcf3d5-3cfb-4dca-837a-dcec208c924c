﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="eDoktor.Taikoban.AccountImporter.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <appSettings>
      <!-- Trace -->
      <add key="EnableTrace" value="true" />
      <add key="EnableDebugTrace" value="true" />
      <add key="EnableDetailedExceptionTrace" value="true" />
      <add key="ContainingFolderPath" value=".\Logs\"/>
      <add key="TraceTermInDays" value="30" />
      
      <!-- ファイル取得方法 0=FTP 1=ローカル 2=共有フォルダ -->
      <add key="FileGetMethod" value="1"/>
      <add key="MaxRetryCount" value="3" />
      <add key="RetryInterval" value="3" />
      <add key="FilePath" value="D:\eDoktor\TaikobanIF\Temp\EYTBRY.txt" />
      <add key="SourcePath" value="D:\eDoktor\TaikobanIF\EYTBRY.txt" />
      <add key="FileBackupDirectory" value="D:\eDoktor\TaikobanIF\Temp\Backup\" />
      <add key="FileBackupPath" value="D:\eDoktor\TaikobanIF\Temp\Backup\EYTBRY_%DATETIME%.txt" />
      <add key="FtpUri" value="ftp://localhost/account.txt" />
      <add key="FtpUser" value="Z8+CJkJ6ne6pu+aCouZdiw==" />
      <add key="FtpPassword" value="Z8+CJkJ6ne6pu+aCouZdiw==" />
      <add key="FileEncode" value="Shift-JIS" />
      <add key="LineLength" value="2005" />
      <add key="LogonTo" value="iims3" />
      <add key="PractitionerID" value="1" />
      <add key="DefaultAccountGroupID" value="1" />
      <add key="DefaultPrivilegeTemplateID" value="1" />
      <add key="ErrorSource" value="8" />
      <add key="ErrorWhenJobInsert" value="False" />
    </appSettings>
    <connectionStrings>
      <!-- 開発サーバー -->
      <add name="Taikoban_192_168_1_10"  connectionString="gwfgQ0W5vk1ziBVLg2sp6txnAa4K45HiRnfZUEJ8TjTNM74RhfPnTgLb2pmS/+QSyGkfYEccnjKluKkb7joLjge+LV63IA3YQpng923z24wm3s33BB0NA3pwzCjbgyJ9HoKKJgfwYVBXWeYlrEjSg7/YN23Hs6E7EZoe10jCYP0NFmxAwfjtXMd+nlfHRO/Xw3352dQipWi3l0M4PQekhQ==" providerName="System.Data.SqlClient" />
      <!--<add name="Taikoban" connectionString="gwfgQ0W5vk1ziBVLg2sp6qc55l3t+X+25DlKmddmxf9AkiOml+sRj17UShZcj0vynr74EgSM7ic9PHQ7m6aa3VFXCZwDuwYWYJ9eWnN2W6Slew0r60ASxWSlT8YzoDFZsOk4f9Wnx0g32fuUJrb//5lO6RXMBvwQznga2hshLSM=" providerName="System.Data.SqlClient" /> -->
      <!--<add name="Taikoban" connectionString="gwfgQ0W5vk1ziBVLg2sp6kolQQDUtnZfwiQMcQUL4TvQeH2uGdrBbOPMAqCcu73YYccBh6eL8+TJ1wRLkfnL44FpoSUTJd41G2g1kVC35Z7jcMH0cYjKRgoE5fPjRf0Roz8Lv6yQX7HP/urhRf714Lkzu0YxD1ByGB02+CWm550=" providerName="System.Data.SqlClient" /> -->
      <add name="Taikoban" connectionString="gwfgQ0W5vk1ziBVLg2sp6kQaf1h8rCH+iff39UGewcPXxkJAaSN/qzg/lk/wEmleJh8UzMiIyIyb4rXPAwl0tvOcTW1cQAtfrpokybLub+S0qM/5jRxIHVCxdTBNk8h4tC10irpPj0rX5pu2VIa4x7GoUJdUWVdSyOBVPjuw1ik=" providerName="System.Data.SqlClient" />
      <add name="Taikoban_Test" connectionString="gwfgQ0W5vk1ziBVLg2sp6h70uykkrb0siW3s50qVkPhM3bHh+ztsyJUWD9b90Bx+LkraoqNJRw1tDoU7TRtLaSjiDj58PTTx134pnwH+0dmyW8p9VbfcD/QabN0bnTk41a+n+9P+GE4MYvaFcb5L4fpuYPnuEML3X+kyWVM+fMc=" providerName="System.Data.SqlClient" />
    </connectionStrings>
    <applicationSettings>
        <eDoktor.Taikoban.AccountImporter.Properties.Settings>
            <setting name="ConnectionStringLabel" serializeAs="String">
                <value>Taikoban_Test</value>
            </setting>
        </eDoktor.Taikoban.AccountImporter.Properties.Settings>
    </applicationSettings>
</configuration>