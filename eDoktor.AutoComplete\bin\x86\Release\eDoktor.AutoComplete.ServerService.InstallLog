﻿アセンブリ 'D:\開発\TaikobanV3\02.開発\01.ソース\AutoComplete\eDoktor.AutoComplete\bin\x86\Release\eDoktor.AutoComplete.ServerService.exe' をインストールしています。
該当するパラメーター:
   logtoconsole = 
   logfile = D:\開発\TaikobanV3\02.開発\01.ソース\AutoComplete\eDoktor.AutoComplete\bin\x86\Release\eDoktor.AutoComplete.ServerService.InstallLog
   assemblypath = D:\開発\TaikobanV3\02.開発\01.ソース\AutoComplete\eDoktor.AutoComplete\bin\x86\Release\eDoktor.AutoComplete.ServerService.exe
サービス 'eDoktor.AutoComplete.ServerService' をインストールしています...
サービス 'eDoktor.AutoComplete.ServerService' は正常にインストールされました。
EventLog ソース eDoktor.AutoComplete.ServerService をログ Application に作成しています...
アセンブリ 'D:\開発\TaikobanV3\02.開発\01.ソース\AutoComplete\eDoktor.AutoComplete\bin\x86\Release\eDoktor.AutoComplete.ServerService.exe' をコミットしています。
該当するパラメーター:
   logtoconsole = 
   logfile = D:\開発\TaikobanV3\02.開発\01.ソース\AutoComplete\eDoktor.AutoComplete\bin\x86\Release\eDoktor.AutoComplete.ServerService.InstallLog
   assemblypath = D:\開発\TaikobanV3\02.開発\01.ソース\AutoComplete\eDoktor.AutoComplete\bin\x86\Release\eDoktor.AutoComplete.ServerService.exe
アセンブリ 'D:\開発\TaikobanV3\02.開発\01.ソース\AutoComplete\eDoktor.AutoComplete\bin\x86\Release\eDoktor.AutoComplete.ServerService.exe' をアンインストールしています。
該当するパラメーター:
   logtoconsole = 
   logfile = D:\開発\TaikobanV3\02.開発\01.ソース\AutoComplete\eDoktor.AutoComplete\bin\x86\Release\eDoktor.AutoComplete.ServerService.InstallLog
   assemblypath = D:\開発\TaikobanV3\02.開発\01.ソース\AutoComplete\eDoktor.AutoComplete\bin\x86\Release\eDoktor.AutoComplete.ServerService.exe
EventLog ソース eDoktor.AutoComplete.ServerService を削除しています。
サービス eDoktor.AutoComplete.ServerService をシステムから削除しています...
サービス 'eDoktor.AutoComplete.ServerService' は正常にシステムから削除されました。
サービス 'eDoktor.AutoComplete.ServerService' を停止しようとしました。
