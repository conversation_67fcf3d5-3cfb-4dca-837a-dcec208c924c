﻿// TcpClient.cs

namespace eDoktor.AutoComplete.Interprocess
{
	public class TcpClient : Common.TcpClient
	{
		#region Fields
		public static readonly int DefaultTransceiveMillisecondsTimeout = 5000;
		private readonly object SyncRoot = new object();
		private volatile int _sequence;
		#endregion

		#region Properties
		public string HostName { get; private set; }
		#endregion

		#region Public Methods
		public Packet Transceive(Packet packet)
		{
			return Transceive(packet, DefaultTransceiveMillisecondsTimeout);
		}

		public Packet Transceive(Packet packet, int millisecondsTimeout)
		{
			packet.Sequence = (ushort)GetNextSequence();
			return Transceive(packet, delegate(Common.Packet receivedPacket) { return (((receivedPacket as Packet).Command == packet.Command) && ((receivedPacket as Packet).Sequence == packet.Sequence)); }, millisecondsTimeout) as Packet;
		}
		#endregion

		#region Protected Methods
		protected override void OnSocketBound()
		{
			try
			{
				base.OnSocketBound();
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		protected override Common.Packet[] SplitReceivedDataIntoPackets(byte[] receivedData, out byte[] fragment)
		{
			return Packet.SplitBytesIntoPackets(receivedData, out fragment);
		}

		protected override void OnPacketReceived(Common.Packet packet)
		{
			Packet receivedPacket = packet as Packet;

			if (Bound)
			{
				string hostName = null;

				using (Common.TimedLock.Lock(SyncRoot))
				{
					if (receivedPacket.Command == Command.ClientToServerAutoCompleteSettingQuery)
					{
					}
				}
			}

			base.OnPacketReceived(packet);
		}
		#endregion

		#region Private Methods
		protected int GetNextSequence()
		{
			using (Common.TimedLock.Lock(SyncRoot))
			{
				if (++_sequence > Packet.MaxSequence)
				{
					_sequence = Packet.MinSequence;
				}

				Common.Trace.OutputDebugTrace("{0} sequence={1}", RemoteEndPoint, _sequence);

				return _sequence;
			}
		}
		#endregion
	}
}
