﻿// AuthModuleLoader.cs

#define UseProxy	// プロキシを使用するかインターフェイスを使用するか

using System;
using System.IO;
using System.Reflection;

namespace eDoktor.AutoComplete.AuthModule
{
	public class AuthModuleLoader
	{
		#region Fields
		private readonly object SyncRoot = new object();
		private volatile bool _abortRequested;
#if UseProxy
		private AuthModuleProxy _instance;
#else
		private IAuthModule _instance;
#endif
		#endregion

		#region Public Methods
		public AuthResponse PerformAuthentication(string assemblyNameOrPath, string typeName, AuthRequest authRequest)
		{
			AuthResponse authResponse = null;
			AppDomain appDomain = null;

			try
			{
				using (Common.TimedLock.Lock(SyncRoot))
				{
					if (_instance != null)
					{
						throw new InvalidOperationException();
					}

					if (_abortRequested)
					{
						return new AuthResponse() { Result = AuthResultType.CallerAborted };
					}

					string appDomainName = Guid.NewGuid().ToString("N");
					appDomain = AppDomain.CreateDomain(appDomainName);
#if UseProxy
					var proxyType = typeof(AuthModuleProxy);
					_instance = appDomain.CreateInstanceAndUnwrap(Assembly.GetExecutingAssembly().FullName, proxyType.FullName) as AuthModuleProxy;
#else
					if (File.Exists(assemblyNameOrPath))
					{
						var targetAssembly = Assembly.LoadFile(assemblyNameOrPath);
						var targetType = targetAssembly.GetType(typeName);
						_instance = appDomain.CreateInstanceAndUnwrap(targetAssembly.FullName, targetType.FullName) as IAuthModule;
					}
					else
					{
						_instance = appDomain.CreateInstanceAndUnwrap(assemblyNameOrPath, typeName) as IAuthModule;
					}
#endif
				}

#if UseProxy
				var tempAuthResponse = _instance.PerformAuthentication(assemblyNameOrPath, typeName, authRequest);
#else

				var tempAuthResponse = _instance.PerformAuthentication(authRequest);
#endif
                // 別のAppDomainで生成されたオブジェクトをそのまま返すとメソッドを抜けた時点でAppDomainが破棄されているため呼び出し元でアクセスした際に例外が発生する
                // ▼ MODIFY ログインボタンを自動押下しない対応 2023/01/01 eDoktor Y.Kihara
                //authResponse = (tempAuthResponse != null) ? new AuthResponse() { Result = tempAuthResponse.Result, Methods = tempAuthResponse.Methods, UserName = tempAuthResponse.UserName } : new AuthResponse() { Result = AuthResultType.SystemFault };
                authResponse = (tempAuthResponse != null) ? new AuthResponse() { Result = tempAuthResponse.Result, Methods = tempAuthResponse.Methods, UserName = tempAuthResponse.UserName, DonotAutoLogin = tempAuthResponse.DonotAutoLogin } : new AuthResponse() { Result = AuthResultType.SystemFault };
                // ▲ MODIFY ログインボタンを自動押下しない対応 2023/01/01 eDoktor Y.Kihara
			}
			finally
			{
				using (Common.TimedLock.Lock(SyncRoot))
				{
					_abortRequested = false;
					_instance = null;

					if (appDomain != null)
					{
						AppDomain.Unload(appDomain);
					}
				}
			}

			return authResponse;
		}

		public void Abort()
		{
			try
			{
				using (Common.TimedLock.Lock(SyncRoot))
				{
					if (_instance != null)
					{
						_instance.Abort();
					}
					else
					{
						_abortRequested = true;
					}
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion
	}
}
