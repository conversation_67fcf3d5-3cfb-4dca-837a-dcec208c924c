﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="eDoktor.Taikoban.TemporaryCardIssuer.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <applicationSettings>
        <eDoktor.Taikoban.TemporaryCardIssuer.Properties.Settings>
            <setting name="ConnectionStringSettingsLabel" serializeAs="String">
                <value>Taikoban</value>
            </setting>
        </eDoktor.Taikoban.TemporaryCardIssuer.Properties.Settings>
    </applicationSettings>
  <connectionStrings>
    <!-- 開発テスト時 -->
    <add name="Taikoban" connectionString="sOEqwRggeOs5J+mrA1PTQP03iS2JvKkkwovlMRimh7NIGBkA6vsDZOSli1ja2eqMXHwU1svdQpoPpl+cAiF71W5rxEQCPpq4d1gvrg7XgSWUOxpzgXIYISoUrCN+yEURta0qlL+UpBogoGF6fBt4Nmo40KsnimIn6wmRkbT0ScNmukl9T8237Gw5Z3A5jBlOw71SlKGl0maMbRVGzGH8J5zm175t4SnPg1pNJkVvp+0=" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <appSettings>
    <!-- Trace -->
    <add key="EnableTrace" value="true" />
    <add key="EnableDebugTrace" value="true" />
    <add key="EnableDetailedExceptionTrace" value="true" />
    <add key="ContainingFolderPath" value=".\logs\%USER%\"/>
    <add key="TraceTermInDays" value="30" />

    <!-- PC/SC START -->
    <add key="EnablePCSC" value="false"/>	<!-- Taikoban経由せずに直接カードリーダーから情報を取得するか:false=Taikoban経由 true=直接取得 -->
    <add key="PCSCVerbose" value="false"/>
    <add key="PCSCWhiteReaderList" value="Sony FeliCa Port/PaSoRi 3.0|SONY FeliCa Port/PaSoRi 4.0"/>
    <add key="PCSCBlackReaderList" value=""/>
    <add key="PCSCMinimumPollingInterval" value="3000"/>
    <add key="PCSCAccessStartedEventMillisecondsTimeout" value="0"/>
    <!-- HPKI -->
    <add key="EnableHPKI" value="false"/>
    <add key="HPKIDataReadingDelayMilliseconds" value="1000"/>
    <!-- JPKI -->
    <add key="EnableJPKI" value="false"/>
    <add key="JPKIDataReadingDelayMilliseconds" value="1000"/>
    <!-- PC/SC END -->
    
    <add key="Issue Term" value="2" />
    <add key="SystemId" value="cZDd3TzWvidCm2Dzol5W5Q=="/>
    <add key="SecurityKey"  value="u7LwBKtr8hR5nW0gums7Qa+2exSm4ioGORe9rrAlWD4="/>
    <!-- 年マスクスイッチ(true:マスクする false:マスクしない) -->
    <add key="YearMaskSw" value="true"/>
    <!-- 生年月日未登録時動作(0:エラーとしない 1:エラーとする) -->
    <add key="BirthdayNullAction" value="0"/>
    <!-- 本人確認をするかどうか(false:しない true:する) -->
    <!--<add key="UseIdentification" value="false"/>-->
    <!-- ICカード状態のSytemFalt(Taikobanサーバーと通信は出来ていないがカード状態は読み取れている時のカード状態)を無視するかどうか(true:無視する false:無視しない) -->
    <!-- Taikobanサーバーが無い状態でICカード読み取りを行う時にはtrueを設定するとカードをの内容を読み取れる。 -->
    <add key="IgnoreSystemFault" value="true"/>
    <!-- 起動直後画面 0:処理選択画面 1:貸出画面 2:返却画面 省略値=0 0,1,2以外も0-->
    <add key="InitaialScreen" value="0"/>
    <!-- 固定貸出期間スイッチ(true:固定貸出期間(Issue Termの値) false:貸出期間は可変) 省略値=false-->
    <add key="IsFixedTerm" value="true"/>
    <!-- 貸出画面に貸出期間を表示するかどうかを示す。省略値=false-->
    <add key="RentalPeriodVisibility" value="true"/>
    <!-- 仮カード登録時に本カードを無効化するかどうかを示す。 false:無効化しない true:無効化する 省略時=true-->
    <add key="DisablingOriginalCard" value="false"/>
    <!-- DisablingOriginalCard=falseの時に有効となる設定で貸出カードが期間内でも貸出情報を上書きするかどうかを示す。 false:上書きしない true:上書きする 省略時=false-->
    <add key="RentalInformationOverwrite" value="false"/>
    <!-- RentalInformationOverwrite=trueの時に有効となる設定で自動返却時にメッセージを出力するかどうかを示す。 false:メッセージを出力しない true:メッセージを出力する 省略時=false-->
    <add key="UsingAutomaticReturnMessage" value="false"/>

    <!-- 本人確認方法（0:なし 1:本人画像 2:ID/Pass入力 3:生年月日） -->
    <add key="IdentificationMethod" value="0"/>
    <!-- 本人画像の共有サーバーと共有名 -->
    <add key="FileServerNameOrAddress" value="\\*************\Shared"/>
    <!-- 共有サーバーへのログイン名(暗号化) -->
    <add key="NetworkAccountUserName" value="Z8+CJkJ6ne6pu+aCouZdiw=="/>
    <!-- 共有サーバーへのログインパスワード(暗号化) -->
    <add key="NetworkAccountPassword" value="HT0MYszIsmXTT9IqOeOxNA=="/>
    <!-- 本人画像の共有フォルダパス -->
    <add key="SharedFolderPath" value="\Images\"/>
    <!-- 本人画像のファイル名フォーマット（%ID%は組織内IDで置換） -->
    <add key="FileNameFormat" value="%ID%.jpg"/>
    <!-- 本人画像取得失敗時の表示画像パス(exeと同じフォルダを想定) -->
    <add key="ErrorFilePath" value="ErrorImage.jpg"/>
    
  </appSettings>
</configuration>