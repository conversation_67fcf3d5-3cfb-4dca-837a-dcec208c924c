﻿// AuthModule.cs

using System;
using System.Reflection;
using System.Threading;
using System.Windows.Forms;

namespace eDoktor.AutoComplete.AuthUI
{
	public class AuthModule : AutoComplete.AuthModule.AuthModuleBase
	{
		#region Protected Methods
		protected override AutoComplete.AuthModule.AuthResponse DoPerformAuthentication(AutoComplete.AuthModule.AuthRequest authRequest, CancellationToken cancellationToken)
		{
			if (authRequest == null)
			{
				throw new ArgumentNullException("authRequest");
			}

			if (authRequest.Context == AutoComplete.AuthModule.AuthContextType.CredentialProvider)
			{
				return DoPerformCredentialProviderAuthentication(authRequest, cancellationToken);
			}
			else if (authRequest.Context == AutoComplete.AuthModule.AuthContextType.Desktop)
			{
				return DoPerformDesktopAuthentication(authRequest, cancellationToken);
			}
			else
			{
				throw new ArgumentOutOfRangeException(string.Format("authRequest.Context={0}", authRequest.Context));
			}
		}
		#endregion

		#region Private Methods
		private AutoComplete.AuthModule.AuthResponse DoPerformCredentialProviderAuthentication(AutoComplete.AuthModule.AuthRequest authRequest, CancellationToken cancellationToken)
		{
			AutoComplete.AuthModule.AuthResponse authResponse = null;

			using (var form = new CredentialProviderAuthForm() { AuthRequest = authRequest, CancellationToken = cancellationToken })
			{
				// CredentialProviderの場合はShowDialog()にownerを渡してモーダルにするとCP側のシャットダウンなどのUIが操作を受け付けなくなるのでAuthFormBaseでのowner設定に任せる。
				form.ShowDialog();
				authResponse = form.AuthResponse;
			}

			return (cancellationToken.IsCancellationRequested) ? new AutoComplete.AuthModule.AuthResponse() { Result = AutoComplete.AuthModule.AuthResultType.CallerAborted } : authResponse;
		}

		private AutoComplete.AuthModule.AuthResponse DoPerformDesktopAuthentication(AutoComplete.AuthModule.AuthRequest authRequest, CancellationToken cancellationToken)
		{
			AutoComplete.AuthModule.AuthResponse authResponse = null;

			using (var form = new DesktopAuthForm() { AuthRequest = authRequest, CancellationToken = cancellationToken })
            {
				if (authRequest.OwnerHandle != System.IntPtr.Zero)
				{
					//var owner = NativeWindow.FromHandle(authRequest.OwnerHandle);	// 別プロセスに属するウィンドウハンドルの場合nullが返る
					var owner = new Win32Window() { Handle = authRequest.OwnerHandle };
					form.ShowDialog(owner);
				}
				else
				{
					form.ShowDialog();
				}

				authResponse = form.AuthResponse;
			}

			return (cancellationToken.IsCancellationRequested) ? new AutoComplete.AuthModule.AuthResponse() { Result = AutoComplete.AuthModule.AuthResultType.CallerAborted } : authResponse;
		}
		#endregion
	}
}
