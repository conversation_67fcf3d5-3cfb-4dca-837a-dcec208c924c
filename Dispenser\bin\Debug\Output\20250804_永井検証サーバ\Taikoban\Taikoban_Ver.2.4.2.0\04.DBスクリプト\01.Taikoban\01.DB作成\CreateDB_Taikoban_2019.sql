USE [master]
GO

/****** Object:  Database [$(Param1)]    Script Date: 06/08/2015 09:51:17 ******/
CREATE DATABASE [$(Param1)] ON  PRIMARY 
( NAME = N'$(Param1)', FILENAME = N'D:\Program Files\Microsoft SQL Server\MSSQL15.MSSQLSERVER\MSSQL\DATA\$(Param1).mdf' , SIZE = 5120KB , MAXSIZE = UNLIMITED, FILEGROWTH = 1024KB )
 LOG ON 
( NAME = N'$(Param1)_log', FILENAME = N'D:\Program Files\Microsoft SQL Server\MSSQL15.MSSQLSERVER\MSSQL\DATA\$(Param1)_log.ldf' , SIZE = 2304KB , MAXSIZE = 2048GB , FILEGROWTH = 10%)
GO

ALTER DATABASE [$(Param1)] SET COMPATIBILITY_LEVEL = 100
GO

IF (1 = FULLTEXTSERVICEPROPERTY('IsFullTextInstalled'))
begin
EXEC [$(Param1)].[dbo].[sp_fulltext_database] @action = 'enable'
end
GO

ALTER DATABASE [$(Param1)] SET ANSI_NULL_DEFAULT OFF 
GO

ALTER DATABASE [$(Param1)] SET ANSI_NULLS OFF 
GO

ALTER DATABASE [$(Param1)] SET ANSI_PADDING OFF 
GO

ALTER DATABASE [$(Param1)] SET ANSI_WARNINGS OFF 
GO

ALTER DATABASE [$(Param1)] SET ARITHABORT OFF 
GO

ALTER DATABASE [$(Param1)] SET AUTO_CLOSE OFF 
GO

ALTER DATABASE [$(Param1)] SET AUTO_CREATE_STATISTICS ON 
GO

ALTER DATABASE [$(Param1)] SET AUTO_SHRINK OFF 
GO

ALTER DATABASE [$(Param1)] SET AUTO_UPDATE_STATISTICS ON 
GO

ALTER DATABASE [$(Param1)] SET CURSOR_CLOSE_ON_COMMIT OFF 
GO

ALTER DATABASE [$(Param1)] SET CURSOR_DEFAULT  GLOBAL 
GO

ALTER DATABASE [$(Param1)] SET CONCAT_NULL_YIELDS_NULL OFF 
GO

ALTER DATABASE [$(Param1)] SET NUMERIC_ROUNDABORT OFF 
GO

ALTER DATABASE [$(Param1)] SET QUOTED_IDENTIFIER OFF 
GO

ALTER DATABASE [$(Param1)] SET RECURSIVE_TRIGGERS OFF 
GO

ALTER DATABASE [$(Param1)] SET  DISABLE_BROKER 
GO

ALTER DATABASE [$(Param1)] SET AUTO_UPDATE_STATISTICS_ASYNC OFF 
GO

ALTER DATABASE [$(Param1)] SET DATE_CORRELATION_OPTIMIZATION OFF 
GO

ALTER DATABASE [$(Param1)] SET TRUSTWORTHY OFF 
GO

ALTER DATABASE [$(Param1)] SET ALLOW_SNAPSHOT_ISOLATION OFF 
GO

ALTER DATABASE [$(Param1)] SET PARAMETERIZATION SIMPLE 
GO

ALTER DATABASE [$(Param1)] SET READ_COMMITTED_SNAPSHOT OFF 
GO

ALTER DATABASE [$(Param1)] SET HONOR_BROKER_PRIORITY OFF 
GO

ALTER DATABASE [$(Param1)] SET  READ_WRITE 
GO

ALTER DATABASE [$(Param1)] SET RECOVERY FULL 
GO

ALTER DATABASE [$(Param1)] SET  MULTI_USER 
GO

ALTER DATABASE [$(Param1)] SET PAGE_VERIFY CHECKSUM  
GO

ALTER DATABASE [$(Param1)] SET DB_CHAINING OFF 
GO


