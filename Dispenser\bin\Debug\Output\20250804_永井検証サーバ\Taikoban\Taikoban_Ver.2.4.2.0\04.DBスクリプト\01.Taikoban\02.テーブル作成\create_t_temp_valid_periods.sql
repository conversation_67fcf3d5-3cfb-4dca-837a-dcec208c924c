USE [$(Param1)]
GO

/****** Object:  Table [dbo].[t_temp_valid_periods]    Script Date: 2023/06/11 15:04:01 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_temp_valid_periods]') AND type in (N'U'))
DROP TABLE [dbo].[t_temp_valid_periods]
GO

CREATE TABLE [dbo].[t_temp_valid_periods](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[registered_by] [int] NOT NULL,
	[modification_datetime] [datetime2](7) NOT NULL,
	[modified_by] [int] NOT NULL,
	[account_id] [int] NOT NULL,
	[before_group_id] [int] NOT NULL,
	[after_group_id] [int] NOT NULL,
	[valid_period] [datetime2](7) NOT NULL
) ON [PRIMARY]

GO

ALTER TABLE [dbo].[t_temp_valid_periods] ADD  CONSTRAINT [DF_t_temp_valid_periods_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO

ALTER TABLE [dbo].[t_temp_valid_periods] ADD  CONSTRAINT [DF_t_temp_valid_periods_registered_by]  DEFAULT ((0)) FOR [registered_by]
GO

ALTER TABLE [dbo].[t_temp_valid_periods] ADD  CONSTRAINT [DF_t_temp_valid_periods_modification_datetime]  DEFAULT (getdate()) FOR [modification_datetime]
GO

ALTER TABLE [dbo].[t_temp_valid_periods] ADD  CONSTRAINT [DF_t_temp_valid_periods_modified_by]  DEFAULT ((0)) FOR [modified_by]
GO

ALTER TABLE [dbo].[t_temp_valid_periods] ADD  CONSTRAINT [DF_t_temp_valid_periods_account_id]  DEFAULT ((0)) FOR [account_id]
GO

ALTER TABLE [dbo].[t_temp_valid_periods] ADD  CONSTRAINT [DF_t_temp_valid_periods_before_group_id]  DEFAULT ((0)) FOR [before_group_id]
GO

ALTER TABLE [dbo].[t_temp_valid_periods] ADD  CONSTRAINT [DF_t_temp_valid_periods_after_group_id]  DEFAULT ((0)) FOR [after_group_id]
GO

ALTER TABLE [dbo].[t_temp_valid_periods] ADD  CONSTRAINT [DF_t_temp_valid_periods_valid_period]  DEFAULT (getdate()) FOR [valid_period]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_temp_valid_periods', @level2type=N'COLUMN',@level2name=N'id'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_temp_valid_periods', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_temp_valid_periods', @level2type=N'COLUMN',@level2name=N'registered_by'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_temp_valid_periods', @level2type=N'COLUMN',@level2name=N'modification_datetime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_temp_valid_periods', @level2type=N'COLUMN',@level2name=N'modified_by'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'アカウントのレコードID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_temp_valid_periods', @level2type=N'COLUMN',@level2name=N'account_id'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'変更前のグループのレコードID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_temp_valid_periods', @level2type=N'COLUMN',@level2name=N'before_group_id'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'変更後のグループのレコードID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_temp_valid_periods', @level2type=N'COLUMN',@level2name=N'after_group_id'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'有効期限' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_temp_valid_periods', @level2type=N'COLUMN',@level2name=N'valid_period'
GO