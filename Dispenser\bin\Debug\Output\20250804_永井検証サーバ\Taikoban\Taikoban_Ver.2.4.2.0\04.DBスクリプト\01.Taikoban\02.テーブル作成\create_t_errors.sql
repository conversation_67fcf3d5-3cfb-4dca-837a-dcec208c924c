USE [$(Param1)]
GO
/****** Object:  Table [dbo].[t_errors]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_errors]') AND type in (N'U'))
DROP TABLE [dbo].[t_errors]
GO

CREATE TABLE [dbo].[t_errors](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[registered_by] [int] NOT NULL,
	[modification_datetime] [datetime2](7) NOT NULL,
	[modified_by] [int] NOT NULL,
	[source] [int] NOT NULL,
	[status] [int] NOT NULL,
	[alarm_flg] [int] NOT NULL,
	[message] [nvarchar](max) NOT NULL,
	[notes] [nvarchar](255) NOT NULL,
 CONSTRAINT [PK_t_errors] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_errors] ADD  CONSTRAINT [DF_t_errors_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO
ALTER TABLE [dbo].[t_errors] ADD  CONSTRAINT [DF_t_errors_registered_by]  DEFAULT ((0)) FOR [registered_by]
GO
ALTER TABLE [dbo].[t_errors] ADD  CONSTRAINT [DF_t_errors_modification_datetime]  DEFAULT (getdate()) FOR [modification_datetime]
GO
ALTER TABLE [dbo].[t_errors] ADD  CONSTRAINT [DF_t_errors_modified_by]  DEFAULT ((0)) FOR [modified_by]
GO
ALTER TABLE [dbo].[t_errors] ADD  CONSTRAINT [DF_t_errors_source]  DEFAULT ((0)) FOR [source]
GO
ALTER TABLE [dbo].[t_errors] ADD  CONSTRAINT [DF_t_errors_status]  DEFAULT ((0)) FOR [status]
GO
ALTER TABLE [dbo].[t_errors] ADD  CONSTRAINT [DF_t_errors_alarm_flg]  DEFAULT ((0)) FOR [alarm_flg]
GO
ALTER TABLE [dbo].[t_errors] ADD  CONSTRAINT [DF_t_errors_message]  DEFAULT ('') FOR [message]
GO
ALTER TABLE [dbo].[t_errors] ADD  CONSTRAINT [DF_t_errors_memo]  DEFAULT ('') FOR [notes]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_errors', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_errors', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_errors', @level2type=N'COLUMN',@level2name=N'registered_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_errors', @level2type=N'COLUMN',@level2name=N'modification_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_errors', @level2type=N'COLUMN',@level2name=N'modified_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'エラー発生元 1:Agent 2:Client 3:Server 4:API 5:Portal用DLL 6:CIS用DLL 7:管理コンソール用DLL 8:ID連携 9:レポートサービス 10:管理コンソール 11:ログ削除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_errors', @level2type=N'COLUMN',@level2name=N'source'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ステータス 0:エラー発生 1:処理中 2:警告 3:処理済' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_errors', @level2type=N'COLUMN',@level2name=N'status'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'エラー発報フラグ 0:未発報 1:発報済み' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_errors', @level2type=N'COLUMN',@level2name=N'alarm_flg'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'エラーメッセージ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_errors', @level2type=N'COLUMN',@level2name=N'message'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'注記' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_errors', @level2type=N'COLUMN',@level2name=N'notes'
GO
