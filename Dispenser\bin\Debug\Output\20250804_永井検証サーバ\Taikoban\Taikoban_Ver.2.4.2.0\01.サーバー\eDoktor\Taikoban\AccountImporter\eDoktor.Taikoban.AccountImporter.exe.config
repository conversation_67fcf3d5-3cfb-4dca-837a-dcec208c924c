<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="eDoktor.Taikoban.AccountImporter.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
    </sectionGroup>
    <section name="ldapSettings" type="eDoktor.Taikoban.AccountImporter.Configuration.LdapConfigurationSection,eDoktor.Taikoban.AccountImporter" />
  </configSections>
  <appSettings>
    <!-- Trace -->
    <add key="EnableTrace" value="true"/>
    <add key="EnableDebugTrace" value="true"/>
    <add key="EnableDetailedExceptionTrace" value="true"/>
    <add key="ContainingFolderPath" value=".\Logs\"/>
    <add key="TraceTermInDays" value="30"/>

    <!-- ファイル取得 -->
    <!-- 取得方法 0=FTP 1=ローカル 2=共有フォルダ -->
    <add key="FileGetMethod" value="0"/>
    <add key="EncryptedConnectUser" value="6Pt3jvUnqmIOZgLnD88WCA=="/>
    <add key="EncryptedConnectPassword" value="CWAktncTsGq+LeT0Tf/QNA=="/>
    <add key="SourceFileUri" value="ftp://172.23.1.118/egc/fix/tmp/eybry.csv"/>
    <add key="MaxRetryCount" value="3"/>
    <add key="RetryInterval" value="3000"/>
    <add key="LocalFilePath" value="D:\eDoktor\TaikobanIF\Temp\eybry.csv"/>
    <add key="FileBackupDirectory" value="D:\eDoktor\TaikobanIF\Temp\Backup\"/>
    <add key="FileBackupName" value="eybry_%DATETIME%.csv"/>
    <add key="FileTermInDays" value="30"/>


    <!-- ファイル内容 -->
    <add key="FileEncoding" value="Shift-JIS"/>
    <add key="LineLength" value="1338"/>
    <!--廃止フラグ-->
    <add key="InvalidatedDataOffset" value="1332"/>
    <add key="InvalidatedDataLength" value="1"/>
    <!--停止フラグ-->
    <add key="LogicallyDeletedDataOffset" value="1336"/>
    <add key="LogicallyDeletedDataLength" value="1"/>
    <!--有効期間開始日-->
    <add key="UsageStartDatetimeDataOffset" value="1310"/>
    <add key="UsageStartDatetimeDataLength" value="8"/>
    <add key="UsageStartDatetimeDataFormat" value="yyyyMMdd"/>
    <add key="UsageStartDatetimeDataNullList" value="00000000"/>
    <!--有効期間終了日-->
    <add key="UsageEndDatetimeDataOffset" value="1321"/>
    <add key="UsageEndDatetimeDataLength" value="8"/>
    <add key="UsageEndDatetimeDataFormat" value="yyyyMMdd"/>
    <add key="UsageEndDatetimeDataNullList" value="99999999"/>
    <!--利用者番号-->
    <add key="OrganizationalIdDataOffset" value="1"/>
    <add key="OrganizationalIdDataLength" value="8"/>
    <!--利用者漢字氏名-->
    <add key="NameDataOffset" value="207"/>
    <add key="NameDataLength" value="20"/>
    <!--利用者カナ氏名-->
    <add key="KanaDataOffset" value="230"/>
    <add key="KanaDataLength" value="40"/>
    <!--生年月日-->
    <add key="BirthdateDataOffset" value="316"/>
    <add key="BirthdateDataLength" value="8"/>
    <add key="BirthdateDataFormat" value="yyyyMMdd"/>
    <add key="BirthdateDataNullList" value="        |00000000|99999999"/>
    <!--利用者番号-->
    <add key="LogonIdDataOffset" value="1"/>
    <add key="LogonIdDataLength" value="8"/>
    <!--パスワード-->
    <add key="PasswordDataOffset" value="12"/>
    <add key="PasswordDataLength" value="192"/>
    <add key="PasswordDataIsEncrypted" value="true"/>
    <!--停止フラグ-->
    <add key="PasswordUpdateDatetimeDataOffset" value="1336"/>
    <add key="PasswordUpdateDatetimeDataLength" value="1"/>
    <add key="PasswordUpdateDatetimeDataFormat" value="yyyyMMdd00HHmmss"/>
    <add key="PasswordUpdateDatetimeDataNullList" value="0|1| "/>
    <add key="PasswordUpdateDatetimeIsUsedForNewAccount" value="false"/>
    <!--職種コード-->
    <add key="JobCodeDataOffset" value="437"/>
    <add key="JobCodeDataLength" value="3"/>
    <!--職種コード-->
    <add key="JobNameDataOffset" value="437"/>
    <add key="JobNameDataLength" value="3"/>


    <!-- 固定登録 -->
    <add key="LogonTo" value=""/>
    <add key="PasswordUpdateDatetimeToUseForNewAccount" value="2023-01-01"/>
    <add key="PractitionerID" value="********"/>
    <add key="DefaultAccountGroupID" value="1"/>
    <add key="DefaultPrivilegeTemplateID" value="1"/>
    <add key="ErrorSource" value="8"/>
    <add key="ErrorWhenJobInsert" value="false"/>
    
    <!-- AD連携 -->
    <add key="SyncronizeAd" value="false" />
    <add key="LdapUrl" value="LDAP://172.17.200.31/OU=HIS_USER,OU=02.Users,DC=kainan-his,DC=local" />
    <add key="EncryptedLdapUser" value="6Pt3jvUnqmIOZgLnD88WCA==" />
    <add key="EncryptedLdapPassword" value="IErkYW7lG+jIRjxjXuTh8w==" />
    <add key="LdapAuthenticationTypes" value="545" />
    <add key="SharePath" value="\\172.17.201.175\個人フォルダ" />
    <add key="EncryptedShareUser" value="rCsFR+dPYWZtjzfszbcAfffMNjj+DmKGBBm0H8qR/9U=" />
    <add key="EncryptedSharePassword" value="IErkYW7lG+jIRjxjXuTh8w==" />
    <add key="EncryptedInitialPassword" value="24zbpxH8NfrjjFWi+ilEzw==" />
    <add key="ShareLocalDrive" value="X:" />
    <add key="ShareRetryCount" value="3" />
    <add key="ShareRetryInterval" value="1000" />
    <add key="ShareDomain" value="mit.local" />
    <add key="ShareUnnecessaryUserList" value="Users" />
    <add key="ExcludedAccountList" value="SYSTEM" />
  </appSettings>
  <connectionStrings>
    <!-- Taikobanサーバー -->
    <add name="Taikoban" connectionString="sOEqwRggeOs5J+mrA1PTQP03iS2JvKkkwovlMRimh7NIGBkA6vsDZOSli1ja2eqMXHwU1svdQpoPpl+cAiF71W5rxEQCPpq4d1gvrg7XgSWUOxpzgXIYISoUrCN+yEURta0qlL+UpBogoGF6fBt4Nmo40KsnimIn6wmRkbT0ScNmukl9T8237Gw5Z3A5jBlOw71SlKGl0maMbRVGzGH8J5zm175t4SnPg1pNJkVvp+0=" providerName="System.Data.SqlClient"/>
  </connectionStrings>
  <!-- AD連携属性 -->
  <ldapSettings>
    <contents>
      <content name="cn" offset="0" length="8" propertyName="cn" prefix="" suffix="" isKey="true" />
      <content name="sAMAccountName" offset="0" length="8" propertyName="sAMAccountName" prefix="" suffix="" />
      <content name="displayName" offset="72" length="20" propertyName="displayName" prefix="" suffix="" />
      <content name="department" offset="325" length="10" propertyName="department" prefix="" suffix="" />
      <content name="mail" offset="181" length="80" propertyName="mail" prefix="" suffix="" />
      <content name="userPrincipalName" offset="0" length="8" propertyName="userPrincipalName" prefix="" suffix="@kainan.jaaikosei.or.jp"  />
      <content name="postalCode" offset="0" length="0" propertyName="postalCode" prefix="" suffix="0"  />
    </contents>
  </ldapSettings>
  <applicationSettings>
    <eDoktor.Taikoban.AccountImporter.Properties.Settings>
      <setting name="ConnectionStringLabel" serializeAs="String">
        <value>Taikoban</value>
      </setting>
    </eDoktor.Taikoban.AccountImporter.Properties.Settings>
  </applicationSettings>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.2"/>
  </startup>
</configuration>
