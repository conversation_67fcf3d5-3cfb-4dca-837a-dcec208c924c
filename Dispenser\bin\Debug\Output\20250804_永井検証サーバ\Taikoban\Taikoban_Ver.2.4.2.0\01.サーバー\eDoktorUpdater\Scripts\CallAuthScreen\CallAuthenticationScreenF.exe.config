﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <appSettings>
    <!-- Trace -->
    <add key="EnableTrace" value="true" />
    <add key="EnableDebugTrace" value="true" />
    <add key="EnableDetailedExceptionTrace" value="true" />
    <add key="ContainingFolderPath" value="D:\eDoktor\Taikoban\Logs\CallAuthScreen\"/>
    <add key="TraceTermInDays" value="30" />
    
    <!-- 確認ファイル -->
    <add key="FileCount" value="1" />
    <!-- <add key="FilePath1" value="%STARTUP%\Egmain-GX.lnk" /> -->
    <!-- <add key="FilePath2" value="%STARTUP%\Egmain-GX 開発系.lnk" /> -->
    <!-- <add key="FilePath3" value="%STARTUP%\Egmain-GX 教育系.lnk" /> -->
    <add key="FilePath1" value="D:\eDoktor\Taikoban\Scripts\CallAuthScreen\NoLock.txt" />
    
    <!-- WaitForDesktop -->
    <add key="WaitInterval" value="1000"/>
    <add key="WaitRetry" value ="600"/>
    
    <!-- 動作スイッチ -->
    <add key="CheckAuthenticationStatus" value="true" />  <!-- 認証状態を確認するか true:確認する false:確認しない -->
  </appSettings>
</configuration>