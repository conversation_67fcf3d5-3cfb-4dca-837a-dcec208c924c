﻿// PromptPage2.cs

using System;
using System.Windows.Forms;

namespace eDoktor.AutoComplete.AuthUI
{
	using Common.EventExtensions;

	partial class PromptPage2 : PageBase
	{
		#region Consts
		// ▼ ADD 閉じるボタンの表示設定追加対応 2024/07/22 eDoktor Y.Kihara
		int StartButtonLeft = 80;
		// ▲ ADD 閉じるボタンの表示設定追加対応 2024/07/22 eDoktor Y.Kihara
		int CloseButtonLeft = 325;
		#endregion

		#region Events
		public event EventHandler StartButtonClick;
		public event EventHandler CloseButtonClick;
		#endregion

        // ▼ MODIFY 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
        #region Properties
        public string Message
        {
            get { return messageLabel.Text; }
            set { messageLabel.Text = value; }
        }
        // ▲ MODIFY 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara

		// ▼ ADD 認証開始ボタンの表示設定追加対応 2023/03/31 eDoktor Y.Kihara
		public bool NotAllowedOneFactorAuthentication { get; set; }
		// ▲ ADD 認証開始ボタンの表示設定追加対応 2023/03/31 eDoktor Y.Kihara

		// ▼ ADD 閉じるボタンの表示設定追加対応 2024/07/22 eDoktor Y.Kihara
		public bool HiddenCloseButton { get; set; }
		// ▲ ADD 閉じるボタンの表示設定追加対応 2024/07/22 eDoktor Y.Kihara

		// ▼ MODIFY 標準SASキー対応 2024/07/13 eDoktor Y.Kihara
		public int[] DefaultSasKeyCombination { get; set; }
		// ▲ MODIFY 標準SASキー対応 2024/07/13 eDoktor Y.Kihara
		#endregion

		#region Constructors
		public PromptPage2()
		{
            InitializeComponent();
			ClickThroughControls = new Control[] { this, messageLabel };
		}
		#endregion

		#region Protected Methods
		protected override void OnVisibleChanged(EventArgs e)
		{
			base.OnVisibleChanged(e);

			if (Visible)
			{
				// ▼ MODIFY 閉じるボタンの表示設定追加対応 2024/07/22 eDoktor Y.Kihara
				// ▲ MODIFY 閉じるボタンの表示設定追加対応 2024/07/22 eDoktor Y.Kihara
				// ▼ MODIFY  2024/01/11 eDoktor Y.Kihara
				//startButton.Select();
				//if (NotAllowedOneFactorAuthentication)
				//{
				//	closeButton.Select();
				//}
				//else
				//{
				//	startButton.Select();
				//}
				if (NotAllowedOneFactorAuthentication && !HiddenCloseButton)	// 閉じるボタンのみ表示
				{
					closeButton.Select();
				}
				else if (!NotAllowedOneFactorAuthentication)	// 認証開始ボタン表示
				{
					startButton.Select();
				}
				// ▲ MODIFY  2024/01/11 eDoktor Y.Kihara
			}
		}

		// ▼ ADD 認証開始ボタンの表示設定追加対応 2023/03/31 eDoktor Y.Kihara
		protected override void OnLoad(EventArgs e)
		{
			base.OnLoad(e);

			startButton.Visible = !NotAllowedOneFactorAuthentication;
			// ▼ ADD  2024/01/11 eDoktor Y.Kihara
			startButton.Enabled = !NotAllowedOneFactorAuthentication;
			// ▲ ADD  2024/01/11 eDoktor Y.Kihara
			// ▼ ADD 閉じるボタンの表示設定追加対応 2024/07/22 eDoktor Y.Kihara
			closeButton.Visible = !HiddenCloseButton;
			closeButton.Enabled = !HiddenCloseButton;
			// ▲ ADD 閉じるボタンの表示設定追加対応 2024/07/22 eDoktor Y.Kihara

			// ▼ MODIFY 閉じるボタンの表示設定追加対応 2024/07/22 eDoktor Y.Kihara
			//if (!NotAllowedOneFactorAuthentication)
			//{
			//	closeButton.Left = CloseButtonLeft;
			//}
			//else
			//{
			//	closeButton.Left = (this.Width - closeButton.Width) / 2;
			//}
			if (!NotAllowedOneFactorAuthentication == !HiddenCloseButton)	// 両ボタン表示 or 両ボタン非表示
			{
				startButton.Left = StartButtonLeft;
				closeButton.Left = CloseButtonLeft;
			}
			else if (NotAllowedOneFactorAuthentication && !HiddenCloseButton)	// 閉じるボタンのみ表示
			{
				closeButton.Left = (this.Width - closeButton.Width) / 2;
			}
			else if (!NotAllowedOneFactorAuthentication && HiddenCloseButton)	// 認証開始ボタンのみ表示
			{
				startButton.Left = (this.Width - startButton.Width) / 2;
			}
			// ▲ MODIFY 閉じるボタンの表示設定追加対応 2024/07/22 eDoktor Y.Kihara
		}
		// ▲ ADD 認証開始ボタンの表示設定追加対応 2023/03/31 eDoktor Y.Kihara

		protected override bool ProcessDialogKey(Keys keyData)
		{
			try
			{
				if ((((keyData & Keys.KeyCode) == Keys.Return) || ((keyData & Keys.KeyCode) == Keys.Space)) && ((keyData & (Keys.Alt | Keys.Control)) == Keys.None) && (ActiveControl is IButtonControl))
				{
					(ActiveControl as IButtonControl).PerformClick();
					return true;
				}

				if ((keyData & Keys.KeyCode) == Keys.Escape && !HiddenCloseButton)
				{
					closeButton.PerformClick();
					return true;
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}

			return base.ProcessDialogKey(keyData);
		}
		#endregion

		#region Private Methods
		private void OnStartButtonClick(object sender, EventArgs e)
		{
			try
			{
				StartButtonClick.SafeInvoke(this, e);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnCloseButtonClick(object sender, EventArgs e)
		{
			try
			{
				CloseButtonClick.SafeInvoke(this, e);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion
	}
}
