USE [$(Param1)]
GO

/****** Object:  Trigger [dbo].[TR_update_t_applications]    Script Date: 10/06/2014 15:55:43 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*** CREATE TRIGGER ***/
IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[TR_update_t_applications]') AND type in (N'TR'))
DROP TRIGGER [dbo].[TR_update_t_applications]
GO

CREATE TRIGGER [dbo].[TR_update_t_applications] ON [dbo].[t_applications]
FOR UPDATE
AS 
BEGIN
UPDATE t_applications
SET modification_datetime = GETDATE()
WHERE id IN (SELECT id FROM inserted)
END
RETURN


GO

