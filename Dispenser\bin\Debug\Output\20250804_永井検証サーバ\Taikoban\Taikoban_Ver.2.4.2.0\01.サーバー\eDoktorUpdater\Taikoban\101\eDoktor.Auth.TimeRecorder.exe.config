<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <!-- 出退勤画面設定 -->
    <!-- 表示位置 0:右上 1:右下 2:左上 3:左下 4:中央 9:カスタム -->
    <add key="ShowLocationMode" value="0"/>
    <!-- 表示位置が「9:カスタム」の場合の座標 -->
    <add key="ShowLocationPoint" value="500,500"/>
    <add key="ShowLocationMargin" value="20"/>
    <!-- デザイン(共通) -->
    <add key="YMDFormat" value="yyyy年MM月dd日 (ddd)"/>
    <add key="TimeFormat" value="HH:mm"/>
    <add key="MainHeaderColor" value="255, 255, 255"/>
    <add key="MainBackColor" value="242, 242, 242"/>
    <add key="FontColor" value="77, 77, 77"/>
    <add key="FontName" value="メイリオ"/>
    <add key="ClockInColor" value="36, 159, 159"/>
    <add key="ClockOutColor" value="223, 139, 176"/>
    <!-- デザイン(出退勤選択画面)-->
    <add key="ClockInOutMessage" value="出勤または退勤ボタンをクリックしてください"/>
    <!-- デザイン(ICカード読込み画面) -->
    <add key="IcCardReaderMessage" value="ICカードをかざしてください"/>
    <!-- デザイン(パスワード入力画面) -->
    <add key="PassWordMessage1" value="{UserID} {UserName} {Time}"/>
    <add key="PassWordMessage2" value="本人確認の為パスワードを入力してください"/>
    <!-- デザイン(出退勤完了画面) -->
    <add key="CompleteMessage1" value="{UserID} {UserName}"/>
    <!-- ログ設定 -->
    <add key="EnableDetailedExceptionTrace" value="true"/>
    <add key="EnableTrace" value="true"/>
    <add key="EnableDebugTrace" value="true"/>
    <add key="ContainingFolderPath" value=".\log\"/>
    <add key="TraceTerm" value="30"/>
  </appSettings>
</configuration>