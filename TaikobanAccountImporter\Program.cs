﻿using System;
using System.Text;
using System.IO;
using System.Net;
using System.Data.Common;
using System.Globalization;
using System.Linq;
using System.Collections;
using System.Collections.Generic;
using Microsoft.VisualBasic.FileIO;
using System.Runtime.InteropServices;

namespace eDoktor.Taikoban.AccountImporter
{
    class Program
    {

        #region Consts
        private static readonly string ReservedUserFolder = "%USERFOLDER%";
        private static readonly string ReservedUser = "%USER%";
        private static readonly string SystemName = "SYSTEM";
        private static readonly string AppConstName = "eDoktor.Taikoban.AccountImporter";
        #endregion

        #region Fields
        private static System.Threading.Mutex _objMutex;
        private System.Configuration.ConnectionStringSettings _connectionStringSettings;
        public eDoktor.Common.Database _database { get; set; }

        //private int practitionerID = eDoktor.Common.Configuration.AppSetting("PractitionerID", 0); //実行者ID
        //private String logonTo = eDoktor.Common.Configuration.AppSetting("LogonTo"); //ログオン先
        //private int defaultAccountGroupID = eDoktor.Common.Configuration.AppSetting("DefaultAccountGroupID", 0); //アカウントグループID
        //private int defaultPrivilegeTemplateID = eDoktor.Common.Configuration.AppSetting("DefaultPrivilegeTemplateID", 0); //権限テンプレートID
        //private int errorSource = eDoktor.Common.Configuration.AppSetting("ErrorSource", 0); //エラー発生元
        //private bool errorWhenJobInsert = eDoktor.Common.Configuration.AppSetting("ErrorWhenJobInsert", false); //職種登録時にエラー発生させるか

        #endregion

        #region DLLImports
        [System.Runtime.InteropServices.DllImport("kernel32.dll")]
        private static extern uint WTSGetActiveConsoleSessionId();
        #endregion

        static void Main(string[] args)
        {
            Program prg = new Program();
            prg.Exec();
        }

        private void Exec()
        {
            //データベース接続情報設定
            _connectionStringSettings = eDoktor.Common.Configuration.ConnectionStringSettings(Properties.Settings.Default.ConnectionStringLabel);
            _connectionStringSettings.ConnectionString = Crypto.DecryptString(_connectionStringSettings.ConnectionString);
            _database = new eDoktor.Common.Database(_connectionStringSettings);

            try
            {
                System.Environment.CurrentDirectory = System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);

                // ログ設定
                eDoktor.Common.Trace.EnableTrace = false;   // ログファイルのパスを設定するまでにエラーが発生するとログファイルのパスがデフォルトに確定してしまうのを防ぐ
                string outputPath = eDoktor.Common.Configuration.AppSetting("ContainingFolderPath");

                outputPath = outputPath.Replace(ReservedUserFolder, System.Environment.GetFolderPath(System.Environment.SpecialFolder.Personal));
                outputPath = outputPath.Replace(ReservedUser, System.Environment.UserName);
                if (!System.IO.Directory.Exists(outputPath))
                {
                    try
                    {
                        System.IO.Directory.CreateDirectory(outputPath);
                    }
                    catch
                    {
                    }
                }
                eDoktor.Common.Trace.ContainingFolderPath = outputPath;
                eDoktor.Common.Trace.SafeEnableTrace = eDoktor.Common.Configuration.AppSetting("EnableTrace", true);
                eDoktor.Common.Trace.EnableDebugTrace = eDoktor.Common.Configuration.AppSetting("EnableDebugTrace", true);
                eDoktor.Common.Trace.EnableDetailedExceptionTrace = eDoktor.Common.Configuration.AppSetting("EnableDetailedExceptionTrace", true);
                eDoktor.Common.Trace.TraceTerm = TimeSpan.FromDays(eDoktor.Common.Configuration.AppSetting("TraceTermInDays", eDoktor.Common.Trace.DefaultTraceTermInDays));

                eDoktor.Common.Trace.OutputTrace("アプリケーションが起動しました。:{0}", System.Environment.UserName);

                // 二重起動チェック（二重ならそのまま終了、特にメッセージなし）
                _objMutex = new System.Threading.Mutex(false, AppConstName);
                if (_objMutex.WaitOne(0, false) == false)
                {
                    eDoktor.Common.Trace.OutputTrace("二重起動検出でアプリケーション終了");
                    return;
                }

                // コンフィグ設定
                Configuration.Configuration.Load();

				if (Configuration.Configuration.SyncronizeAd)
				{
					foreach (Configuration.ContentConfigurationElement content in Configuration.Configuration.Contents)
					{
						eDoktor.Common.Trace.OutputDebugTrace("content name={0},offset={1},length={2},prefix={3},suffix={4},isKey={5}", content.Name, content.Offset, content.Lenght, content.Prefix, content.Suffix, content.IsKey);
					}
				}

                Console.WriteLine("Taikobanアカウント連携処理中です...");
                eDoktor.Common.Trace.OutputTrace("アカウント連携処理開始");

                // アカウント連携ファイル取得
                int retry = 0;
                for (retry = 0; retry < Configuration.Configuration.MaxRetryCount; retry++)
                {
                    eDoktor.Common.Trace.OutputTrace("アカウント連携ファイル取得：" + (retry+1) + "回目");
                    if (GetAccountFile(Configuration.Configuration.LocalFilePath))
                    {
                        break;
                    }
                    else
                    {
                        //ダウンロード失敗時は指定秒数待機
                        System.Threading.Thread.Sleep(Configuration.Configuration.RetryInterval);
                    }
                }

                //リトライ回数超過時はエラー出力して終了
                if (retry == Configuration.Configuration.MaxRetryCount)
                {
                    //エラー出力処理
                    InsertError("アカウント連携ファイルの取得に失敗しました。");
                    return;
                }

                // AD連携を行う場合、共有フォルダに接続する
                if (Configuration.Configuration.SyncronizeAd)
                {
                    ConnectShareDirectory();
                }

                //ファイル読み取り＋各種登録・更新処理
                try
                {
                    eDoktor.Common.Trace.OutputTrace("アカウント連携ファイル処理開始");
                    ReadFile(Configuration.Configuration.LocalFilePath);
                }
                catch (Exception ex)
                {
                    //処理内の全エラーをここで処理
                    InsertError(ex.Message);

                    eDoktor.Common.Trace.OutputExceptionTrace(ex);
                }

                // 共有フォルダに接続されている場合、共有フォルダを切断する
                if (System.IO.Directory.Exists(Configuration.Configuration.ShareLocalDrive))
                {
                    DisconnectShareDirectory();
                }

                // ▼ MODIFY ログインできない問題ログ強化 2019/07/01 eDoktor Y.Kihara \\\\
                ////ローカル保存したファイルを削除
                //File.Delete(filePath);
                // ローカル保存したファイルをバックアップ
                string backupPath = Path.Combine(Configuration.Configuration.FileBackupDirectory, Configuration.Configuration.FileBackupName).Replace("%DATETIME%", DateTime.Now.ToString("yyyyMMddHHmmss"));
                if (!string.IsNullOrWhiteSpace(backupPath))
                {
                    File.Move(Configuration.Configuration.LocalFilePath, backupPath);

                    // 30日以前のファイルを削除する
                    string[] files = Directory.GetFiles(Configuration.Configuration.FileBackupDirectory);
                    foreach (string file in files)
                    {
                        // 更新日時を比較する
                        DateTime updateDate = File.GetLastWriteTime(file);

                        if (updateDate.AddDays(Configuration.Configuration.FileTermInDays) < DateTime.Now)
                        {
                            // 削除
                            eDoktor.Common.Trace.OutputTrace("{0}を削除", file);

                            File.Delete(file);
                        }
                    }
                }
                else
                {
                    // バックアップできなかったため、削除
                    File.Delete(Configuration.Configuration.LocalFilePath);
                }
                // ▲ MODIFY ログインできない問題ログ強化 2019/07/01 eDoktor Y.Kihara \\\\

                eDoktor.Common.Trace.OutputTrace("アカウント連携処理終了");

            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputTrace(ex.ToString());
                //Console.WriteLine(ex.ToString());
                //Console.ReadLine();
            }
        }

        #region Private Methods
        //private static void comSpec_OutputDataReceived(object sender, System.Diagnostics.DataReceivedEventArgs e)
        //{
        //    try
        //    {
        //        Console.WriteLine(e.Data);
        //    }
        //    catch (System.Exception ex)
        //    {
        //        eDoktor.Common.Trace.OutputExceptionTrace(ex);
        //    }
        //}

        //private static void comSpec_ErrorDataReceived(object sender, System.Diagnostics.DataReceivedEventArgs e)
        //{
        //    try
        //    {
        //        Console.WriteLine(e.Data);
        //    }
        //    catch (System.Exception ex)
        //    {
        //        eDoktor.Common.Trace.OutputExceptionTrace(ex);
        //    }
        //}

        /// <summary>
        /// アカウント連携ファイル取得
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        private bool GetAccountFile(string filePath)
        {
            try
            {
                switch(Configuration.Configuration.FileGetMethod)
                {
                    case 0: // ftp
                        return FtpDownload(filePath);

                    case 1: // 共有フォルダ
                        return GetShareFile(filePath);

                    case 2: // ローカル
                        return GetLocalFile(filePath);

					//case 3:	// HXDWH
					//	return GetHXDWH(filePath);

                    default:    // 手法の指定が不正
                        eDoktor.Common.Trace.OutputErrorTrace("ファイル取得方法の指定が不正");

                        return false;
                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);

                return false;
            }
        }


        /// <summary>
        /// FTP経由でアカウント連携ファイルを取得
        /// </summary>
        /// <param name="filePath">ローカルのファイル保存場所</param>
        /// <returns></returns>
        private Boolean FtpDownload(String filePath)
        {
            string ftpUri = Configuration.Configuration.SourceFileUri;
            string ftpUser = Configuration.Configuration.ConnectUser;
            string ftpPassword = Configuration.Configuration.ConnectPassword;

			bool result = false;

            try
            {
                // 移動先にファイルが存在する場合は先にリネームする
                if (System.IO.File.Exists(filePath))
                {
                    System.IO.File.Move(filePath, string.Format("{0}_{1:yyyyMMddHHmmssfff}.bak", filePath, DateTime.Now));
                }

                using (var wc = new WebClient())
                {
                    wc.Credentials = new NetworkCredential(ftpUser, ftpPassword);
                    wc.DownloadFile(ftpUri, filePath);
                    result = true;
                }
            }
            catch (Exception ex)
            {
                //例外発生時はダウンロード失敗とみなしスルー
                //最終的な失敗処理は呼び出し元で行う
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }

            return result;
        }

		/// <summary>
		/// 共有フォルダからアカウント連携ファイルを取得
		/// </summary>
		/// <param name="filePath">ローカルのファイル保存場所</param>
		/// <returns></returns>
		private bool GetShareFile(string filePath)
		{
			string ftpUri = Configuration.Configuration.SourceFileUri;
			string ftpUser = Configuration.Configuration.ConnectUser;
			string ftpPassword = Configuration.Configuration.ConnectPassword;

			bool result = false;

			try
			{
				// 移動先にファイルが存在する場合は先にリネームする
				if (System.IO.File.Exists(filePath))
				{
					System.IO.File.Move(filePath, string.Format("{0}_{1:yyyyMMddHHmmssfff}.bak", filePath, DateTime.Now));
				}

				// 接続する
				if (ConnectSourceDirectory())
				{
					System.IO.File.Copy(Configuration.Configuration.SourceFileUri, filePath);

					DisconnectSourceDirectory();
				}

				result = true;
			}
			catch (Exception ex)
			{
				//例外発生時はダウンロード失敗とみなしスルー
				//最終的な失敗処理は呼び出し元で行う
				eDoktor.Common.Trace.OutputExceptionTrace(ex);
			}

			return result;
		}

        /// <summary>
        /// ローカルのアカウント連携ファイルを取得
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        private bool GetLocalFile(string filePath)
        {
            string source = Configuration.Configuration.SourceFileUri;

            try
            {
                // 移動先にファイルが存在する場合は先にリネームする
                if (System.IO.File.Exists(filePath))
                {
                    System.IO.File.Move(filePath, string.Format("{0}_{1:yyyyMMddHHmmssfff}.bak", filePath, DateTime.Now));
                }

                System.IO.File.Copy(source, filePath);

                return true;
            }
            catch (Exception ex)
            {
                //例外発生時はファイル移動失敗とみなしスルー
                //最終的な失敗処理は呼び出しもとで行う
                eDoktor.Common.Trace.OutputExceptionTrace(ex);

                return false;
            }
        }

		/// <summary>
		/// HXDWHからアカウント連携ファイルを取得
		/// </summary>
		/// <param name="filePath"></param>
		/// <returns></returns>
		private bool GetHXDWH(string filePath)
		{
			try
			{
				//データベース接続情報設定
				eDoktor.Common.Configuration.Reload();
				var connectionStringSettings = eDoktor.Common.Configuration.ConnectionStringSettings("AccountsSource");
				connectionStringSettings.ConnectionString = Crypto.DecryptString(connectionStringSettings.ConnectionString);

				eDoktor.Common.Trace.OutputDebugTrace("ConnectionString={0}", connectionStringSettings.ConnectionString);

				var database = new eDoktor.Common.Database(connectionStringSettings);

				// 移動先にファイルが存在する場合は先にリネームする
				if (System.IO.File.Exists(filePath))
				{
					System.IO.File.Move(filePath, string.Format("{0}_{1:yyyyMMddHHmmssfff}.bak", filePath, DateTime.Now));
				}

				// DBから情報を取得する
				string commandText = @"SELECT
				  i.HospitalCode
				  , CONVERT(VARCHAR(64), uc.UserID + REPLICATE(' ', 64)) AS UserID
				  , CONVERT(VARCHAR(192), uc.UserPassword + REPLICATE(' ', 192)) AS UserPassword
				  , '********' AS UserPassValidEndDateTimeSec
				  , CONVERT(VARCHAR(40), uc.UserName + REPLICATE(' ', 20)) AS UserName
				  , CONVERT(VARCHAR(80), uc.UserKatakanaName + REPLICATE(' ', 40)) AS UserKatakanaName
				  , FORMAT(uc.UserBirthday, 'yyyyMMdd') AS UserBirthday
				  , CONVERT(VARCHAR(3), p.UserPropertyValue + REPLICATE(' ', 3)) AS JobCode
				  , FORMAT(i.ValidStartDateTimeSec, 'yyyyMMdd') AS ValidStartDateTimeSec
				  , FORMAT(i.ValidEndDateTimeSec, 'yyyyMMdd') AS ValidEndDateTimeSec
				  , i.DeleteFlag
				  , i.StopUseFlag
				FROM (SELECT * FROM T_USER WHERE ActiveFlag = 1) AS uc
				INNER JOIN (SELECT * FROM T_USER_IDENTITY WHERE ValidEndDateTimeSec <> '0001-01-01 00:00:00') i ON uc.UserID = i.UserID
				LEFT JOIN (SELECT * FROM T_USER_PROPERTY WHERE ActiveFlag = 1 AND UserPropertyKind = 'UserJobCode') p ON i.HospitalCode = p.HospitalCode AND i.UserID = p.UserID
				ORDER BY uc.UserID, i.HospitalCode";

				eDoktor.Common.Trace.OutputDebugTrace("query={0}", commandText);

				using (System.IO.StreamWriter sw = new StreamWriter(filePath, false, Configuration.Configuration.FileEncoding))
				{
					database.ExecuteQuery(
						commandText,
						delegate(DbDataReader reader)
						{
							StringBuilder sb = new StringBuilder();

							sb.AppendFormat("{0}{1}{0}", "\"", eDoktor.Common.Database.GetInt32(reader, "HospitalCode", 0));
							sb.AppendFormat("{0}{1}{2}{1}", ",", "\"", eDoktor.Common.Database.GetString(reader, "UserID", string.Empty).Replace("\"", "\"\""));
							sb.AppendFormat("{0}{1}{2}{1}", ",", "\"", eDoktor.Common.Database.GetString(reader, "UserPassword", string.Empty).Replace("\"", "\"\""));
							sb.AppendFormat("{0}{1}{2}{1}", ",", "\"", eDoktor.Common.Database.GetString(reader, "UserPassValidEndDateTimeSec", string.Empty).Replace("\"", "\"\""));
							sb.AppendFormat("{0}{1}{2}{1}", ",", "\"", eDoktor.Common.Database.GetString(reader, "UserName", string.Empty).Replace("\"", "\"\""));
							sb.AppendFormat("{0}{1}{2}{1}", ",", "\"", eDoktor.Common.Database.GetString(reader, "UserKatakanaName", string.Empty).Replace("\"", "\"\""));
							sb.AppendFormat("{0}{1}{2}{1}", ",", "\"", eDoktor.Common.Database.GetString(reader, "UserBirthday", "00010101").Replace("\"", "\"\""));
							sb.AppendFormat("{0}{1}{2}{1}", ",", "\"", eDoktor.Common.Database.GetString(reader, "JobCode", "   ").Replace("\"", "\"\""));
							sb.AppendFormat("{0}{1}{2}{1}", ",", "\"", eDoktor.Common.Database.GetString(reader, "ValidStartDateTimeSec", string.Empty).Replace("\"", "\"\""));
							sb.AppendFormat("{0}{1}{2}{1}", ",", "\"", eDoktor.Common.Database.GetString(reader, "ValidEndDateTimeSec", string.Empty).Replace("\"", "\"\""));
							sb.AppendFormat("{0}{1}{2}{1}", ",", "\"", eDoktor.Common.Database.GetInt32(reader, "DeleteFlag", 0));
							sb.AppendFormat("{0}{1}{2}{1}", ",", "\"", eDoktor.Common.Database.GetInt32(reader, "StopUseFlag", 0));

							sw.WriteLine(sb.ToString());
						}
					);
				}

				return true;
			}
			catch (Exception ex)
			{
				//例外発生時はファイル移動失敗とみなしスルー
				//最終的な失敗処理は呼び出しもとで行う
				eDoktor.Common.Trace.OutputExceptionTrace(ex);

				return false;
			}
		}

        /// <summary>
        /// アカウント連携ファイルの読み取り
        /// </summary>
        /// <param name="filePath">ローカルのファイル保存場所</param>
        private void ReadFile(String filePath)
        {
            int lineCount = 0;
            bool headerExec = false;

            //***debug***
            eDoktor.Common.Trace.OutputDebugTrace("IgnoreFirstRow={0}", Configuration.Configuration.IgnoreFirstRow);
            //***debug***

			if (Configuration.Configuration.DataType == Configuration.Configuration.DataTypeEnum.Fixed)
			{
				using (StreamReader reader = new StreamReader(filePath, Configuration.Configuration.FileEncoding))
				{
					while (reader.Peek() >= 0)
					{
						try
						{
							// 処理開始
							lineCount++;
							eDoktor.Common.Trace.OutputTrace(lineCount + "行目の処理開始");

							// 次の改行まで読み取り
							string line = reader.ReadLine();

                            // ヘッダー確認
                            if (Configuration.Configuration.IgnoreFirstRow && !headerExec)
                            {
                                headerExec = true;

                                eDoktor.Common.Trace.OutputTrace("ヘッダーのため無視");

                                continue;
                            }

							// 読み取った文字列を解析
							Model.LineAnalyzer analyzedLine = new Model.LineAnalyzer(line);

							using (System.Transactions.TransactionScope scope = new System.Transactions.TransactionScope())
							{
								// 職種関連処理
								eDoktor.Common.Trace.OutputTrace("職種情報({0},{1}) 処理開始", analyzedLine.JobCode, analyzedLine.JobName);
								Model.Job tempJob = JobProc(analyzedLine);

								// ユーザ関連処理
								eDoktor.Common.Trace.OutputTrace("ユーザー情報({0},{1}) 処理開始", analyzedLine.OrganizationalId, analyzedLine.Name);
								Model.User tempUser = UserProc(analyzedLine, tempJob);

								// アカウント関連処理
								eDoktor.Common.Trace.OutputTrace("アカウント情報({0}) 処理開始", analyzedLine.LogonId);
								AccountProc(analyzedLine, tempJob, tempUser);

                                // ICカード関連処理
                                if (Configuration.Configuration.CardIdDataOffset >= 0)
                                {
                                    eDoktor.Common.Trace.OutputTrace("ICカード情報({0},{1}) 処理開始", analyzedLine.UniqueId, analyzedLine.CardType);
                                    ICCardProc(analyzedLine);
                                }

                                // SSOアカウント関連処理
                                if (Configuration.Configuration.SsoLogonIdDataOffset >= 0)
                                {
                                    eDoktor.Common.Trace.OutputTrace("SSOアカウント情報({0}) 処理開始", analyzedLine.SsoLogonId);
                                    SsoAccountProc(analyzedLine);
                                }
                                
								scope.Complete();
							}

							// AD連携
							if (Configuration.Configuration.SyncronizeAd)
							{
								if (!CheckExcludedAccount(analyzedLine.OrganizationalId))
								{
									eDoktor.Common.Trace.OutputTrace("AD連携情報({0}={1}) 処理開始", analyzedLine.LdapKey.First().Key, analyzedLine.LdapKey.First().Value);
									System.DirectoryServices.SearchResult adUser = ADProc(analyzedLine);

									eDoktor.Common.Trace.OutputTrace("個人フォルダ連携({0}) 処理開始", analyzedLine.LdapKey.First().Value);
									ShareProc(adUser.Properties[analyzedLine.LdapKey.First().Key][0] as string);
								}
								else
								{
									eDoktor.Common.Trace.OutputTrace("AD連携情報({0}) 対象外ユーザ", analyzedLine.OrganizationalId);
								}
							}
						}
						catch (Exception ex)
						{
							InsertError(lineCount + "行目：" + ex.Message);

							eDoktor.Common.Trace.OutputExceptionTrace(ex);
						}
					}
				}
			}
			else
			{

				//  CSVファイルを読み取る
				using (TextFieldParser parser = new TextFieldParser(filePath, Configuration.Configuration.FileEncoding))
				{
					parser.TextFieldType = FieldType.Delimited;
					parser.SetDelimiters(Configuration.Configuration.Delimiter);

					while (parser.EndOfData == false)
					{
						try
						{
							// 処理開始
							lineCount++;
							eDoktor.Common.Trace.OutputTrace(lineCount + "行目の処理開始");

							// 次の改行まで読み取り
                            string[] columns = parser.ReadFields();

                            // ヘッダー確認
                            if (Configuration.Configuration.IgnoreFirstRow && !headerExec)
                            {
                                headerExec = true;

                                eDoktor.Common.Trace.OutputTrace("ヘッダーのため無視");

                                continue;
                            }

							// 読み取った文字列を解析
							Model.LineAnalyzer analyzedLine = new Model.LineAnalyzer(columns);

							using (System.Transactions.TransactionScope scope = new System.Transactions.TransactionScope())
							{
								// 職種関連処理
								eDoktor.Common.Trace.OutputTrace("職種情報({0},{1}) 処理開始", analyzedLine.JobCode, analyzedLine.JobName);
								Model.Job tempJob = JobProc(analyzedLine);

								// ユーザ関連処理
								eDoktor.Common.Trace.OutputTrace("ユーザー情報({0},{1}) 処理開始", analyzedLine.OrganizationalId, analyzedLine.Name);
								Model.User tempUser = UserProc(analyzedLine, tempJob);

								// アカウント関連処理
								eDoktor.Common.Trace.OutputTrace("アカウント情報({0}) 処理開始", analyzedLine.LogonId);
								AccountProc(analyzedLine, tempJob, tempUser);

                                // ICカード関連処理
                                if (Configuration.Configuration.CardIdDataOffset >= 0)
                                {
                                    eDoktor.Common.Trace.OutputTrace("ICカード情報({0},{1}) 処理開始", analyzedLine.UniqueId, analyzedLine.CardType);
                                    ICCardProc(analyzedLine);
                                }

                                // SSOアカウント関連処理
                                if (Configuration.Configuration.SsoLogonIdDataOffset >= 0)
                                {
                                    eDoktor.Common.Trace.OutputTrace("SSOアカウント情報({0}) 処理開始", analyzedLine.SsoLogonId);
                                    SsoAccountProc(analyzedLine);
                                }

								scope.Complete();
							}

							// AD連携
							if (Configuration.Configuration.SyncronizeAd)
							{
								if (!CheckExcludedAccount(analyzedLine.OrganizationalId))
								{
									eDoktor.Common.Trace.OutputTrace("AD連携情報({0}={1}) 処理開始", analyzedLine.LdapKey.First().Key, analyzedLine.LdapKey.First().Value);
									System.DirectoryServices.SearchResult adUser = ADProc(analyzedLine);

									eDoktor.Common.Trace.OutputTrace("個人フォルダ連携({0}) 処理開始", analyzedLine.LdapKey.First().Value);
									ShareProc(adUser.Properties[analyzedLine.LdapKey.First().Key][0] as string);
								}
								else
								{
									eDoktor.Common.Trace.OutputTrace("AD連携情報({0}) 対象外ユーザ", analyzedLine.OrganizationalId);
								}
							}
						}
						catch (Exception ex)
						{
							InsertError(lineCount + "行目：" + ex.Message);

							eDoktor.Common.Trace.OutputExceptionTrace(ex);
						}
					}
				}
			}
        }

        private void SsoAccountProc(Model.LineAnalyzer line)
        {
            // SSOログオンIDが空白の場合はSSOアカウント削除を実施する
            if (string.IsNullOrWhiteSpace(line.SsoLogonId))
            {
                // SSOアカウント情報のチェックをする
                var ssoAccount = GetSsoAccount(line.LogonId);
                if (ssoAccount == null)
                {
                    // 以下の処理を実行しない
                    eDoktor.Common.Trace.OutputTrace("SSOアカウントが存在しないため更新なし sso_logon_id={0}", line.SsoLogonId);
                    return;
                }

                // SSOアカウントを削除する
                InvalidateSsoAccount(ssoAccount.Id);

                eDoktor.Common.Trace.OutputTrace("SSOアカウント情報が空白のためSSOアカウントを削除");
            }
            else
            {
                var ssoAccount = new Model.SsoAccount(line);

                // SSOアカウントの新規追加
                var dbSsoAccount = GetSsoAccount(line.LogonId);
                if (dbSsoAccount == null)
                {
                    // 該当レコードがない場合、t_smard_cardsを新規追加
                    eDoktor.Common.Trace.OutputTrace("新規SSOアカウントのため登録");
                    InsertSsoAccount(ssoAccount);
                }
                else
                {
                    ssoAccount.Id = dbSsoAccount.Id;

                    //変更箇所がある場合のみ更新する
                    if (CheckNeedUpdateSsoAccount(ref ssoAccount, dbSsoAccount))
                    {
                        eDoktor.Common.Trace.OutputTrace("情報更新あり");
                        UpdateSsoAccount(ssoAccount);
                    }
                    else
                    {
                        eDoktor.Common.Trace.OutputTrace("情報更新なし");
                    }
                }
            }
        }

        private void InsertSsoAccount(Model.SsoAccount ssoAccount)
        {
            StringBuilder query = new StringBuilder();
            query.Append("INSERT INTO t_sso_accounts ( ");
            query.Append(" registration_datetime");
            query.Append(", registered_by");
            query.Append(", modification_datetime");
            query.Append(", modified_by");
            query.Append(", invalidated");
            query.Append(", invalidation_datetime");
            query.Append(", invalidated_by");
            query.Append(", logically_deleted");
            query.Append(", logical_deletion_datetime");
            query.Append(", logically_deleted_by");
            query.Append(", synchronized");
            query.Append(", usage_start_datetime");
            query.Append(", usage_end_datetime");
            query.Append(", account_logon_id");
            query.Append(", sso_account_system_id");
            query.Append(", logon_id");
            query.Append(", password");
            query.Append(", notes) ");
            query.Append("VALUES (");
            query.Append(" GETDATE()");
            query.AppendFormat(", {0}", Configuration.Configuration.PractitionerID);
            query.Append(", GETDATE()");
            query.AppendFormat(", {0}", Configuration.Configuration.PractitionerID);
            query.AppendFormat(", 'false'");
            query.Append(", null");
            query.AppendFormat(", {0}", 0);
            query.AppendFormat(", 'false'");
            query.Append(", null");
            query.AppendFormat(", {0}", 0);
            query.AppendFormat(", {0}", 1);
            query.Append(", null");
            query.Append(", null");
            query.AppendFormat(", '{0}'", eDoktor.Common.Database.EscapeParam(ssoAccount.AccountLogonId));
            query.AppendFormat(", {0}", Configuration.Configuration.TargetSsoAccountSystemId);
            query.AppendFormat(", '{0}'", eDoktor.Common.Database.EscapeParam(ssoAccount.SsoLogonId));
            query.AppendFormat(", '{0}'", eDoktor.Common.Database.EscapeParam(Crypto.EncryptString(ssoAccount.SsoPassword)));
            query.AppendFormat(", '{0}'", string.Empty);
            query.Append(") ");
            eDoktor.Common.Trace.OutputDebugTrace("query={0}", query.ToString());
            _database.ExecuteNonQuery(query.ToString());
        }

        private void UpdateSsoAccount(Model.SsoAccount ssoAccount)
        {
            StringBuilder query = new StringBuilder();
            query.Append("UPDATE t_sso_accounts SET");
            query.Append(" modification_datetime = GETDATE()");
            query.AppendFormat(", modified_by = {0}", Configuration.Configuration.PractitionerID);
            query.AppendFormat(", invalidated = 'false'");
            query.Append(", invalidation_datetime = null");
            query.AppendFormat(", invalidated_by = {0}", 0);
            query.AppendFormat(", logically_deleted = 'false'");
            query.Append(", logical_deletion_datetime = null");
            query.AppendFormat(", logically_deleted_by = {0}", 0);
            query.AppendFormat(", synchronized = {0}", 1);
            query.Append(", usage_start_datetime = null");
            query.Append(", usage_end_datetime = null");
            query.AppendFormat(", logon_id = '{0}'", eDoktor.Common.Database.EscapeParam(ssoAccount.SsoLogonId));
            if (Configuration.Configuration.SsoPasswordDataOffset >= 0)
            {
                query.AppendFormat(", password = '{0}'", eDoktor.Common.Database.EscapeParam(Crypto.EncryptString(ssoAccount.SsoPassword)));
            }
            query.AppendFormat(" WHERE id = {0}", ssoAccount.Id);

            eDoktor.Common.Trace.OutputDebugTrace("query={0}", query.ToString());
            _database.ExecuteNonQuery(query.ToString());
        }

        private bool CheckNeedUpdateSsoAccount(ref Model.SsoAccount ssoAccount, Model.SsoAccount dbSsoAccount)
        {
            bool result = false;

            if (!ssoAccount.SsoLogonId.Equals(dbSsoAccount.SsoLogonId))
            {
                result = true;
            }
            // パスワード暗号化状態により、比較する項目を変える
            if (Configuration.Configuration.SsoPasswordDataOffset >= 0)
            {
                if (!ssoAccount.SsoPassword.Equals(dbSsoAccount.SsoPassword))
                {
                    result = true;
                }
            }

            return result;
        }

        private Model.SsoAccount GetSsoAccount(string logonId)
        {
            Model.SsoAccount dbSsoAccount = null;
            StringBuilder query = new StringBuilder();
            query.Append("SELECT");
            query.Append(" sa.id");
            query.Append(", sa.invalidated");
            query.Append(", sa.logically_deleted");
            query.Append(", sa.usage_start_datetime");
            query.Append(", sa.usage_end_datetime");
            query.Append(", sa.account_logon_id");
            query.Append(", sa.sso_account_system_id");
            query.Append(", sa.logon_id");
            query.Append(", sa.password");
            query.Append(" FROM t_sso_accounts sa ");
            query.AppendFormat(" WHERE sa.account_logon_id = '{0}'", eDoktor.Common.Database.EscapeParam(logonId));
            query.AppendFormat(" AND sa.sso_account_system_id = {0};", Configuration.Configuration.TargetSsoAccountSystemId);

            _database.ExecuteQuery(
                query.ToString(),
                delegate(DbDataReader reader)
                {
                    //存在しない場合はこれ以降の処理不要
                    if (!(eDoktor.Common.Database.GetInt32(reader, "id", -1) == -1))
                    {
                        dbSsoAccount = new Model.SsoAccount();
                        dbSsoAccount.Id = eDoktor.Common.Database.GetInt32(reader, "id", -1);
                        dbSsoAccount.Invalidated = eDoktor.Common.Database.GetBoolean(reader, "invalidated", false);
                        dbSsoAccount.LogicallyDeleted = eDoktor.Common.Database.GetBoolean(reader, "logically_deleted", false);
                        dbSsoAccount.UsageStartDatetime = eDoktor.Common.Database.GetDateTime(reader, "usage_start_datetime");
                        dbSsoAccount.UsageEndDatetime = eDoktor.Common.Database.GetDateTime(reader, "usage_end_datetime");
                        dbSsoAccount.AccountLogonId = eDoktor.Common.Database.GetString(reader, "account_logon_id", string.Empty);
                        dbSsoAccount.SsoAccountSystemId = eDoktor.Common.Database.GetInt32(reader, "sso_account_system_id", 0);
                        dbSsoAccount.SsoLogonId = eDoktor.Common.Database.GetString(reader, "logon_id", String.Empty);
                        dbSsoAccount.SsoPassword = Crypto.DecryptString(eDoktor.Common.Database.GetString(reader, "password", Crypto.EncryptString(String.Empty)));
                    }
                }
            );

            return dbSsoAccount;
        }

        /// <summary>
        /// t_sso_accounts内のデータを無効化する。
        /// </summary>
        /// <param name="ssoAccountId">SSOアカウントレコードID</param>
        /// <returns></returns>
        public int InvalidateSsoAccount(int ssoAccountId)
        {
            StringBuilder query = new StringBuilder();
            query.Length = 0;
            query.AppendFormat("UPDATE t_sso_accounts ");
            query.AppendFormat("   SET modification_datetime = GETDATE(), ");
            query.AppendFormat("       modified_by = {0}, ", Configuration.Configuration.PractitionerID);
            query.AppendFormat("       invalidated = 'true', ");
            query.AppendFormat("       invalidation_datetime = GETDATE(), ");
            query.AppendFormat("       invalidated_by = {0}, ", Configuration.Configuration.PractitionerID);
            query.AppendFormat("       logically_deleted = 'true', ");
            query.AppendFormat("       logical_deletion_datetime = GETDATE(), ");
            query.AppendFormat("       logically_deleted_by = {0} ", Configuration.Configuration.PractitionerID);
            query.AppendFormat(" WHERE id = {0}", ssoAccountId);

            //***debug***
            eDoktor.Common.Trace.OutputDebugTrace("query={0}", query.ToString());
            //***debug***

            return _database.ExecuteNonQuery(query.ToString());
        }

        private void ICCardProc(Model.LineAnalyzer line)
        {
            int usageType = 1;                                          // 1 固定

            // カード番号が空白の場合は紐づけ削除を実施する
            if (string.IsNullOrWhiteSpace(line.UniqueId))
            {
                // アカウント情報のチェックをする
                int accountId = GetAccountId(line.LogonId);
                if (accountId == 0)
                {
                    // 以下の処理を実行しない
                    eDoktor.Common.Trace.OutputTrace("存在しないアカウントのため処理をスキップ logon_id={0}", line.LogonId);
                    return;
                }

                // アカウントの紐づけを削除する
                InvalidateSmartCardHolders(0, accountId);

                eDoktor.Common.Trace.OutputTrace("ICカード情報が空白のため紐づけを削除");
            }
            else
            {
                // ICカード情報の新規追加
                int smartCardId = GetSmartCardId(line.UniqueId);
                int newSmartCardId = 0;
                if (smartCardId == 0)
                {
                    // 該当レコードがない場合、t_smard_cardsを新規追加
                    newSmartCardId = InsertSmartCards(line.UniqueId, line.CardType, usageType);
                    eDoktor.Common.Trace.OutputTrace("新規ICカードのため登録");
                }
                else
                {
                    // 該当レコードがある場合、t_smart_cardsを有効化
                    EnableSmartCard(smartCardId);
                    newSmartCardId = smartCardId;
                }

                // アカウント情報のチェックをする
                int accountId = GetAccountId(line.LogonId);
                if (accountId == 0)
                {
                    // 以下の処理を実行しない
                    eDoktor.Common.Trace.OutputTrace("存在しないアカウントのため処理をスキップ logon_id={0}", line.LogonId);
                    return;
                }

                // ICカード紐づけ情報の紐づけをチェックする
                int smartCardHolders = GetSmartCardHoldersId(newSmartCardId, accountId);
                if (smartCardHolders != 0)
                {
                    // 以下の処理を実行しない
                    eDoktor.Common.Trace.OutputTrace("ICカード情報の紐づけ更新なし");
                    return;
                }

                if (line.Invalidated == true || line.LogicallyDeleted == true || (DateTime.Now < line.UsageStartDatetime || line.UsageEndDatetime < DateTime.Now))
                {
                    eDoktor.Common.Trace.OutputTrace("使用停止ユーザーの為ICカード情報の紐づけを行わない");
                }
                else
                {
                    // 紐づけ更新
                    using (var scope = new System.Transactions.TransactionScope())
                    {
                        InvalidateSmartCardHolders(smartCardId, accountId);
                        // ICカード情報の紐づけを新規追加にする
                        int smartCardHolderId = InsertSmartCardHoldes(newSmartCardId, accountId, usageType);
                        eDoktor.Common.Trace.OutputTrace("ICカード情報の紐づけを登録");

                        scope.Complete();
                    }
                }
            }
        }

        /// <summary>
        /// t_smart_card_holdersにIC関連付け情報(アカウントとICカードの関連付け)を登録する。
        /// </summary>
        /// <param name="smartCardId">t_smart_cards上のid</param>
        /// <param name="smartCardId">t_accounts上のid</param>
        /// <param name="usageType">カード使用タイプ</param>
        /// <returns>登録したIC関連付け情報のt_smart_card_holdersのid</returns>
        public int InsertSmartCardHoldes(int smartCardId, int accountId, int usageType)
        {
            int smartCardHolderId = 0;
            StringBuilder query = new StringBuilder();
            query.Length = 0;
            query.Append("INSERT INTO t_smart_card_holders( ");
            query.Append("registration_datetime, ");
            query.Append("registered_by, ");
            query.Append("modification_datetime, ");
            query.Append("modified_by, ");
            query.Append("invalidated, ");
            query.Append("invalidation_datetime, ");
            query.Append("invalidated_by, ");
            query.Append("logically_deleted, ");
            query.Append("logical_deletion_datetime, ");
            query.Append("logically_deleted_by, ");
            query.Append("usage_start_datetime, ");
            query.Append("usage_end_datetime, ");
            query.Append("account_id, ");
            query.Append("smart_card_id, ");
            query.Append("usage_type, ");
            query.Append("notes) ");
            query.Append("VALUES( ");
            query.Append("GETDATE(), ");                                                                        // registration_datetime
            query.AppendFormat("{0},", Configuration.Configuration.PractitionerID);                             // registered_by
            query.AppendFormat("DEFAULT, ");                                                                    // modification_datetime
            query.AppendFormat("DEFAULT, ");                                                                    // modified_by
            query.AppendFormat("'false',");                                                                     // invalidated
            query.AppendFormat("DEFAULT,");                                                                     // invalidation_datetime
            query.AppendFormat("DEFAULT,");                                                                     // invalidated_by
            query.AppendFormat("'false',");                                                                     // logically_deleted
            query.AppendFormat("DEFAULT,");                                                                     // logical_deletion_datetime
            query.AppendFormat("DEFAULT,");                                                                     // logically_deleted_by
            query.AppendFormat("null,");                                                                        // usage_start_datetime
            query.AppendFormat("null,");                                                                        // usage_end_datetime
            query.AppendFormat("{0},", accountId);                                              // account_id
            query.AppendFormat("{0},", smartCardId);                                            // smart_card_id
            query.AppendFormat("{0},", usageType);                                              // usage_type
            query.AppendFormat("'{0}')", eDoktor.Common.Database.EscapeParam(string.Empty));                                           // notes
            query.AppendFormat(";SELECT SCOPE_IDENTITY() AS id");                                               // オートナンバー型のフィールドの値を取得
            string sql = query.ToString();

            //***debug***
            eDoktor.Common.Trace.OutputDebugTrace("query={0}", query.ToString());
            //***debug***

            _database.ExecuteQuery(
                sql,
                delegate(DbDataReader reader)
                {
                    int columnIndex = reader.GetOrdinal("id");
                    smartCardHolderId = (int)reader.GetDecimal(columnIndex);
                }
            );
            return smartCardHolderId;
        }


        /// <summary>
        /// t_smart_card_holders内の有効なレコードで指定smart_card_idに一致するものを無効化する。
        /// </summary>
        /// <param name="smartCardId">スマートカードID番号</param>
        /// <returns></returns>
        public int InvalidateSmartCardHolders(int smartCardId, int accountId)
        {
            StringBuilder query = new StringBuilder();
            query.Length = 0;
            query.AppendFormat("UPDATE t_smart_card_holders ");
            query.AppendFormat("   SET modification_datetime = GETDATE(), ");
            query.AppendFormat("       modified_by = {0}, ", Configuration.Configuration.PractitionerID);
            query.AppendFormat("       invalidated = 'true', ");
            query.AppendFormat("       invalidation_datetime = GETDATE(), ");
            query.AppendFormat("       invalidated_by = {0}, ", Configuration.Configuration.PractitionerID);
            query.AppendFormat("       logically_deleted = 'true', ");
            query.AppendFormat("       logical_deletion_datetime = GETDATE(), ");
            query.AppendFormat("       logically_deleted_by = {0} ", Configuration.Configuration.PractitionerID);
            query.AppendFormat(" WHERE (smart_card_id = {0} OR account_id = {1})", smartCardId, accountId);
            query.AppendFormat("   AND logically_deleted = 'false' ");
            query.AppendFormat("   AND invalidated = 'false' ");
            query.AppendFormat("   AND smart_card_id NOT IN (SELECT id FROM t_smart_cards WHERE card_type = 4 OR card_type = 5)");

            //***debug***
            eDoktor.Common.Trace.OutputDebugTrace("query={0}", query.ToString());
            //***debug***

            return _database.ExecuteNonQuery(query.ToString());
        }


        public int GetSmartCardHoldersId(int smartCardId, int accountId)
        {
            int id = 0;
            StringBuilder query = new StringBuilder();
            query.Length = 0;
            query.AppendFormat("SELECT h.id FROM t_smart_card_holders h " +
                               "  WHERE h.smart_card_id = {0} ", smartCardId);
            query.AppendFormat("    AND h.account_id = {0} ", accountId);
            query.AppendFormat("    AND h.invalidated = 'false' ");
            query.AppendFormat("    AND h.logically_deleted = 'false' ");

            //***debug***
            eDoktor.Common.Trace.OutputDebugTrace("query={0}", query.ToString());
            //***debug***

            _database.ExecuteQuery(
                query.ToString(),
                delegate(DbDataReader reader)
                {
                    id = eDoktor.Common.Database.GetInt32(reader, "id", 0);
                }
            );
            return id;
        }

        public int GetAccountId(string logonId)
        {
            int id = 0;
            StringBuilder query = new StringBuilder();
            query.Length = 0;
            query.AppendFormat("SELECT a.id FROM t_accounts a " +
                               "  WHERE a.logon_id = '{0}' ", eDoktor.Common.Database.EscapeParam(logonId));

            //***debug***
            eDoktor.Common.Trace.OutputDebugTrace("query={0}", query.ToString());
            //***debug***

            _database.ExecuteQuery(
                query.ToString(),
                delegate(DbDataReader reader)
                {
                    id = eDoktor.Common.Database.GetInt32(reader, "id", 0);
                }
            );
            return id;
        }

        public int EnableSmartCard(int smartCardId)
        {
            StringBuilder query = new StringBuilder();
            query.Append("UPDATE t_smart_cards SET ");
            query.Append("modification_datetime = GETDATE(), ");
            query.AppendFormat("modified_by = {0}, ", Configuration.Configuration.PractitionerID);
            query.Append("invalidated = 0, ");
            query.Append("invalidation_datetime = NULL, ");
            query.Append("invalidated_by = 0, ");
            query.Append("logically_deleted = 0, ");
            query.Append("logical_deletion_datetime = NULL, ");
            query.Append("logically_deleted_by = 0, ");
            query.Append("usage_start_datetime = NULL, ");
            query.Append("usage_end_datetime = NULL ");
            query.AppendFormat("WHERE id = {0} ", smartCardId);
            query.AppendFormat("AND (invalidated = 1 OR logically_deleted = 1 OR usage_start_datetime IS NOT NULL OR usage_end_datetime IS NOT NULL)");
            string sql = query.ToString();

            //***debug***
            eDoktor.Common.Trace.OutputDebugTrace("query={0}", query.ToString());
            //***debug***

            var result = _database.ExecuteNonQuery(sql);
            return result;
        }

        /// <summary>
        /// t_smart_cardsを検索してICカードのユニークIDが登録されているかチェックする
        /// </summary>
        /// <param name="uniqueId">カードのユニークID</param>
        /// <returns>0:未登録 1以上:登録されている</returns>
        public int GetSmartCardId(string uniqueId)
        {
            int id = 0;
            StringBuilder query = new StringBuilder();
            query.Length = 0;
            query.AppendFormat("SELECT s.id FROM t_smart_cards s " +
                               "  WHERE s.unique_id = '{0}' ", eDoktor.Common.Database.EscapeParam(uniqueId));

            //***debug***
            eDoktor.Common.Trace.OutputDebugTrace("query={0}", query.ToString());
            //***debug***

            _database.ExecuteQuery(
                query.ToString(),
                delegate(DbDataReader reader)
                {
                    id = eDoktor.Common.Database.GetInt32(reader, "id", 0);
                }
            );
            return id;
        }

        /// <summary>
        /// t_smart_cardsにICカードの情報(ユニークIDなど)を登録する。
        /// </summary>
        /// <param name="uniqueId">カードのユニークID</param>
        /// <param name="cardType">ICカードタイプ</param>
        /// <param name="usageType">カード使用タイプ</param>
        /// <returns>追加したICカード情報のt_smart_cards上のid</returns>
        public int InsertSmartCards(string uniqueId, int cardType, int usageType)
        {
            int smartCardId = 0;
            StringBuilder query = new StringBuilder();
            query.Append("INSERT INTO t_smart_cards( ");
            query.Append("registration_datetime, ");
            query.Append("registered_by, ");
            query.Append("modification_datetime, ");
            query.Append("modified_by, ");
            query.Append("invalidated, ");
            query.Append("invalidation_datetime, ");
            query.Append("invalidated_by, ");
            query.Append("logically_deleted, ");
            query.Append("logical_deletion_datetime, ");
            query.Append("logically_deleted_by, ");
            query.Append("usage_start_datetime, ");
            query.Append("usage_end_datetime, ");
            query.Append("unique_id, ");
            query.Append("card_type, ");
            query.Append("notes, ");
            query.Append("usage_type)");
            query.Append("VALUES( ");
            query.AppendFormat("GETDATE(), ");                                                                  // registration_datetime
            query.AppendFormat("{0},", Configuration.Configuration.PractitionerID);                             // registered_by
            query.AppendFormat("DEFAULT,");                                                                     // modification_datetime
            query.AppendFormat("DEFAULT,");                                                                     // modified_by
            query.AppendFormat("'false',");                                                                     // invalidated
            query.AppendFormat("DEFAULT,");                                                                     // invalidation_datetime
            query.AppendFormat("DEFAULT,");                                                                     // invalidated_by
            query.AppendFormat("'false',");                                                                     // logically_deleted
            query.AppendFormat("DEFAULT,");                                                                     // logical_deletion_datetime
            query.AppendFormat("DEFAULT,");                                                                     // logically_deleted_by
            query.AppendFormat("NULL,");                                                                        // usage_start_datetime
            query.AppendFormat("NULL,");                                                                        // usage_end_datetime
            query.AppendFormat("'{0}',", eDoktor.Common.Database.EscapeParam(uniqueId));                                               // unique_id
            query.AppendFormat("{0},", cardType);                                               // card_type
            query.AppendFormat("'{0}', ", eDoktor.Common.Database.EscapeParam(string.Empty));                                          // notes
            query.AppendFormat("{0})", usageType);                                              // usage_type
            query.AppendFormat(";SELECT SCOPE_IDENTITY() AS id");                                               // オートナンバー型のフィールドの値を取得
            string sql = query.ToString();

            //***debug***
            eDoktor.Common.Trace.OutputDebugTrace("query={0}", query.ToString());
            //***debug***

            _database.ExecuteQuery(
                sql,
                delegate(DbDataReader reader)
                {
                    int columnIndex = reader.GetOrdinal("id");
                    smartCardId = (int)reader.GetDecimal(columnIndex);
                }
            );
            return smartCardId;
        }

        private void AccountProc(Model.LineAnalyzer line, Model.Job job, Model.User user)
        {
            //アカウント関連情報の読み取り
            Model.Account account = new Model.Account(line, user, job);

            //アカウント情報存在確認し、存在する場合は更新必要性チェック、存在しない場合は登録
            Model.Account dbAccount = GetAccount(line.LogonId);
            if (dbAccount != null)
            {
                account.Id = dbAccount.Id;

                //変更箇所がある場合のみ更新する
                if (CheckNeedUpdateAccount(ref account, dbAccount))
                {
                    eDoktor.Common.Trace.OutputTrace("情報更新あり");
                    UpdateAccount(account);
                }
                else
                {
                    eDoktor.Common.Trace.OutputTrace("情報更新なし");
                }
            }
            else
            {
                eDoktor.Common.Trace.OutputTrace("新規アカウントのため登録");
                InsertAccount(account);
            }
        }

        private void InsertAccount(Model.Account account)
        {
            StringBuilder query = new StringBuilder();
            query.Append("INSERT INTO t_accounts ( ");
            query.Append(" registration_datetime");
            query.Append(", registered_by");
            query.Append(", modification_datetime");
            query.Append(", modified_by");
            query.Append(", invalidated");
            query.Append(", invalidation_datetime");
            query.Append(", invalidated_by");
            query.Append(", logically_deleted");
            query.Append(", logical_deletion_datetime");
            query.Append(", logically_deleted_by");
            query.Append(", synchronized");
            query.Append(", usage_start_datetime");
            query.Append(", usage_end_datetime");
            query.Append(", user_id");
            query.Append(", group_id");
            query.Append(", privilege_template_id");
            query.Append(", logon_id");
            query.Append(", password");
            query.Append(", encrypted_password");
            query.Append(", logon_to");
            query.Append(", password_update_datetime");
            query.Append(", notes) ");
            query.Append("VALUES (");
            query.Append(" GETDATE()");
            query.AppendFormat(", {0}", Configuration.Configuration.PractitionerID);
            query.Append(", GETDATE()");
            query.AppendFormat(", {0}", Configuration.Configuration.PractitionerID);
            query.AppendFormat(", '{0}'", account.Invalidated);
            if (account.Invalidated)
            {
                query.Append(", GETDATE()");
                query.AppendFormat(", {0}", Configuration.Configuration.PractitionerID);
            }
            else
            {
                query.Append(", null");
                query.AppendFormat(", {0}", 0);
            }
            query.AppendFormat(", '{0}'", account.LogicallyDeleted);
            if (account.LogicallyDeleted)
            {
                query.Append(", GETDATE()");
                query.AppendFormat(", {0}", Configuration.Configuration.PractitionerID);
            }
            else
            {
                query.Append(", null");
                query.AppendFormat(", {0}", 0);
            }
            query.AppendFormat(", {0}", 1);

            if (account.UsageStartDatetime == null)
            {
                query.Append(", null");
            }
            else
            {
                query.AppendFormat(", '{0}'", eDoktor.Common.Database.EscapeParam(account.UsageStartDatetime.ToString()));
            }
            if (account.UsageEndDatetime == null)
            {
                query.Append(", null");
            }
            else
            {
                query.AppendFormat(", '{0}'", eDoktor.Common.Database.EscapeParam(account.UsageEndDatetime.ToString()));
            }
            query.AppendFormat(", {0}", account.UserId);
            query.AppendFormat(", {0}", account.GroupId);
            query.AppendFormat(", {0}", account.PrivilegeTemplateId);
            query.AppendFormat(", '{0}'", eDoktor.Common.Database.EscapeParam(account.LogonId));
            if (Configuration.Configuration.PasswordDataIsEncrypted)
            {
                query.AppendFormat(", '{0}'", Configuration.Configuration.PasswordDataInitial);
                query.AppendFormat(", '{0}'", eDoktor.Common.Database.EscapeParam(account.EncryptedPassword));
            }
            else
            {
                query.AppendFormat(", '{0}'", eDoktor.Common.Database.EscapeParam(Crypto.EncryptString(account.Password)));
                query.AppendFormat(", ''");
            }
            query.AppendFormat(", '{0}'", eDoktor.Common.Database.EscapeParam(account.LogonTo));
            if (Configuration.Configuration.PasswordUpdateDatetimeIsUsedForNewAccount)
            {
                query.AppendFormat(", '{0}'", eDoktor.Common.Database.EscapeParam(Configuration.Configuration.PasswordUpdateDatetimeToUseForNewAccount));
            }
            else
            {
                query.AppendFormat(", '{0}'", eDoktor.Common.Database.EscapeParam(account.PasswordUpdateDatetime.ToString()));
            }
            query.AppendFormat(", '{0}'", string.Empty);
            query.Append(") ");
			eDoktor.Common.Trace.OutputDebugTrace("query={0}", query.ToString());
            _database.ExecuteNonQuery(query.ToString());
        }

        private void UpdateAccount(Model.Account account)
        {
            StringBuilder query = new StringBuilder();
            query.Append("UPDATE t_accounts SET");
            query.Append(" modification_datetime = GETDATE()");
            query.AppendFormat(", modified_by = {0}", Configuration.Configuration.PractitionerID);
            query.AppendFormat(", invalidated = '{0}'", account.Invalidated);
			if (Configuration.Configuration.InvalidatedDataOffset >= 0)
			{
				if (account.IsInvalidatedChanged)
				{
					if (account.Invalidated)
					{
						query.Append(", invalidation_datetime = GETDATE()");
						query.AppendFormat(", invalidated_by = {0}", Configuration.Configuration.PractitionerID);
					}
					else
					{
						query.Append(", invalidation_datetime = null");
						query.AppendFormat(", invalidated_by = {0}", 0);
					}
				}
			}
			if (Configuration.Configuration.LogicallyDeletedDataOffset >= 0)
			{
				query.AppendFormat(", logically_deleted = '{0}'", account.LogicallyDeleted);
				if (account.IsLogicallyDeletedChanged)
				{
					if (account.LogicallyDeleted)
					{
						query.Append(", logical_deletion_datetime = GETDATE()");
						query.AppendFormat(", logically_deleted_by = {0}", Configuration.Configuration.PractitionerID);
					}
					else
					{
						query.Append(", logical_deletion_datetime = null");
						query.AppendFormat(", logically_deleted_by = {0}", 0);
					}
				}
			}
            query.AppendFormat(", synchronized = {0}", 1);
			if (Configuration.Configuration.UsageStartDatetimeDataOffset >= 0)
			{
				if (account.UsageStartDatetime == null)
				{
					query.Append(", usage_start_datetime = null");
				}
				else
				{
					query.AppendFormat(", usage_start_datetime = '{0}'", eDoktor.Common.Database.EscapeParam(account.UsageStartDatetime.ToString()));
				}
			}
			if (Configuration.Configuration.UsageEndDatetimeDataOffset >= 0)
			{
				if (account.UsageEndDatetime == null)
				{
					query.Append(", usage_end_datetime = null");
				}
				else
				{
					query.AppendFormat(", usage_end_datetime = '{0}'", eDoktor.Common.Database.EscapeParam(account.UsageEndDatetime.ToString()));
				}
			}
            query.AppendFormat(", user_id = {0}", account.UserId);
            //query.AppendFormat("group_id = {0}, ", account.GroupId);
            //query.AppendFormat("privilege_template_id = {0}, ", account.PrivilegeTemplateId);
            query.AppendFormat(", logon_id = '{0}'", eDoktor.Common.Database.EscapeParam(account.LogonId));
			if (Configuration.Configuration.PasswordDataOffset >= 0)
			{
				if (account.IsPasswordChanged)
				{
					// ▼ ADD ログインできない問題ログ強化 2019/07/01 eDoktor Y.Kihara \\\\
					eDoktor.Common.Trace.OutputTrace("★パスワード変更：logon_id={0}", account.LogonId);
					// ▲ ADD ログインできない問題ログ強化 2019/07/01 eDoktor Y.Kihara \\\\
					if (Configuration.Configuration.PasswordDataIsEncrypted)
					{
						//query.AppendFormat("password = null, ");
						query.AppendFormat(", encrypted_password = '{0}'", eDoktor.Common.Database.EscapeParam(account.EncryptedPassword));
					}
					else
					{
						query.AppendFormat(", password = '{0}'", eDoktor.Common.Database.EscapeParam(Crypto.EncryptString(account.Password)));
						//query.AppendFormat("encrypted_password = '', ");
					}
					query.AppendFormat(", password_update_datetime = '{0}'", eDoktor.Common.Database.EscapeParam(account.PasswordUpdateDatetime.ToString()));
				}
			}
            //query.AppendFormat("logon_to = '{0}' ", eDoktor.Common.Database.EscapeParam(account.LogonTo));

            query.AppendFormat(" WHERE id = {0}", account.Id);

			eDoktor.Common.Trace.OutputDebugTrace("query={0}", query.ToString());
            _database.ExecuteNonQuery(query.ToString());
        }

        private bool CheckNeedUpdateAccount(ref Model.Account account, Model.Account dbAccount)
        {
            bool result = false;

			if (Configuration.Configuration.InvalidatedDataOffset >= 0)
			{
				if (!account.Invalidated.Equals(dbAccount.Invalidated))
				{
					//無効フラグ変更フラグを立てておく
					account.IsInvalidatedChanged = true;
					result = true;
				}
			}
			if (Configuration.Configuration.LogicallyDeletedDataOffset >= 0)
			{
				if (!account.LogicallyDeleted.Equals(dbAccount.LogicallyDeleted))
				{
					//論理削除フラグ変更フラグを立てておく
					account.IsLogicallyDeletedChanged = true;
					result = true;
				}
			}
			if (Configuration.Configuration.UsageStartDatetimeDataOffset >= 0)
			{
				if (!account.UsageStartDatetime.Equals(dbAccount.UsageStartDatetime))
				{
					result = true;
				}
			}
			if (Configuration.Configuration.UsageEndDatetimeDataOffset >= 0)
			{
				if (!account.UsageEndDatetime.Equals(dbAccount.UsageEndDatetime))
				{
					result = true;
				}
			}
            if (!account.UserId.Equals(dbAccount.UserId)) //外部の操作で変わる可能性あり
            {
                result = true;
            }
			//if (!account.GroupId.Equals(dbAccount.GroupId)) //外部の操作で変わる可能性あり
			//{
			//	result = true;Re
			//}
			//if (!account.PrivilegeTemplateId.Equals(dbAccount.PrivilegeTemplateId)) //外部の操作で変わる可能性あり
			//{
			//	result = true;
			//}
            if (!account.LogonId.Equals(dbAccount.LogonId))
            {
                result = true;
            }
            // パスワード暗号化状態により、比較する項目を変える
			if (Configuration.Configuration.PasswordDataOffset >= 0)
			{
				if ((Configuration.Configuration.PasswordDataIsEncrypted && !account.EncryptedPassword.Equals(dbAccount.EncryptedPassword))
					|| (!Configuration.Configuration.PasswordDataIsEncrypted && !account.Password.Equals(dbAccount.Password)))
				{
					//パスワードはパスワード更新日時が新しくなっている場合のみ更新対象（同じ日時も更新対象とする）
					//***debug***
					eDoktor.Common.Trace.OutputDebugTrace("dbAccount.PasswordUpdateDatetime={0},account.PasswordUpdateDatetime={1}", dbAccount.PasswordUpdateDatetime, account.PasswordUpdateDatetime);
					//***debug***
					if (dbAccount.PasswordUpdateDatetime <= account.PasswordUpdateDatetime)
					{
						//パスワード変更フラグを立てておく
						account.IsPasswordChanged = true;
						result = true;
					}
					// ▼ ADD ログインできない問題ログ強化 2019/07/01 eDoktor Y.Kihara \\\\
					else
					{
						eDoktor.Common.Trace.OutputTrace("★パスワード更新日が古いため更新対象外です");
					}
					// ▲ ADD ログインできない問題ログ強化 2019/07/01 eDoktor Y.Kihara \\\\
				}
			}
			//if (!account.LogonTo.Equals(dbAccount.LogonTo))
			//{
			//	result = true;
			//}

            return result;
        }

        private Model.Account GetAccount(string logonId)
        {
            Model.Account dbAccount = null;
            StringBuilder query = new StringBuilder();
            query.Append("SELECT");
            query.Append(" a.id");
            query.Append(", a.invalidated");
            query.Append(", a.logically_deleted");
            query.Append(", a.usage_start_datetime");
            query.Append(", a.usage_end_datetime");
            query.Append(", a.user_id");
            query.Append(", a.group_id");
            query.Append(", a.privilege_template_id");
            query.Append(", a.logon_id");
            query.Append(", a.password");
            query.Append(", a.encrypted_password");
            query.Append(", a.logon_to");
            query.Append(", a.password_update_datetime");
            query.Append(" FROM t_accounts a ");
            query.Append(" WHERE");
            query.AppendFormat(" a.logon_id = '{0}' ", eDoktor.Common.Database.EscapeParam(logonId));

            _database.ExecuteQuery(
                query.ToString(),
                delegate(DbDataReader reader)
                {
                    //存在しない場合はこれ以降の処理不要
                    if (!(eDoktor.Common.Database.GetInt32(reader, "id", -1) == -1))
                    {
                        dbAccount = new Model.Account();
                        dbAccount.Id = eDoktor.Common.Database.GetInt32(reader, "id", -1);
                        dbAccount.Invalidated = eDoktor.Common.Database.GetBoolean(reader, "invalidated", false);
                        dbAccount.LogicallyDeleted = eDoktor.Common.Database.GetBoolean(reader, "logically_deleted", false);
                        dbAccount.UsageStartDatetime = eDoktor.Common.Database.GetDateTime(reader, "usage_start_datetime");
                        dbAccount.UsageEndDatetime = eDoktor.Common.Database.GetDateTime(reader, "usage_end_datetime");
                        dbAccount.UserId = eDoktor.Common.Database.GetInt32(reader, "user_id", 0);
                        dbAccount.GroupId = eDoktor.Common.Database.GetInt32(reader, "group_id", 0);
                        dbAccount.PrivilegeTemplateId = eDoktor.Common.Database.GetInt32(reader, "privilege_template_id", 0);
                        dbAccount.LogonId = eDoktor.Common.Database.GetString(reader, "logon_id", String.Empty);
                        dbAccount.Password = Crypto.DecryptString(eDoktor.Common.Database.GetString(reader, "password", Crypto.EncryptString(String.Empty)));
                        dbAccount.EncryptedPassword = eDoktor.Common.Database.GetString(reader, "encrypted_password", string.Empty);
                        dbAccount.LogonTo = eDoktor.Common.Database.GetString(reader, "logon_to", String.Empty);
                        dbAccount.PasswordUpdateDatetime = eDoktor.Common.Database.GetDateTime(reader, "password_update_datetime");
                    }
                }
            );

            return dbAccount;
        }
        
        /// <summary>
        /// ユーザー情報関連の処理
        /// </summary>
        /// <param name="bufferBytes">アカウント連携ファイルから1行分読み取ったバイト列</param>
        /// <param name="job">ユーザの職種情報</param>
        private Model.User UserProc(Model.LineAnalyzer line, Model.Job job)
        {
            //ユーザー関連情報の読み取り
            Model.User user = new Model.User(line, job);
            
            //ユーザー情報存在確認し、存在する場合は更新必要性チェック、存在しない場合は登録
            Model.User dbUser = GetUser(line.OrganizationalId);
            if (dbUser != null)
            {
                user.Id = dbUser.Id;

                //変更箇所がある場合のみ更新する
                if (CheckNeedUpdateUser(ref user, dbUser))
                {
                    eDoktor.Common.Trace.OutputTrace("情報更新あり");
                    UpdateUser(user);
                }
                else
                {
                    eDoktor.Common.Trace.OutputTrace("情報更新なし");
                }
            }
            else
            {
                eDoktor.Common.Trace.OutputTrace("新規ユーザーのため登録");
                InsertUser(user);

                //登録後、レコードIDを取得するために再取得
                user = GetUser(line.OrganizationalId);
            }

            return user;
        }

        /// <summary>
        /// ユーザー情報を更新する
        /// </summary>
        /// <param name="user"></param>
        private void UpdateUser(Model.User user)
        {
            StringBuilder query = new StringBuilder();
            query.Append("UPDATE t_users SET");
            query.Append(" modification_datetime = GETDATE()");
            query.AppendFormat(", modified_by = {0}", Configuration.Configuration.PractitionerID);
			if (Configuration.Configuration.InvalidatedDataOffset >= 0)
			{
				query.AppendFormat(", invalidated = '{0}'", user.Invalidated);
				if (user.IsInvalidatedChanged)
				{
					if (user.Invalidated)
					{
						query.Append(", invalidation_datetime = GETDATE()");
						query.AppendFormat(", invalidated_by = {0}", Configuration.Configuration.PractitionerID);
					}
					else
					{
						query.Append(", invalidation_datetime = null");
						query.AppendFormat(", invalidated_by = {0}", 0);
					}
				}
			}
			if (Configuration.Configuration.LogicallyDeletedDataOffset >= 0)
			{
				query.AppendFormat(", logically_deleted = '{0}'", user.LogicallyDeleted);
				if (user.IsLogicallyDeletedChanged)
				{
					if (user.LogicallyDeleted)
					{
						query.Append(", logical_deletion_datetime = GETDATE()");
						query.AppendFormat(", logically_deleted_by = {0}", Configuration.Configuration.PractitionerID);
					}
					else
					{
						query.Append(", logical_deletion_datetime = null");
						query.AppendFormat(", logically_deleted_by = {0}", 0);
					}
				}
			}
            query.AppendFormat(", synchronized = {0}", 1);
			if (Configuration.Configuration.UsageStartDatetimeDataOffset >= 0)
			{
				if (user.UsageStartDatetime == null)
				{
					query.Append(", usage_start_datetime = null");
				}
				else
				{
					query.AppendFormat(", usage_start_datetime = '{0}'", eDoktor.Common.Database.EscapeParam(user.UsageStartDatetime.ToString()));
				}
			}
			if (Configuration.Configuration.UsageEndDatetimeDataOffset >= 0)
			{
				if (user.UsageEndDatetime == null)
				{
					query.Append(", usage_end_datetime = null");
				}
				else
				{
					query.AppendFormat(", usage_end_datetime = '{0}'", eDoktor.Common.Database.EscapeParam(user.UsageEndDatetime.ToString()));
				}
			}
            query.AppendFormat(", organizational_id = '{0}'", eDoktor.Common.Database.EscapeParam(user.OrganizationalId));
            query.AppendFormat(", name = '{0}'", eDoktor.Common.Database.EscapeParam(user.Name));
			if (Configuration.Configuration.KanaDataOffset >= 0)
			{
				query.AppendFormat(", kana = '{0}'", eDoktor.Common.Database.EscapeParam(user.Kana));
			}
			if (Configuration.Configuration.BirthdateDataOffset >= 0)
			{
				if (user.Birthdate == null)
				{
					query.Append(", birthdate = null");
				}
				else
				{
					query.AppendFormat(", birthdate = '{0}'", eDoktor.Common.Database.EscapeParam(user.Birthdate.ToString()));
				}
			}
			if (Configuration.Configuration.JobCodeDataOffset >= 0)
			{
	            query.AppendFormat(", job_id = {0}", user.JobId);
			}
            query.AppendFormat(" WHERE id = {0}", user.Id);

			eDoktor.Common.Trace.OutputDebugTrace("query={0}", query.ToString());
            _database.ExecuteNonQuery(query.ToString());
        }

        /// <summary>
        /// ユーザー情報を更新する必要があるか判定する
        /// </summary>
        /// <param name="user">アカウント連携ファイルから読み取ったユーザー情報</param>
        /// <param name="dbUser">データベースから取得したユーザー情報</param>
        /// <returns></returns>
        private bool CheckNeedUpdateUser(ref Model.User user, Model.User dbUser)
        {
            bool result = false;

			if (Configuration.Configuration.InvalidatedDataOffset >= 0)
			{
				if (!user.Invalidated.Equals(dbUser.Invalidated))
				{
					//無効フラグ変更フラグを立てておく
					user.IsInvalidatedChanged = true;
					result = true;
				}
			}
			if (Configuration.Configuration.LogicallyDeletedDataOffset >= 0)
			{
				if (!user.LogicallyDeleted.Equals(dbUser.LogicallyDeleted))
				{
					//論理削除フラグ変更フラグを立てておく
					user.IsLogicallyDeletedChanged = true;
					result = true;
				}
			}
			if (Configuration.Configuration.UsageStartDatetimeDataOffset >= 0)
			{
				if (!user.UsageStartDatetime.Equals(dbUser.UsageStartDatetime))
				{
					result = true;
				}
			}
			if (Configuration.Configuration.UsageEndDatetimeDataOffset >= 0)
			{
				if (!user.UsageEndDatetime.Equals(dbUser.UsageEndDatetime))
				{
					result = true;
				}
			}
            if (!user.OrganizationalId.Equals(dbUser.OrganizationalId))
            {
                result = true;
            }
            if (!user.Name.Equals(dbUser.Name))
            {
                result = true;
            }
			if (Configuration.Configuration.KanaDataOffset >= 0)
			{
				if (!user.Kana.Equals(dbUser.Kana))
				{
					result = true;
				}
			}
			if (Configuration.Configuration.BirthdateDataOffset >= 0)
			{
				if (!user.Birthdate.Equals(dbUser.Birthdate))
				{
					result = true;
				}
			}
			if (Configuration.Configuration.JobCodeDataOffset >= 0)
			{
				if (!user.JobId.Equals(dbUser.JobId))
				{
					result = true;
				}
			}

            return result;
        }

        /// <summary>
        /// ユーザー情報をデータベースに登録する
        /// </summary>
        /// <param name="user">アカウント連携ファイルから読み取ったユーザー情報</param>
        private void InsertUser(Model.User user)
        {
            StringBuilder query = new StringBuilder();
            query.Append("INSERT INTO t_users ( ");
            query.Append("registration_datetime");
            query.Append(", registered_by");
            query.Append(", modification_datetime");
            query.Append(", modified_by");
            query.Append(", invalidated");
            query.Append(", invalidation_datetime");
            query.Append(", invalidated_by");
            query.Append(", logically_deleted");
            query.Append(", logical_deletion_datetime");
            query.Append(", logically_deleted_by");
            query.Append(", synchronized");
            query.Append(", usage_start_datetime");
            query.Append(", usage_end_datetime");
            query.Append(", organizational_id");
            query.Append(", name");
            query.Append(", kana");
            query.Append(", birthdate");
            query.Append(", job_id");
            query.Append(", notes) ");
            query.Append("VALUES ( ");
            query.Append("GETDATE()");
            query.AppendFormat(", {0}", Configuration.Configuration.PractitionerID);
            query.Append(", GETDATE()");
            query.AppendFormat(", {0}", Configuration.Configuration.PractitionerID);
            query.AppendFormat(", '{0}'", user.Invalidated);
            if (user.Invalidated)
            {
                query.Append(", GETDATE()");
                query.AppendFormat(", {0}", Configuration.Configuration.PractitionerID);
            }
            else
            {
                query.Append(", null");
                query.AppendFormat(", {0}", 0);
            }
            query.AppendFormat(", '{0}'", user.LogicallyDeleted);
            if (user.LogicallyDeleted)
            {
                query.Append(", GETDATE()");
                query.AppendFormat(", {0}", Configuration.Configuration.PractitionerID);
            }
            else
            {
                query.Append(", null");
                query.AppendFormat(", {0}", 0);
            }
            query.AppendFormat(", {0}", 1);
            if (user.UsageStartDatetime == null)
            {
                query.Append(", null");
            }
            else
            {
                query.AppendFormat(", '{0}'", eDoktor.Common.Database.EscapeParam(user.UsageStartDatetime.ToString()));
            }
            if (user.UsageEndDatetime == null)
            {
                query.Append(", null");
            }
            else
            {
                query.AppendFormat(", '{0}'", eDoktor.Common.Database.EscapeParam(user.UsageEndDatetime.ToString()));
            }
            query.AppendFormat(", '{0}'", eDoktor.Common.Database.EscapeParam(user.OrganizationalId));
            query.AppendFormat(", '{0}'", eDoktor.Common.Database.EscapeParam(user.Name));
            query.AppendFormat(", '{0}'", eDoktor.Common.Database.EscapeParam(user.Kana));
            query.AppendFormat(", '{0}'", eDoktor.Common.Database.EscapeParam(user.Birthdate.ToString()));
            query.AppendFormat(", {0}", user.JobId);
            query.AppendFormat(", '{0}'", String.Empty);
            query.Append(") ");

			eDoktor.Common.Trace.OutputDebugTrace("query={0}", query.ToString());
            _database.ExecuteNonQuery(query.ToString());
        }

        /// <summary>
        /// データベースからユーザー情報を取得する
        /// </summary>
        /// <param name="user">アカウント連携ファイルから読み取ったユーザー情報</param>
        /// <returns></returns>
        private Model.User GetUser(string orgId)
        {
            Model.User dbUser = null;
            StringBuilder query = new StringBuilder();
            query.Append("SELECT");
            query.Append(" u.id");
            query.Append(", u.invalidated");
            query.Append(", u.logically_deleted");
            query.Append(", u.usage_start_datetime");
            query.Append(", u.usage_end_datetime");
            query.Append(", u.organizational_id");
            query.Append(", u.name");
            query.Append(", u.kana");
            query.Append(", u.birthdate");
			query.Append(", j.id as jid ");
            query.Append("FROM t_users u ");
            query.Append("LEFT JOIN t_jobs j ON u.job_id = j.id");
            query.Append(" WHERE ");
            query.AppendFormat(" u.organizational_id = '{0}' ", eDoktor.Common.Database.EscapeParam(orgId));

            _database.ExecuteQuery(
                query.ToString(),
                delegate(DbDataReader reader)
                {
                    //存在しない場合はこれ以降の処理不要
                    if (!(eDoktor.Common.Database.GetInt32(reader, "id", -1) == -1))
                    {
                        dbUser = new Model.User();
                        dbUser.Id = eDoktor.Common.Database.GetInt32(reader, "id", -1);
                        dbUser.Invalidated = eDoktor.Common.Database.GetBoolean(reader, "invalidated", false);
                        dbUser.LogicallyDeleted = eDoktor.Common.Database.GetBoolean(reader, "logically_deleted", false);
                        dbUser.UsageStartDatetime = eDoktor.Common.Database.GetDateTime(reader, "usage_start_datetime");
                        dbUser.UsageEndDatetime = eDoktor.Common.Database.GetDateTime(reader, "usage_end_datetime");
                        dbUser.OrganizationalId = eDoktor.Common.Database.GetString(reader, "organizational_id", String.Empty);
                        dbUser.Name = eDoktor.Common.Database.GetString(reader, "name", String.Empty);
                        dbUser.Kana = eDoktor.Common.Database.GetString(reader, "kana", String.Empty);
                        dbUser.Birthdate = eDoktor.Common.Database.GetDateTime(reader, "birthdate");
                        dbUser.JobId = eDoktor.Common.Database.GetInt32(reader, "jid", -1);
                    }
                }
            );

            return dbUser;
        }

        /// <summary>
        /// 職種情報関連の処理
        /// </summary>
        /// <param name="line">解析済みのアカウント連携データ</param>
        private Model.Job JobProc(Model.LineAnalyzer line)
        {
			if (string.IsNullOrWhiteSpace(line.JobCode))
			{
				return null;
			}

            // 職種関連情報の読み取り
            Model.Job job = new Model.Job();

            // t_jobsの職種情報を取得
            Model.Job dbJob = GetJob(line.JobCode);
            if (dbJob == null)
            {
                // 未登録の場合は新規登録
                InsertJob(line);
            }
            else
            {
                // 登録済みの場合は更新（職種名がアカウント連携に含まれていない場合は更新しない）
                if (Configuration.Configuration.JobNameDataLength > 0)
                {
                    UpdateJob(line);
                }
            }

            // 登録・更新後の情報を再取得
            job = GetJob(line.JobCode);

            return job;
        }

        /// <summary>
        /// 職種をデータベースから取得する
        /// </summary>
        /// <param name="jobCode">職種コード</param>
        /// <returns>データベースから取得した職種情報</returns>
        private Model.Job GetJob(string jobCode)
        {

            Model.Job dbJob = null;

            StringBuilder query = new StringBuilder();
            query.AppendFormat("select id, account_group_id, privilege_template_id from t_jobs where job_code = '{0}'", eDoktor.Common.Database.EscapeParam(jobCode));

            _database.ExecuteQuery(
                query.ToString(),
                delegate(DbDataReader reader)
                {
                    if (!(eDoktor.Common.Database.GetInt32(reader, "id", -1) == -1))
                    {
                        dbJob = new Model.Job();
                        dbJob.Id = eDoktor.Common.Database.GetInt32(reader, "id", 0);
                        dbJob.AccountGroupId = eDoktor.Common.Database.GetInt32(reader, "account_group_id", 0);
                        dbJob.PrivilegeTemplateId = eDoktor.Common.Database.GetInt32(reader, "privilege_template_id", 0);
                    }
                }
            );

            return dbJob;
        }

        /// <summary>
        /// 職種情報を登録
        /// </summary>
        /// <param name="line">連携情報</param>
        private void InsertJob(Model.LineAnalyzer line)
        {
            StringBuilder query = new StringBuilder();
            query.Append("insert into t_jobs ( ");
            query.Append("registration_datetime, ");
            query.Append("registered_by, ");
            query.Append("modification_datetime, ");
            query.Append("modified_by, ");
            query.Append("invalidated, ");
            query.Append("invalidation_datetime, ");
            query.Append("invalidated_by, ");
            query.Append("logically_deleted, ");
            query.Append("logical_deletion_datetime, ");
            query.Append("logically_deleted_by, ");
            query.Append("synchronized, ");
            query.Append("job_code, ");
            query.Append("job_name, ");
            query.Append("account_group_id, ");
            query.Append("privilege_template_id, ");
            query.Append("notes) ");
            query.Append("values ( ");
            query.Append("GETDATE(), ");
            query.AppendFormat("{0}, ", Configuration.Configuration.PractitionerID);
            query.Append("GETDATE(), ");
            query.AppendFormat("{0}, ", Configuration.Configuration.PractitionerID);
            query.AppendFormat("{0}, ", 0);
            query.Append("null, ");
            query.AppendFormat("{0}, ", 0);
            query.AppendFormat("{0}, ", 0);
            query.Append("null, ");
            query.AppendFormat("{0}, ", 0);
            query.AppendFormat("{0}, ", 1);
            query.AppendFormat("'{0}', ", eDoktor.Common.Database.EscapeParam(line.JobCode));
            query.AppendFormat("'{0}', ", eDoktor.Common.Database.EscapeParam((string.IsNullOrWhiteSpace(line.JobName) ? line.JobCode : line.JobName)));
            query.AppendFormat("{0}, ", Configuration.Configuration.DefaultAccountGroupID);
            query.AppendFormat("{0}, ", Configuration.Configuration.DefaultPrivilegeTemplateID);
            query.AppendFormat("'{0}' ", string.Empty);
            query.Append(") ");
            _database.ExecuteNonQuery(query.ToString());
        }

        /// <summary>
        /// 職種情報を更新
        /// </summary>
        /// <param name="line">連携情報</param>
        private void UpdateJob(Model.LineAnalyzer line)
        {
            StringBuilder query = new StringBuilder();
            query.AppendFormat("UPDATE t_jobs SET");
            query.AppendFormat(" modification_datetime = GETDATE()");
            query.AppendFormat(", modified_by = {0}", Configuration.Configuration.PractitionerID);
            query.AppendFormat(", invalidated = 'false'");
            query.AppendFormat(", invalidation_datetime = null");
            query.AppendFormat(", invalidated_by = 0");
            query.AppendFormat(", logically_deleted = 'false'");
            query.AppendFormat(", logical_deletion_datetime = null");
            query.AppendFormat(", logically_deleted_by = 0");
            query.AppendFormat(", job_name = '{0}'", eDoktor.Common.Database.EscapeParam(line.JobName));
            query.AppendFormat(" WHERE job_code = '{0}'", eDoktor.Common.Database.EscapeParam(line.JobCode));
            query.AppendFormat(" AND (invalidated = 'true' OR logically_deleted = 'true' OR job_name != '{0}')", eDoktor.Common.Database.EscapeParam(line.JobName));
            _database.ExecuteNonQuery(query.ToString());
        }

        /// <summary>
        /// エラー情報をデータベースに登録する
        /// </summary>
        /// <param name="errorMessage">エラーメッセージ</param>
        /// <param name="database">接続情報設定済みのDatabaseインスタンス</param>
        private void InsertError(string errorMessage)
        {
			if (!Configuration.Configuration.RegistErrorTable)
			{
				return;
			}

            try
            {
                const string ErrorInsertSql = "INSERT INTO t_errors(" +
                                              "registration_datetime, " +
                                              "registered_by, " +
                                              "modification_datetime, " +
                                              "modified_by, " +
                                              "source, " +
                                              "status, " +
                                              "alarm_flg, " +
                                              "message, " +
                                              "notes) " +
                                              "VALUES(" +
                                              "GETDATE(), " +                   // registration_datetime
                                              "{0}, " +                         // {0}registered_by
                                              "DEFAULT, " +                     // modification_datetime
                                              "{1}, " +                         // {1}modified_by
                                              "{2}, " +                         // {2}source
                                              "{3}, " +                         // {3}status
                                              "DEFAULT, " +                     // alarm_flg(省略値は0:未発報)
                                              "'{4}', " +                       // {4}message
                                              "DEFAULT);";                      // notes
                string sql = string.Format(ErrorInsertSql,
                                           Configuration.Configuration.PractitionerID,                      // {0}registered_by
                                           Configuration.Configuration.PractitionerID,                      // {1}modified_by
                                           Configuration.Configuration.ErrorSource,                         // {2}source
                                           0,                                   // {3}status
                                           eDoktor.Common.Database.EscapeParam(errorMessage)); // {4}message
                _database.ExecuteNonQuery(sql);
            }
            catch (System.Data.SqlClient.SqlException ex)
            {
                //DBに繋がらない系エラーは諦める
            }
        }

        /// <summary>
        /// AD情報関連の処理
        /// </summary>
        /// <param name="line">解析済みのアカウント連携データ</param>
        private System.DirectoryServices.SearchResult ADProc(Model.LineAnalyzer line)
        {
            // 職種関連情報の読み取り
            // ADのアカウント情報を取得
            System.DirectoryServices.SearchResult adUser = GetADUser(line.LdapKey);
            if (adUser == null)
            {
                // 未登録の場合は新規登録
                eDoktor.Common.Trace.OutputTrace("新規ユーザのため登録");
                adUser = RegisterADUser(line);
            }
            else
            {
                //変更箇所がある場合のみ更新する
                if (CheckNeedUpdateADUser(line, adUser))
                {
                    eDoktor.Common.Trace.OutputTrace("情報更新あり");
                    adUser = UpdateADUser(line, adUser);
                }
                else
                {
                    eDoktor.Common.Trace.OutputTrace("情報更新なし");
                }
            }

            return adUser;
        }

        private System.DirectoryServices.SearchResult GetADUser(Dictionary<string, string> keys)
        {
            // AD情報の取得
            using (System.DirectoryServices.DirectoryEntry entry = 
                new System.DirectoryServices.DirectoryEntry(Configuration.Configuration.LdapUrl, Configuration.Configuration.LdapUser, Configuration.Configuration.LdapPassword, Configuration.Configuration.LdapAuthenticationTypes))
            {
                object obj = entry.NativeObject;

                using (System.DirectoryServices.DirectorySearcher search = new System.DirectoryServices.DirectorySearcher())
                {
                    search.SearchRoot = entry;
                    StringBuilder filter = new StringBuilder();
                    filter.Append("(&(objectCategory=User)");
                    foreach (KeyValuePair<string, string> pair in keys)
                    {
                        filter.AppendFormat("({0}={1})", pair.Key, pair.Value);
                    }
                    filter.Append(")");
                    search.Filter = filter.ToString();
                    System.DirectoryServices.SearchResult searchResult = search.FindOne();

                    return searchResult;
                }
            }

        }

        private System.DirectoryServices.SearchResult RegisterADUser(Model.LineAnalyzer line)
        {
            using (System.DirectoryServices.DirectoryEntry entry =
                new System.DirectoryServices.DirectoryEntry(Configuration.Configuration.LdapUrl, Configuration.Configuration.LdapUser, Configuration.Configuration.LdapPassword, Configuration.Configuration.LdapAuthenticationTypes))
            {
                object obj = entry.NativeObject;

                // OUの検索
                string ou = System.Text.RegularExpressions.Regex.Match(Configuration.Configuration.LdapUrl, @"OU=[^,]+").Value;
                var entryOU = entry.Children.Find(ou);

                // ユーザ作成
                var user = entry.Children.Add(string.Format("{0}={1}", line.LdapKey.First().Key, line.LdapKey.First().Value), "user");
                foreach (KeyValuePair<string, string> pair in line.LdapProperty)
                {
                    if (!string.IsNullOrWhiteSpace(pair.Value))
                    {
                        user.Properties[pair.Key].Value = pair.Value;
                    }
                }

                user.CommitChanges();

                // ユーザ有効化
                int userAccountControl = (int)user.Properties["userAccountControl"].Value;
                if (!line.Invalidated && !line.LogicallyDeleted
                    && (line.UsageStartDatetime == null || line.UsageStartDatetime <= DateTime.Now)
                    && (line.UsageEndDatetime == null || line.UsageEndDatetime >= DateTime.Now))
                {
                    user.Properties["userAccountControl"].Value = userAccountControl & ~0x2;    // 無効フラグを消す
                }
                else
                {
                    user.Properties["userAccountControl"].Value = userAccountControl | 0x2;     // 無効フラグを付与する
                }
                user.CommitChanges();

                user.Invoke("SetPassword", Configuration.Configuration.InitialPassword);
            }

            // 登録・更新後の情報を再取得
            System.DirectoryServices.SearchResult adUser = GetADUser(line.LdapKey);

            return adUser;
        }

        private bool CheckNeedUpdateADUser(Model.LineAnalyzer line, System.DirectoryServices.SearchResult adUser)
        {
            // 設定された属性の内容を比較
            foreach (KeyValuePair<string, string> pair in line.LdapProperty)
            {
                string temp = (adUser.Properties[pair.Key].Count > 0) ? adUser.Properties[pair.Key][0].ToString() : string.Empty;
                if (pair.Value != temp)
                {
                    return true;
                }
            }

            // 無効フラグを比較
            bool newInvalid = line.Invalidated || line.LogicallyDeleted
                || (line.UsageStartDatetime != null && line.UsageStartDatetime > DateTime.Now)
                || (line.UsageEndDatetime != null && line.UsageEndDatetime < DateTime.Now);

            bool oldInvalid = ((int)adUser.Properties["userAccountControl"][0] & 0x2) == 0x2;
            
            if (newInvalid != oldInvalid)
            {
                return true;
            }

            return false;
        }

        private System.DirectoryServices.SearchResult UpdateADUser(Model.LineAnalyzer line, System.DirectoryServices.SearchResult searchResult)
        {
            using (System.DirectoryServices.DirectoryEntry entry =
                new System.DirectoryServices.DirectoryEntry(Configuration.Configuration.LdapUrl, Configuration.Configuration.LdapUser, Configuration.Configuration.LdapPassword, Configuration.Configuration.LdapAuthenticationTypes))
            {
                object obj = entry.NativeObject;

                if (searchResult == null)
                {
                    throw new Exception("ADに更新ユーザがいませんでした。");
                }

                // ユーザ変更
                var user = searchResult.GetDirectoryEntry();
                foreach (KeyValuePair<string, string> pair in line.LdapProperty)
                {
                    user.Properties[pair.Key].Value = (string.IsNullOrWhiteSpace(pair.Value)) ? null : pair.Value;
                }

                // ユーザ有効化
                int userAccountControl = (int)user.Properties["userAccountControl"].Value;
                if (!line.Invalidated && !line.LogicallyDeleted)
                {
                    user.Properties["userAccountControl"].Value = userAccountControl & ~0x2;    // 無効フラグを消す
                }
                else
                {
                    user.Properties["userAccountControl"].Value = userAccountControl | 0x2;     // 無効フラグを付与する
                }

                user.CommitChanges();
            }

            // 登録・更新後の情報を再取得
            System.DirectoryServices.SearchResult adUser = GetADUser(line.LdapKey);

            return adUser;
        }

        private void ShareProc(string userName)
        {
            // フォルダ確認
            string personalPath = System.IO.Path.Combine(Configuration.Configuration.ShareLocalDrive, userName);
            if (System.IO.Directory.Exists(personalPath))
            {
                // 個人フォルダが存在する場合は何もしない
                eDoktor.Common.Trace.OutputTrace("個人フォルダ作成済み。");
            }
            else
            {
                eDoktor.Common.Trace.OutputTrace("個人フォルダを作成します。");

                int retry = 0;
                for (retry = 0; retry < Configuration.Configuration.MaxRetryCount; retry++)
                {
                    eDoktor.Common.Trace.OutputTrace("個人フォルダ作成：" + (retry + 1) + "回目");

                    try
                    {
                        CreateNewDirectory(userName, personalPath);

                        break;
                    }
                    catch (Exception ex)
                    {
                        eDoktor.Common.Trace.OutputExceptionTrace(ex);
                    }

                    // 失敗時は指定秒数待機
                    System.Threading.Thread.Sleep(Configuration.Configuration.RetryInterval);
                }

                //リトライ回数超過時はエラー出力して終了
                if (retry == Configuration.Configuration.MaxRetryCount)
                {
                    //エラー出力処理
                    InsertError("個人フォルダの作成に失敗しました。");
                    return;
                }
            }
        }


        [System.Runtime.InteropServices.StructLayout(System.Runtime.InteropServices.LayoutKind.Sequential)]
        internal struct NETRESOURCE
        {
            public int dwScope;                 //列挙の範囲
            public int dwType;                  //リソースタイプ
            public int dwDisplayType;           //表示オブジェクト
            public int dwUsage;                 //リソースの使用方法
            [System.Runtime.InteropServices.MarshalAs(System.Runtime.InteropServices.UnmanagedType.LPWStr)]
            public string lpLocalName;          //ローカルデバイス名。使わないならNULL。
            [System.Runtime.InteropServices.MarshalAs(System.Runtime.InteropServices.UnmanagedType.LPWStr)]
            public string lpRemoteName;         //リモートネットワーク名。使わないならNULL
            [System.Runtime.InteropServices.MarshalAs(System.Runtime.InteropServices.UnmanagedType.LPWStr)]
            public string lpComment;            //ネットワーク内の提供者に提供された文字列
            [System.Runtime.InteropServices.MarshalAs(System.Runtime.InteropServices.UnmanagedType.LPWStr)]
            public string lpProvider;           //リソースを所有しているプロバイダ名
        }
        [System.Runtime.InteropServices.DllImport("mpr.dll", EntryPoint = "WNetCancelConnection2", CharSet = System.Runtime.InteropServices.CharSet.Unicode)]
        private static extern int WNetCancelConnection2(string lpName, Int32 dwFlags, bool fForce);

        [System.Runtime.InteropServices.DllImport("mpr.dll", EntryPoint = "WNetAddConnection2", CharSet = System.Runtime.InteropServices.CharSet.Unicode)]
        private static extern int WNetAddConnection2(ref NETRESOURCE lpNetResource, string lpPassword, string lpUsername, Int32 dwFlags);

		private bool ConnectSourceDirectory()
		{
			try
			{
				// ネットワークドライブから切断
				//WNetCancelConnection2(Configuration.Configuration.SourceDirectoryPath, 0, true);
				WNetCancelConnection2(Configuration.Configuration.SourceFileUri, 0, true);

				// ネットワークドライブに接続
				NETRESOURCE netResource = new NETRESOURCE();
				netResource.dwScope = 0;
				netResource.dwType = 1;
				netResource.dwDisplayType = 0;
				netResource.dwUsage = 0;
				netResource.lpLocalName = string.Empty;   // ネットワークドライブにする場合は"z:"などドライブレター設定
				//netResource.lpRemoteName = Configuration.Configuration.SourceDirectoryPath;
				netResource.lpRemoteName = Configuration.Configuration.SourceFileUri;
				netResource.lpProvider = "";

				for (int i = 0; i < Configuration.Configuration.MaxRetryCount; i++)
				{
					try
					{
						eDoktor.Common.Trace.OutputTrace("利用者連携 共有フォルダ接続：{0}回目", (i + 1));

						int ret = WNetAddConnection2(ref netResource, Configuration.Configuration.ConnectPassword, Configuration.Configuration.ConnectUser, 0);

						eDoktor.Common.Trace.OutputTrace("結果：{0}", ret);

						if (ret == 0)
						{
							return true;
						}

						// 一定時間待機
						System.Threading.Thread.Sleep(Configuration.Configuration.RetryInterval);
					}
					catch (Exception ex)
					{
						eDoktor.Common.Trace.OutputExceptionTrace(ex);
					}
				}

				return false;
			}
			catch (Exception ex)
			{
				eDoktor.Common.Trace.OutputExceptionTrace(ex);

				return false;
			}
		}

		private void DisconnectSourceDirectory()
		{
			try
			{
				// ネットワークドライブから切断
				//WNetCancelConnection2(Configuration.Configuration.SourceDirectoryPath, 0, true);
				WNetCancelConnection2(Configuration.Configuration.SourceFileUri, 0, true);
			}
			catch (Exception ex)
			{
				eDoktor.Common.Trace.OutputExceptionTrace(ex);
			}
		}


        private bool ConnectShareDirectory()
        {
            try
            {
                // ネットワークドライブから切断
                WNetCancelConnection2(Configuration.Configuration.ShareLocalDrive, 0, true);

                // ネットワークドライブに接続
                NETRESOURCE netResource = new NETRESOURCE();
                netResource.dwScope = 0;
                netResource.dwType = 1;
                netResource.dwDisplayType = 0;
                netResource.dwUsage = 0;
                netResource.lpLocalName = Configuration.Configuration.ShareLocalDrive;   // ネットワークドライブにする場合は"z:"などドライブレター設定
                netResource.lpRemoteName = Configuration.Configuration.SharePath;
                netResource.lpProvider = "";

                for (int i = 0; i < Configuration.Configuration.ShareRetryCount; i++)
                {
                    try
                    {
                        eDoktor.Common.Trace.OutputTrace("共有フォルダ接続：{0}回目", (i + 1));
                        
                        int ret = WNetAddConnection2(ref netResource, Configuration.Configuration.SharePassword, Configuration.Configuration.ShareUser, 0);

                        eDoktor.Common.Trace.OutputTrace("結果：{0}", ret);

                        if (ret == 0)
                        {
                            return true;
                        }

                        // 一定時間待機
                        System.Threading.Thread.Sleep(Configuration.Configuration.ShareRetryInterval);
                    }
                    catch (Exception ex)
                    {
                        eDoktor.Common.Trace.OutputExceptionTrace(ex);
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);

                return false;
            }
        }

        private void DisconnectShareDirectory()
        {
            try
            {
                // ネットワークドライブから切断
                WNetCancelConnection2(Configuration.Configuration.ShareLocalDrive, 0, true);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        private void CreateNewDirectory(string userName, string personalPath)
        {
            // フォルダ作成
            System.IO.Directory.CreateDirectory(personalPath);

            // アクセス権取得
            var sec = System.IO.Directory.GetAccessControl(personalPath);

            // 継承しているアクセス権を変更可能とする
            sec.SetAccessRuleProtection(true, true);

            // アクセス権削除（不要ユーザの）
            foreach (string user in Configuration.Configuration.ShareUnnecessaryUserList)
            {
                sec.PurgeAccessRules(new System.Security.Principal.NTAccount(user));
            }

            // ここで一度アクセス権を適用する
            System.IO.Directory.SetAccessControl(personalPath, sec);
            
            // 再度アクセス権を取得
            sec = System.IO.Directory.GetAccessControl(personalPath);

            // アクセス権付与（該当ユーザの）
            sec.AddAccessRule(new System.Security.AccessControl.FileSystemAccessRule(
                string.Format("{0}\\{1}", Configuration.Configuration.ShareDomain, userName)
                , System.Security.AccessControl.FileSystemRights.Modify
                , System.Security.AccessControl.InheritanceFlags.ContainerInherit | System.Security.AccessControl.InheritanceFlags.ObjectInherit
                , System.Security.AccessControl.PropagationFlags.None
                , System.Security.AccessControl.AccessControlType.Allow));

            System.IO.Directory.SetAccessControl(personalPath, sec);
        }

        private bool CheckExcludedAccount(string userName)
        {
            foreach (string user in Configuration.Configuration.ExcludedAccountList)
            {
                if (string.Compare(user, userName, StringComparison.OrdinalIgnoreCase) == 0)
                {
                    // 対象外リストに一致するため対象外とする
                    return true;
                }
            }

            // 対象外リストに含まれなかったため対象とする
            return false;
        }
        #endregion
    }
}
