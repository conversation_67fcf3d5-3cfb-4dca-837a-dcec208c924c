﻿// MainForm.cs

using System;
using System.Windows.Forms;

namespace eDoktor.AutoComplete.AuthModuleTester
{
	public partial class MainForm : Form
	{
		#region Fields
		AuthModule.AuthModuleLoader _authModuleLoader;
		System.Threading.Timer _abortTimer;
		#endregion

		#region Constructors
		public MainForm()
		{
			InitializeComponent();
		}
		#endregion

		#region Protected Methods
		protected override void OnLoad(EventArgs e)
		{
			try
			{
				base.OnLoad(e);
				var authContextArray = new string[] { "クレデンシャルプロバイダ", "デスクトップ" };
				authContextComboBox.Items.AddRange(authContextArray);
				authContextComboBox.SelectedIndex = 0;
				var authModeArray = new string[] { "ユーザー認証", "ユーザー検索" };
				authModeComboBox.Items.AddRange(authModeArray);
				authModeComboBox.SelectedIndex = 0;
				var displayPositionArray = new string[]
				{
					"未定義",
					"親ウィンドウの中央",
					"中央",
					"左上",
					"上",
					"右上",
					"右",
					"右下",
					"下",
					"左下",
					"左"
				};
				displayPositionComboBox.Items.AddRange(displayPositionArray);
				displayPositionComboBox.SelectedIndex = 0;
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message);
			}
		}

		protected override void OnFormClosed(FormClosedEventArgs e)
		{
			base.OnFormClosed(e);

			var timer = _abortTimer;

			if (timer != null)
			{
				timer.Dispose();
				_abortTimer = null;
			}
		}
		#endregion

		#region Private Methods
		private void OnAuthButtonClick(object sender, EventArgs e)
		{
			try
			{
				if ((_authModuleLoader != null) || (_abortTimer != null))
				{
					return;
				}

				_authModuleLoader = new AuthModule.AuthModuleLoader();
				var authRequest = new AuthModule.AuthRequest()
				{
					Context = (AuthModule.AuthContextType)authContextComboBox.SelectedIndex,
					Mode = (AuthModule.AuthModeType)authModeComboBox.SelectedIndex,
					OwnerHandle = Handle,
					UserName = userNameTextBox.Text,
					DisplayPosition = (AuthModule.DisplayPositionType)displayPositionComboBox.SelectedIndex,
					Margin = new Padding((int)marginLeftNumericUpDown.Value, (int)marginTopNumericUpDown.Value, (int)marginRightNumericUpDown.Value, (int)marginBottomNumericUpDown.Value),
					TopMost = topMostCheckBox.Checked,
					Draggable = draggableCheckBox.Checked
				};
				string assemblyNameOrPath = Common.Configuration.AppSetting("AuthModuleAssemblyNameOrPath");
				string typeName = Common.Configuration.AppSetting("AuthModuleTypeName");
				AuthModule.AuthResponse authResponse = null;

				if (abortCheckBox.Checked)
				{
					int millisecondsTimeout = (int)abortMillisecondsNmericUpDown.Value;

					using (_abortTimer = new System.Threading.Timer(OnTimer, null, millisecondsTimeout, System.Threading.Timeout.Infinite))
					{
						authResponse = _authModuleLoader.PerformAuthentication(assemblyNameOrPath, typeName, authRequest);
					}
				}
				else
				{
					authResponse = _authModuleLoader.PerformAuthentication(assemblyNameOrPath, typeName, authRequest);
				}

				MessageBox.Show((authResponse != null) ? string.Format("認証結果={0} 認証方法={1} ユーザー名={2}", authResponse.Result, authResponse.Methods, authResponse.UserName) : "レスポンスがnullです。");
			}
			catch (Exception ex)
			{
				MessageBox.Show(ex.Message);
			}
			finally
			{
				_authModuleLoader = null;

				if (_abortTimer != null)
				{
					_abortTimer.Dispose();
					_abortTimer = null;
				}
			}
		}

		private void OnTimer(object state)
		{
			var helper = _authModuleLoader;

			if (helper != null)
			{
				helper.Abort();
			}
		}
		#endregion
	}
}
