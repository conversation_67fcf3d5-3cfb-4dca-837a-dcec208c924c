#ifndef _LIVENESSCHECK_HPP_
#define _LIVENESSCHECK_HPP_

#include <string>
#include <memory>
#include <opencv2/opencv.hpp>

namespace sw{
namespace livenesscheck{

  struct ExternalFilePath
  {
    std::string faceDetectModel;
    std::string faceDetectParam;
    std::string fakeModelH;
    std::string fakeModelV;
    std::string faceDirHModel;
    std::string faceDirVModel;
    std::string glassesModel;
    std::string maskModel;
    std::string eyeDetectModel;
    std::string eyeStatusModel;
    std::string eyeDirModel;
  };

  struct Params
  {
    int minFaceWidth;
    int maxFaceWidth;
    float faceAreaMinRatio;
    float faceAreaMaxRatio;
    bool edgePosErrMode;
    bool isFakeMode;
    float fakeJudgeTh;
    bool isFaceDirMode;
    bool isGlassesMode;
    bool isMaskMode;
    float maskJudgeTh;
    bool isEyeMode;
    bool isEyeDirMode;
  };

  class BioData
  {
  public:
    BioData();
    ~BioData();

    cv::Mat originalImg;
    cv::Mat checkImg;

    cv::Mat faceRectImg;
    cv::Rect faceRectArea;
    float faceBrightness;
    bool isValidFace;
    std::vector<cv::Rect> vFaceRect;

    bool isFakeFace;
    float isFakeLikelihood;
    short faceDirStatusH;
    short faceDirStatusV;

    short eyeGlassStatus;

    bool maskStatus;
    float isMaskLikelihood;

    cv::Mat eyeRectImgLeft;
    cv::Mat eyeRectImgRight;
    cv::Rect eyeRectAreaLeft;
    cv::Rect eyeRectAreaRight;
    float eyeRectAvgBrightLeft;
    float eyeRectAvgBrightRight;
    short eyeStatusLeft;
    short eyeStatusRight;

    short eyeDirStatusLeftH;
    short eyeDirStatusRightH;

    std::string msg;
    void clear();
  };

  class LivenessCheck
  {
  public:
      virtual ~LivenessCheck() {};
      virtual bool init(const ExternalFilePath &path, const Params &params=Params()) = 0;
      virtual bool registerBioData(std::shared_ptr< BioData >) = 0;
      virtual bool process(cv::Mat data, cv::Rect faceRect=cv::Rect(0,0,0,0)) = 0;
  };

  class ActivationChallenge
    {
    public:
        virtual ~ActivationChallenge() {}
        virtual std::string config() = 0;
        virtual bool checkToken(const char* token) = 0;
        virtual bool validToken(const char* token) = 0;
        virtual long getExpDate(const char* token) = 0;
    };

}  // namespace livenesscheck
}  // namespace sw

extern "C" {
  sw::livenesscheck::LivenessCheck *newLivenessCheck();
  void releaseLivenessCheck(sw::livenesscheck::LivenessCheck*);

  sw::livenesscheck::ActivationChallenge *newActivationChallenge();
  void releaseActivationChallenge(sw::livenesscheck::ActivationChallenge*);
}

#endif // _LIVENESSCHECK_HPP_
