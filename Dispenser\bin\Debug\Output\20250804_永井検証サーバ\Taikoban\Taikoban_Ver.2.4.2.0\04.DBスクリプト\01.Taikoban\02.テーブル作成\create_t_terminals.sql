USE [$(Param1)]
GO
/****** Object:  Table [dbo].[t_terminals]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_terminals]') AND type in (N'U'))
DROP TABLE [dbo].[t_terminals]
GO

CREATE TABLE [dbo].[t_terminals](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[registered_by] [int] NOT NULL,
	[modification_datetime] [datetime2](7) NOT NULL,
	[modified_by] [int] NOT NULL,
	[invalidated] [bit] NOT NULL,
	[invalidation_datetime] [datetime2](7) NULL,
	[invalidated_by] [int] NOT NULL,
	[logically_deleted] [bit] NOT NULL,
	[logical_deletion_datetime] [datetime2](7) NULL,
	[logically_deleted_by] [int] NOT NULL,
	[usage_start_datetime] [datetime2](7) NULL,
	[usage_end_datetime] [datetime2](7) NULL,
	[group_id] [int] NOT NULL,
	[name] [nvarchar](255) NOT NULL,
	[use_sbc] [bit] NOT NULL,
	[authentication_enabled] [bit] NOT NULL,
	[shared_account_user] [nvarchar](max) NOT NULL,
	[shared_account_password] [nvarchar](max) NOT NULL,
	[shared_account_domain] [nvarchar](max) NOT NULL,
	[notes] [nvarchar](255) NOT NULL,
	[usage_type] [int] NOT NULL,
	[allow_clock_in_out] [int] NOT NULL,
 CONSTRAINT [PK_t_terminals] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [IX_t_terminals] UNIQUE NONCLUSTERED 
(
	[name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_terminals] ADD  CONSTRAINT [DF_t_terminals_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO
ALTER TABLE [dbo].[t_terminals] ADD  CONSTRAINT [DF_t_terminals_registered_by]  DEFAULT ((0)) FOR [registered_by]
GO
ALTER TABLE [dbo].[t_terminals] ADD  CONSTRAINT [DF_t_terminals_modification_datetime]  DEFAULT (getdate()) FOR [modification_datetime]
GO
ALTER TABLE [dbo].[t_terminals] ADD  CONSTRAINT [DF_t_terminals_modified_by]  DEFAULT ((0)) FOR [modified_by]
GO
ALTER TABLE [dbo].[t_terminals] ADD  CONSTRAINT [DF_t_terminals_invalidated]  DEFAULT ('false') FOR [invalidated]
GO
ALTER TABLE [dbo].[t_terminals] ADD  CONSTRAINT [DF_t_terminals_invalidated_by]  DEFAULT ((0)) FOR [invalidated_by]
GO
ALTER TABLE [dbo].[t_terminals] ADD  CONSTRAINT [DF_t_terminals_logically_deleted]  DEFAULT ('false') FOR [logically_deleted]
GO
ALTER TABLE [dbo].[t_terminals] ADD  CONSTRAINT [DF_t_terminals_logically_deleted_by]  DEFAULT ((0)) FOR [logically_deleted_by]
GO
ALTER TABLE [dbo].[t_terminals] ADD  CONSTRAINT [DF_t_terminals_group_id]  DEFAULT ((0)) FOR [group_id]
GO
ALTER TABLE [dbo].[t_terminals] ADD  CONSTRAINT [DF_t_terminals_name]  DEFAULT ('') FOR [name]
GO
ALTER TABLE [dbo].[t_terminals] ADD  CONSTRAINT [DF_t_terminals_use_sbc]  DEFAULT ('false') FOR [use_sbc]
GO
ALTER TABLE [dbo].[t_terminals] ADD  CONSTRAINT [DF_t_terminals_authentication_enabled]  DEFAULT ('true') FOR [authentication_enabled]
GO
ALTER TABLE [dbo].[t_terminals] ADD  CONSTRAINT [DF_t_terminals_shared_account_user]  DEFAULT ('') FOR [shared_account_user]
GO
ALTER TABLE [dbo].[t_terminals] ADD  CONSTRAINT [DF_t_terminals_shared_account_password]  DEFAULT ('') FOR [shared_account_password]
GO
ALTER TABLE [dbo].[t_terminals] ADD  CONSTRAINT [DF_t_terminals_shared_account_domain]  DEFAULT ('') FOR [shared_account_domain]
GO
ALTER TABLE [dbo].[t_terminals] ADD  CONSTRAINT [DF_t_terminals_notes]  DEFAULT ('') FOR [notes]
GO
ALTER TABLE [dbo].[t_terminals] ADD  CONSTRAINT [DF_t_terminals_usage_type]  DEFAULT ((0)) FOR [usage_type]
GO
ALTER TABLE [dbo].[t_terminals] ADD  CONSTRAINT [DF_t_terminals_allow_clock_in_out]  DEFAULT ((0)) FOR [allow_clock_in_out]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'registered_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'modification_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'modified_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'invalidated'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効化日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'invalidation_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効化を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'invalidated_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'logically_deleted'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'logical_deletion_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'logically_deleted_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'有効期間開始日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'usage_start_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'有効期限終了日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'usage_end_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'所属する端末グループのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'group_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'端末名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'SBC利用端末 0:未利用 1:利用' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'use_sbc'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'Taikoban利用 0:無効 1:有効' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'authentication_enabled'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'共用アカウントのユーザー' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'shared_account_user'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'共用アカウントのユーザーパスワード' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'shared_account_password'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'共用アカウントのドメイン' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'shared_account_domain'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'注記' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'notes'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用途タイプ 0:標準 1:手術室' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'usage_type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'出退勤操作を許可 0:しない 1:する' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_terminals', @level2type=N'COLUMN',@level2name=N'allow_clock_in_out'
GO
