﻿// PasswordCredentialPage.cs

using System;
using System.Windows.Forms;

namespace eDoktor.AutoComplete.AuthUI
{
	using Common.EventExtensions;

	partial class PasswordCredentialPage : PageBase
	{
		#region Events
		public event EventHandler<PasswordCredentialEventArgs> AuthenticateUser;
		public event EventHandler CancelButtonClick;
		#endregion

		#region Constructors
		public PasswordCredentialPage()
		{
			InitializeComponent();
		}
		#endregion

		#region Protected Methods
		protected override void OnVisibleChanged(EventArgs e)
		{
			base.OnVisibleChanged(e);

			if (Visible)
			{
				userNameTextBox.Focus();

                // ▼ ADD CapsLockメッセージ対応 2023/01/01 eDoktor Y.Kihara
                capsLockLabel.Visible = Control.IsKeyLocked(Keys.CapsLock);
                // ▲ ADD CapsLockメッセージ対応 2023/01/01 eDoktor Y.Kihara
			}
			else
			{
				userNameTextBox.Text = string.Empty;
				passwordTextBox.Text = string.Empty;
			}
		}

		protected override bool ProcessDialogKey(Keys keyData)
		{
			try
			{
				if (((keyData & Keys.KeyCode) == Keys.Return) && ((keyData & (Keys.Alt | Keys.Control)) == Keys.None))
				{
					OnOKButtonClick(this, EventArgs.Empty);
					return true;
				}
				else if (((keyData & Keys.KeyCode) == Keys.Escape) && ((keyData & (Keys.Alt | Keys.Control)) == Keys.None))
				{
					OnCancelButtonClick(this, EventArgs.Empty);
					return true;
				}
				else if (((keyData & Keys.KeyCode) == Keys.Space) && ((keyData & (Keys.Alt | Keys.Control)) == Keys.None) && (ActiveControl is IButtonControl))
				{
					(ActiveControl as IButtonControl).PerformClick();
					return true;
                }
                // ▼ ADD CapsLockメッセージ対応 2023/01/01 eDoktor Y.Kihara
                else if (((keyData & Keys.KeyCode) == Keys.CapsLock) && ((keyData & Keys.Shift) == Keys.Shift))
                {
                    capsLockLabel.Visible = Control.IsKeyLocked(Keys.CapsLock);
                }
                // ▲ ADD CapsLockメッセージ対応 2023/01/01 eDoktor Y.Kihara
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}

			return base.ProcessDialogKey(keyData);
		}
		#endregion

		#region Private Methods
		private void OnOKButtonClick(object sender, EventArgs e)
		{
			try
			{
				var eventArgs = new PasswordCredentialEventArgs() { UserName = userNameTextBox.Text, Password = passwordTextBox.Text };
				AuthenticateUser.SafeInvoke(this, eventArgs);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnCancelButtonClick(object sender, EventArgs e)
		{
			try
			{
				CancelButtonClick.SafeInvoke(this, e);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion	
	}
}
