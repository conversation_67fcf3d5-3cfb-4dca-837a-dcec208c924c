﻿//  Client.cs

namespace eDoktor.AutoComplete.Interprocess
{
	using Common.EventExtensions;
	using TcpClientExtensions;

	public class Client : System.IDisposable
	{
		#region Events
		public event System.EventHandler SocketConnected;
		public event System.EventHandler SocketDisconnected;
		public event System.EventHandler<Common.PacketEventArgs> PacketReceived;
		#endregion

		#region Fields
		private readonly object SyncRoot = new object();
		private TcpClient _tcpClient;
		private ConnectionType _connectionType;
		private string[] _hostNamesOrAddresses;
		private int _port;
		private bool _autoReconnect;
		private IConnectionMonitor _connectionMonitor;
		#endregion

		#region Properties
		public bool Connected
		{
			get
			{
				try
				{
					var tcpClient = _tcpClient;

					return ((tcpClient != null) && (tcpClient.Connected));
				}
				catch (System.Exception ex)
				{
					Common.Trace.OutputExceptionTrace(ex);
				}

				return false;
			}
		}

		public string[] HostNamesOrAddresses
		{
			get { return _hostNamesOrAddresses; }
		}
		#endregion

		#region Constructors
		public Client(string hostNameOrAddress, int port, bool autoReconnect)
			: this(new string[] { hostNameOrAddress }, port, autoReconnect)
		{

		}

		public Client(string[] hostNamesOrAddresses, int port, bool autoReconnect)
		{
			_hostNamesOrAddresses = hostNamesOrAddresses;
			_port = port;
			_autoReconnect = autoReconnect;
		}

		public Client(ConnectionType connectionType, bool autoReconnect)
		{
			switch (connectionType)
			{
				case ConnectionType.ToRemoteServer:
					_hostNamesOrAddresses = Configuration.ServerAddressList;
					_port = Configuration.ServerPortNumber;
					break;
				default:
					throw new System.ArgumentOutOfRangeException(string.Format("connectionType({0})", connectionType));
			}

			_connectionType = connectionType;
			_autoReconnect = autoReconnect;
		}
		#endregion

		#region IDisposable
		~Client()
		{
			Dispose(false);
		}

		public void Dispose()
		{
			Dispose(true);
			System.GC.SuppressFinalize(this);
		}

		protected virtual void Dispose(bool disposing)
		{
			Disconnect();

			if (_connectionMonitor != null)
			{
				_connectionMonitor.Dispose();
				_connectionMonitor = null;
			}

			if (disposing)
			{
				SocketConnected = null;
				SocketDisconnected = null;
				PacketReceived = null;
			}
		}
		#endregion

		#region Public Methods
		public void Start()
		{
			using (Common.TimedLock.Lock(SyncRoot))
			{
				Connect();

				if (_connectionMonitor != null)
				{
					_connectionMonitor.Start();
				}
			}
		}

		// クライアント→サーバー 1～20
		public ClientToServerAutoCompleteSettingQueryResponseData Transceive(ClientToServerAutoCompleteSettingQueryRequestData requestData)
		{
			return _tcpClient.Transceive(requestData);
		}
		#endregion

		#region Private Methods
		private void Connect()
		{
			using (Common.TimedLock.Lock(SyncRoot))
			{
				Disconnect();

				if ((_hostNamesOrAddresses == null) || (_hostNamesOrAddresses.Length == 0))
				{
					return;
				}

				_tcpClient = new TcpClient();
				_tcpClient.SocketConnected += OnSocketConnected;
				_tcpClient.SocketDisconnected += OnSocketDisconnected;
				_tcpClient.NonResponsePacketReceived += OnPacketReceived;

				if (_autoReconnect)
				{
					bool onOff = true;
					uint keepAliveTime = Configuration.ClientKeepAliveTime;
					uint keepAliveInterval = Common.Network.DefaultKeepAliveInterval;
					_tcpClient.SetKeepAliveValues(onOff, keepAliveTime, keepAliveInterval);
				}

				_tcpClient.Connect(_hostNamesOrAddresses, _port, _autoReconnect);
			}
		}

		private void Disconnect()
		{
			using (Common.TimedLock.Lock(SyncRoot))
			{
				if (_tcpClient != null)
				{
					_tcpClient.Dispose();
					_tcpClient = null;
				}
			}
		}

		private void OnSocketConnected(object sender, System.EventArgs e)
		{
			SocketConnected.SafeInvoke(sender, e);
		}

		private void OnSocketDisconnected(object sender, System.EventArgs e)
		{
			
			SocketDisconnected.SafeInvoke(sender, e);
		}

		private void OnPacketReceived(object sender, Common.PacketEventArgs e)
		{
			PacketReceived.SafeInvoke(sender, e);
		}
		#endregion
	}
}
