﻿using System;
using System.Threading;
using System.Threading.Tasks;

using NLog;

using Fit.SmartTools.Navi.SBC;

namespace Fit.SmartTools.Navi.Sample
{
    class DVW40Sample
    {
        private static Logger logger = LogManager.GetCurrentClassLogger();

        [STAThread]
        static void Main()
        {
            var args = Environment.GetCommandLineArgs();
            if (args == null || args.Length < 4)
            {
                logger.Error("ユーザID/パスワードが指定されれていない。");
                Console.WriteLine("ユーザID/パスワードが指定されれていない。");
                Environment.Exit(1);
            }

            var userId = string.Empty;
            var password = string.Empty;

            for (var i = 0; i < args.Length; i++)
            {
                try
                {
                    switch (args[i].ToLower())
                    {
                        case "/u":
                        case "-u":
                            userId = args[++i];
                            break;
                        case "/p":
                        case "-p":
                            password = args[++i];
                            break;
                    }
                }
                catch { }
            }

            if (string.IsNullOrWhiteSpace(userId)
                || string.IsNullOrWhiteSpace(password))
            {
                logger.Error("ユーザID/パスワードが指定されれていない。");
                Console.WriteLine("ユーザID/パスワードが指定されれていない。");
                Environment.Exit(1);
            }
            else
            {
                new DVW40Sample().Connect(userId, password);
                Environment.Exit(0);
            }
        }

        public void Connect(string userId, string password)
        {
            var wrapper = new DesktopViewerWrapper();

            wrapper.SessionConnect += (o, e) =>
            {
                logger.Debug("SesssionConnect User:[{0}] Host:[{1}]", e.UserId, e.SessionHost);
                Console.WriteLine("SesssionConnect User:[{0}] Host:[{1}]", e.UserId, e.SessionHost);

                logger.Debug("Disconnect after 60 seconds.");
                Console.WriteLine("Disconnect after 60 seconds.");
                Task.Factory.StartNew(() =>
                {
                    Thread.Sleep(TimeSpan.FromSeconds(30));
                }).Wait();
                wrapper.Disconnect();
            };

            wrapper.SessionDisconnect += (o, e) =>
            {
                logger.Debug("SessionDisconnect");
                Console.WriteLine("SessionDisconnect");
            };

            wrapper.SessionConnectFailure += (o, e) =>
            {
                logger.Debug("SessionConnectFailure Cause:[{0}]", e.Cause);
                Console.WriteLine("SessionConnectFailure Cause:[{0}]", e.Cause);
            };

            wrapper.SessionLoginFailure += (o, e) =>
            {
                logger.Debug("SessionLoginFailure");
                Console.WriteLine("SessionLoginFailure");
            };

            wrapper.Connect(userId, password);
        }
    }
}
