USE [$(Param1)]
GO

/****** Object:  Trigger [dbo].[TR_update_t_terminal_config_ties]    Script Date: 10/06/2014 15:56:23 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*** CREATE TRIGGER ***/
IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[TR_update_t_terminal_config_ties]') AND type in (N'TR'))
DROP TRIGGER [dbo].[TR_update_t_terminal_config_ties]
GO

CREATE TRIGGER [dbo].[TR_update_t_terminal_config_ties] ON [dbo].[t_terminal_config_ties]
FOR UPDATE
AS 
BEGIN
UPDATE t_terminal_config_ties
SET modification_datetime = GETDATE()
WHERE id IN (SELECT id FROM inserted)
END
RETURN


GO

