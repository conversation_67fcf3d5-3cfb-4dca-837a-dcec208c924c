﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.31729.503
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.AutoComplete.Server", "eDoktor.AutoComplete.Server\eDoktor.AutoComplete.Server.csproj", "{77AC62BD-F9BA-415D-9AD8-A412AC43FEC9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.AutoComplete.Interprocess", "eDoktor.AutoComplete.Interprocess\eDoktor.AutoComplete.Interprocess.csproj", "{82FCC6FF-259C-4CB3-B77C-B71557AFB297}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.AutoComplete.Database", "eDoktor.AutoCompelte.Database\eDoktor.AutoComplete.Database.csproj", "{34354A22-BFBB-48E5-B120-910E1A0E53B7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.AutoComplete.Agent", "eDoktor.AutoComplete.Agent\eDoktor.AutoComplete.Agent.csproj", "{840131F0-9341-4F52-877A-C78EBD4D391E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.AutoComplete.ServerService", "eDoktor.AutoComplete.ServerService\eDoktor.AutoComplete.ServerService.csproj", "{FC54F90C-926B-4894-99B1-CC35B7D42BF2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.AutoComplete.AuthModule", "eDoktor.AutoCompelte.AuthModule\eDoktor.AutoComplete.AuthModule.csproj", "{1D4F08D7-C6D0-4E27-B336-95896BF6CE8B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.AutoComplete.AuthModuleTester", "eDoktor.AutoCompelte.AuthModuleTester\eDoktor.AutoComplete.AuthModuleTester.csproj", "{6C70BC7F-900A-451D-8008-B1A781CFF65D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.AutoComplete.AuthUI", "eDoktor.AutoCompelte.AuthUI\eDoktor.AutoComplete.AuthUI.csproj", "{58B67F7E-D947-4B7E-8287-EE5D1A9A15B6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.AutoComplete.AuthUIServer", "eDoktor.AutoCompelte.AuthUIServer\eDoktor.AutoComplete.AuthUIServer.csproj", "{74C91A2A-5836-4558-ACA1-FC006A4E6CEE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "eDoktor.AutoComplete.ExternalAuth", "eDoktor.AutoComplete.ExternalAuth\eDoktor.AutoComplete.ExternalAuth.csproj", "{940AAA5C-AD71-42D3-96AB-AF008E52E785}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{77AC62BD-F9BA-415D-9AD8-A412AC43FEC9}.Debug|Any CPU.ActiveCfg = Debug|x86
		{77AC62BD-F9BA-415D-9AD8-A412AC43FEC9}.Debug|x64.ActiveCfg = Debug|x86
		{77AC62BD-F9BA-415D-9AD8-A412AC43FEC9}.Debug|x86.ActiveCfg = Debug|x86
		{77AC62BD-F9BA-415D-9AD8-A412AC43FEC9}.Debug|x86.Build.0 = Debug|x86
		{77AC62BD-F9BA-415D-9AD8-A412AC43FEC9}.Release|Any CPU.ActiveCfg = Release|x86
		{77AC62BD-F9BA-415D-9AD8-A412AC43FEC9}.Release|x64.ActiveCfg = Release|x86
		{77AC62BD-F9BA-415D-9AD8-A412AC43FEC9}.Release|x86.ActiveCfg = Release|x86
		{77AC62BD-F9BA-415D-9AD8-A412AC43FEC9}.Release|x86.Build.0 = Release|x86
		{82FCC6FF-259C-4CB3-B77C-B71557AFB297}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{82FCC6FF-259C-4CB3-B77C-B71557AFB297}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{82FCC6FF-259C-4CB3-B77C-B71557AFB297}.Debug|x64.ActiveCfg = Debug|x64
		{82FCC6FF-259C-4CB3-B77C-B71557AFB297}.Debug|x64.Build.0 = Debug|x64
		{82FCC6FF-259C-4CB3-B77C-B71557AFB297}.Debug|x86.ActiveCfg = Debug|x86
		{82FCC6FF-259C-4CB3-B77C-B71557AFB297}.Debug|x86.Build.0 = Debug|x86
		{82FCC6FF-259C-4CB3-B77C-B71557AFB297}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{82FCC6FF-259C-4CB3-B77C-B71557AFB297}.Release|Any CPU.Build.0 = Release|Any CPU
		{82FCC6FF-259C-4CB3-B77C-B71557AFB297}.Release|x64.ActiveCfg = Release|x64
		{82FCC6FF-259C-4CB3-B77C-B71557AFB297}.Release|x64.Build.0 = Release|x64
		{82FCC6FF-259C-4CB3-B77C-B71557AFB297}.Release|x86.ActiveCfg = Release|x86
		{82FCC6FF-259C-4CB3-B77C-B71557AFB297}.Release|x86.Build.0 = Release|x86
		{34354A22-BFBB-48E5-B120-910E1A0E53B7}.Debug|Any CPU.ActiveCfg = Debug|x86
		{34354A22-BFBB-48E5-B120-910E1A0E53B7}.Debug|x64.ActiveCfg = Debug|x86
		{34354A22-BFBB-48E5-B120-910E1A0E53B7}.Debug|x86.ActiveCfg = Debug|x86
		{34354A22-BFBB-48E5-B120-910E1A0E53B7}.Debug|x86.Build.0 = Debug|x86
		{34354A22-BFBB-48E5-B120-910E1A0E53B7}.Release|Any CPU.ActiveCfg = Release|x86
		{34354A22-BFBB-48E5-B120-910E1A0E53B7}.Release|x64.ActiveCfg = Release|x86
		{34354A22-BFBB-48E5-B120-910E1A0E53B7}.Release|x86.ActiveCfg = Release|x86
		{34354A22-BFBB-48E5-B120-910E1A0E53B7}.Release|x86.Build.0 = Release|x86
		{840131F0-9341-4F52-877A-C78EBD4D391E}.Debug|Any CPU.ActiveCfg = Debug|x86
		{840131F0-9341-4F52-877A-C78EBD4D391E}.Debug|x64.ActiveCfg = Debug|x86
		{840131F0-9341-4F52-877A-C78EBD4D391E}.Debug|x86.ActiveCfg = Debug|x86
		{840131F0-9341-4F52-877A-C78EBD4D391E}.Debug|x86.Build.0 = Debug|x86
		{840131F0-9341-4F52-877A-C78EBD4D391E}.Release|Any CPU.ActiveCfg = Release|x86
		{840131F0-9341-4F52-877A-C78EBD4D391E}.Release|x64.ActiveCfg = Release|x86
		{840131F0-9341-4F52-877A-C78EBD4D391E}.Release|x86.ActiveCfg = Release|x86
		{840131F0-9341-4F52-877A-C78EBD4D391E}.Release|x86.Build.0 = Release|x86
		{FC54F90C-926B-4894-99B1-CC35B7D42BF2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FC54F90C-926B-4894-99B1-CC35B7D42BF2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FC54F90C-926B-4894-99B1-CC35B7D42BF2}.Debug|x64.ActiveCfg = Debug|Any CPU
		{FC54F90C-926B-4894-99B1-CC35B7D42BF2}.Debug|x64.Build.0 = Debug|Any CPU
		{FC54F90C-926B-4894-99B1-CC35B7D42BF2}.Debug|x86.ActiveCfg = Debug|x86
		{FC54F90C-926B-4894-99B1-CC35B7D42BF2}.Debug|x86.Build.0 = Debug|x86
		{FC54F90C-926B-4894-99B1-CC35B7D42BF2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FC54F90C-926B-4894-99B1-CC35B7D42BF2}.Release|Any CPU.Build.0 = Release|Any CPU
		{FC54F90C-926B-4894-99B1-CC35B7D42BF2}.Release|x64.ActiveCfg = Release|Any CPU
		{FC54F90C-926B-4894-99B1-CC35B7D42BF2}.Release|x64.Build.0 = Release|Any CPU
		{FC54F90C-926B-4894-99B1-CC35B7D42BF2}.Release|x86.ActiveCfg = Release|x86
		{FC54F90C-926B-4894-99B1-CC35B7D42BF2}.Release|x86.Build.0 = Release|x86
		{1D4F08D7-C6D0-4E27-B336-95896BF6CE8B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1D4F08D7-C6D0-4E27-B336-95896BF6CE8B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1D4F08D7-C6D0-4E27-B336-95896BF6CE8B}.Debug|x64.ActiveCfg = Debug|x64
		{1D4F08D7-C6D0-4E27-B336-95896BF6CE8B}.Debug|x64.Build.0 = Debug|x64
		{1D4F08D7-C6D0-4E27-B336-95896BF6CE8B}.Debug|x86.ActiveCfg = Debug|x86
		{1D4F08D7-C6D0-4E27-B336-95896BF6CE8B}.Debug|x86.Build.0 = Debug|x86
		{1D4F08D7-C6D0-4E27-B336-95896BF6CE8B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1D4F08D7-C6D0-4E27-B336-95896BF6CE8B}.Release|Any CPU.Build.0 = Release|Any CPU
		{1D4F08D7-C6D0-4E27-B336-95896BF6CE8B}.Release|x64.ActiveCfg = Release|x64
		{1D4F08D7-C6D0-4E27-B336-95896BF6CE8B}.Release|x64.Build.0 = Release|x64
		{1D4F08D7-C6D0-4E27-B336-95896BF6CE8B}.Release|x86.ActiveCfg = Release|x86
		{1D4F08D7-C6D0-4E27-B336-95896BF6CE8B}.Release|x86.Build.0 = Release|x86
		{6C70BC7F-900A-451D-8008-B1A781CFF65D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6C70BC7F-900A-451D-8008-B1A781CFF65D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6C70BC7F-900A-451D-8008-B1A781CFF65D}.Debug|x64.ActiveCfg = Debug|x64
		{6C70BC7F-900A-451D-8008-B1A781CFF65D}.Debug|x64.Build.0 = Debug|x64
		{6C70BC7F-900A-451D-8008-B1A781CFF65D}.Debug|x86.ActiveCfg = Debug|x86
		{6C70BC7F-900A-451D-8008-B1A781CFF65D}.Debug|x86.Build.0 = Debug|x86
		{6C70BC7F-900A-451D-8008-B1A781CFF65D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6C70BC7F-900A-451D-8008-B1A781CFF65D}.Release|Any CPU.Build.0 = Release|Any CPU
		{6C70BC7F-900A-451D-8008-B1A781CFF65D}.Release|x64.ActiveCfg = Release|x64
		{6C70BC7F-900A-451D-8008-B1A781CFF65D}.Release|x64.Build.0 = Release|x64
		{6C70BC7F-900A-451D-8008-B1A781CFF65D}.Release|x86.ActiveCfg = Release|x86
		{6C70BC7F-900A-451D-8008-B1A781CFF65D}.Release|x86.Build.0 = Release|x86
		{58B67F7E-D947-4B7E-8287-EE5D1A9A15B6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{58B67F7E-D947-4B7E-8287-EE5D1A9A15B6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{58B67F7E-D947-4B7E-8287-EE5D1A9A15B6}.Debug|x64.ActiveCfg = Debug|x64
		{58B67F7E-D947-4B7E-8287-EE5D1A9A15B6}.Debug|x64.Build.0 = Debug|x64
		{58B67F7E-D947-4B7E-8287-EE5D1A9A15B6}.Debug|x86.ActiveCfg = Debug|x86
		{58B67F7E-D947-4B7E-8287-EE5D1A9A15B6}.Debug|x86.Build.0 = Debug|x86
		{58B67F7E-D947-4B7E-8287-EE5D1A9A15B6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{58B67F7E-D947-4B7E-8287-EE5D1A9A15B6}.Release|Any CPU.Build.0 = Release|Any CPU
		{58B67F7E-D947-4B7E-8287-EE5D1A9A15B6}.Release|x64.ActiveCfg = Release|x64
		{58B67F7E-D947-4B7E-8287-EE5D1A9A15B6}.Release|x64.Build.0 = Release|x64
		{58B67F7E-D947-4B7E-8287-EE5D1A9A15B6}.Release|x86.ActiveCfg = Release|x86
		{58B67F7E-D947-4B7E-8287-EE5D1A9A15B6}.Release|x86.Build.0 = Release|x86
		{74C91A2A-5836-4558-ACA1-FC006A4E6CEE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{74C91A2A-5836-4558-ACA1-FC006A4E6CEE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{74C91A2A-5836-4558-ACA1-FC006A4E6CEE}.Debug|x64.ActiveCfg = Debug|x64
		{74C91A2A-5836-4558-ACA1-FC006A4E6CEE}.Debug|x64.Build.0 = Debug|x64
		{74C91A2A-5836-4558-ACA1-FC006A4E6CEE}.Debug|x86.ActiveCfg = Debug|x86
		{74C91A2A-5836-4558-ACA1-FC006A4E6CEE}.Debug|x86.Build.0 = Debug|x86
		{74C91A2A-5836-4558-ACA1-FC006A4E6CEE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{74C91A2A-5836-4558-ACA1-FC006A4E6CEE}.Release|Any CPU.Build.0 = Release|Any CPU
		{74C91A2A-5836-4558-ACA1-FC006A4E6CEE}.Release|x64.ActiveCfg = Release|x64
		{74C91A2A-5836-4558-ACA1-FC006A4E6CEE}.Release|x64.Build.0 = Release|x64
		{74C91A2A-5836-4558-ACA1-FC006A4E6CEE}.Release|x86.ActiveCfg = Release|x86
		{74C91A2A-5836-4558-ACA1-FC006A4E6CEE}.Release|x86.Build.0 = Release|x86
		{940AAA5C-AD71-42D3-96AB-AF008E52E785}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{940AAA5C-AD71-42D3-96AB-AF008E52E785}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{940AAA5C-AD71-42D3-96AB-AF008E52E785}.Debug|x64.ActiveCfg = Debug|Any CPU
		{940AAA5C-AD71-42D3-96AB-AF008E52E785}.Debug|x64.Build.0 = Debug|Any CPU
		{940AAA5C-AD71-42D3-96AB-AF008E52E785}.Debug|x86.ActiveCfg = Debug|Any CPU
		{940AAA5C-AD71-42D3-96AB-AF008E52E785}.Debug|x86.Build.0 = Debug|Any CPU
		{940AAA5C-AD71-42D3-96AB-AF008E52E785}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{940AAA5C-AD71-42D3-96AB-AF008E52E785}.Release|Any CPU.Build.0 = Release|Any CPU
		{940AAA5C-AD71-42D3-96AB-AF008E52E785}.Release|x64.ActiveCfg = Release|Any CPU
		{940AAA5C-AD71-42D3-96AB-AF008E52E785}.Release|x64.Build.0 = Release|Any CPU
		{940AAA5C-AD71-42D3-96AB-AF008E52E785}.Release|x86.ActiveCfg = Release|x86
		{940AAA5C-AD71-42D3-96AB-AF008E52E785}.Release|x86.Build.0 = Release|x86
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {CABDD7F1-2585-4AB2-A4F6-F4B93C13CF37}
	EndGlobalSection
EndGlobal
