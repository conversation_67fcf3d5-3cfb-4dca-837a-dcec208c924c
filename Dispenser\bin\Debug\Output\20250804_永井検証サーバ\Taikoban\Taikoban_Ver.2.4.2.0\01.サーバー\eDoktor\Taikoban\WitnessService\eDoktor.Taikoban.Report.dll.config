﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="eDoktor.Taikoban.Report.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <applicationSettings>
        <eDoktor.Taikoban.Report.Properties.Settings>
            <setting name="ConnectionStringSettingsLabel" serializeAs="String">
                <value>Taikoban</value>
            </setting>
        </eDoktor.Taikoban.Report.Properties.Settings>
    </applicationSettings>
  <connectionStrings>
    <add name="Taikoban" connectionString="sOEqwRggeOs5J+mrA1PTQP03iS2JvKkkwovlMRimh7NIGBkA6vsDZOSli1ja2eqMXHwU1svdQpoPpl+cAiF71W5rxEQCPpq4d1gvrg7XgSWUOxpzgXIYISoUrCN+yEURta0qlL+UpBogoGF6fBt4Nmo40KsnimIn6wmRkbT0ScNmukl9T8237Gw5Z3A5jBlOw71SlKGl0maMbRVGzGH8J5zm175t4SnPg1pNJkVvp+0=" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <appSettings>
    <add key ="Mode" value="0"/>                                <!-- 動作モード（0：監視サービス、1：通知サービス） -->
    <add key="RegisterdBy" value="1" />                       <!-- 更新者(t_usersのid)、存在チェックはしない -->
    <add key="ReportInterval" value="600000" />                 <!-- エラー情報を確認する間隔(ms) -->
    <add key="ServiceInterval" value="610000" />                <!-- サービスを確認する間隔(ms) -->
    <add key="ProcessInterval" value="590000" />                <!-- プロセスを確認する間隔(ms) -->
    <add key="RetryCountUntilError" value="3"/>                 <!-- エラーと判断するまでの再試行回数 -->
    <add key="DeterminationTimeOfNormalOperation" value="30000" />    <!-- 再起動して正常稼働したか判断するまでの時間)(ms) -->
    <add key="ErrorAlarmMethod" value="2" />                    <!-- エラー発報方法（0：サービス終了、1：メール送信　2：処理継続） -->
    <add key="ReportServiceName" value="eDoktor.Taikoban.ReportService" />              <!-- 通知サービス名 -->
    <add key="ReportServiceTerminal" value="192.168.1.194" />       <!-- 通知サービス端末(Mode = 0の時のみ有効) -->
  </appSettings>
</configuration>