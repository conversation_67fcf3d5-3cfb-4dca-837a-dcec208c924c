USE [$(Param1)]
GO

/****** Object:  Trigger [dbo].[TR_insert_t_log_logout]    Script Date: 07/15/2015 11:52:27 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[TR_insert_t_log_logout]') AND type in (N'TR'))
DROP TRIGGER [dbo].[TR_insert_t_log_logout]
GO

CREATE TRIGGER [dbo].[TR_insert_t_log_logout] ON [dbo].[t_log]
FOR INSERT
AS 
BEGIN
UPDATE t_terminal_statuses
SET modification_datetime = GETDATE()
  , authentication = 0
  , authenticated_logon_id = ''
WHERE terminal_name in (SELECT terminal_name FROM inserted WHERE log_type = 8)
END
RETURN



GO

