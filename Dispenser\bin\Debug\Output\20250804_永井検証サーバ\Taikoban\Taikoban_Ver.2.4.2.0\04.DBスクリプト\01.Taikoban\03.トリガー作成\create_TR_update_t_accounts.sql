USE [$(Param1)]
GO

/****** Object:  Trigger [dbo].[TR_update_t_accounts]    Script Date: 10/06/2014 16:00:58 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*** CREATE TRIGGER ***/
IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[TR_update_t_accounts]') AND type in (N'TR'))
DROP TRIGGER [dbo].[TR_update_t_accounts]
GO

CREATE TRIGGER [dbo].[TR_update_t_accounts] ON [dbo].[t_accounts]
FOR UPDATE
AS 
BEGIN

UPDATE t_accounts
SET modification_datetime = GETDATE()
WHERE id IN (SELECT id FROM inserted)

INSERT INTO t_password_history (logon_id, password)
 SELECT i.logon_id, i.password FROM inserted i LEFT JOIN deleted d ON i.id = d.id WHERE i.password <> d.password AND i.password IS NOT NULL AND i.password <> '' AND i.password <> 'IISPqVAtKzsTtc/kgrvdFw=='

UPDATE t_accounts SET password = 'rLJvHjUBvA6guNrmEhzO1A==' WHERE id IN (SELECT id FROM inserted) AND password IS NULL OR password = '' OR password = 'IISPqVAtKzsTtc/kgrvdFw=='

END
RETURN


GO

