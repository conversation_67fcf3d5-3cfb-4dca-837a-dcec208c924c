﻿// XenAppConnectionMonitor.cs

namespace eDoktor.AutoComplete.Interprocess
{
	using Common.EventExtensions;

	class XenAppConnectionMonitor : IConnectionMonitor
	{
		#region Events
		public event System.EventHandler<ConnectionStateEventArgs> StateChanged;
		#endregion

		#region Fields
		private static readonly ConnectionStateEventArgs EmptyConnectionStateEventArgs = new ConnectionStateEventArgs();
		private readonly object SyncRoot = new object();
		private Common.Polling _polling;
		private const int PollingInterval = 1000;
		private ConnectionStateEventArgs _connectionStateEventArgs;
		private int _sessionId;
		private Common.WtsClient _wtsClient;
		#endregion

		#region Properties
		public ConnectionStateEventArgs LastConnectionStateEventArgs { get { return _connectionStateEventArgs; } }
		#endregion

		#region Constructors
		private XenAppConnectionMonitor()
		{

		}

		private void Initialize()
		{
			_connectionStateEventArgs = EmptyConnectionStateEventArgs;
			_sessionId = Common.Environment.SessionId;
			_wtsClient = new Common.WtsClient();
			UpdateHostNamesOrAddresses();
		}

		public static XenAppConnectionMonitor Create()
		{
			var temp = new XenAppConnectionMonitor();

			try
			{
				temp.Initialize();
				var self = temp;
				temp = null;
				return self;
			}
			finally
			{
				if (temp != null)
				{
					temp.Dispose();
				}
			}
		}
		#endregion

		#region IDisposable
		~XenAppConnectionMonitor()
		{
			Dispose(false);
		}

		public void Dispose()
		{
			Dispose(true);
			System.GC.SuppressFinalize(this);
		}

		protected virtual void Dispose(bool disposing)
		{
			try
			{
				using (Common.TimedLock.Lock(SyncRoot))
				{
					if (_polling != null)
					{
						_polling.Dispose();
						_polling = null;
					}

					if (_wtsClient != null)
					{
						_wtsClient.Dispose();
						_wtsClient = null;
					}
				}

				StateChanged = null;
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion

		#region Public Methods
		public void Start()
		{
			using (Common.TimedLock.Lock(SyncRoot))
			{
				if (_polling != null)
				{
					throw new System.InvalidOperationException();
				}

				_polling = new Common.Polling() { Interval = PollingInterval };
				_polling.Start(Poll, null);
			}
		}
		#endregion

		#region Private Methods
		private void Poll(object state)
		{
			try
			{
				UpdateHostNamesOrAddresses();
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void UpdateHostNamesOrAddresses()
		{
			ConnectionStateEventArgs connectionStateEventArgs = EmptyConnectionStateEventArgs;

			using (Common.TimedLock.Lock(SyncRoot))
			{
				if (_wtsClient == null)
				{
					return;
				}

				bool remote = _wtsClient.IsSessionRemote(_sessionId);
				var connectState = _wtsClient.GetConnectState(_sessionId);
				bool connectable = false;

				switch (connectState)
				{
					case Common.WtsClient.ConnectStateType.Active:
					case Common.WtsClient.ConnectStateType.Connected:
					case Common.WtsClient.ConnectStateType.Shadow:
						connectable = true;
						break;
					default:
						break;
				}

				string clientAddress = null;
				string clientName = null;

				if ((remote) && (connectable))
				{
					System.Net.IPAddress address = _wtsClient.GetClientAddress(_sessionId);
					clientAddress = (address != null) ? address.ToString() : null;
					clientName = _wtsClient.GetClientName(_sessionId);

					if ((!string.IsNullOrWhiteSpace(clientAddress)) && (!string.IsNullOrWhiteSpace(clientName)))
					{
						// アドレスとマシン名の両方取れた場合以外接続不可
						connectionStateEventArgs = new ConnectionStateEventArgs() { Connectable = connectable, Remote = remote, ClientAddress = clientAddress, ClientName = clientName };
					}
				}
				else
				{
					// XenAppのアプリケーションサーバーにはクライアントはセットアップされていないと思われるためローカルへの接続を試みない
				}

				if ((_connectionStateEventArgs.Connectable == connectionStateEventArgs.Connectable) && (_connectionStateEventArgs.Remote == connectionStateEventArgs.Remote) && (_connectionStateEventArgs.ClientAddress == connectionStateEventArgs.ClientAddress))
				{
					return;
				}

				_connectionStateEventArgs = connectionStateEventArgs;
			}

			StateChanged.SafeInvoke(this, connectionStateEventArgs);
		}
		#endregion
	}
}
