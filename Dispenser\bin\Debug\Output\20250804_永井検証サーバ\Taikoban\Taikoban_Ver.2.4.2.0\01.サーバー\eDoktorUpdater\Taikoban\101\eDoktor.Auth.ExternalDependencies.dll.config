﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <appSettings>
        <!-- Mode 0:Stub 1:CIS 2:PasswordAPI 3:GX -->
        <add key="Mode" value="0"/>
        <!-- Stub -->
        <add key="StubResult" value="true"/>
        <add key="StubResponseDelayTime" value="5000"/>
        <!-- CIS -->
        <add key="CISWebServiceInterfaceBaseUri" value="http://192.168.1.62/cis-rest/"/>
        <add key="CISPasswordUpdateServiceName" value="HsUpdatePasswordUGFH"/>
        <!-- PasswordAPI -->
        <add key="PasswordAPIFixedKey" value=""/>
        <!-- SKYIF -->
        <add key="SkySeaInterfaceMinimumInterval" value="100"/>
        <add key="SkySeaInterfaceClearNameOnLogoff" value="true"/>
        <add key="SkySeaInterfaceNoLockEventWhenNoLogonUser" value="true"/>
        <!-- FJIF -->
        <add key="ShunkaiInterfaceMinimumInterval" value="500"/>
    </appSettings>
</configuration>