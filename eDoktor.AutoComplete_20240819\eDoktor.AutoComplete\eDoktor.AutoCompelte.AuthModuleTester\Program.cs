﻿// Program.cs

using System;
using System.Windows.Forms;

namespace eDoktor.AutoComplete.AuthModuleTester
{
	static class Program
	{
		[STAThread]
		static void Main()
		{
			try
			{
				Common.Trace.EnableDebugTrace = true;
				Common.Trace.EnableDetailedExceptionTrace = true;
				Common.Trace.SafeEnableTrace = true;

				Application.EnableVisualStyles();
				Application.SetCompatibleTextRenderingDefault(false);
				Application.Run(new MainForm());
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
	}
}
