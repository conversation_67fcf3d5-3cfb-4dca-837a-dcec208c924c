USE [$(Param1)]
GO

/****** Object:  Table [dbo].[t_usb_control_log]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_usb_control_log]') AND type in (N'U'))
DROP TABLE [dbo].[t_usb_control_log]
GO

CREATE TABLE [dbo].[t_usb_control_log](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[output_datetime] [datetime2](7) NOT NULL,
	[terminal_name] [nvarchar](255) NOT NULL,
	[logon_id] [nvarchar](255) NOT NULL,
	[account_groups_id] [int] NOT NULL,
	[terminal_groups_id] [int] NOT NULL,
	[vendor_id] [nvarchar](200) NOT NULL,
	[product_id] [nvarchar](200) NOT NULL,
	[serialnumber] [nvarchar](200) NOT NULL,
	[permission_to_use] [int] NOT NULL,
	[message] [nvarchar](2048) NOT NULL,
	[vendor_name] [nvarchar](100) NOT NULL,
	[product_name] [nvarchar](100) NOT NULL,
	[notes] [nvarchar](255) NOT NULL,
	[user_name] [nvarchar](255) NOT NULL,
	[terminal_groups_name] [nvarchar](255) NOT NULL,
 CONSTRAINT [PK_t_usb_control_log] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_usb_control_log] ADD  CONSTRAINT [DF_t_usb_control_log_output_datetime]  DEFAULT (getdate()) FOR [output_datetime]
GO
ALTER TABLE [dbo].[t_usb_control_log] ADD  CONSTRAINT [DF_t_usb_control_log_terminal_name]  DEFAULT ('') FOR [terminal_name]
GO
ALTER TABLE [dbo].[t_usb_control_log] ADD  CONSTRAINT [DF_t_usb_control_log_logon_id]  DEFAULT ('') FOR [logon_id]
GO
ALTER TABLE [dbo].[t_usb_control_log] ADD  CONSTRAINT [DF_t_usb_control_log_account_groups_id]  DEFAULT ((0)) FOR [account_groups_id]
GO
ALTER TABLE [dbo].[t_usb_control_log] ADD  CONSTRAINT [DF_t_usb_control_log_terminal_groups_id]  DEFAULT ((0)) FOR [terminal_groups_id]
GO
ALTER TABLE [dbo].[t_usb_control_log] ADD  CONSTRAINT [DF_t_usb_control_log_vendor_id]  DEFAULT ('') FOR [vendor_id]
GO
ALTER TABLE [dbo].[t_usb_control_log] ADD  CONSTRAINT [DF_t_usb_control_log_product_id]  DEFAULT ('') FOR [product_id]
GO
ALTER TABLE [dbo].[t_usb_control_log] ADD  CONSTRAINT [DF_t_usb_control_log_serialnumber]  DEFAULT ('') FOR [serialnumber]
GO
ALTER TABLE [dbo].[t_usb_control_log] ADD  CONSTRAINT [DF_t_usb_control_log_permission_to_use]  DEFAULT ((0)) FOR [permission_to_use]
GO
ALTER TABLE [dbo].[t_usb_control_log] ADD  CONSTRAINT [DF_t_usb_control_log_message]  DEFAULT ('') FOR [message]
GO
ALTER TABLE [dbo].[t_usb_control_log] ADD  CONSTRAINT [DF_t_usb_control_log_vendor_name]  DEFAULT ('') FOR [vendor_name]
GO
ALTER TABLE [dbo].[t_usb_control_log] ADD  CONSTRAINT [DF_t_usb_control_log_product_name]  DEFAULT ('') FOR [product_name]
GO
ALTER TABLE [dbo].[t_usb_control_log] ADD  CONSTRAINT [DF_t_usb_control_log_notes]  DEFAULT ('') FOR [notes]
GO
ALTER TABLE [dbo].[t_usb_control_log] ADD  CONSTRAINT [DF_t_usb_control_log_user_name]  DEFAULT ('') FOR [user_name]
GO
ALTER TABLE [dbo].[t_usb_control_log] ADD  CONSTRAINT [DF_t_usb_control_log_terminal_groups_name]  DEFAULT ('') FOR [terminal_groups_name]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_log', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ログ出力日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_log', @level2type=N'COLUMN',@level2name=N'output_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'端末名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_log', @level2type=N'COLUMN',@level2name=N'terminal_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ログオンID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_log', @level2type=N'COLUMN',@level2name=N'logon_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'アカウントのグループid' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_log', @level2type=N'COLUMN',@level2name=N'account_groups_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'端末グループのid' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_log', @level2type=N'COLUMN',@level2name=N'terminal_groups_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'USBのベンダーid' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_log', @level2type=N'COLUMN',@level2name=N'vendor_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'USBのプロダクトid' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_log', @level2type=N'COLUMN',@level2name=N'product_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'USBのシリアル番号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_log', @level2type=N'COLUMN',@level2name=N'serialnumber'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'使用許可(1)/不許可(0)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_log', @level2type=N'COLUMN',@level2name=N'permission_to_use'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'メッセージ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_log', @level2type=N'COLUMN',@level2name=N'message'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'USBのベンダー名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_log', @level2type=N'COLUMN',@level2name=N'vendor_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'USBのプロダクト名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_log', @level2type=N'COLUMN',@level2name=N'product_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'メモ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_log', @level2type=N'COLUMN',@level2name=N'notes'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'氏名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_log', @level2type=N'COLUMN',@level2name=N'user_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'端末グループ名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_log', @level2type=N'COLUMN',@level2name=N'terminal_groups_name'
GO
