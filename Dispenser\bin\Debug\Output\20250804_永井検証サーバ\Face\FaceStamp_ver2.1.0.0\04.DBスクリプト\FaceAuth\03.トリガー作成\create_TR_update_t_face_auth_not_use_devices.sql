USE [$(Param1)]
GO

/****** Object:  Trigger [dbo].[TR_update_t_face_auth_not_use_devices]    Script Date: 2021/07/07 9:47:12 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[TR_update_t_face_auth_not_use_devices]') AND type in (N'TR'))
DROP TRIGGER [dbo].[TR_update_t_face_auth_not_use_devices]
GO

CREATE TRIGGER [dbo].[TR_update_t_face_auth_not_use_devices] ON [dbo].[t_face_auth_not_use_devices]
FOR UPDATE
AS 
BEGIN

UPDATE t_face_auth_not_use_devices

SET modification_datetime = GETDATE()
WHERE id IN (SELECT id FROM inserted)

END
RETURN
GO

ALTER TABLE [dbo].[t_face_auth_not_use_devices] ENABLE TRIGGER [TR_update_t_face_auth_not_use_devices]
GO


