﻿// Crypto.cs

namespace eDoktor.AutoComplete.Interprocess
{
	public static class Crypto
	{
		#region Fields
		private static readonly Common.CryptoParams CryptoParams;
		#endregion

		#region Constructors
		static Crypto()
		{
			try
			{
				const string algorithmName = "Rijndael";
				const string password = "=o8^2a<~\"/R*";
				const string salt = "E-/~mo?Z";
				const int iterationCount = 1226;
				const int keySize = 256;
				const int blockSize = 128;

				byte[] key = null;
				byte[] iv = null;
				Common.Crypto.DeriveKeyFromPassword(password, salt, iterationCount, keySize, out key, blockSize, out iv);
				CryptoParams = new Common.CryptoParams();
				CryptoParams.AlgorithmName = algorithmName;
				CryptoParams.KeySize = keySize;
				CryptoParams.Key = key;
				CryptoParams.BlockSize = blockSize;
				CryptoParams.IV = iv;
			}
			catch (System.Exception ex)
			{
				eDoktor.Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion

		#region Public Methods
		public static string Encrypt(string text)
		{
			return Common.Crypto.Encrypt(text, CryptoParams);
		}

		public static string Decrypt(string text)
		{
			return Common.Crypto.Decrypt(text, CryptoParams);
		}

		public static byte[] Serialize<T>(T graph) where T : class
		{
			return Common.Serializer<T>.SerializeAndCompressAndEncrypt(graph, CryptoParams);
		}

		public static T Deserialize<T>(byte[] buffer) where T : class
		{
			return Common.Serializer<T>.DecryptAndDecompressAndDeserialize(buffer, CryptoParams);
		}

		public static void Serialize<T>(System.IO.Stream stream, T graph) where T : class
		{
			Common.Serializer<T>.SerializeAndCompressAndEncrypt(stream, graph, CryptoParams);
		}

		public static T Deserialize<T>(System.IO.Stream stream) where T : class
		{
			return Common.Serializer<T>.DecryptAndDecompressAndDeserialize(stream, CryptoParams);
		}
		#endregion
	}
}
