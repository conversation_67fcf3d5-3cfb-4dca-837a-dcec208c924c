@echo off

cd /d %~dp0

rem ========== �ݒ�l ==========
SET INSTALL_DRIVE=D:
rem ���[�h:0=�V���O���T�[�o,1=�璷�T�[�o#1,2=�璷�T�[�o#2,3=�璷�T�[�o#3
SET INSTALL_MODE=%1

rem ========== �萔 ==========
SET REGISTER=%SYSTEMROOT%\Microsoft.NET\Framework\v4.0.30319\InstallUtil.exe

rem ========== �ϐ� ==========
SET TARGET=0

echo ���W���[���R�s�[
robocopy ..\eDoktor %INSTALL_DRIVE%\eDoktor /E /R:3 /W:3

rem �z�M�t�H���_�\�z
if %INSTALL_MODE% equ 0 (
	SET TARGET=1
) else if %INSTALL_MODE% equ 3 (
	SET TARGET=1
)
if %TARGET% equ 1 (
	robocopy ..\eDoktorUpdater %INSTALL_DRIVE%\eDoktorUpdater /E /R:3 /W:3
	echo.
	echo.
	echo Windows�A�J�E���g�ǉ�
	net user tkbupduser /add tkbupdU$er /comment:"Taikoban�z�M�p���[�U�[" /expires:never /passwordchg:no /logonpasswordchg:no
	wmic useraccount where "Name='tkbupduser'" set PasswordExpires=FALSE
	net user tkbloguser /add tkblogU$er /comment:"Taikoban���O�A�b�v���[�h�p���[�U�[" /expires:never  /passwordchg:no /logonpasswordchg:no
	wmic useraccount where "Name='tkbloguser'" set PasswordExpires=FALSE

	echo.
	echo.
	echo ���L�t�H���_�ݒ�
	net share eDoktorUpdater=%INSTALL_DRIVE%\eDoktorUpdater /GRANT:Administrators,FULL /GRANT:tkbupduser,READ
	net share TaikobanClientLog=%INSTALL_DRIVE%\eDoktor\Taikoban\TaikobanClientLog /GRANT:Administrators,FULL /GRANT:tkbloguser,CHANGE
)

rem ���U�g�����U�N�V�����ݒ�
SET TARGET=0
if %INSTALL_MODE% equ 2 (
	SET TARGET=1
) else if %INSTALL_MODE% equ 3 (
	SET TARGET=1
)
if %TARGET% equ 1 (
	rem todo
)

rem echo.
rem echo.
rem echo �t�@�C�A�[�E�H�[���ݒ�
rem echo SQL Server �̃|�[�g�J���iTCP�|�[�g�F1433,5022�j
netsh advfirewall firewall add rule name="SQL Server" dir=in action=allow description="TCP�|�[�g�F1433,5022�����" profile=public,private,domain localport=1433,5022 protocol=tcp

rem �T�[�o�[�T�[�r�X�A�����[�g�T�[�o�[�T�[�r�X�A�㗝���O�C���T�[�r�X�\�z
SET TARGET=1
if %INSTALL_MODE% equ 3 (
	SET TARGET=0
)
if %TARGET% equ 1 (
	echo.
	echo.
	echo Taikoban�T�[�o�[�T�[�r�X�̃C���X�g�[��
	"%SYSTEMROOT%\Microsoft.NET\Framework\v4.0.30319\InstallUtil.exe" %INSTALL_DRIVE%\eDoktor\Taikoban\Server\eDoktor.Taikoban.ServerService.exe

	echo.
	echo.
	echo �����[�g�T�[�o�[�T�[�r�X�̃C���X�g�[��
	"%SYSTEMROOT%\Microsoft.NET\Framework\v4.0.30319\InstallUtil.exe" %INSTALL_DRIVE%\eDoktor\Taikoban\RemoteServer\eDoktor.Taikoban.RemoteServerService.exe

	echo.
	echo.
	echo �㗝���O�C���T�[�o�[�T�[�r�X�̃C���X�g�[��
	"%SYSTEMROOT%\Microsoft.NET\Framework\v4.0.30319\InstallUtil.exe" %INSTALL_DRIVE%\eDoktor\Taikoban\AutoCompleteServer\eDoktor.AutoComplete.ServerService.exe

)

rem �Ď��T�[�r�X
SET TARGET=0
if %INSTALL_MODE% equ 0 (
	SET TARGET=1
) else if %INSTALL_MODE% equ 1 (
	SET TARGET=1
)
if %TARGET% equ 1 (
	echo.
	echo.
	echo �Ď��T�[�r�X�̃C���X�g�[��
	"%SYSTEMROOT%\Microsoft.NET\Framework\v4.0.30319\InstallUtil.exe" %INSTALL_DRIVE%\eDoktor\Taikoban\WitnessService\eDoktor.Taikoban.WitnessService.exe
)

rem �ʒm�T�[�r�X
SET TARGET=0
if %INSTALL_MODE% equ 0 (
	SET TARGET=1
) else if %INSTALL_MODE% equ 2 (
	SET TARGET=1
)
if %TARGET% equ 1 (
	echo.
	echo.
	echo �ʒm�T�[�r�X�̃C���X�g�[��
	"%SYSTEMROOT%\Microsoft.NET\Framework\v4.0.30319\InstallUtil.exe" %INSTALL_DRIVE%\eDoktor\Taikoban\ReportService\eDoktor.Taikoban.ReportService.exe
)

echo.
echo.
echo TaikobanAPI�̃��W�X�g
"%SYSTEMROOT%\Microsoft.NET\Framework\v4.0.30319\regasm.exe" %INSTALL_DRIVE%\eDoktor\Taikoban\API\TaikobanAPI.exe /tlb /codebase
