<?xml version="1.0"?>
<configuration>
  <system.data>
    <DbProviderFactories>
      <remove invariant="System.Data.SQLite" />
      <remove invariant="System.Data.SQLite.Linq" />
      <add name="SQLite Data Provider (LINQ)" invariant="System.Data.SQLite.Linq" description=".NET Framework Data Provider for SQLite (LINQ)" type="System.Data.SQLite.Linq.SQLiteProviderFactory, System.Data.SQLite.Linq, Version=********, Culture=neutral, PublicKeyToken=db937bc2d44ff139" />
    </DbProviderFactories>
  </system.data>
  <connectionStrings>
    <add name="northwindEFEntities" connectionString="metadata=res://*/NorthwindModel.Linq.2010.csdl|res://*/NorthwindModel.Linq.2010.ssdl|res://*/NorthwindModel.Linq.2010.msl;provider=System.Data.SQLite.Linq;provider connection string=&quot;data source=.\northwindEF.db&quot;" providerName="System.Data.EntityClient" />
  </connectionStrings>
</configuration>
