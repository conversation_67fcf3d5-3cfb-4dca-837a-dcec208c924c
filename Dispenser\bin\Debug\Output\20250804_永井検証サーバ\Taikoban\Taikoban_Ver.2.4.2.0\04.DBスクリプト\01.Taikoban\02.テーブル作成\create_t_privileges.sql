USE [$(Param1)]
GO
/****** Object:  Table [dbo].[t_privileges]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_privileges]') AND type in (N'U'))
DROP TABLE [dbo].[t_privileges]
GO

CREATE TABLE [dbo].[t_privileges](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[registered_by] [int] NOT NULL,
	[modification_datetime] [datetime2](7) NOT NULL,
	[modified_by] [int] NOT NULL,
	[invalidated] [bit] NOT NULL,
	[invalidation_datetime] [datetime2](7) NULL,
	[invalidated_by] [int] NOT NULL,
	[logically_deleted] [bit] NOT NULL,
	[logical_deletion_datetime] [datetime2](7) NULL,
	[logically_deleted_by] [int] NOT NULL,
	[tie_type] [int] NOT NULL,
	[tie_id] [int] NOT NULL,
	[can_force_unlock] [int] NOT NULL,
	[can_admin_console] [int] NOT NULL,
	[can_card_register] [int] NOT NULL,
	[can_temp_card_issuer] [int] NOT NULL,
	[can_password_initialization] [int] NOT NULL,
	[notes] [nvarchar](255) NOT NULL,
 CONSTRAINT [PK_t_privileges] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_privileges] ADD  CONSTRAINT [DF_t_privileges_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO
ALTER TABLE [dbo].[t_privileges] ADD  CONSTRAINT [DF_t_privileges_registered_by]  DEFAULT ((0)) FOR [registered_by]
GO
ALTER TABLE [dbo].[t_privileges] ADD  CONSTRAINT [DF_t_privileges_modification_datetime]  DEFAULT (getdate()) FOR [modification_datetime]
GO
ALTER TABLE [dbo].[t_privileges] ADD  CONSTRAINT [DF_t_privileges_modified_by]  DEFAULT ((0)) FOR [modified_by]
GO
ALTER TABLE [dbo].[t_privileges] ADD  CONSTRAINT [DF_t_privileges_invalidated]  DEFAULT ('false') FOR [invalidated]
GO
ALTER TABLE [dbo].[t_privileges] ADD  CONSTRAINT [DF_t_privileges_invalidated_by]  DEFAULT ((0)) FOR [invalidated_by]
GO
ALTER TABLE [dbo].[t_privileges] ADD  CONSTRAINT [DF_t_privileges_logically_deleted]  DEFAULT ('false') FOR [logically_deleted]
GO
ALTER TABLE [dbo].[t_privileges] ADD  CONSTRAINT [DF_t_privileges_logically_deleted_by]  DEFAULT ((0)) FOR [logically_deleted_by]
GO
ALTER TABLE [dbo].[t_privileges] ADD  CONSTRAINT [DF_t_privileges_tie_type]  DEFAULT ((0)) FOR [tie_type]
GO
ALTER TABLE [dbo].[t_privileges] ADD  CONSTRAINT [DF_t_privileges_tie_id]  DEFAULT ((0)) FOR [tie_id]
GO
ALTER TABLE [dbo].[t_privileges] ADD  CONSTRAINT [DF_t_privileges_can_force_unlock]  DEFAULT ((0)) FOR [can_force_unlock]
GO
ALTER TABLE [dbo].[t_privileges] ADD  CONSTRAINT [DF_t_privileges_can_admin_console]  DEFAULT ((0)) FOR [can_admin_console]
GO
ALTER TABLE [dbo].[t_privileges] ADD  CONSTRAINT [DF_t_privileges_can_card_register]  DEFAULT ((0)) FOR [can_card_register]
GO
ALTER TABLE [dbo].[t_privileges] ADD  CONSTRAINT [DF_t_privileges_can_temp_card_issuer]  DEFAULT ((0)) FOR [can_temp_card_issuer]
GO
ALTER TABLE [dbo].[t_privileges] ADD  CONSTRAINT [DF_t_privileges_can_password_initialization]  DEFAULT ((0)) FOR [can_password_initialization]
GO
ALTER TABLE [dbo].[t_privileges] ADD  CONSTRAINT [DF_t_privileges_notes]  DEFAULT ('') FOR [notes]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_privileges', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_privileges', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_privileges', @level2type=N'COLUMN',@level2name=N'registered_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_privileges', @level2type=N'COLUMN',@level2name=N'modification_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_privileges', @level2type=N'COLUMN',@level2name=N'modified_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_privileges', @level2type=N'COLUMN',@level2name=N'invalidated'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効化日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_privileges', @level2type=N'COLUMN',@level2name=N'invalidation_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効化を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_privileges', @level2type=N'COLUMN',@level2name=N'invalidated_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_privileges', @level2type=N'COLUMN',@level2name=N'logically_deleted'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_privileges', @level2type=N'COLUMN',@level2name=N'logical_deletion_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_privileges', @level2type=N'COLUMN',@level2name=N'logically_deleted_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'紐付タイプ 0:未定義 1:権限テンプレート 2:アカウント' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_privileges', @level2type=N'COLUMN',@level2name=N'tie_type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'紐付け元のID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_privileges', @level2type=N'COLUMN',@level2name=N'tie_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'離席画面の強制解除権限 0:不許可 1:許可' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_privileges', @level2type=N'COLUMN',@level2name=N'can_force_unlock'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'管理ツール権限 0:不許可 1:許可 2:カード関連のみ許可' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_privileges', @level2type=N'COLUMN',@level2name=N'can_admin_console'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'カード登録ツール権限 0:不許可 1:許可' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_privileges', @level2type=N'COLUMN',@level2name=N'can_card_register'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'仮カード貸出ツール権限 0:不許可 1:許可' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_privileges', @level2type=N'COLUMN',@level2name=N'can_temp_card_issuer'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード初期化ツール権限 0:不許可 1:許可' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_privileges', @level2type=N'COLUMN',@level2name=N'can_password_initialization'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'注記' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_privileges', @level2type=N'COLUMN',@level2name=N'notes'
GO
