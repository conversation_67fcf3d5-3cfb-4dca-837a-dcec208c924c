﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="eDoktor.Taikoban.SmartCardRegister.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <applicationSettings>
        <eDoktor.Taikoban.SmartCardRegister.Properties.Settings>
            <setting name="ConnectionStringSettingsLabel" serializeAs="String">
                <value>Taikoban</value>
            </setting>
        </eDoktor.Taikoban.SmartCardRegister.Properties.Settings>
    </applicationSettings>
  <connectionStrings>
    <add name="Taikoban" connectionString="sOEqwRggeOs5J+mrA1PTQP03iS2JvKkkwovlMRimh7NIGBkA6vsDZOSli1ja2eqMXHwU1svdQpoPpl+cAiF71W5rxEQCPpq4d1gvrg7XgSWUOxpzgXIYISoUrCN+yEURta0qlL+UpBogoGF6fBt4Nmo40KsnimIn6wmRkbT0ScNmukl9T8237Gw5Z3A5jBlOw71SlKGl0maMbRVGzGH8J5zm175t4SnPg1pNJkVvp+0=" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <appSettings>

    <!-- Trace -->
    <add key="EnableTrace" value="true" />
    <add key="EnableDebugTrace" value="true" />
    <add key="EnableDetailedExceptionTrace" value="true" />
    <add key="ContainingFolderPath" value="..\logs\"/>
    <add key="TraceTermInDays" value="30" />

    <!-- Mode:0=管理者モード 1=利用者モード-->
    <add key="Mode" value="0"/>

    <!-- PC/SC START -->
    <add key="EnablePCSC" value="false"/>	<!-- Taikoban経由せずに直接カードリーダーから情報を取得するか:false=Taikoban経由 true=直接取得 -->
    <add key="PCSCVerbose" value="false"/>
    <add key="PCSCWhiteReaderList" value="Sony FeliCa Port/PaSoRi 3.0|SONY FeliCa Port/PaSoRi 4.0"/>
    <add key="PCSCBlackReaderList" value=""/>
    <add key="PCSCMinimumPollingInterval" value="3000"/>
    <add key="PCSCAccessStartedEventMillisecondsTimeout" value="0"/>

    <add key="End Button Off" value="false" />                              <!-- ID入力画面で終了ボタンの表示設定 true:表示しない 以外:表示する -->
    <add key="registered by" value="1" />                                   <!-- 更新者(t_usersのid)、存在チェックはしない -->
    <add key="smartcard usage start datetime" value="2001/01/01 00:00:00" /><!-- スマートカードの有効開始日 -->
    <add key="smartcard usage end datetime"   value="2099/12/31 23:59:59" /><!-- スマートカードの有効終了日 -->
    <add key="holder usage start datetime"    value="2001/01/01 00:00:01" /><!-- 関連付け情報の有効開始日 -->
    <add key="holder usage end datetime"      value="2099/12/31 23:59:58" /><!-- 関連付け情報の有効終了日 -->
    <add key="Id Input Length" value="99"/>                                  <!-- Id入力時、指定して桁数入力されたらOKボタンが押される -->
    <add key="SystemId" value="7WF2bZCUYUX20tW7k8e1Pw=="/>
    <add key="SecurityKey"  value="KRr156m7yhL+rtm7efqKRLWQv0AgKjySsK6CjC9K2x4="/>
    <!-- ICカード状態のSytemFalt(Taikobanサーバーと通信は出来ていないがカード状態は読み取れている時のカード状態)を無視するかどうか(true:無視する false:無視しない) -->
    <!-- Taikobanサーバーが無い状態でICカード読み取りを行う時にはtrueを設定するとカードをの内容を読み取れる。 -->
    <add key="IgnoreSystemFault" value="true"/>
    <!-- カードの用途を設定する。0:本カード 1:手術室 省略値=0-->
    <add key="UsagType" value="0"/>
    <!-- UsageType=1の時にタイトル画面に追加する文言を設定する。未設定時または空文字の時は"（手術室用）"とみなす。-->
    <add key="AdditionalTitle" value="*手術室用カード登録*"/>
    <!-- カード用途のイメージを示す画像ファイル名を設定する。-->
    <add key="UsageTypeImage" value="ope.png"/>
    <!-- 登録済みのカードへの上書き登録を許可する false:許可しない true:許可する 省略値=false -->
    <add key="AllowOverwrite" value="false" />
    <!-- 削除済みユーザの場合のメッセージを無効メッセージに含める true:削除済みメッセージは無効メッセージと同じになる false:削除済みメッセージはデータなしメッセージと同じになる -->
    <add key="DeleteMessageMode" value="true"/>
  </appSettings>
</configuration>