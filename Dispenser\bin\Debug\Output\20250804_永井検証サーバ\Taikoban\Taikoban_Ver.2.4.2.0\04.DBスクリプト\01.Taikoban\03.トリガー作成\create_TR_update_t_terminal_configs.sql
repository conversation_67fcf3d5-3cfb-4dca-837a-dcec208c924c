USE [$(Param1)]
GO

/****** Object:  Trigger [dbo].[TR_update_t_terminal_configs]    Script Date: 10/06/2014 15:56:52 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*** CREATE TRIGGER ***/
IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[TR_update_t_terminal_configs]') AND type in (N'TR'))
DROP TRIGGER [dbo].[TR_update_t_terminal_configs]
GO

CREATE TRIGGER [dbo].[TR_update_t_terminal_configs] ON [dbo].[t_terminal_configs]
FOR UPDATE
AS 
BEGIN
UPDATE t_terminal_configs
SET modification_datetime = GETDATE()
WHERE id IN (SELECT id FROM inserted)
END
RETURN


GO

