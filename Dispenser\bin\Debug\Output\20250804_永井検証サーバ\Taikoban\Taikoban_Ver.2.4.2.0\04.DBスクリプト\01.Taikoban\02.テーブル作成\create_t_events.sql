USE [$(Param1)]
GO
/****** Object:  Table [dbo].[t_events]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_events]') AND type in (N'U'))
DROP TABLE [dbo].[t_events]
GO

CREATE TABLE [dbo].[t_events](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[registered_by] [int] NOT NULL,
	[level] [int] NOT NULL,
	[source] [int] NOT NULL,
	[message] [nvarchar](max) NOT NULL,
 CONSTRAINT [PK_t_events] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_events] ADD  CONSTRAINT [DF_t_events_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO
ALTER TABLE [dbo].[t_events] ADD  CONSTRAINT [DF_t_events_registered_by]  DEFAULT ((0)) FOR [registered_by]
GO
ALTER TABLE [dbo].[t_events] ADD  CONSTRAINT [DF_t_events_level]  DEFAULT ((0)) FOR [level]
GO
ALTER TABLE [dbo].[t_events] ADD  CONSTRAINT [DF_t_events_source]  DEFAULT ((0)) FOR [source]
GO
ALTER TABLE [dbo].[t_events] ADD  CONSTRAINT [DF_t_events_message]  DEFAULT ('') FOR [message]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_events', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_events', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_events', @level2type=N'COLUMN',@level2name=N'registered_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'イベントレベル 0:情報 1:警告 2:重要 3:エラー' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_events', @level2type=N'COLUMN',@level2name=N'level'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'エラー発生元　1:Agent 2:Client 3:Server 4:API 5:Portal用DLL 6:CIS用DLL 7:管理コンソール用DLL 8:ID連携 9:レポートサービス 10:管理コンソール 11:ログ削除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_events', @level2type=N'COLUMN',@level2name=N'source'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'イベント内容' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_events', @level2type=N'COLUMN',@level2name=N'message'
GO
