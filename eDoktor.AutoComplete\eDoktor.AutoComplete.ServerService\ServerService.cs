﻿// ServerService.cs

using System;
using System.ServiceProcess;

namespace eDoktor.Taikoban.ServerService
{
	public partial class ServerService : ServiceBase
	{
		#region Fields
		private AutoComplete.Server.Server _server;
		#endregion

		#region Constructors
		public ServerService()
		{
			InitializeComponent();
		}
		#endregion

		#region Protected Methods
		protected override void OnStart(string[] args)
		{
			try
			{
				base.OnStart(args);

				if (_server != null)
				{
					return;
				}

				_server = new AutoComplete.Server.Server();
				_server.Start();
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		protected override void OnStop()
		{
			try
			{
				base.OnStop();

				if (_server != null)
				{
					_server.Dispose();
					_server = null;
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		protected override void OnShutdown()
		{
			try
			{
				base.OnShutdown();

				if (_server != null)
				{
					_server.Dispose();
					_server = null;
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion
	}
}
