﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
	<appSettings>
		<!-- Trace -->
		<add key="EnableTrace" value="true"/>
		<add key="EnableDebugTrace" value="true"/>
		<add key="EnableDetailedExceptionTrace" value="true"/>
		<add key="ContainingFolderPath" value="D:\eDoktor\Taikoban\Logs\HXScreenSaverMonitor\"/>
		<add key="TraceTermInDays" value="30"/>
		
		<!-- Monitor -->
		<add key="HXScreenSaverProcessName" value="Fujitsu.HOPE.EGMAINHX.Tools.HXScreenSaver"/>
		<add key="HXScreenSaverTargetWindowClassName" value=""/>
		<add key="HXScreenSaverTargetWindowCaption" value="Fujitsu.HOPE.EGMAINHX.HXScreenSaver.CertView"/>
		<add key="HXScreenSaverUserElementAutomationId" value="LoginText"/>
		<add key="HXScreenSaverPasswordElementAutomationId" value="PasswordText"/>
		<add key="HXScreenSaverButtonElementAutomationId" value="SignInButton"/>
		<add key="HXScreenSaverRetryCount" value="100"/>
		<add key="HXScreenSaverRetryWait" value="1000"/>
		<add key="HXScreenSaverMoveDistance" value="101"/>
		<add key="MoveDistance" value="101"/>
		<add key="LockScriptPath" value="D:\eDoktor\Taikoban\Scripts\CallAuthScreen\LockLeavingSeat.cmd"/>
	</appSettings>
</configuration>