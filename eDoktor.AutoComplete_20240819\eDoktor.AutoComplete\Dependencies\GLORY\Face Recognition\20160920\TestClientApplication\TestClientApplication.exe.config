﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="TestClientApplication.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="Glory.FaceRecognition.Net.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <startup> 
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.1" />
    </startup>
    <userSettings>
        <TestClientApplication.Properties.Settings>
            <setting name="RetrieveImageFilePath" serializeAs="String">
                <value />
            </setting>
            <setting name="RetrieveStartX" serializeAs="String">
                <value />
            </setting>
            <setting name="RetrieveStartY" serializeAs="String">
                <value />
            </setting>
            <setting name="RetrieveEndX" serializeAs="String">
                <value />
            </setting>
            <setting name="RetrieveEndY" serializeAs="String">
                <value />
            </setting>
            <setting name="RetrieveGroupID" serializeAs="String">
                <value />
            </setting>
            <setting name="RetrieveLevel" serializeAs="String">
                <value />
            </setting>
            <setting name="RetrieveMaxOutputCount" serializeAs="String">
                <value />
            </setting>
            <setting name="RegisterImageFilePath" serializeAs="String">
                <value />
            </setting>
            <setting name="RegisterStartX" serializeAs="String">
                <value />
            </setting>
            <setting name="RegisterStartY" serializeAs="String">
                <value />
            </setting>
            <setting name="RegisterEndX" serializeAs="String">
                <value />
            </setting>
            <setting name="RegisterEndY" serializeAs="String">
                <value />
            </setting>
            <setting name="RegisterMinEyeLength" serializeAs="String">
                <value />
            </setting>
            <setting name="RegisterMaxEyeLength" serializeAs="String">
                <value />
            </setting>
            <setting name="RegisterGroupID" serializeAs="String">
                <value />
            </setting>
            <setting name="ConnectPassword" serializeAs="String">
                <value />
            </setting>
            <setting name="ConnectDeviceCode" serializeAs="String">
                <value />
            </setting>
            <setting name="GetFaceSearchInfoImageFilePath" serializeAs="String">
                <value />
            </setting>
            <setting name="GetFaceSearchInfoStartX" serializeAs="String">
                <value />
            </setting>
            <setting name="GetFaceSearchInfoStartY" serializeAs="String">
                <value />
            </setting>
            <setting name="GetFaceSearchInfoEndX" serializeAs="String">
                <value />
            </setting>
            <setting name="GetFaceSearchInfoEndY" serializeAs="String">
                <value />
            </setting>
            <setting name="ConnectLoginID" serializeAs="String">
                <value />
            </setting>
            <setting name="DeleteFaceGroupID" serializeAs="String">
                <value />
            </setting>
            <setting name="DeleteFaceFaceID2" serializeAs="String">
                <value />
            </setting>
            <setting name="DeleteFaceFaceID1" serializeAs="String">
                <value />
            </setting>
            <setting name="GetFaceSearchInfotxtImage" serializeAs="String">
                <value />
            </setting>
            <setting name="RegisterProcessingID" serializeAs="String">
                <value />
            </setting>
            <setting name="DeleteProcessingID" serializeAs="String">
                <value />
            </setting>
            <setting name="RetrieveProcessingID" serializeAs="String">
                <value />
            </setting>
            <setting name="RetrieveMinEyeLength" serializeAs="String">
                <value />
            </setting>
            <setting name="RetrieveMaxEyeLength" serializeAs="String">
                <value />
            </setting>
        </TestClientApplication.Properties.Settings>
    </userSettings>
    <applicationSettings>
      <Glory.FaceRecognition.Net.Properties.Settings>
        <setting name="FaceRecognitionUri" serializeAs="String">
          <value>http://192.168.1.75/FaceRecognition/api/</value>
        </setting>
        <setting name="EnableJsonOutput" serializeAs="String">
          <value>False</value>
        </setting>
        <setting name="TimeoutSeconds" serializeAs="String">
          <value>100</value>
        </setting>
      </Glory.FaceRecognition.Net.Properties.Settings>
    </applicationSettings>
</configuration>