﻿// AuthUIServer.cs

using eDoktor.AutoComplete.AuthModule;
using eDoktor.Common;
using eDoktor.LocalServer;
using System;
using System.ComponentModel;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;
using System.Runtime.Remoting.Lifetime;

namespace eDoktor_AuthUIServer
{
	[Guid("76BAB059-762D-4760-A7E1-1F46E6B23CD2")]
	[ComVisible(true)]
	public interface IAuthUIServer
	{
		bool Start(int ownerHandle);
		void Abort();
	}

	[Guid("F403D791-DA00-49EF-BD50-290C53CFF6D7")]
	[ComVisible(true)]
	[InterfaceType(ComInterfaceType.InterfaceIsIDispatch)]
	public interface IAuthUIServerEvents
	{
		#region Events
		[DispId(1)]
		void Completed(int result, int methods, string userName);
		#endregion
	}

	[ClassInterface(ClassInterfaceType.None)]
	[ComSourceInterfaces(typeof(IAuthUIServerEvents))]
	[Guid("1863F369-4879-4AA4-8B5D-C283AA9EC7C6")]
	[ComVisible(true)]
	public class AuthUIServer : ReferenceCountedObject, IAuthUIServer
	{
		#region Fields
		private const int ThreadStartMillisecondsTimeout = 5000;
		private const int ThreadJoinMillisecondsTimeout = 5000;
		private readonly object SyncRoot = new object();
		private Thread _thread;
		private AutoResetEvent _startEvent;
		private AuthModuleLoader _loader;
		private volatile int _ownerHandle;
		#endregion

		#region Events
		public delegate void CompletedDelegate(int result, int methods, string userName);
		public event CompletedDelegate Completed;
		#endregion

		#region Constructors
		public AuthUIServer()
		{
			LifetimeServices.LeaseTime = TimeSpan.Zero;
			LifetimeServices.RenewOnCallTime = TimeSpan.Zero;
		}
		#endregion

		#region Register & Unregister
		[EditorBrowsable(EditorBrowsableState.Never)]
		[ComRegisterFunction()]
		public static void Register(Type type)
		{
			try
			{
				LocalServer.Register(type);
			}
			catch (Exception ex)
			{
				Trace.OutputExceptionTrace(ex);
			}
		}

		[EditorBrowsable(EditorBrowsableState.Never)]
		[ComUnregisterFunction()]
		public static void Unregister(Type type)
		{
			try
			{
				LocalServer.Unregister(type);
			}
			catch (Exception ex)
			{
				Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion

		#region IAuthUIServer
		public bool Start(int ownerHandle)
		{
			try
			{
				using (TimedLock.Lock(SyncRoot))
				{
					if (_thread != null)
					{
						return true;
					}

					_thread = new Thread(ThreadMain);
					_startEvent = new AutoResetEvent(false);
					_ownerHandle = ownerHandle;
					_thread.SetApartmentState(System.Threading.ApartmentState.STA);
					_thread.Start();

					if (!_startEvent.WaitOne(ThreadStartMillisecondsTimeout))
					{
						throw new TimeoutException();
					}
				}

				return true;
			}
			catch (Exception ex)
			{
				try
				{
					Trace.OutputExceptionTrace(ex);

					var thread = _thread;

					if ((thread != null) && (thread.IsAlive))
					{
						Abort();

						if (!thread.Join(ThreadJoinMillisecondsTimeout))
						{
							thread.Abort();
							_thread = null;
						}
					}
				}
				catch (Exception ex2)
				{
					Trace.OutputExceptionTrace(ex2);
				}
			}
			finally
			{
				try
				{
					using (TimedLock.Lock(SyncRoot))
					{
						if (_startEvent != null)
						{
							_startEvent.Dispose();
							_startEvent = null;
						}
					}
				}
				catch (Exception ex)
				{
					Trace.OutputExceptionTrace(ex);
				}
			}

			return false;
		}

		public void Abort()
		{
			try
			{
				using (TimedLock.Lock(SyncRoot))
				{
					if (_loader != null)
					{
						_loader.Abort();

						Trace.OutputDebugTrace("aborted");
					}
				}
			}
			catch (Exception ex)
			{
				Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion

		#region Private Methods
		private void ThreadMain()
		{
			var result = AuthResultType.SystemFault;
			var methods = AuthMethodTypes.Undefined;
			string userName = null;

			try
			{
				Trace.OutputDebugTrace("_ownerHandle={0}", _ownerHandle);

				var authRequest = new AuthRequest()
				{
					Context = AuthContextType.CredentialProvider,
					Mode = AuthModeType.FindUser,
					OwnerHandle = new IntPtr(_ownerHandle),
					UserName = string.Empty,
					DisplayPosition = DisplayPositionType.Center,
					Margin = Padding.Empty,
					TopMost = false,
					Draggable = false
				};
				string assemblyNameOrPath = Configuration.AppSetting("AuthModuleAssemblyNameOrPath");
				string typeName = Configuration.AppSetting("AuthModuleTypeName");

				Trace.OutputDebugTrace("assemblyNameOrPath={0} typeName={1}", assemblyNameOrPath, typeName);

				_loader = new AuthModuleLoader();

				if (_startEvent != null)
				{
					_startEvent.Set();
				}

				var authResponse = _loader.PerformAuthentication(assemblyNameOrPath, typeName, authRequest);

				if (authResponse != null)
				{
					result = authResponse.Result;
					methods = authResponse.Methods;
					userName = authResponse.UserName;
				}
			}
			catch (Exception ex)
			{
				Trace.OutputExceptionTrace(ex);
			}
			finally
			{
				try
				{
					var completed = Completed;

					if (completed != null)
					{
						completed((int)result, (int)methods, userName);
					}

					using (TimedLock.Lock(SyncRoot))
					{
						_loader = null;
						_thread = null;
					}
				}
				catch (Exception ex)
				{
					Trace.OutputExceptionTrace(ex);
				}
			}
		}
		#endregion
	}
}
