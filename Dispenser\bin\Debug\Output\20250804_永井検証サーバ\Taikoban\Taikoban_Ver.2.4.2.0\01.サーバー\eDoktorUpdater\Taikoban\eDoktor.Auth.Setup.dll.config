﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <section name="scriptConfiguration" type="eDoktor.Auth.Setup.Configuration.ScriptConfigurationSection,eDoktor.Auth.Setup"/>
    </configSections>
    <scriptConfiguration>
        <scripts>
            <!-- idは一意になるようにしてください。同じtypeの処理を複数追加できます。 -->
            <script id="1" type="PreInstall" command="" arguments="" asUser="false" createNoWindow="false" milliseconsTimeout="0" message=""/>
            <script id="2" type="PostInstall" command="register_contentDisplayComServer.bat" arguments="" asUser="false" createNoWindow="false" milliseconsTimeout="0" message=""/>
            <script id="3" type="PreUninstall" command="unregister_contentDisplayComServer.bat" arguments="" asUser="false" createNoWindow="false" milliseconsTimeout="0" message=""/>
            <script id="4" type="PostUninstall" command="" arguments="" asUser="false" createNoWindow="false" milliseconsTimeout="0" message=""/>
        </scripts>
    </scriptConfiguration>
    <appSettings>
        <add key="Version" value="101"/>
        <add key="InstallPath" value="D:\eDoktor\Taikoban\Client"/>
        <add key="FileServerNameOrAddress" value="*************"/>
        <add key="SharedFolderName" value="eDoktorUpdater"/>
        <add key="ConfigurationName" value="Taikoban"/>
        <!-- ProductType 0:Taikoban 1:FaceLink -->
        <add key="ProductType" value="0"/>
        <add key="NetworkAccountUserName" value="gm50roxpFAifwyXI9cO/JLYbfU53/wNDQVKKlcag2a0="/>
        <add key="NetworkAccountPassword" value="n06PS7vItOcIlwJO+Kg6BQ=="/>
        <add key="DefaultUserName" value="NdAO6cCTFzm5bB3UD/xW8g=="/>
        <add key="DefaultPassword" value="2ZtP3chcIlPyEwcfhOQABw=="/>
        <add key="DefaultLogonTo" value="IISPqVAtKzsTtc/kgrvdFw=="/>
        <add key="DefaultDomain" value="IISPqVAtKzsTtc/kgrvdFw=="/>
        <add key="Enabled" value="true"/>
        <add key="AutoUpdate" value="true"/>
        <add key="CredentialProvider" value="true"/>
        <add key="SystemFaultMessage" value="認証サーバーに接続できません。"/>
        <add key="PromptMessage0" value="カードをかざしてください。"/>
        <add key="PromptMessage1" value="カードリーダーが接続されていません。"/>
        <add key="LoginNotAllowedMessage" value="エラー: 該当アカウントはこの端末へのログインを許可されていません。"/>
        <add key="SetupMessage" value="認証システムを更新しています。電源を切らないでください。"/>
        <add key="EmergencyLoginEnabled" value="false"/>
        <add key="EmergencyLoginUsageMessage" value="緊急ログインは緊急時にのみ使用してください。"/>
        <add key="EmergencyLoginCheckboxLabel" value="緊急ログイン"/>
        <add key="EmergencyLoginCommentMessage" value="緊急ログインコメント入力"/>
        <add key="EmergencyLoginCommentRuleMessage" value="コメント（4文字以上）を入力してください。"/>
        <add key="EmergencyLoginCommentSampleMessage" value="コメント"/>
        <add key="ExtAuthErrorLoginCommentMessage" value="外部認証エラーログインコメント入力"/>
        <add key="ExtAuthErrorLoginCommentRuleMessage" value="コメント（4文字以上）を入力してください。"/>
        <add key="ExtAuthErrorLoginCommentSampleMessage" value="コメント"/>
        <add key="CachedPasswordUpdateMessage" value="パスワードの確認を行います。"/>
        <add key="CachedPasswordUpdateInstructionMessage" value="現在のパスワードを入力してください。"/>
        <add key="CachedPasswordUpdateInvalidPasswordMessage" value="エラー: 現在のパスワードが一致していません。"/>
        <add key="MinimumCommentLength" value="4"/>
        <add key="MaximumCommentLength" value="200"/>
        <add key="CommentInputTimeout" value="60000"/>
        <add key="BioAuthMaxRetryCount" value="2"/>
        <add key="LogRotationFileSize" value="104857600"/>
        <add key="DevicesToBeExcludedFromVideoSources" value="The Imaging Source DFG/USB2pro|DFG/USB2pro unused IR receiver"/>
        <add key="PlaySound" value="true"/>
        <add key="ConnectSoundPath" value="C:\Windows\Media\Windows Background.wav"/>
        <add key="DisconnectSoundPath" value="C:\Windows\Media\Windows Foreground.wav"/>
        <add key="PasswordUpdateWaitingMilliseconds" value="10000"/>
        <add key="CountdownCountingSeconds" value="11"/>
        <add key="CountdownDisplaySeconds" value="10"/>
        <!-- MaintenanceKey 160:VK_LSHIFT(0xA0), 90:Z key(0x5A), 88:X key(0x58) -->
        <add key="MaintenanceKeyCombination" value="160|90"/>
    </appSettings>
</configuration>