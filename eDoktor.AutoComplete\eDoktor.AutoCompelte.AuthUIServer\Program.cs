﻿// Program.cs

using System;
using eDoktor.Common;
using eDoktor.LocalServer;

namespace eDoktor_AuthUIServer
{
	class Program
	{
		static void Main(string[] args)
		{
			try
			{
				Trace.EnableDebugTrace = Configuration.AppSetting("EnableDebugTrace", false);
				Trace.EnableDetailedExceptionTrace = Configuration.AppSetting("EnableDetailedExceptionTrace", false);
				Trace.TraceTerm = System.TimeSpan.FromDays(Configuration.AppSetting("TraceTermInDays", Trace.DefaultTraceTerm.Days));
				Trace.SafeEnableTrace = Configuration.AppSetting("EnableTrace", false);
				var localServer = LocalServer.Instance;
				localServer.Run(new eDoktor.LocalServer.ClassFactory<AuthUIServer>(typeof(IAuthUIServer), "1863F369-4879-4AA4-8B5D-C283AA9EC7C6"));
			}
			catch (Exception ex)
			{
				Trace.OutputExceptionTrace(ex);
			}
		}
	}
}
