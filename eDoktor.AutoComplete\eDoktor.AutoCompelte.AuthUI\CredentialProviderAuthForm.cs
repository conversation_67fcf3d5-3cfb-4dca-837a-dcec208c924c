﻿// CredentialProviderAuthForm.cs

using System;
using System.Drawing;
using System.Security.Permissions;
using System.Windows.Forms;

namespace eDoktor.AutoComplete.AuthUI
{
	using Interprocess.ObjectValidationExtensions;

	partial class CredentialProviderAuthForm : AutoComplete.AuthModule.AuthFormBase
	{
		#region Enumerations
		private enum PageType
		{
			Undefined = -1,
			<PERSON><PERSON><PERSON><PERSON><PERSON>,
			FaceAuthFailure,
			InvalidUserNameOrPassword,
			NetworkError,
			PasswordCredential,
			Prompt,
			SystemFault
		}
		#endregion

		#region Fields
		private readonly object SyncRoot = new object();
		private AutoComplete.AuthModule.AuthModuleLoader _loader;
		private Common.KeyCombination _sasShortcutKeyCombination;
		private PageBase[] _pages;
		private PageType _currentPage = PageType.Undefined;
		private PageType _nextPage = PageType.Undefined;
		#endregion

		#region Properties
		protected PageBase CurrentPage { get { return _pages[(int)_currentPage]; } }
		#endregion

		#region Constructors
		public CredentialProviderAuthForm()
		{
			InitializeComponent();
			InitializePages();
		}
		#endregion

		#region Protected Methods
		[UIPermission(SecurityAction.Demand, Window = UIPermissionWindow.AllWindows)]
		protected override bool ProcessDialogKey(Keys keyData)
		{
			try
			{
				if (((keyData & Keys.KeyCode) == Keys.Return) && ((keyData & (Keys.Alt | Keys.Control)) == Keys.None) && (_currentPage == PageType.Prompt))
				{
					StartFaceAuth();
					return true;
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}

			return base.ProcessDialogKey(keyData);
		}

		protected override void OnLoad(EventArgs e)
		{
			try
			{
				base.OnLoad(e);
				SetPage(PageType.Prompt);
				int[] sasKeyCodes = Common.Configuration.AppSettingArray("SasKeyCodes", Common.Configuration.DefaultSeparator, StringSplitOptions.RemoveEmptyEntries, 0);

				if ((sasKeyCodes != null) && (sasKeyCodes.Length > 0))
				{
					_sasShortcutKeyCombination = new Common.KeyCombination(sasKeyCodes);
					_sasShortcutKeyCombination.Pressed += OnSasShortcutKeyCombinationPressed;
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
				CloseInternally();
			}
		}

		protected override void OnFormClosed(FormClosedEventArgs e)
		{
			try
			{
				base.OnFormClosed(e);

				if (_sasShortcutKeyCombination != null)
				{
					_sasShortcutKeyCombination.Dispose();
					_sasShortcutKeyCombination = null;
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		protected override void OnCancelRequested(EventArgs e)
		{
			try
			{
				AbortLoader();
				base.OnCancelRequested(e);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}

		}
		#endregion

		#region Private Methods
		private void InitializePages()
		{
			SuspendLayout();
			var deviceErrorPage = new DeviceErrorPage();
			var faceAuthFailurePage = new FaceAuthFailurePage();
			var invalidUserNameOrPasswordPage = new InvalidUserNameOrPasswordPage();
			var networkErrorPage = new NetworkErrorPage();
			var passwordCredentialPage = new PasswordCredentialPage();
			var promptPage = new PromptPage();
			var systemFaultPage = new SystemFaultPage();
			_pages = new PageBase[] { deviceErrorPage, faceAuthFailurePage, invalidUserNameOrPasswordPage, networkErrorPage, passwordCredentialPage, promptPage, systemFaultPage };
			Controls.AddRange(_pages);

			foreach (var page in _pages)
			{
				page.Visible = false;
				AddClickThroughControls(page.ClickThroughControls);
			}

			deviceErrorPage.OKButtonClick += OnDeviceErrorPageOKButtonClick;
			faceAuthFailurePage.OKButtonClick += OnFaceAuthFailurePageOKButtonClick;
			invalidUserNameOrPasswordPage.OKButtonClick += OnInvalidUserNameOrPasswordPageOKButtonClick;
			networkErrorPage.OKButtonClick += OnNetworkErrorPageOKButtonClick;
			passwordCredentialPage.AuthenticateUser += OnPasswordCredentialPageAuthenticateUser;
			passwordCredentialPage.CancelButtonClick += OnPasswordCredentialPageCancelButtonClick;
			promptPage.ShutdownButtonClick += OnPromptPageShutdownButtonClick;
			promptPage.RestartButtonClick += OnPromptPageRestartButtonClick;
			systemFaultPage.OKButtonClick += OnSystemFaultPageOKButtonClick;
			this.ResumeLayout(false);
		}

		private bool AbortLoader()
		{
			using (Common.TimedLock.Lock(SyncRoot))
			{
				if (_loader != null)
				{
					_loader.Abort();
					return true;
				}

				return false;
			}
		}

		private static bool IsNetworkAvailable()
		{
			return Common.Network.IsNetworkAvailable();
		}

		private PageBase GetPage(PageType pageType)
		{
			return _pages[(int)pageType];
		}

		private void SetPage(PageType pageType, bool ignoreLoader = false)
		{
			if (pageType == _currentPage)
			{
				return;
			}

			if (InvokeRequired)
			{
				Action<PageType, bool> setPageDelegate = (tempPageType, tempIgnoreLoader) => SetPage(tempPageType, tempIgnoreLoader);
				Invoke(setPageDelegate, pageType, ignoreLoader);
				return;
			}

			_nextPage = pageType;

			if ((!ignoreLoader) && (AbortLoader()))
			{
				return;
			}

			if (_currentPage != PageType.Undefined)
			{
				_pages[(int)_currentPage].Visible = false;
			}

			_currentPage = _nextPage;
			_nextPage = PageType.Undefined;
			CurrentPage.Visible = true;
			ActiveControl = CurrentPage;
		}

		private void StartFaceAuth()
		{
			try
			{
				using (Common.TimedLock.Lock(SyncRoot))
				{
					if (_loader != null)
					{
						return;
					}

					if (!IsNetworkAvailable())
					{
						SetPage(PageType.NetworkError);
						return;
					}

					_loader = new AutoComplete.AuthModule.AuthModuleLoader();
				}

				var assemblyNameOrPath = Common.Configuration.AppSetting("AuthModuleAssemblyNameOrPath");
				var typeName = Common.Configuration.AppSetting("AuthModuleTypeName");
				var authRequest = AuthRequest;
				var newAuthRequest = (authRequest != null) ? new AutoComplete.AuthModule.AuthRequest() { Context = authRequest.Context, Mode = authRequest.Mode, OwnerHandle = Handle, TopMost = authRequest.TopMost, Draggable = authRequest.Draggable, DisplayPosition = AutoComplete.AuthModule.DisplayPositionType.CenterOwner, Margin = authRequest.Margin, UserName = authRequest.UserName } : null;
				var tempAuthResponse = _loader.PerformAuthentication(assemblyNameOrPath, typeName, newAuthRequest);

				if (CancellationToken.IsCancellationRequested)
				{
					return;
				}

				if (_nextPage != PageType.Undefined)
				{
					SetPage(_nextPage, true);
					return;
				}

				if ((tempAuthResponse == null) || (tempAuthResponse.Result == AutoComplete.AuthModule.AuthResultType.Undefined) || (tempAuthResponse.Result == AutoComplete.AuthModule.AuthResultType.SystemFault) || ((tempAuthResponse.Result == AutoComplete.AuthModule.AuthResultType.Success) && (string.IsNullOrWhiteSpace(tempAuthResponse.UserName))))
				{
					if (!IsNetworkAvailable())
					{
						SetPage(PageType.NetworkError, true);
						return;
					}

					SetPage(PageType.SystemFault, true);
					return;
				}
				else if (tempAuthResponse.Result == AutoComplete.AuthModule.AuthResultType.NoMatchFound)
				{
					SetPage(PageType.FaceAuthFailure, true);
					return;
				}
				else if (tempAuthResponse.Result == AutoComplete.AuthModule.AuthResultType.DeviceError)
				{
					SetPage(PageType.DeviceError, true);
					return;
				}
				else if (tempAuthResponse.Result == AutoComplete.AuthModule.AuthResultType.UserAborted)
				{
					return;
				}

				AuthResponse = tempAuthResponse;
				CloseInternally();
			}
			finally
			{
				_loader = null;
			}
		}

		private void OnSasShortcutKeyCombinationPressed(object sender, EventArgs e)
		{
			try
			{
				var nextPage = (!IsNetworkAvailable()) ? PageType.NetworkError : PageType.PasswordCredential;
				SetPage(nextPage);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnDeviceErrorPageOKButtonClick(object sender, EventArgs e)
		{
			try
			{
				SetPage(PageType.Prompt);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnFaceAuthFailurePageOKButtonClick(object sender, EventArgs e)
		{
			try
			{
				SetPage(PageType.PasswordCredential);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnInvalidUserNameOrPasswordPageOKButtonClick(object sender, EventArgs e)
		{
			try
			{
				SetPage(PageType.PasswordCredential);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnNetworkErrorPageOKButtonClick(object sender, EventArgs e)
		{
			try
			{
				SetPage(PageType.Prompt);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnPasswordCredentialPageAuthenticateUser(object sender, PasswordCredentialEventArgs e)
		{
			try
			{
				if ((string.IsNullOrWhiteSpace(e.UserName)) || (string.IsNullOrWhiteSpace(e.Password)))
				{
					SetPage(PageType.InvalidUserNameOrPassword);
					return;
				}

				if (!IsNetworkAvailable())
				{
					SetPage(PageType.NetworkError);
					return;
				}

				using (var client = new Auth.Interprocess.Client(Auth.Interprocess.ConnectionType.ToRemoteServer, false))
				{
					client.Start();
					var request = new Auth.Interprocess.ClientToServerUserQueryRequestData() { HostName = Auth.Interprocess.Configuration.MachineName, UserName = e.UserName };
					var response = client.Transceive(request);

					if (response == null)
					{
						var nextPage = (IsNetworkAvailable()) ? PageType.SystemFault : PageType.NetworkError; 
						SetPage(nextPage);
						return;
					}

					if ((!response.Credential.IsValid) || (response.Credential.Password != e.Password))
					{
						SetPage(PageType.InvalidUserNameOrPassword);
						return;
					}

					AuthResponse = new AutoComplete.AuthModule.AuthResponse() { Result = AutoComplete.AuthModule.AuthResultType.Success, Methods = AutoComplete.AuthModule.AuthMethodTypes.Password, UserName = response.Credential.UserName };
					CloseInternally();
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
				SetPage(PageType.SystemFault);
			}
		}

		private void OnPasswordCredentialPageCancelButtonClick(object sender, EventArgs e)
		{
			try
			{
				SetPage(PageType.Prompt);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnPromptPageShutdownButtonClick(object sender, EventArgs e)
		{
			try
			{
				AuthResponse = new AutoComplete.AuthModule.AuthResponse() { Result = AutoComplete.AuthModule.AuthResultType.ShutdownRequested };
				CloseInternally();
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnPromptPageRestartButtonClick(object sender, EventArgs e)
		{
			try
			{
				AuthResponse = new AutoComplete.AuthModule.AuthResponse() { Result = AutoComplete.AuthModule.AuthResultType.RestartRequested };
				CloseInternally();
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnSystemFaultPageOKButtonClick(object sender, EventArgs e)
		{
			try
			{
				AuthResponse = new AutoComplete.AuthModule.AuthResponse() { Result = AutoComplete.AuthModule.AuthResultType.SystemFault };
				CloseInternally();
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion
	}
}
