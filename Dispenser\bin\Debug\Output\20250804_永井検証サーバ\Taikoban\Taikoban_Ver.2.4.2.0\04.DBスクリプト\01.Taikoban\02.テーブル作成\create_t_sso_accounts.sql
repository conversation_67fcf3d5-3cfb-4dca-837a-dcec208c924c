USE [$(Param1)]
GO
/****** Object:  Table [dbo].[t_sso_accounts]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_sso_accounts]') AND type in (N'U'))
DROP TABLE [dbo].[t_sso_accounts]
GO

CREATE TABLE [dbo].[t_sso_accounts](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[registered_by] [int] NOT NULL,
	[modification_datetime] [datetime2](7) NOT NULL,
	[modified_by] [int] NOT NULL,
	[invalidated] [bit] NOT NULL,
	[invalidation_datetime] [datetime2](7) NULL,
	[invalidated_by] [int] NOT NULL,
	[logically_deleted] [bit] NOT NULL,
	[logical_deletion_datetime] [datetime2](7) NULL,
	[logically_deleted_by] [int] NOT NULL,
	[synchronized] [bit] NOT NULL,
	[usage_start_datetime] [datetime2](7) NULL,
	[usage_end_datetime] [datetime2](7) NULL,
	[account_logon_id] [nvarchar](255) NOT NULL,
	[sso_account_system_id] [int] NOT NULL,
	[logon_id] [nvarchar](255) NOT NULL,
	[password] [nvarchar](255) NOT NULL,
	[notes] [nvarchar](255) NOT NULL,
 CONSTRAINT [PK_t_sso_accounts] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [IX_t_sso_accounts] UNIQUE NONCLUSTERED 
(
	[sso_account_system_id] ASC,
	[logon_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [IX_t_sso_accounts2] UNIQUE NONCLUSTERED 
(
	[account_logon_id] ASC,
	[sso_account_system_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_sso_accounts] ADD  CONSTRAINT [DF_t_sso_accounts_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO
ALTER TABLE [dbo].[t_sso_accounts] ADD  CONSTRAINT [DF_t_sso_accounts_registered_by]  DEFAULT ((0)) FOR [registered_by]
GO
ALTER TABLE [dbo].[t_sso_accounts] ADD  CONSTRAINT [DF_t_sso_accounts_modification_datetime]  DEFAULT (getdate()) FOR [modification_datetime]
GO
ALTER TABLE [dbo].[t_sso_accounts] ADD  CONSTRAINT [DF_t_sso_accounts_modified_by]  DEFAULT ((0)) FOR [modified_by]
GO
ALTER TABLE [dbo].[t_sso_accounts] ADD  CONSTRAINT [DF_t_sso_accounts_invalidated]  DEFAULT ((0)) FOR [invalidated]
GO
ALTER TABLE [dbo].[t_sso_accounts] ADD  CONSTRAINT [DF_t_sso_accounts_invalidated_by]  DEFAULT ((0)) FOR [invalidated_by]
GO
ALTER TABLE [dbo].[t_sso_accounts] ADD  CONSTRAINT [DF_t_sso_accounts_logically_deleted]  DEFAULT ((0)) FOR [logically_deleted]
GO
ALTER TABLE [dbo].[t_sso_accounts] ADD  CONSTRAINT [DF_t_sso_accounts_logically_deleted_by]  DEFAULT ((0)) FOR [logically_deleted_by]
GO
ALTER TABLE [dbo].[t_sso_accounts] ADD  CONSTRAINT [DF_t_sso_accounts_synchronized]  DEFAULT ('false') FOR [synchronized]
GO
ALTER TABLE [dbo].[t_sso_accounts] ADD  CONSTRAINT [DF_t_sso_accounts_account_logon_id]  DEFAULT ('') FOR [account_logon_id]
GO
ALTER TABLE [dbo].[t_sso_accounts] ADD  CONSTRAINT [DF_t_sso_accounts_sso_account_system_id]  DEFAULT ((0)) FOR [sso_account_system_id]
GO
ALTER TABLE [dbo].[t_sso_accounts] ADD  CONSTRAINT [DF_t_sso_accounts_logon_id]  DEFAULT ('') FOR [logon_id]
GO
ALTER TABLE [dbo].[t_sso_accounts] ADD  CONSTRAINT [DF_t_sso_accounts_password]  DEFAULT ('') FOR [password]
GO
ALTER TABLE [dbo].[t_sso_accounts] ADD  CONSTRAINT [DF_t_sso_accounts_notes]  DEFAULT ('') FOR [notes]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_sso_accounts', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_sso_accounts', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_sso_accounts', @level2type=N'COLUMN',@level2name=N'registered_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_sso_accounts', @level2type=N'COLUMN',@level2name=N'modification_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_sso_accounts', @level2type=N'COLUMN',@level2name=N'modified_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効フラグ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_sso_accounts', @level2type=N'COLUMN',@level2name=N'invalidated'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_sso_accounts', @level2type=N'COLUMN',@level2name=N'invalidation_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効登録者' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_sso_accounts', @level2type=N'COLUMN',@level2name=N'invalidated_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_sso_accounts', @level2type=N'COLUMN',@level2name=N'logically_deleted'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_sso_accounts', @level2type=N'COLUMN',@level2name=N'logical_deletion_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_sso_accounts', @level2type=N'COLUMN',@level2name=N'logically_deleted_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'連携データ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_sso_accounts', @level2type=N'COLUMN',@level2name=N'synchronized'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'有効期間開始日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_sso_accounts', @level2type=N'COLUMN',@level2name=N'usage_start_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'有効期間終了日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_sso_accounts', @level2type=N'COLUMN',@level2name=N'usage_end_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'アカウントのログオンID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_sso_accounts', @level2type=N'COLUMN',@level2name=N'account_logon_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'SSOアカウント体系ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_sso_accounts', @level2type=N'COLUMN',@level2name=N'sso_account_system_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ログオンID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_sso_accounts', @level2type=N'COLUMN',@level2name=N'logon_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_sso_accounts', @level2type=N'COLUMN',@level2name=N'password'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'注記' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_sso_accounts', @level2type=N'COLUMN',@level2name=N'notes'
GO
