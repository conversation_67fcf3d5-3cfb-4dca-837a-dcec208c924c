﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <section name="authentication" type="eDoktor.Auth.AuthenticationSchemes.Configuration.AuthenticationConfigurationSection,eDoktor.Auth.AuthenticationSchemes"/>
    </configSections>
    <authentication>
        <domains>
            <!-- domain type 1:AD 2:LDAP -->
            <domain name="domain" type="1" partner="" partnerType="0" encryptedBindUser="" encryptedBindPassword="">
                <servers>
                    <server name="01" url="" type="545" timeout="5000" mark="cn=" properties="cn"/>
                </servers>
            </domain>
        </domains>
    </authentication>
</configuration>