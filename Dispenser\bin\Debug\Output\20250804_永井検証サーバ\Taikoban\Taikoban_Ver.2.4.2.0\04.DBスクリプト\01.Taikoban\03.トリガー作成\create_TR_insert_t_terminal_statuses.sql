USE [$(Param1)]
GO

/****** Object:  Trigger [dbo].[TR_insert_t_terminal_statuses]    Script Date: 01/07/2015 13:22:57 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[TR_insert_t_terminal_statuses]') AND type in (N'TR'))
DROP TRIGGER [dbo].[TR_insert_t_terminal_statuses]
GO

CREATE TRIGGER [dbo].[TR_insert_t_terminal_statuses] ON [dbo].[t_terminal_statuses]
FOR INSERT
AS 
BEGIN

IF (SELECT COUNT(*) FROM t_terminals WHERE name = (SELECT terminal_name FROM inserted)) = 0
BEGIN
INSERT t_terminals
 (
registration_datetime
, registered_by
, modification_datetime
, modified_by
, invalidated
, invalidation_datetime
, invalidated_by
, logically_deleted
, logical_deletion_datetime
, logically_deleted_by
, usage_start_datetime
, usage_end_datetime
, group_id
, name
, use_sbc
, notes
, authentication_enabled
, shared_account_password
)
 VALUES
 (
GETDATE()
, ********
, GETDATE()
, ********
, 'FALSE'
, NULL
, 0
, 'FALSE'
, NULL
, 0
, NULL
, NULL
, 1
, (SELECT terminal_name FROM inserted)
, 'TRUE'
, ''
, 'TRUE'
, 'IISPqVAtKzsTtc/kgrvdFw=='
)
END
ELSE IF (SELECT COUNT(*) FROM t_terminals WHERE name = (SELECT terminal_name FROM inserted) AND logically_deleted = 'true') > 0
BEGIN
UPDATE t_terminals
 SET
 modification_datetime = GETDATE()
, modified_by = ********
, invalidated = 'FALSE'
, invalidation_datetime = NULL
, invalidated_by = 0
, logically_deleted = 'FALSE'
, logical_deletion_datetime = NULL
, logically_deleted_by = 0
, usage_start_datetime = NULL
, usage_end_datetime = NULL
, group_id = 1
, name = (SELECT terminal_name FROM inserted)
, use_sbc = 'TRUE'
, notes = ''
, authentication_enabled = 'TRUE'
WHERE name = (SELECT terminal_name FROM inserted)
END

END
RETURN

GO


