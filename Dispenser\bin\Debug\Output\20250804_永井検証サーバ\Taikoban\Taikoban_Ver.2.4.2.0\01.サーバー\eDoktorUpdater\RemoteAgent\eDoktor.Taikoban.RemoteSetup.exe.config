﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <appSettings>
    <add key="Version" value="101" />
    <add key="InstallPath" value="D:\eDoktor\Taikoban\RemoteAgent" />
    <add key="FileServerNameOrAddress" value="*************" />
    <add key="SharedFolderName" value="eDoktorUpdater" />
    <add key="ConfigurationName" value="RemoteAgent" />
    <add key="NetworkAccountUserName" value="gm50roxpFAifwyXI9cO/JLYbfU53/wNDQVKKlcag2a0=" />
    <add key="NetworkAccountPassword" value="n06PS7vItOcIlwJO+Kg6BQ==" />
    <add key="DefaultUserName" value="ca8rgeZxg08qhQcuAO3laA==" />
    <add key="DefaultPassword" value="ca8rgeZxg08qhQcuAO3laA==" />
    <add key="DefaultLogonTo" value="JhX2IN39vpCSG020YWRtpA==" />
    <add key="DefaultDomain" value="JhX2IN39vpCSG020YWRtpA==" />
    <add key="Enabled" value="true"/>

    <!-- スクリプト実行用設定を追加 -->
    <!-- 古いバージョンのRemoteAgentのプログラムを更新するとプログラムだけではアンインストールが出来ないので-->
    <!-- アンインストール時にアンインストール用バッチファイルを実行することにより古いバージョンのプログラムをアンインストールするようにする-->
    <add key="PreInstallScript1" value="" />
    <add key="PreInstallArguments1" value="" />
    <add key="PreInstallAsUser" value="false" />
    <add key="PostInstallScript1" value="" />
    <add key="PostInstallArguments1" value="" />
    <add key="PostInstallAsUser" value="false" />
    <add key="PreUninstallScript1" value="preUninstall_RemoteAgent.bat" />
    <add key="PreUninstallArguments1" value="" />
    <add key="PreUninstallAsUser" value="false" />
    <add key="PostUninstallScript1" value="" />
    <add key="PostUninstallArguments1" value="" />
    <add key="PostUninstallAsUser" value="false" />
    <!-- スクリプト(コマンド)のタイムアウト値(ms) 省略値=60000(60秒)-->
    <add key="ScriptTimeOut" value="60000" />
    <!-- Preアンインストールスクリプト実行時もUninstallメソードを呼ぶかどうか 省略値=false-->
    <add key="UninstallMethodExecute" value="false" />
    <!-- Preインストールスクリプト実行時もInstallメソードを呼ぶかどうか 省略値=false-->
    <add key="InstallMethodExecute" value="false" />
  </appSettings>
</configuration>