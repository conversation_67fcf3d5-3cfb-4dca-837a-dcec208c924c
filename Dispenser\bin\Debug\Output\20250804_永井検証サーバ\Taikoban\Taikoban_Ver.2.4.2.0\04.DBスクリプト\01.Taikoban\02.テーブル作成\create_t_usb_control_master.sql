USE [$(Param1)]
GO

/****** Object:  Table [dbo].[t_usb_control_master]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_usb_control_master]') AND type in (N'U'))
DROP TABLE [dbo].[t_usb_control_master]
GO

CREATE TABLE [dbo].[t_usb_control_master](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[registered_by] [int] NOT NULL,
	[modification_datetime] [datetime2](7) NOT NULL,
	[modified_by] [int] NOT NULL,
	[account_groups_id] [int] NOT NULL,
	[terminal_groups_id] [int] NOT NULL,
	[vendor_id] [nvarchar](200) NOT NULL,
	[vendor_name] [nvarchar](100) NOT NULL,
	[product_id] [nvarchar](200) NOT NULL,
	[product_name] [nvarchar](100) NOT NULL,
	[serialnumber] [nvarchar](200) NOT NULL,
	[notes] [nvarchar](255) NOT NULL,
 CONSTRAINT [PK_t_usb_control_master] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_usb_control_master] ADD  CONSTRAINT [DF_t_usb_control_master_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO
ALTER TABLE [dbo].[t_usb_control_master] ADD  CONSTRAINT [DF_t_usb_control_master_registered_by]  DEFAULT ((0)) FOR [registered_by]
GO
ALTER TABLE [dbo].[t_usb_control_master] ADD  CONSTRAINT [DF_t_usb_control_master_modification_datetime]  DEFAULT (getdate()) FOR [modification_datetime]
GO
ALTER TABLE [dbo].[t_usb_control_master] ADD  CONSTRAINT [DF_t_usb_control_master_modified_by]  DEFAULT ((0)) FOR [modified_by]
GO
ALTER TABLE [dbo].[t_usb_control_master] ADD  CONSTRAINT [DF_t_usb_control_master_account_groups_id]  DEFAULT ((0)) FOR [account_groups_id]
GO
ALTER TABLE [dbo].[t_usb_control_master] ADD  CONSTRAINT [DF_t_usb_control_master_terminal_groups_id]  DEFAULT ((0)) FOR [terminal_groups_id]
GO
ALTER TABLE [dbo].[t_usb_control_master] ADD  CONSTRAINT [DF_t_usb_control_master_vendor_id]  DEFAULT ('') FOR [vendor_id]
GO
ALTER TABLE [dbo].[t_usb_control_master] ADD  CONSTRAINT [DF_t_usb_control_master_vendor_name]  DEFAULT ('') FOR [vendor_name]
GO
ALTER TABLE [dbo].[t_usb_control_master] ADD  CONSTRAINT [DF_t_usb_control_master_product_id]  DEFAULT ('') FOR [product_id]
GO
ALTER TABLE [dbo].[t_usb_control_master] ADD  CONSTRAINT [DF_t_usb_control_master_product_name]  DEFAULT ('') FOR [product_name]
GO
ALTER TABLE [dbo].[t_usb_control_master] ADD  CONSTRAINT [DF_t_usb_control_master_serialnumber]  DEFAULT ('') FOR [serialnumber]
GO
ALTER TABLE [dbo].[t_usb_control_master] ADD  CONSTRAINT [DF_t_usb_control_master_notes]  DEFAULT ('') FOR [notes]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_master', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_master', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_master', @level2type=N'COLUMN',@level2name=N'registered_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_master', @level2type=N'COLUMN',@level2name=N'modification_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_master', @level2type=N'COLUMN',@level2name=N'modified_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'アカウントのグループid' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_master', @level2type=N'COLUMN',@level2name=N'account_groups_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'端末グループのid' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_master', @level2type=N'COLUMN',@level2name=N'terminal_groups_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'USBのベンダーid' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_master', @level2type=N'COLUMN',@level2name=N'vendor_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'USBのベンダー名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_master', @level2type=N'COLUMN',@level2name=N'vendor_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'USBのプロダクトid' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_master', @level2type=N'COLUMN',@level2name=N'product_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'USBのプロダクト名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_master', @level2type=N'COLUMN',@level2name=N'product_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'USBのシリアル番号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_master', @level2type=N'COLUMN',@level2name=N'serialnumber'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'メモ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_usb_control_master', @level2type=N'COLUMN',@level2name=N'notes'
GO
