2025-08-04 19:25:21.9899 [INFO] Dispenser.Utils.Logger - アプリケーションが開始されました 
2025-08-04 19:25:22.4302 [INFO] Dispenser.Utils.Logger - EncryptionService が初期化されました（Crypto.cs使用） 
2025-08-04 19:25:22.4302 [INFO] Dispenser.Utils.Logger - 電子カルテシステムを読み込みました: HX,GX,MIRAIs 
2025-08-04 19:25:22.4302 [INFO] Dispenser.Utils.Logger - メインウィンドウが初期化されました 
2025-08-04 19:26:08.4189 [INFO] Dispenser.Utils.Logger - 接続文字列を自動生成しました: サーバー1=192.168.1.194, サーバー2=192.168.1.194 
2025-08-04 19:26:08.4686 [INFO] Dispenser.Utils.Logger - 暗号化機能の利用可能性: True 
2025-08-04 19:26:08.4686 [INFO] Dispenser.Utils.Logger - Crypto.EncryptStringを使用して暗号化を開始します 
2025-08-04 19:26:08.4686 [INFO] Dispenser.Utils.Logger - 文字列の暗号化が完了しました 
2025-08-04 19:26:08.4686 [INFO] Dispenser.Utils.Logger - 接続文字列の自動暗号化が完了しました 
2025-08-04 19:26:41.7665 [INFO] Dispenser.Utils.Logger - 入力チェック完了 
2025-08-04 19:26:42.5863 [INFO] Dispenser.Utils.Logger - 高度な文字列置き換え処理を開始します 
2025-08-04 19:26:42.5863 [INFO] Dispenser.Utils.Logger - 必須対象フォルダを追加: N:\work\VSCode\Dispenser\resource\Package\Taikoban 
2025-08-04 19:26:42.5863 [INFO] Dispenser.Utils.Logger - 条件付き対象フォルダを追加: N:\work\VSCode\Dispenser\resource\Package\Face (認証要素に「顔」が含まれているため) 
2025-08-04 19:26:42.5863 [INFO] Dispenser.Utils.Logger - 文字列置き換え処理を開始します 
2025-08-04 19:26:42.6306 [INFO] Dispenser.Utils.Logger - 対象フォルダの処理を開始: N:\work\VSCode\Dispenser\resource\Package\Taikoban 
2025-08-04 19:26:42.6697 [DEBUG] Dispenser.Utils.Logger - ディレクトリを作成: Output\20250804_永井検証サーバ\Taikoban 
2025-08-04 19:26:42.6697 [DEBUG] Dispenser.Utils.Logger - サブディレクトリを処理: N:\work\VSCode\Dispenser\resource\Package\Taikoban\Taikoban_Ver.******* → Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.******* 
2025-08-04 19:27:17.2841 [INFO] Dispenser.Utils.Logger - 見つかった対象ファイル数: 429 
2025-08-04 19:27:17.2942 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\Batch\ServerSetup.cmd 
2025-08-04 19:27:17.3281 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AccountImporter\eDoktor.Taikoban.AccountImporter.exe.config 
2025-08-04 19:27:17.4186 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\ServerAddress.txt 
2025-08-04 19:27:17.4340 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\ContextExtensions.txt 
2025-08-04 19:27:17.4340 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\SContextExtensions.txt 
2025-08-04 19:27:17.4925 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\API\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:27:17.4925 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AutoCompleteServer\eDoktor.AutoComplete.Database.dll.config 
2025-08-04 19:27:17.5220 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AutoCompleteServer\eDoktor.AutoComplete.Interprocess.dll.config 
2025-08-04 19:27:17.5303 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AutoCompleteServer\eDoktor.AutoComplete.ServerService.exe.config 
2025-08-04 19:27:17.5482 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\RemoteServer\eDoktor.Taikoban.RemoteInterprocess.dll.config 
2025-08-04 19:27:17.5544 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\RemoteServer\eDoktor.Taikoban.RemoteServer.dll.config 
2025-08-04 19:27:17.5754 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\ReportService\eDoktor.Taikoban.Report.dll.config 
2025-08-04 19:27:17.5916 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\Config.ps1 
2025-08-04 19:27:17.5916 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\ConfirmIsAlive.ps1 
2025-08-04 19:27:17.6061 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\ConfirmLicense.ps1 
2025-08-04 19:27:17.6061 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\DeleteAuthlog.ps1 
2025-08-04 19:27:17.6238 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\ExecAutoBackup.cmd 
2025-08-04 19:27:17.6418 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\GraphedTerminalUsage.ps1 
2025-08-04 19:27:17.6647 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\MonthlyLendingList.ps1 
2025-08-04 19:27:17.6647 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\MonthlyReleaseLicenseList.ps1 
2025-08-04 19:27:17.6921 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\AutomaticFailback\AutomaticFailback.bat 
2025-08-04 19:27:17.6921 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\UpdateValidPeriods\UpdateValidPeriods.exe.config 
2025-08-04 19:27:17.7146 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Auth.Database.dll.config 
2025-08-04 19:27:17.7146 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:27:17.7282 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Taikoban.ServerService.exe.config 
2025-08-04 19:27:17.7282 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\SmartCardBulkRegister\ServerAddress.txt 
2025-08-04 19:27:17.7556 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\WitnessService\eDoktor.Taikoban.Report.dll.config 
2025-08-04 19:27:17.7721 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\AdminConsole\1_1\ContextExtensions.txt 
2025-08-04 19:27:17.7889 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\AdminConsole\1_1\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:27:17.7889 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\AdminConsole\1_1\eDoktor.Taikoban.AdminConsole.exe.config 
2025-08-04 19:27:17.7889 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\AdminConsole\1_1\eDoktor.Taikoban.RemoteInterprocess.dll.config 
2025-08-04 19:27:17.8380 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\AdminConsole\Starter\ServerAddress.txt 
2025-08-04 19:27:17.8580 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\ClientInstaller\Taikoban\AdminConsole\ServerAddress.txt 
2025-08-04 19:27:17.8745 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\ClientInstaller\Taikoban\SmartCardBulkRegister\ServerAddress.txt 
2025-08-04 19:27:17.8880 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\ClientInstaller\Taikoban\SmartCardRegister\ServerAddress.txt 
2025-08-04 19:27:17.8993 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\ClientInstaller\Taikoban\TemporaryAllowOneFactorAuthentication\ServerAddress.txt 
2025-08-04 19:27:17.8993 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\ClientInstaller\Taikoban\TemporaryCardIssuer\ServerAddress.txt 
2025-08-04 19:27:17.9212 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\ClientInstallerStarter\ServerAddress.txt 
2025-08-04 19:27:17.9339 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\RemoteAgent\eDoktor.Taikoban.RemoteSetup.exe.config 
2025-08-04 19:27:17.9339 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\RemoteAgent\101\eDoktor.Taikoban.Interprocess.dll.config 
2025-08-04 19:27:17.9544 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\RemoteAgent\101\eDoktor.Taikoban.RemoteInterprocess.dll.config 
2025-08-04 19:27:17.9544 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\RemoteAgent\101\eDoktor.Taikoban.RemoteSetup.exe.config 
2025-08-04 19:27:17.9921 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\InstallPath.txt 
2025-08-04 19:27:17.9921 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ServerAddress.txt 
2025-08-04 19:27:18.0431 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ApplicationManage\ApplicationManage.exe.config 
2025-08-04 19:27:18.0599 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ApplicationManage\AutomationSpy.exe.config 
2025-08-04 19:27:18.0728 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ApplicationManage\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:27:18.0728 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ApplicationManage\eDoktor.Auth.Setup.dll.config 
2025-08-04 19:27:18.0863 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ApplicationManage\eDoktor.Taikoban.AdminConsole.exe.config 
2025-08-04 19:27:18.1027 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\AutoCompleteAgent\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:27:18.1027 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\AutoCompleteAgent\eDoktor.AutoComplete.Agent.exe.config 
2025-08-04 19:27:18.1027 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\AutoCompleteAgent\eDoktor.AutoComplete.Interprocess.dll.config 
2025-08-04 19:27:18.1275 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\CallAuthScreen\CallAuthenticationScreenF.exe.config 
2025-08-04 19:27:18.1407 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\CallAuthScreen\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:27:18.1593 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ClientExtensionTool\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:27:18.1664 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\CloseApplication\CloseApplication.exe.config 
2025-08-04 19:27:18.1664 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\HXAgency\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:27:18.1801 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\HXAgency\HXAgency.exe.config 
2025-08-04 19:27:18.1801 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\HXScreenSaverMonitor\HXScreenSaverMonitor.exe.config 
2025-08-04 19:27:18.2091 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\SmartCardBulkRegister\1\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:27:18.2091 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\SmartCardBulkRegister\1\eDoktor.Taikoban.SmartCardBulkRegister.exe.config 
2025-08-04 19:27:18.2325 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\SmartCardBulkRegister\Starter\ServerAddress.txt 
2025-08-04 19:27:18.2459 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\SmartCardRegister\1\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:27:18.2583 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\SmartCardRegister\1\eDoktor.Taikoban.SmartCardRegister.exe.config 
2025-08-04 19:27:18.2583 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\SmartCardRegister\Starter\ServerAddress.txt 
2025-08-04 19:27:18.2767 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\eDoktor.Auth.Setup.dll.config 
2025-08-04 19:27:18.3079 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\101\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:27:18.3079 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\101\eDoktor.Auth.Setup.dll.config 
2025-08-04 19:27:18.3242 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\101\eDoktor.ContentDisplay.API.exe.config 
2025-08-04 19:27:18.3406 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\101\eDoktor.VeinAuthClient.API.exe.config 
2025-08-04 19:27:18.3406 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\101\InstallPath.txt 
2025-08-04 19:27:18.3838 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\101\DisplayAPI\LockDetailMessage\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:27:18.4062 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\TemporaryAllowOneFactorAuthentication\1\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:27:18.4258 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\TemporaryAllowOneFactorAuthentication\1\eDoktor.Taikoban.TemporaryAllowOneFactorAuthentication.exe.config 
2025-08-04 19:27:18.4417 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\TemporaryAllowOneFactorAuthentication\Starter\ServerAddress.txt 
2025-08-04 19:27:18.4613 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\TemporaryCardIssuer\1\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:27:18.4757 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\TemporaryCardIssuer\1\eDoktor.Taikoban.TemporaryCardIssuer.exe.config 
2025-08-04 19:27:18.4757 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\TemporaryCardIssuer\Starter\ServerAddress.txt 
2025-08-04 19:27:18.4935 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\02.クライアント\ServerAddress.txt 
2025-08-04 19:27:18.7432 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\99.ドキュメント\【Taikoban】設定メモ.txt 
2025-08-04 19:27:18.7432 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\ModuleBackup\BackupModule.cmd 
2025-08-04 19:27:18.7432 [INFO] Dispenser.Utils.Logger - 対象フォルダの処理が完了: N:\work\VSCode\Dispenser\resource\Package\Taikoban → Output\20250804_永井検証サーバ\Taikoban 
2025-08-04 19:27:18.7432 [INFO] Dispenser.Utils.Logger - 対象フォルダの処理を開始: N:\work\VSCode\Dispenser\resource\Package\Face 
2025-08-04 19:27:18.7749 [DEBUG] Dispenser.Utils.Logger - ディレクトリを作成: Output\20250804_永井検証サーバ\Face 
2025-08-04 19:27:18.7749 [DEBUG] Dispenser.Utils.Logger - サブディレクトリを処理: N:\work\VSCode\Dispenser\resource\Package\Face\FaceStamp_ver2.1.0.0 → Output\20250804_永井検証サーバ\Face\FaceStamp_ver2.1.0.0 
2025-08-04 19:27:38.9259 [INFO] Dispenser.Utils.Logger - 見つかった対象ファイル数: 103 
2025-08-04 19:27:45.3725 [INFO] Dispenser.Utils.Logger - 対象フォルダの処理が完了: N:\work\VSCode\Dispenser\resource\Package\Face → Output\20250804_永井検証サーバ\Face 
2025-08-04 19:27:45.3725 [INFO] Dispenser.Utils.Logger - 文字列置き換え処理が完了しました。処理時間: 62.77秒 
2025-08-04 19:27:45.3725 [INFO] Dispenser.Utils.Logger - 処理結果サマリーをログファイルに出力しました 
2025-08-04 19:27:45.3725 [INFO] Dispenser.Utils.Logger - 高度な文字列置き換え処理が完了しました。出力先: Output\20250804_永井検証サーバ 
2025-08-04 19:27:45.3725 [INFO] Dispenser.Utils.Logger - 処理結果: ReplacementResult: Success, Files: 532 (81 processed, 451 skipped), Time: 62.77s 
2025-08-04 19:27:45.3825 [INFO] Dispenser.Utils.Logger - パッケージ作成処理を開始します 
2025-08-04 19:27:45.4478 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: 置換結果_20250804_192642.log 
2025-08-04 19:27:45.4478 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/LicenseFile/仮ライセンスなので本番用ライセンスを入手し次第入れ替えてください.txt 
2025-08-04 19:27:45.4719 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Common.dll 
2025-08-04 19:27:45.4810 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Install.dll 
2025-08-04 19:27:45.4810 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthCommon.dll 
2025-08-04 19:27:45.4810 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthCommon.pdb 
2025-08-04 19:27:45.4919 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthSDK.dll 
2025-08-04 19:27:45.4919 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthSDK.dll.config 
2025-08-04 19:27:45.4919 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthSDK.pdb 
2025-08-04 19:27:45.4919 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthServer.dll 
2025-08-04 19:27:45.4919 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthServer.dll.config 
2025-08-04 19:27:45.5124 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthServer.pdb 
2025-08-04 19:27:45.5124 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthServerConsole.exe 
2025-08-04 19:27:45.5124 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthServerConsole.exe.config 
2025-08-04 19:27:45.5124 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthServerConsole.pdb 
2025-08-04 19:27:45.5238 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthServerService.exe 
2025-08-04 19:27:45.5238 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthServerService.exe.config 
2025-08-04 19:27:45.5238 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthServerService.InstallLog 
2025-08-04 19:27:45.5238 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthServerService.pdb 
2025-08-04 19:27:45.5238 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthTemplateUpdateJudge.dll 
2025-08-04 19:27:45.5238 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthTemplateUpdateJudge.pdb 
2025-08-04 19:27:45.5238 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceCrypt.dll 
2025-08-04 19:27:45.5398 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceCrypt.pdb 
2025-08-04 19:27:45.5398 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceInterprocess.dll 
2025-08-04 19:27:45.5398 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceInterprocess.dll.config 
2025-08-04 19:27:45.5548 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceInterprocess.pdb 
2025-08-04 19:27:45.5548 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor_Taikoban_face_auth_server_service_installer.bat 
2025-08-04 19:27:45.5548 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor_Taikoban_face_auth_server_service_uninstaller.bat 
2025-08-04 19:27:45.5548 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GFRL.dll 
2025-08-04 19:27:45.5708 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/Glory.SentinelKeyLibrary.dll 
2025-08-04 19:27:45.5919 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFaceCLI.dll 
2025-08-04 19:27:45.5919 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRLog.dll 
2025-08-04 19:27:45.5919 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/hasp_net_windows.dll 
2025-08-04 19:27:45.7197 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/glic.dll 
2025-08-04 19:27:46.2712 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/GRFace_ef10.dll 
2025-08-04 19:27:46.3875 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/GRFace_et50.dll 
2025-08-04 19:27:46.4742 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/GRFace_fs50.dll 
2025-08-04 19:27:46.5945 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/GRFace_icr50.dll 
2025-08-04 19:27:56.0891 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/GRFace_lbc10.dll 
2025-08-04 19:28:15.2862 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/GRFace_mr50.dll 
2025-08-04 19:28:15.4269 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/GRFace_vn50.dll 
2025-08-04 19:28:17.1983 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/guic.dll 
2025-08-04 19:28:17.2264 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/haspvlib_112398.dll 
2025-08-04 19:28:17.3650 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/hasp_rt.exe 
2025-08-04 19:28:17.4507 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/inference_engine.dll 
2025-08-04 19:28:17.5531 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/inference_engine_legacy.dll 
2025-08-04 19:28:17.5905 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/inference_engine_lp_transformations.dll 
2025-08-04 19:28:17.6450 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/inference_engine_preproc.dll 
2025-08-04 19:28:17.6899 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/inference_engine_transformations.dll 
2025-08-04 19:28:17.7525 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/libiomp5md.dll 
2025-08-04 19:28:17.9706 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/libmmd.dll 
2025-08-04 19:28:18.8832 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/MKLDNNPlugin.dll 
2025-08-04 19:28:19.1279 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/ngraph.dll 
2025-08-04 19:28:19.7607 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/svml_dispmd.dll 
2025-08-04 19:28:19.7817 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/tbb.dll 
2025-08-04 19:28:19.7817 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/ja/GFRL.resources.dll 
2025-08-04 19:28:19.7817 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceInsert/eDoktor.Taikoban.FaceInsert.cmd 
2025-08-04 19:28:19.7817 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceInsert/eDoktor.Taikoban.FaceInsert.vbe 
2025-08-04 19:28:19.7889 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceInsert/ServerAddress.txt 
2025-08-04 19:28:19.7889 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceInsert/version.txt 
2025-08-04 19:28:19.7889 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/ClientInstaller/TaikobanClinetSetupExec.cmd 
2025-08-04 19:28:20.6737 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/ClientInstaller/Runtime/VC_redist.x64.exe 
2025-08-04 19:28:20.6737 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/FaceInsert/eDoktor.Taikoban.FaceInsert.cmd 
2025-08-04 19:28:20.6787 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/FaceInsert/eDoktor.Taikoban.FaceInsert.vbe 
2025-08-04 19:28:20.6787 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/FaceInsert/ServerAddress.txt 
2025-08-04 19:28:20.6787 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/FaceInsert/version.txt 
2025-08-04 19:28:20.6787 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/version.txt 
2025-08-04 19:28:20.6787 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/AForge.Video.DirectShow.dll 
2025-08-04 19:28:20.6947 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/AForge.Video.DirectShow.xml 
2025-08-04 19:28:20.6947 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/AForge.Video.dll 
2025-08-04 19:28:20.6947 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/AForge.Video.xml 
2025-08-04 19:28:20.7308 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Common.dll 
2025-08-04 19:28:20.7308 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.AdminConsoleExtension.dll 
2025-08-04 19:28:20.7308 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.AdminConsoleExtension.dll.config 
2025-08-04 19:28:20.7308 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.AppCommon.dll 
2025-08-04 19:28:20.7308 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.AppCommon.dll.config 
2025-08-04 19:28:20.7435 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.AppExtension.dll 
2025-08-04 19:28:20.7435 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.AppExtension.dll.config 
2025-08-04 19:28:20.7435 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.ClientExtension.dll 
2025-08-04 19:28:20.7435 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.ClientExtension.dll.config 
2025-08-04 19:28:20.7584 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceAuthCommon.dll 
2025-08-04 19:28:20.7584 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceAuthCommon.pdb 
2025-08-04 19:28:20.7584 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceCrypt.dll 
2025-08-04 19:28:20.7584 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceCrypt.pdb 
2025-08-04 19:28:20.7584 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceDetection.dll 
2025-08-04 19:28:20.7762 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceDetection.pdb 
2025-08-04 19:28:20.7762 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceImageCntl.dll 
2025-08-04 19:28:20.7762 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceImageCntl.pdb 
2025-08-04 19:28:20.7892 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceInsert.exe 
2025-08-04 19:28:20.7892 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceInsert.exe.config 
2025-08-04 19:28:20.7892 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceInsert.pdb 
2025-08-04 19:28:20.8056 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceInterprocess.dll 
2025-08-04 19:28:20.8056 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceInterprocess.dll.config 
2025-08-04 19:28:20.8056 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceInterprocess.pdb 
2025-08-04 19:28:20.8266 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.Interprocess.dll 
2025-08-04 19:28:20.8266 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.Interprocess.dll.config 
2025-08-04 19:28:20.8266 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.RSACrypto.dll 
2025-08-04 19:28:20.8266 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktorTaikoban.FaceClientCommon.dll 
2025-08-04 19:28:20.8358 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktorTaikoban.FaceClientCommon.pdb 
2025-08-04 19:28:20.8358 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/GFRL.dll 
2025-08-04 19:28:20.8358 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/Glory.SentinelKeyLibrary.dll 
2025-08-04 19:28:20.8656 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/GRFaceCLI.dll 
2025-08-04 19:28:20.8686 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/GRLog.dll 
2025-08-04 19:28:20.8866 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/haarcascade_eye_tree_eyeglasses.xml 
2025-08-04 19:28:20.9126 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/haarcascade_frontalface_default.xml 
2025-08-04 19:28:20.9186 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/hasp_net_windows.dll 
2025-08-04 19:28:20.9186 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/LivenessCheck.dll 
2025-08-04 19:28:20.9296 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/LivenessCheck.hpp 
2025-08-04 19:28:20.9296 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/LivenessCheck.lib 
2025-08-04 19:28:20.9491 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/LivenessCheckSharp.dll 
2025-08-04 19:28:21.0666 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/LivenessCheckSharp.pdb 
2025-08-04 19:28:21.1096 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/OpenCvSharp.dll 
2025-08-04 19:28:21.1096 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/OpenCvSharp.Extensions.dll 
2025-08-04 19:28:21.1096 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/OpenCvSharp.Extensions.xml 
2025-08-04 19:28:21.1952 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/OpenCvSharp.xml 
2025-08-04 19:28:22.1561 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/opencv_videoio_ffmpeg455_64.dll 
2025-08-04 19:28:25.7099 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/opencv_world455.dll 
2025-08-04 19:28:25.8265 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/opencv_world455.lib 
2025-08-04 19:28:25.8265 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.Buffers.dll 
2025-08-04 19:28:25.8265 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.Buffers.xml 
2025-08-04 19:28:25.8265 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.Drawing.Common.dll 
2025-08-04 19:28:25.8430 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.Memory.dll 
2025-08-04 19:28:25.8430 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.Memory.xml 
2025-08-04 19:28:25.8511 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.Numerics.Vectors.dll 
2025-08-04 19:28:25.8511 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.Numerics.Vectors.xml 
2025-08-04 19:28:25.8511 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.Runtime.CompilerServices.Unsafe.dll 
2025-08-04 19:28:25.8511 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.Runtime.CompilerServices.Unsafe.xml 
2025-08-04 19:28:25.8511 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.ValueTuple.dll 
2025-08-04 19:28:25.8511 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.ValueTuple.xml 
2025-08-04 19:28:25.8666 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/ja/GFRL.resources.dll 
2025-08-04 19:28:25.8666 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/deploy.prototxt 
2025-08-04 19:28:25.9772 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/eyeDetect.onnx 
2025-08-04 19:28:26.2679 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/eyeDir.onnx 
2025-08-04 19:28:26.5438 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/eyeStatus.onnx 
2025-08-04 19:28:26.7988 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/faceDirH.onnx 
2025-08-04 19:28:27.0947 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/faceDirV.onnx 
2025-08-04 19:28:27.2748 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/glasses.onnx 
2025-08-04 19:28:27.7534 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/livenessH.onnx 
2025-08-04 19:28:29.2918 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/livenessV.onnx 
2025-08-04 19:28:29.4130 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/mask.onnx 
2025-08-04 19:28:29.5835 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/res10_300x300_ssd_iter_140000_fp16.caffemodel 
2025-08-04 19:28:29.5855 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/token/token.txt 
2025-08-04 19:28:29.5855 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/token/tokenメモ.txt 
2025-08-04 19:28:29.5855 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.Controls.dll 
2025-08-04 19:28:29.5855 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.Controls.xml 
2025-08-04 19:28:29.5855 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.dll 
2025-08-04 19:28:29.6126 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.Imaging.dll 
2025-08-04 19:28:29.6345 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.Imaging.xml 
2025-08-04 19:28:29.6345 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.Math.dll 
2025-08-04 19:28:29.6345 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.Math.xml 
2025-08-04 19:28:29.6528 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.Video.DirectShow.dll 
2025-08-04 19:28:29.6528 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.Video.DirectShow.xml 
2025-08-04 19:28:29.6528 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.Video.dll 
2025-08-04 19:28:29.6645 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.Video.xml 
2025-08-04 19:28:29.6645 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.xml 
2025-08-04 19:28:29.6867 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Common.dll 
2025-08-04 19:28:29.6867 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.BioAuthMessage.dll 
2025-08-04 19:28:29.6867 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceAuthCommon.dll 
2025-08-04 19:28:29.6963 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceAuthCommon.pdb 
2025-08-04 19:28:29.7190 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceClient.exe 
2025-08-04 19:28:29.7190 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceClient.exe.config 
2025-08-04 19:28:29.7306 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceClient.pdb 
2025-08-04 19:28:29.7306 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceCrypt.dll 
2025-08-04 19:28:29.7603 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceCrypt.pdb 
2025-08-04 19:28:29.7603 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceDetection.dll 
2025-08-04 19:28:29.7603 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceDetection.pdb 
2025-08-04 19:28:29.7733 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceImageCntl.dll 
2025-08-04 19:28:29.7733 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceImageCntl.pdb 
2025-08-04 19:28:29.7733 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceInterprocess.dll 
2025-08-04 19:28:29.7733 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceInterprocess.dll.config 
2025-08-04 19:28:29.7903 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceInterprocess.pdb 
2025-08-04 19:28:29.7903 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktorTaikoban.FaceClientCommon.dll 
2025-08-04 19:28:29.7903 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktorTaikoban.FaceClientCommon.pdb 
2025-08-04 19:28:29.8043 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/GFRL.dll 
2025-08-04 19:28:29.8043 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/Glory.SentinelKeyLibrary.dll 
2025-08-04 19:28:29.8263 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/GRFaceCLI.dll 
2025-08-04 19:28:29.8263 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/GRLog.dll 
2025-08-04 19:28:29.8489 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/haarcascade_eye_tree_eyeglasses.xml 
2025-08-04 19:28:29.8739 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/haarcascade_frontalface_default.xml 
2025-08-04 19:28:29.8739 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/hasp_net_windows.dll 
2025-08-04 19:28:29.8879 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/LivenessCheck.dll 
2025-08-04 19:28:29.9059 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/LivenessCheckSharp.dll 
2025-08-04 19:28:30.0304 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/LivenessCheckSharp.pdb 
2025-08-04 19:28:30.0784 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/OpenCvSharp.dll 
2025-08-04 19:28:30.0784 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/OpenCvSharp.Extensions.dll 
2025-08-04 19:28:30.0784 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/OpenCvSharp.Extensions.xml 
2025-08-04 19:28:30.1194 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/OpenCvSharp.xml 
2025-08-04 19:28:31.1560 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/opencv_videoio_ffmpeg455_64.dll 
2025-08-04 19:28:34.5981 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/opencv_world455.dll 
2025-08-04 19:28:34.6011 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/policy.PNG 
2025-08-04 19:28:34.6011 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.Buffers.dll 
2025-08-04 19:28:34.6011 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.Buffers.xml 
2025-08-04 19:28:34.6011 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.Drawing.Common.dll 
2025-08-04 19:28:34.6166 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.Memory.dll 
2025-08-04 19:28:34.6166 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.Memory.xml 
2025-08-04 19:28:34.6166 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.Numerics.Vectors.dll 
2025-08-04 19:28:34.6166 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.Numerics.Vectors.xml 
2025-08-04 19:28:34.6166 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.Runtime.CompilerServices.Unsafe.dll 
2025-08-04 19:28:34.6326 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.Runtime.CompilerServices.Unsafe.xml 
2025-08-04 19:28:34.6326 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.ValueTuple.dll 
2025-08-04 19:28:34.6326 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.ValueTuple.xml 
2025-08-04 19:28:37.2134 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/dll/x64/OpenCvSharpExtern.dll 
2025-08-04 19:28:38.2884 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/dll/x64/opencv_videoio_ffmpeg452_64.dll 
2025-08-04 19:28:40.1518 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/dll/x86/OpenCvSharpExtern.dll 
2025-08-04 19:28:41.3304 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/dll/x86/opencv_videoio_ffmpeg452.dll 
2025-08-04 19:28:41.3304 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/ja/GFRL.resources.dll 
2025-08-04 19:28:41.3354 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/deploy.prototxt 
2025-08-04 19:28:41.4450 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/eyeDetect.onnx 
2025-08-04 19:28:41.8366 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/eyeDir.onnx 
2025-08-04 19:28:42.2357 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/eyeStatus.onnx 
2025-08-04 19:28:42.4204 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/faceDirH.onnx 
2025-08-04 19:28:43.4914 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/faceDirV.onnx 
2025-08-04 19:28:43.6713 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/glasses.onnx 
2025-08-04 19:28:44.2273 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/livenessH.onnx 
2025-08-04 19:28:44.7169 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/livenessV.onnx 
2025-08-04 19:28:44.8470 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/mask.onnx 
2025-08-04 19:28:45.0253 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/res10_300x300_ssd_iter_140000_fp16.caffemodel 
2025-08-04 19:28:45.0253 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/token/token.txt 
2025-08-04 19:28:45.0253 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/token/開発用ライセンスなので本番用ライセンスを入手し次第入れ替えてください.txt 
2025-08-04 19:28:45.0253 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/00.DBName/DBName.txt 
2025-08-04 19:28:45.0253 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/00.DBName/localhost.txt 
2025-08-04 19:28:45.0253 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/02.テーブル作成/create_t_face_auth_liveness_settings.sql 
2025-08-04 19:28:45.0253 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/02.テーブル作成/create_t_face_auth_logs.sql 
2025-08-04 19:28:45.0253 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/02.テーブル作成/create_t_face_auth_messages.sql 
2025-08-04 19:28:45.0382 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/02.テーブル作成/create_t_face_auth_not_use_devices.sql 
2025-08-04 19:28:45.0382 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/02.テーブル作成/create_t_face_auth_settings.sql 
2025-08-04 19:28:45.0382 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/02.テーブル作成/create_t_face_auth_templates.sql 
2025-08-04 19:28:45.0382 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/02.テーブル作成/create_t_face_auth_use_devices.sql 
2025-08-04 19:28:45.0382 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/02.テーブル作成/make_table_win.bat 
2025-08-04 19:28:45.0382 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/03.トリガー作成/create_TR_update_t_face_auth_messages.sql 
2025-08-04 19:28:45.0382 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/03.トリガー作成/create_TR_update_t_face_auth_not_use_devices.sql 
2025-08-04 19:28:45.0382 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/03.トリガー作成/create_TR_update_t_face_auth_settings.sql 
2025-08-04 19:28:45.0382 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/03.トリガー作成/create_TR_update_t_face_auth_templates.sql 
2025-08-04 19:28:45.0382 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/03.トリガー作成/create_TR_update_t_face_auth_use_devices.sql 
2025-08-04 19:28:45.0382 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/03.トリガー作成/make_trigger.bat 
2025-08-04 19:28:45.0542 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/04.データ作成/insert_t_face_auth_liveness_settings.sql 
2025-08-04 19:28:45.0542 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/04.データ作成/insert_t_face_auth_messages.sql 
2025-08-04 19:28:45.0542 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/04.データ作成/insert_t_face_auth_settings.sql 
2025-08-04 19:28:45.0542 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/04.データ作成/make_data.bat 
2025-08-04 19:28:47.1908 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/05.Tools/【Taikoban】顔認証ライセンス依頼ファイル作成ツール.zip 
2025-08-04 19:28:47.1998 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/05.Tools/【Taikoban】顔認証ライセンス依頼ファイル作成手順.pdf 
2025-08-04 19:28:47.1998 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/05.Tools/【Taikoban】顔認証ライセンス依頼ファイル作成手順.xlsx 
2025-08-04 19:28:47.1998 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/リリースノート.txt 
2025-08-04 19:29:11.3353 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/00.ミドルウェア/SQL Server/SQLEXPRADV_x64_JPN.exe 
2025-08-04 19:29:30.9324 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\00.ミドルウェア\SQL Server\SSMS-Setup-JPN.exe - ディスクに十分な空き領域がありません。
 
2025-08-04 19:29:30.9575 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\Batch\ServerSetup.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:30.9664 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\Batch\ServerSetup_1号機用.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:30.9774 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\Batch\ServerSetup_2号機用.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:30.9774 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\Batch\ServerSetup_3号機用.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:30.9987 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\Batch\ServerSetup_Firewall.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.0088 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\Batch\ServerSetup_シングルサーバー用.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.0088 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AccountImporter\eDoktor.Common.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.0313 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AccountImporter\eDoktor.Taikoban.AccountImporter.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.0413 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AccountImporter\eDoktor.Taikoban.AccountImporter.exe.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.0413 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AccountImporter\ImportAccounts.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.0605 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AccountImporter\Jobs\ImportJobs.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.0706 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AccountImporter\Jobs\ImportJobs.sql - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.0706 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AccountImporterLocal\eDoktor.Common.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.0899 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AccountImporterLocal\eDoktor.Taikoban.AccountImporter.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.0899 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AccountImporterLocal\eDoktor.Taikoban.AccountImporter.exe.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.1080 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AccountImporterLocal\ImportAccounts.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.1175 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AccountImporterLocal\Jobs\ImportJobs.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.1175 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AccountImporterLocal\Jobs\ImportJobs.sql - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.1391 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\eDoktor.Taikoban.AdminConsole.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.1490 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\eDoktor.Taikoban.AdminConsole.vbe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.1490 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\ServerAddress.txt - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.1672 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\version.txt - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.1672 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\Capture\Thumbs.db - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.1922 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\ConnectInstallFolder.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.2022 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\ContextExtensions.txt - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.2122 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\SContextExtensions.txt - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.2122 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\ExecRemoteCommand\eDoktor.Common.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.2327 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\ExecRemoteCommand\eDoktor.Taikoban.RSACrypto.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.2426 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\ExecRemoteCommand\ExecRemoteCommand.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.2426 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\ExecRemoteCommand\ExecRemoteCommand.ps1 - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.2622 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\ExecRemoteCommand\terminals.txt - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.2732 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\ExecRemoteTool\eDoktor.Common.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.2732 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\ExecRemoteTool\eDoktor.Taikoban.RSACrypto.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.2947 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\ExecRemoteTool\ExecRemoteTool.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.3047 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\ExecRemoteTool\ExecRemoteTool.ps1 - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.3047 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\PSTools\Eula.txt - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.3238 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\PSTools\PsExec.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.3238 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\PSTools\psfile.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.3429 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\PSTools\PsGetsid.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.3526 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\PSTools\PsInfo.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.3526 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\PSTools\pskill.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.3726 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\PSTools\pslist.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.3838 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\PSTools\PsLoggedon.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.3838 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\PSTools\psloglist.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.4062 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\PSTools\pspasswd.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.4197 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\PSTools\psping.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.4307 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\PSTools\PsService.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.4307 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\PSTools\psshutdown.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.4527 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\PSTools\pssuspend.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.4627 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\PSTools\Pstools.chm - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.4627 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\PSTools\PSTools.zip - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.4827 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\PSTools\psversion.txt - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.4967 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\API\api_exe_installer.bat - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.5127 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\API\api_exe_uninstaller.bat - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.5290 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\API\eDoktor.Auth.Interprocess.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.5440 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\API\eDoktor.Auth.Interprocess.dll.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.5600 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\API\eDoktor.Common.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.5770 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\API\eDoktor.LocalServer.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.5970 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\API\TaikobanAPI.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.6130 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\API\TaikobanAPI.exe.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.6305 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\API\TaikobanAPI.tlb - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.6505 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AutoCompleteServer\eDoktor.AutoComplete.Database.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.6700 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AutoCompleteServer\eDoktor.AutoComplete.Database.dll.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.6868 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AutoCompleteServer\eDoktor.AutoComplete.Interprocess.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.6999 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AutoCompleteServer\eDoktor.AutoComplete.Interprocess.dll.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.7119 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AutoCompleteServer\eDoktor.AutoComplete.Server.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.7119 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AutoCompleteServer\eDoktor.AutoComplete.Server.dll.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.7321 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AutoCompleteServer\eDoktor.AutoComplete.ServerService.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.7431 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AutoCompleteServer\eDoktor.AutoComplete.ServerService.exe.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.7431 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AutoCompleteServer\eDoktor.Common.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.7620 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AutoCompleteServer\eDoktor.Install.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.7620 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AutoCompleteServer\server_service_installer.bat - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.7801 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AutoCompleteServer\server_service_uninstaller.bat - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.7961 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\ExBSecurity\ExBSecurity.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.8101 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\ExBSecurity\ex_com_installer.bat - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.8215 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\ExBSecurity\ex_com_uninstaller.bat - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.8215 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\ExBSecurityTest\ExBSecurityTest.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.8416 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\ExBSecurityTest\ExBSecurityTest.exe.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.8518 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\RemoteServer\eDoktor.Common.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.8518 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\RemoteServer\eDoktor.Install.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.8712 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\RemoteServer\eDoktor.Taikoban.RemoteInterprocess.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.8712 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\RemoteServer\eDoktor.Taikoban.RemoteInterprocess.dll.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.8902 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\RemoteServer\eDoktor.Taikoban.RemoteServer.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.9006 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\RemoteServer\eDoktor.Taikoban.RemoteServer.dll.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.9006 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\RemoteServer\eDoktor.Taikoban.RemoteServerService.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.9207 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\RemoteServer\eDoktor.Taikoban.RemoteServerService.exe.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.9335 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\RemoteServer\eDoktor.Taikoban.RSACrypto.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.9335 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\RemoteServer\remoteserver_installer.bat - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.9532 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\RemoteServer\remoteserver_uninstaller.bat - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.9696 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\ReportService\eDoktor.Common.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:31.9846 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\ReportService\eDoktor.Install.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.0015 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\ReportService\eDoktor.Taikoban.AppCommon.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.0228 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\ReportService\eDoktor.Taikoban.AppCommon.dll.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.0388 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\ReportService\eDoktor.Taikoban.Report.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.0559 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\ReportService\eDoktor.Taikoban.Report.dll.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.0707 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\ReportService\eDoktor.Taikoban.ReportService.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.0871 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\ReportService\eDoktor.Taikoban.ReportService.exe.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.1041 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\ReportService\TaikobanReportService_installer.bat - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.1197 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\ReportService\TaikobanReportService_uninstaller.bat - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.1357 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\Common.ps1 - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.1507 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\Config.ps1 - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.1643 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\ConfirmIsAlive.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.1643 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\ConfirmIsAlive.ps1 - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.1865 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\ConfirmLicense.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.1985 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\ConfirmLicense.ps1 - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.1985 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\DeleteAuthlog.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.2187 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\DeleteAuthlog.ps1 - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.2321 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\eDoktor.Common.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.2430 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\eDoktor.Taikoban.RSACrypto.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.2430 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\ExecAutoBackup.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.2635 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\ExecAutoBackup.sql - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.2734 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\ExecShrinkLog.sql - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.2734 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\GraphedTerminalUsage.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.2924 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\GraphedTerminalUsage.ps1 - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.2924 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\GraphedTerminalUsage.xlsm - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.3130 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\MonthlyLendingList.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.3238 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\MonthlyLendingList.ps1 - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.3238 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\MonthlyReleaseLicenseList.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.3419 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\MonthlyReleaseLicenseList.ps1 - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.3509 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\ShutdownExec.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.3509 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\TerminalUsage.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.3731 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\TerminalUsage.ps1 - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.3822 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\リリースノート.txt - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.3822 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\AutomaticFailback\AutomaticFailback.bat - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.4037 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\AutomaticFailback\Failback.sql - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.4141 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\UpdateValidPeriods\eDoktor.Common.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.4141 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\UpdateValidPeriods\UpdateValidPeriods.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.4339 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\UpdateValidPeriods\UpdateValidPeriods.exe.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.4489 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Auth.AuthenticationSchemes.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.4489 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Auth.AuthenticationSchemes.dll.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.4703 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Auth.Database.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.4815 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Auth.Database.dll.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.4925 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Auth.ExternalDependencies.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.4925 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Auth.ExternalDependencies.dll.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.5209 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Auth.Interprocess.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.5349 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Auth.Interprocess.dll.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.5519 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Auth.Licensing.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.5689 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Auth.Licensing.dll.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.5869 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Auth.Server.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.6039 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Auth.Server.dll.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.6194 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Common.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.6394 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Install.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.6564 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Licensing.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.6724 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Taikoban.LicenseActivator.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.6891 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Taikoban.ServerService.exe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.7051 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Taikoban.ServerService.exe.config - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.7227 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\server_service_installer.bat - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.7346 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\server_service_uninstaller.bat - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.7482 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\SmartCardBulkRegister\eDoktor.Taikoban.SmartCardBulkRegister.cmd - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.7573 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\SmartCardBulkRegister\eDoktor.Taikoban.SmartCardBulkRegister.vbe - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.7573 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\SmartCardBulkRegister\ServerAddress.txt - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.7792 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\SmartCardBulkRegister\version.txt - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.7899 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\WitnessService\eDoktor.Common.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.7899 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\WitnessService\eDoktor.Install.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:32.8132 [WARN] Dispenser.Utils.Logger - ファイルの追加に失敗しました: Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\WitnessService\eDoktor.Taikoban.AppCommon.dll - 以前に作成されたエントリが開いているときはエントリを作成できません。 
2025-08-04 19:29:39.1545 [ERROR] Dispenser.Utils.Logger - ZIP作成中にエラーが発生しました: ディスクに十分な空き領域がありません。
 
2025-08-04 19:34:38.2204 [ERROR] Dispenser.Utils.Logger - パッケージ作成処理中にエラーが発生しました: ディスクに十分な空き領域がありません。
 System.IO.IOException: ディスクに十分な空き領域がありません。

   場所 System.IO.__Error.WinIOError(Int32 errorCode, String maybeFullPath)
   場所 System.IO.FileStream.WriteCore(Byte[] buffer, Int32 offset, Int32 count)
   場所 System.IO.FileStream.Write(Byte[] array, Int32 offset, Int32 count)
   場所 System.IO.BinaryWriter.Write(UInt16 value)
   場所 System.IO.Compression.ZipArchiveEntry.WriteCentralDirectoryFileHeader()
   場所 System.IO.Compression.ZipArchive.WriteFile()
   場所 System.IO.Compression.ZipArchive.Dispose(Boolean disposing)
   場所 System.IO.Compression.ZipArchive.Dispose()
   場所 Dispenser.Services.PackagingService.CreateZipFile(String sourceDirectory, String zipFilePath) 場所 N:\work\VSCode\Dispenser\Services\PackagingService.cs:行 77
   場所 Dispenser.Services.PackagingService.<>c__DisplayClass1_0.<CreatePackageAsync>b__0() 場所 N:\work\VSCode\Dispenser\Services\PackagingService.cs:行 45
   場所 System.Threading.Tasks.Task.InnerInvoke()
   場所 System.Threading.Tasks.Task.Execute()
--- 直前に例外がスローされた場所からのスタック トレースの終わり ---
   場所 System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess(Task task)
   場所 System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNotification(Task task)
   場所 System.Runtime.CompilerServices.TaskAwaiter.GetResult()
   場所 Dispenser.Services.PackagingService.<CreatePackageAsync>d__1.MoveNext() 場所 N:\work\VSCode\Dispenser\Services\PackagingService.cs:行 45
2025-08-04 19:34:39.3017 [ERROR] Dispenser.Utils.Logger - 処理中にエラーが発生しました: ディスクに十分な空き領域がありません。
 
2025-08-04 19:34:47.5204 [INFO] Dispenser.Utils.Logger - 高度な文字列置き換え処理を開始します 
2025-08-04 19:34:49.1158 [INFO] Dispenser.Utils.Logger - 必須対象フォルダを追加: N:\work\VSCode\Dispenser\resource\Package\Taikoban 
2025-08-04 19:34:49.1168 [INFO] Dispenser.Utils.Logger - 条件付き対象フォルダを追加: N:\work\VSCode\Dispenser\resource\Package\Face (認証要素に「顔」が含まれているため) 
2025-08-04 19:34:49.1168 [INFO] Dispenser.Utils.Logger - 文字列置き換え処理を開始します 
2025-08-04 19:34:49.1168 [INFO] Dispenser.Utils.Logger - 対象フォルダの処理を開始: N:\work\VSCode\Dispenser\resource\Package\Taikoban 
2025-08-04 19:34:49.1168 [DEBUG] Dispenser.Utils.Logger - ディレクトリを作成: Output\20250804_永井検証サーバ\Taikoban 
2025-08-04 19:34:49.1168 [DEBUG] Dispenser.Utils.Logger - サブディレクトリを処理: N:\work\VSCode\Dispenser\resource\Package\Taikoban\Taikoban_Ver.******* → Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.******* 
2025-08-04 19:35:24.4045 [INFO] Dispenser.Utils.Logger - 見つかった対象ファイル数: 429 
2025-08-04 19:35:24.4045 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\Batch\ServerSetup.cmd 
2025-08-04 19:35:24.4225 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AccountImporter\eDoktor.Taikoban.AccountImporter.exe.config 
2025-08-04 19:35:24.4445 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\ServerAddress.txt 
2025-08-04 19:35:24.4445 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\ContextExtensions.txt 
2025-08-04 19:35:24.4605 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\SContextExtensions.txt 
2025-08-04 19:35:24.4890 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\API\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:35:24.4970 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AutoCompleteServer\eDoktor.AutoComplete.Database.dll.config 
2025-08-04 19:35:24.4970 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AutoCompleteServer\eDoktor.AutoComplete.Interprocess.dll.config 
2025-08-04 19:35:24.4970 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AutoCompleteServer\eDoktor.AutoComplete.ServerService.exe.config 
2025-08-04 19:35:24.5150 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\RemoteServer\eDoktor.Taikoban.RemoteInterprocess.dll.config 
2025-08-04 19:35:24.5150 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\RemoteServer\eDoktor.Taikoban.RemoteServer.dll.config 
2025-08-04 19:35:24.5271 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\ReportService\eDoktor.Taikoban.Report.dll.config 
2025-08-04 19:35:24.5271 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\Config.ps1 
2025-08-04 19:35:24.5410 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\ConfirmIsAlive.ps1 
2025-08-04 19:35:24.5410 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\ConfirmLicense.ps1 
2025-08-04 19:35:24.5410 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\DeleteAuthlog.ps1 
2025-08-04 19:35:24.5560 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\ExecAutoBackup.cmd 
2025-08-04 19:35:24.5560 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\GraphedTerminalUsage.ps1 
2025-08-04 19:35:24.5756 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\MonthlyLendingList.ps1 
2025-08-04 19:35:24.5756 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\MonthlyReleaseLicenseList.ps1 
2025-08-04 19:35:24.5901 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\AutomaticFailback\AutomaticFailback.bat 
2025-08-04 19:35:24.5901 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\UpdateValidPeriods\UpdateValidPeriods.exe.config 
2025-08-04 19:35:24.5901 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Auth.Database.dll.config 
2025-08-04 19:35:24.6041 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:35:24.6041 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Taikoban.ServerService.exe.config 
2025-08-04 19:35:24.6201 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\SmartCardBulkRegister\ServerAddress.txt 
2025-08-04 19:35:24.6201 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\WitnessService\eDoktor.Taikoban.Report.dll.config 
2025-08-04 19:35:24.6461 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\AdminConsole\1_1\ContextExtensions.txt 
2025-08-04 19:35:24.6511 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\AdminConsole\1_1\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:35:24.6511 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\AdminConsole\1_1\eDoktor.Taikoban.AdminConsole.exe.config 
2025-08-04 19:35:24.6641 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\AdminConsole\1_1\eDoktor.Taikoban.RemoteInterprocess.dll.config 
2025-08-04 19:35:24.6791 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\AdminConsole\Starter\ServerAddress.txt 
2025-08-04 19:35:24.6791 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\ClientInstaller\Taikoban\AdminConsole\ServerAddress.txt 
2025-08-04 19:35:24.7016 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\ClientInstaller\Taikoban\SmartCardBulkRegister\ServerAddress.txt 
2025-08-04 19:35:24.7016 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\ClientInstaller\Taikoban\SmartCardRegister\ServerAddress.txt 
2025-08-04 19:35:24.7216 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\ClientInstaller\Taikoban\TemporaryAllowOneFactorAuthentication\ServerAddress.txt 
2025-08-04 19:35:24.7306 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\ClientInstaller\Taikoban\TemporaryCardIssuer\ServerAddress.txt 
2025-08-04 19:35:24.7306 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\ClientInstallerStarter\ServerAddress.txt 
2025-08-04 19:35:24.7436 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\RemoteAgent\eDoktor.Taikoban.RemoteSetup.exe.config 
2025-08-04 19:35:24.7436 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\RemoteAgent\101\eDoktor.Taikoban.Interprocess.dll.config 
2025-08-04 19:35:24.7596 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\RemoteAgent\101\eDoktor.Taikoban.RemoteInterprocess.dll.config 
2025-08-04 19:35:24.7596 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\RemoteAgent\101\eDoktor.Taikoban.RemoteSetup.exe.config 
2025-08-04 19:35:24.7849 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\InstallPath.txt 
2025-08-04 19:35:24.7964 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ServerAddress.txt 
2025-08-04 19:35:24.8284 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ApplicationManage\ApplicationManage.exe.config 
2025-08-04 19:35:24.8284 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ApplicationManage\AutomationSpy.exe.config 
2025-08-04 19:35:24.8925 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ApplicationManage\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:35:24.8925 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ApplicationManage\eDoktor.Auth.Setup.dll.config 
2025-08-04 19:35:24.8996 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ApplicationManage\eDoktor.Taikoban.AdminConsole.exe.config 
2025-08-04 19:35:24.8996 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\AutoCompleteAgent\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:35:24.8996 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\AutoCompleteAgent\eDoktor.AutoComplete.Agent.exe.config 
2025-08-04 19:35:24.9205 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\AutoCompleteAgent\eDoktor.AutoComplete.Interprocess.dll.config 
2025-08-04 19:35:24.9205 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\CallAuthScreen\CallAuthenticationScreenF.exe.config 
2025-08-04 19:35:24.9335 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\CallAuthScreen\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:35:24.9445 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ClientExtensionTool\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:35:24.9445 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\CloseApplication\CloseApplication.exe.config 
2025-08-04 19:35:24.9445 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\HXAgency\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:35:24.9605 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\HXAgency\HXAgency.exe.config 
2025-08-04 19:35:24.9605 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\HXScreenSaverMonitor\HXScreenSaverMonitor.exe.config 
2025-08-04 19:35:24.9881 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\SmartCardBulkRegister\1\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:35:24.9991 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\SmartCardBulkRegister\1\eDoktor.Taikoban.SmartCardBulkRegister.exe.config 
2025-08-04 19:35:25.0081 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\SmartCardBulkRegister\Starter\ServerAddress.txt 
2025-08-04 19:35:25.0081 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\SmartCardRegister\1\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:35:25.0271 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\SmartCardRegister\1\eDoktor.Taikoban.SmartCardRegister.exe.config 
2025-08-04 19:35:25.0271 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\SmartCardRegister\Starter\ServerAddress.txt 
2025-08-04 19:35:25.0461 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\eDoktor.Auth.Setup.dll.config 
2025-08-04 19:35:25.0721 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\101\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:35:25.0721 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\101\eDoktor.Auth.Setup.dll.config 
2025-08-04 19:35:25.0721 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\101\eDoktor.ContentDisplay.API.exe.config 
2025-08-04 19:35:25.0946 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\101\eDoktor.VeinAuthClient.API.exe.config 
2025-08-04 19:35:25.0946 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\101\InstallPath.txt 
2025-08-04 19:35:25.1196 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\101\DisplayAPI\LockDetailMessage\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:35:25.1196 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\TemporaryAllowOneFactorAuthentication\1\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:35:25.1386 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\TemporaryAllowOneFactorAuthentication\1\eDoktor.Taikoban.TemporaryAllowOneFactorAuthentication.exe.config 
2025-08-04 19:35:25.1386 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\TemporaryAllowOneFactorAuthentication\Starter\ServerAddress.txt 
2025-08-04 19:35:25.1526 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\TemporaryCardIssuer\1\eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:35:25.1526 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\TemporaryCardIssuer\1\eDoktor.Taikoban.TemporaryCardIssuer.exe.config 
2025-08-04 19:35:25.1696 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\TemporaryCardIssuer\Starter\ServerAddress.txt 
2025-08-04 19:35:25.1696 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\02.クライアント\ServerAddress.txt 
2025-08-04 19:35:25.3901 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\99.ドキュメント\【Taikoban】設定メモ.txt 
2025-08-04 19:35:25.3901 [DEBUG] Dispenser.Utils.Logger - ファイルを更新しました: N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\ModuleBackup\BackupModule.cmd 
2025-08-04 19:35:25.3901 [INFO] Dispenser.Utils.Logger - 対象フォルダの処理が完了: N:\work\VSCode\Dispenser\resource\Package\Taikoban → Output\20250804_永井検証サーバ\Taikoban 
2025-08-04 19:35:25.3901 [INFO] Dispenser.Utils.Logger - 対象フォルダの処理を開始: N:\work\VSCode\Dispenser\resource\Package\Face 
2025-08-04 19:35:25.4041 [DEBUG] Dispenser.Utils.Logger - ディレクトリを作成: Output\20250804_永井検証サーバ\Face 
2025-08-04 19:35:25.4041 [DEBUG] Dispenser.Utils.Logger - サブディレクトリを処理: N:\work\VSCode\Dispenser\resource\Package\Face\FaceStamp_ver2.1.0.0 → Output\20250804_永井検証サーバ\Face\FaceStamp_ver2.1.0.0 
2025-08-04 19:35:42.6175 [INFO] Dispenser.Utils.Logger - 見つかった対象ファイル数: 103 
2025-08-04 19:35:51.7592 [INFO] Dispenser.Utils.Logger - 対象フォルダの処理が完了: N:\work\VSCode\Dispenser\resource\Package\Face → Output\20250804_永井検証サーバ\Face 
2025-08-04 19:35:51.7592 [INFO] Dispenser.Utils.Logger - 文字列置き換え処理が完了しました。処理時間: 62.64秒 
2025-08-04 19:35:51.7592 [INFO] Dispenser.Utils.Logger - 処理結果サマリーをログファイルに出力しました 
2025-08-04 19:35:51.7592 [INFO] Dispenser.Utils.Logger - 高度な文字列置き換え処理が完了しました。出力先: Output\20250804_永井検証サーバ 
2025-08-04 19:35:51.7592 [INFO] Dispenser.Utils.Logger - 処理結果: ReplacementResult: Success, Files: 532 (81 processed, 451 skipped), Time: 62.64s 
2025-08-04 19:35:51.7592 [INFO] Dispenser.Utils.Logger - パッケージ作成処理を開始します 
2025-08-04 19:35:51.9633 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: 置換結果_20250804_193447.log 
2025-08-04 19:35:51.9633 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/LicenseFile/仮ライセンスなので本番用ライセンスを入手し次第入れ替えてください.txt 
2025-08-04 19:35:51.9953 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Common.dll 
2025-08-04 19:35:51.9953 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Install.dll 
2025-08-04 19:35:52.0099 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthCommon.dll 
2025-08-04 19:35:52.0099 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthCommon.pdb 
2025-08-04 19:35:52.0099 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthSDK.dll 
2025-08-04 19:35:52.0238 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthSDK.dll.config 
2025-08-04 19:35:52.0238 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthSDK.pdb 
2025-08-04 19:35:52.0238 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthServer.dll 
2025-08-04 19:35:52.0238 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthServer.dll.config 
2025-08-04 19:35:52.0439 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthServer.pdb 
2025-08-04 19:35:52.0439 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthServerConsole.exe 
2025-08-04 19:35:52.0439 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthServerConsole.exe.config 
2025-08-04 19:35:52.0439 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthServerConsole.pdb 
2025-08-04 19:35:52.0538 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthServerService.exe 
2025-08-04 19:35:52.0538 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthServerService.exe.config 
2025-08-04 19:35:52.0538 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthServerService.InstallLog 
2025-08-04 19:35:52.0538 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthServerService.pdb 
2025-08-04 19:35:52.0538 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthTemplateUpdateJudge.dll 
2025-08-04 19:35:52.0538 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceAuthTemplateUpdateJudge.pdb 
2025-08-04 19:35:52.0538 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceCrypt.dll 
2025-08-04 19:35:52.0708 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceCrypt.pdb 
2025-08-04 19:35:52.0708 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceInterprocess.dll 
2025-08-04 19:35:52.0708 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceInterprocess.dll.config 
2025-08-04 19:35:52.0878 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor.Taikoban.FaceInterprocess.pdb 
2025-08-04 19:35:52.0878 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor_Taikoban_face_auth_server_service_installer.bat 
2025-08-04 19:35:52.0878 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/eDoktor_Taikoban_face_auth_server_service_uninstaller.bat 
2025-08-04 19:35:52.1028 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GFRL.dll 
2025-08-04 19:35:52.1028 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/Glory.SentinelKeyLibrary.dll 
2025-08-04 19:35:52.1351 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFaceCLI.dll 
2025-08-04 19:35:52.1351 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRLog.dll 
2025-08-04 19:35:52.1351 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/hasp_net_windows.dll 
2025-08-04 19:35:52.3271 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/glic.dll 
2025-08-04 19:35:53.1704 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/GRFace_ef10.dll 
2025-08-04 19:35:53.3424 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/GRFace_et50.dll 
2025-08-04 19:35:53.4751 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/GRFace_fs50.dll 
2025-08-04 19:35:53.6526 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/GRFace_icr50.dll 
2025-08-04 19:36:04.3385 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/GRFace_lbc10.dll 
2025-08-04 19:36:35.9121 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/GRFace_mr50.dll 
2025-08-04 19:36:36.1953 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/GRFace_vn50.dll 
2025-08-04 19:36:36.7385 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/guic.dll 
2025-08-04 19:36:36.7900 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/haspvlib_112398.dll 
2025-08-04 19:36:36.9959 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/hasp_rt.exe 
2025-08-04 19:36:37.1170 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/inference_engine.dll 
2025-08-04 19:36:37.2612 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/inference_engine_legacy.dll 
2025-08-04 19:36:37.3092 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/inference_engine_lp_transformations.dll 
2025-08-04 19:36:37.4674 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/inference_engine_preproc.dll 
2025-08-04 19:36:37.5304 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/inference_engine_transformations.dll 
2025-08-04 19:36:37.6290 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/libiomp5md.dll 
2025-08-04 19:36:37.9675 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/libmmd.dll 
2025-08-04 19:36:38.9559 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/MKLDNNPlugin.dll 
2025-08-04 19:36:39.2596 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/ngraph.dll 
2025-08-04 19:36:40.0396 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/svml_dispmd.dll 
2025-08-04 19:36:40.1517 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/GRFace3/tbb.dll 
2025-08-04 19:36:40.1517 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceAuthServer/ja/GFRL.resources.dll 
2025-08-04 19:36:40.1517 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceInsert/eDoktor.Taikoban.FaceInsert.cmd 
2025-08-04 19:36:40.1517 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceInsert/eDoktor.Taikoban.FaceInsert.vbe 
2025-08-04 19:36:40.1517 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceInsert/ServerAddress.txt 
2025-08-04 19:36:40.1517 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktor/Taikoban/FaceInsert/version.txt 
2025-08-04 19:36:40.1657 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/ClientInstaller/TaikobanClinetSetupExec.cmd 
2025-08-04 19:36:42.5065 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/ClientInstaller/Runtime/VC_redist.x64.exe 
2025-08-04 19:36:42.5095 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/FaceInsert/eDoktor.Taikoban.FaceInsert.cmd 
2025-08-04 19:36:42.5095 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/FaceInsert/eDoktor.Taikoban.FaceInsert.vbe 
2025-08-04 19:36:42.5095 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/FaceInsert/ServerAddress.txt 
2025-08-04 19:36:42.5095 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/FaceInsert/version.txt 
2025-08-04 19:36:42.5095 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/version.txt 
2025-08-04 19:36:42.5256 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/AForge.Video.DirectShow.dll 
2025-08-04 19:36:42.5256 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/AForge.Video.DirectShow.xml 
2025-08-04 19:36:42.5256 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/AForge.Video.dll 
2025-08-04 19:36:42.5416 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/AForge.Video.xml 
2025-08-04 19:36:42.5671 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Common.dll 
2025-08-04 19:36:42.5701 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.AdminConsoleExtension.dll 
2025-08-04 19:36:42.5701 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.AdminConsoleExtension.dll.config 
2025-08-04 19:36:42.5701 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.AppCommon.dll 
2025-08-04 19:36:42.5701 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.AppCommon.dll.config 
2025-08-04 19:36:42.5701 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.AppExtension.dll 
2025-08-04 19:36:42.5701 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.AppExtension.dll.config 
2025-08-04 19:36:42.5941 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.ClientExtension.dll 
2025-08-04 19:36:42.5941 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.ClientExtension.dll.config 
2025-08-04 19:36:42.5941 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceAuthCommon.dll 
2025-08-04 19:36:42.6041 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceAuthCommon.pdb 
2025-08-04 19:36:42.6041 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceCrypt.dll 
2025-08-04 19:36:42.6041 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceCrypt.pdb 
2025-08-04 19:36:42.6171 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceDetection.dll 
2025-08-04 19:36:42.6171 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceDetection.pdb 
2025-08-04 19:36:42.6171 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceImageCntl.dll 
2025-08-04 19:36:42.6171 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceImageCntl.pdb 
2025-08-04 19:36:42.6401 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceInsert.exe 
2025-08-04 19:36:42.6401 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceInsert.exe.config 
2025-08-04 19:36:42.6546 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceInsert.pdb 
2025-08-04 19:36:42.6656 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceInterprocess.dll 
2025-08-04 19:36:42.6656 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceInterprocess.dll.config 
2025-08-04 19:36:42.6806 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.FaceInterprocess.pdb 
2025-08-04 19:36:42.6946 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.Interprocess.dll 
2025-08-04 19:36:42.6946 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.Interprocess.dll.config 
2025-08-04 19:36:42.6946 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktor.Taikoban.RSACrypto.dll 
2025-08-04 19:36:42.6946 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktorTaikoban.FaceClientCommon.dll 
2025-08-04 19:36:42.6946 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/eDoktorTaikoban.FaceClientCommon.pdb 
2025-08-04 19:36:42.7186 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/GFRL.dll 
2025-08-04 19:36:42.7186 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/Glory.SentinelKeyLibrary.dll 
2025-08-04 19:36:42.7447 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/GRFaceCLI.dll 
2025-08-04 19:36:42.7447 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/GRLog.dll 
2025-08-04 19:36:42.7727 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/haarcascade_eye_tree_eyeglasses.xml 
2025-08-04 19:36:42.8067 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/haarcascade_frontalface_default.xml 
2025-08-04 19:36:42.8067 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/hasp_net_windows.dll 
2025-08-04 19:36:42.8267 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/LivenessCheck.dll 
2025-08-04 19:36:42.8267 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/LivenessCheck.hpp 
2025-08-04 19:36:42.8267 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/LivenessCheck.lib 
2025-08-04 19:36:42.8563 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/LivenessCheckSharp.dll 
2025-08-04 19:36:43.0078 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/LivenessCheckSharp.pdb 
2025-08-04 19:36:43.0594 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/OpenCvSharp.dll 
2025-08-04 19:36:43.0594 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/OpenCvSharp.Extensions.dll 
2025-08-04 19:36:43.0594 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/OpenCvSharp.Extensions.xml 
2025-08-04 19:36:43.1114 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/OpenCvSharp.xml 
2025-08-04 19:36:44.3775 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/opencv_videoio_ffmpeg455_64.dll 
2025-08-04 19:36:48.8957 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/opencv_world455.dll 
2025-08-04 19:36:49.1956 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/opencv_world455.lib 
2025-08-04 19:36:49.1956 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.Buffers.dll 
2025-08-04 19:36:49.1956 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.Buffers.xml 
2025-08-04 19:36:49.1956 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.Drawing.Common.dll 
2025-08-04 19:36:49.2166 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.Memory.dll 
2025-08-04 19:36:49.2166 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.Memory.xml 
2025-08-04 19:36:49.2266 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.Numerics.Vectors.dll 
2025-08-04 19:36:49.2266 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.Numerics.Vectors.xml 
2025-08-04 19:36:49.2266 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.Runtime.CompilerServices.Unsafe.dll 
2025-08-04 19:36:49.2266 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.Runtime.CompilerServices.Unsafe.xml 
2025-08-04 19:36:49.2446 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.ValueTuple.dll 
2025-08-04 19:36:49.2446 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/System.ValueTuple.xml 
2025-08-04 19:36:49.2446 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/ja/GFRL.resources.dll 
2025-08-04 19:36:49.2446 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/deploy.prototxt 
2025-08-04 19:36:49.3977 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/eyeDetect.onnx 
2025-08-04 19:36:49.7753 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/eyeDir.onnx 
2025-08-04 19:36:50.1379 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/eyeStatus.onnx 
2025-08-04 19:36:50.4630 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/faceDirH.onnx 
2025-08-04 19:36:50.8646 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/faceDirV.onnx 
2025-08-04 19:36:51.1467 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/glasses.onnx 
2025-08-04 19:36:52.9978 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/livenessH.onnx 
2025-08-04 19:36:53.6267 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/livenessV.onnx 
2025-08-04 19:36:53.7808 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/mask.onnx 
2025-08-04 19:36:54.0072 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/MODELS/res10_300x300_ssd_iter_140000_fp16.caffemodel 
2025-08-04 19:36:54.0072 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/token/token.txt 
2025-08-04 19:36:54.0072 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/FaceInsert/101/token/tokenメモ.txt 
2025-08-04 19:36:54.0072 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.Controls.dll 
2025-08-04 19:36:54.0072 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.Controls.xml 
2025-08-04 19:36:54.0242 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.dll 
2025-08-04 19:36:54.0432 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.Imaging.dll 
2025-08-04 19:36:54.0693 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.Imaging.xml 
2025-08-04 19:36:54.0693 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.Math.dll 
2025-08-04 19:36:54.0853 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.Math.xml 
2025-08-04 19:36:54.0853 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.Video.DirectShow.dll 
2025-08-04 19:36:54.0853 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.Video.DirectShow.xml 
2025-08-04 19:36:54.1023 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.Video.dll 
2025-08-04 19:36:54.1023 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.Video.xml 
2025-08-04 19:36:54.1023 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/AForge.xml 
2025-08-04 19:36:54.1343 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Common.dll 
2025-08-04 19:36:54.1343 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.BioAuthMessage.dll 
2025-08-04 19:36:54.1343 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceAuthCommon.dll 
2025-08-04 19:36:54.1343 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceAuthCommon.pdb 
2025-08-04 19:36:54.1745 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceClient.exe 
2025-08-04 19:36:54.1745 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceClient.exe.config 
2025-08-04 19:36:54.1875 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceClient.pdb 
2025-08-04 19:36:54.1875 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceCrypt.dll 
2025-08-04 19:36:54.1945 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceCrypt.pdb 
2025-08-04 19:36:54.1945 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceDetection.dll 
2025-08-04 19:36:54.2105 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceDetection.pdb 
2025-08-04 19:36:54.2105 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceImageCntl.dll 
2025-08-04 19:36:54.2105 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceImageCntl.pdb 
2025-08-04 19:36:54.2105 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceInterprocess.dll 
2025-08-04 19:36:54.2265 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceInterprocess.dll.config 
2025-08-04 19:36:54.2265 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktor.Taikoban.FaceInterprocess.pdb 
2025-08-04 19:36:54.2425 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktorTaikoban.FaceClientCommon.dll 
2025-08-04 19:36:54.2425 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/eDoktorTaikoban.FaceClientCommon.pdb 
2025-08-04 19:36:54.2590 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/GFRL.dll 
2025-08-04 19:36:54.2590 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/Glory.SentinelKeyLibrary.dll 
2025-08-04 19:36:54.2880 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/GRFaceCLI.dll 
2025-08-04 19:36:54.2920 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/GRLog.dll 
2025-08-04 19:36:54.3200 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/haarcascade_eye_tree_eyeglasses.xml 
2025-08-04 19:36:54.3505 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/haarcascade_frontalface_default.xml 
2025-08-04 19:36:54.3585 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/hasp_net_windows.dll 
2025-08-04 19:36:54.3695 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/LivenessCheck.dll 
2025-08-04 19:36:54.3925 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/LivenessCheckSharp.dll 
2025-08-04 19:36:54.6183 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/LivenessCheckSharp.pdb 
2025-08-04 19:36:54.6728 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/OpenCvSharp.dll 
2025-08-04 19:36:54.6728 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/OpenCvSharp.Extensions.dll 
2025-08-04 19:36:54.6788 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/OpenCvSharp.Extensions.xml 
2025-08-04 19:36:54.7258 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/OpenCvSharp.xml 
2025-08-04 19:36:56.0044 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/opencv_videoio_ffmpeg455_64.dll 
2025-08-04 19:37:01.7468 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/opencv_world455.dll 
2025-08-04 19:37:01.7468 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/policy.PNG 
2025-08-04 19:37:02.1144 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.Buffers.dll 
2025-08-04 19:37:02.4692 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.Buffers.xml 
2025-08-04 19:37:02.8538 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.Drawing.Common.dll 
2025-08-04 19:37:02.8675 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.Memory.dll 
2025-08-04 19:37:02.8675 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.Memory.xml 
2025-08-04 19:37:02.9334 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.Numerics.Vectors.dll 
2025-08-04 19:37:02.9334 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.Numerics.Vectors.xml 
2025-08-04 19:37:03.0061 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.Runtime.CompilerServices.Unsafe.dll 
2025-08-04 19:37:03.0119 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.Runtime.CompilerServices.Unsafe.xml 
2025-08-04 19:37:03.0119 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.ValueTuple.dll 
2025-08-04 19:37:03.0119 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/System.ValueTuple.xml 
2025-08-04 19:37:06.3410 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/dll/x64/OpenCvSharpExtern.dll 
2025-08-04 19:37:07.8248 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/dll/x64/opencv_videoio_ffmpeg452_64.dll 
2025-08-04 19:37:10.0773 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/dll/x86/OpenCvSharpExtern.dll 
2025-08-04 19:37:11.3672 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/dll/x86/opencv_videoio_ffmpeg452.dll 
2025-08-04 19:37:11.3672 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/ja/GFRL.resources.dll 
2025-08-04 19:37:11.3672 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/deploy.prototxt 
2025-08-04 19:37:11.5137 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/eyeDetect.onnx 
2025-08-04 19:37:11.8701 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/eyeDir.onnx 
2025-08-04 19:37:12.4405 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/eyeStatus.onnx 
2025-08-04 19:37:12.6707 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/faceDirH.onnx 
2025-08-04 19:37:13.0710 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/faceDirV.onnx 
2025-08-04 19:37:14.5232 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/glasses.onnx 
2025-08-04 19:37:15.1590 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/livenessH.onnx 
2025-08-04 19:37:15.7314 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/livenessV.onnx 
2025-08-04 19:37:15.8844 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/mask.onnx 
2025-08-04 19:37:16.1820 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/MODELS/res10_300x300_ssd_iter_140000_fp16.caffemodel 
2025-08-04 19:37:16.1820 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/token/token.txt 
2025-08-04 19:37:16.1820 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/FaceAuthClient/token/開発用ライセンスなので本番用ライセンスを入手し次第入れ替えてください.txt 
2025-08-04 19:37:16.1820 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/00.DBName/DBName.txt 
2025-08-04 19:37:16.1820 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/00.DBName/localhost.txt 
2025-08-04 19:37:16.1820 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/02.テーブル作成/create_t_face_auth_liveness_settings.sql 
2025-08-04 19:37:16.1820 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/02.テーブル作成/create_t_face_auth_logs.sql 
2025-08-04 19:37:16.1960 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/02.テーブル作成/create_t_face_auth_messages.sql 
2025-08-04 19:37:16.1960 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/02.テーブル作成/create_t_face_auth_not_use_devices.sql 
2025-08-04 19:37:16.1960 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/02.テーブル作成/create_t_face_auth_settings.sql 
2025-08-04 19:37:16.1960 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/02.テーブル作成/create_t_face_auth_templates.sql 
2025-08-04 19:37:16.1960 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/02.テーブル作成/create_t_face_auth_use_devices.sql 
2025-08-04 19:37:16.1960 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/02.テーブル作成/make_table_win.bat 
2025-08-04 19:37:16.1960 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/03.トリガー作成/create_TR_update_t_face_auth_messages.sql 
2025-08-04 19:37:16.2110 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/03.トリガー作成/create_TR_update_t_face_auth_not_use_devices.sql 
2025-08-04 19:37:16.2110 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/03.トリガー作成/create_TR_update_t_face_auth_settings.sql 
2025-08-04 19:37:16.2110 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/03.トリガー作成/create_TR_update_t_face_auth_templates.sql 
2025-08-04 19:37:16.2110 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/03.トリガー作成/create_TR_update_t_face_auth_use_devices.sql 
2025-08-04 19:37:16.2110 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/03.トリガー作成/make_trigger.bat 
2025-08-04 19:37:16.2110 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/04.データ作成/insert_t_face_auth_liveness_settings.sql 
2025-08-04 19:37:16.2110 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/04.データ作成/insert_t_face_auth_messages.sql 
2025-08-04 19:37:16.2110 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/04.データ作成/insert_t_face_auth_settings.sql 
2025-08-04 19:37:16.2270 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/04.DBスクリプト/FaceAuth/04.データ作成/make_data.bat 
2025-08-04 19:37:17.0047 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/05.Tools/【Taikoban】顔認証ライセンス依頼ファイル作成ツール.zip 
2025-08-04 19:37:17.0187 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/05.Tools/【Taikoban】顔認証ライセンス依頼ファイル作成手順.pdf 
2025-08-04 19:37:17.0267 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Face/FaceStamp_ver2.1.0.0/05.Tools/【Taikoban】顔認証ライセンス依頼ファイル作成手順.xlsx 
2025-08-04 19:37:17.0267 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/リリースノート.txt 
2025-08-04 19:37:50.9593 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/00.ミドルウェア/SQL Server/SQLEXPRADV_x64_JPN.exe 
2025-08-04 19:38:34.2918 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/00.ミドルウェア/SQL Server/SSMS-Setup-JPN.exe 
2025-08-04 19:38:34.2918 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/Batch/ServerSetup.cmd 
2025-08-04 19:38:34.2918 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/Batch/ServerSetup_1号機用.cmd 
2025-08-04 19:38:34.3039 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/Batch/ServerSetup_2号機用.cmd 
2025-08-04 19:38:34.3039 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/Batch/ServerSetup_3号機用.cmd 
2025-08-04 19:38:34.3039 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/Batch/ServerSetup_Firewall.cmd 
2025-08-04 19:38:34.3039 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/Batch/ServerSetup_シングルサーバー用.cmd 
2025-08-04 19:38:34.3314 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AccountImporter/eDoktor.Common.dll 
2025-08-04 19:38:34.3375 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AccountImporter/eDoktor.Taikoban.AccountImporter.exe 
2025-08-04 19:38:34.3375 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AccountImporter/eDoktor.Taikoban.AccountImporter.exe.config 
2025-08-04 19:38:34.3375 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AccountImporter/ImportAccounts.cmd 
2025-08-04 19:38:34.3375 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AccountImporter/Jobs/ImportJobs.cmd 
2025-08-04 19:38:34.3515 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AccountImporter/Jobs/ImportJobs.sql 
2025-08-04 19:38:34.3765 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AccountImporterLocal/eDoktor.Common.dll 
2025-08-04 19:38:34.3825 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AccountImporterLocal/eDoktor.Taikoban.AccountImporter.exe 
2025-08-04 19:38:34.3825 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AccountImporterLocal/eDoktor.Taikoban.AccountImporter.exe.config 
2025-08-04 19:38:34.3825 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AccountImporterLocal/ImportAccounts.cmd 
2025-08-04 19:38:34.3825 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AccountImporterLocal/Jobs/ImportJobs.cmd 
2025-08-04 19:38:34.3825 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AccountImporterLocal/Jobs/ImportJobs.sql 
2025-08-04 19:38:34.3825 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/eDoktor.Taikoban.AdminConsole.cmd 
2025-08-04 19:38:34.3984 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/eDoktor.Taikoban.AdminConsole.vbe 
2025-08-04 19:38:34.3984 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/ServerAddress.txt 
2025-08-04 19:38:34.3984 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/version.txt 
2025-08-04 19:38:34.3984 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/Capture/Thumbs.db 
2025-08-04 19:38:34.3984 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/ConnectInstallFolder.cmd 
2025-08-04 19:38:34.3984 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/ContextExtensions.txt 
2025-08-04 19:38:34.3984 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/SContextExtensions.txt 
2025-08-04 19:38:34.4341 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/ExecRemoteCommand/eDoktor.Common.dll 
2025-08-04 19:38:34.4341 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/ExecRemoteCommand/eDoktor.Taikoban.RSACrypto.dll 
2025-08-04 19:38:34.4341 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/ExecRemoteCommand/ExecRemoteCommand.cmd 
2025-08-04 19:38:34.4341 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/ExecRemoteCommand/ExecRemoteCommand.ps1 
2025-08-04 19:38:34.4341 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/ExecRemoteCommand/terminals.txt 
2025-08-04 19:38:34.4660 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/ExecRemoteTool/eDoktor.Common.dll 
2025-08-04 19:38:34.4660 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/ExecRemoteTool/eDoktor.Taikoban.RSACrypto.dll 
2025-08-04 19:38:34.4660 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/ExecRemoteTool/ExecRemoteTool.cmd 
2025-08-04 19:38:34.4760 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/ExecRemoteTool/ExecRemoteTool.ps1 
2025-08-04 19:38:34.4760 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/PSTools/Eula.txt 
2025-08-04 19:38:34.5080 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/PSTools/PsExec.exe 
2025-08-04 19:38:34.5080 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/PSTools/psfile.exe 
2025-08-04 19:38:34.5405 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/PSTools/PsGetsid.exe 
2025-08-04 19:38:34.5695 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/PSTools/PsInfo.exe 
2025-08-04 19:38:34.6006 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/PSTools/pskill.exe 
2025-08-04 19:38:34.6216 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/PSTools/pslist.exe 
2025-08-04 19:38:34.6361 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/PSTools/PsLoggedon.exe 
2025-08-04 19:38:34.6492 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/PSTools/psloglist.exe 
2025-08-04 19:38:34.6631 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/PSTools/pspasswd.exe 
2025-08-04 19:38:34.6791 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/PSTools/psping.exe 
2025-08-04 19:38:34.6952 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/PSTools/PsService.exe 
2025-08-04 19:38:34.6952 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/PSTools/psshutdown.exe 
2025-08-04 19:38:34.7217 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/PSTools/pssuspend.exe 
2025-08-04 19:38:34.7268 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/PSTools/Pstools.chm 
2025-08-04 19:38:34.8067 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/PSTools/PSTools.zip 
2025-08-04 19:38:34.8067 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AdminConsole/SContextExtension/PSTools/psversion.txt 
2025-08-04 19:38:34.8067 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/API/api_exe_installer.bat 
2025-08-04 19:38:34.8067 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/API/api_exe_uninstaller.bat 
2025-08-04 19:38:34.8242 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/API/eDoktor.Auth.Interprocess.dll 
2025-08-04 19:38:34.8242 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/API/eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:38:34.8462 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/API/eDoktor.Common.dll 
2025-08-04 19:38:34.8623 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/API/eDoktor.LocalServer.dll 
2025-08-04 19:38:34.8723 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/API/TaikobanAPI.exe 
2025-08-04 19:38:34.8723 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/API/TaikobanAPI.exe.config 
2025-08-04 19:38:34.8723 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/API/TaikobanAPI.tlb 
2025-08-04 19:38:34.8723 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AutoCompleteServer/eDoktor.AutoComplete.Database.dll 
2025-08-04 19:38:34.8842 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AutoCompleteServer/eDoktor.AutoComplete.Database.dll.config 
2025-08-04 19:38:34.8842 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AutoCompleteServer/eDoktor.AutoComplete.Interprocess.dll 
2025-08-04 19:38:34.8842 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AutoCompleteServer/eDoktor.AutoComplete.Interprocess.dll.config 
2025-08-04 19:38:34.8842 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AutoCompleteServer/eDoktor.AutoComplete.Server.dll 
2025-08-04 19:38:34.8983 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AutoCompleteServer/eDoktor.AutoComplete.Server.dll.config 
2025-08-04 19:38:34.8983 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AutoCompleteServer/eDoktor.AutoComplete.ServerService.exe 
2025-08-04 19:38:34.8983 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AutoCompleteServer/eDoktor.AutoComplete.ServerService.exe.config 
2025-08-04 19:38:34.9339 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AutoCompleteServer/eDoktor.Common.dll 
2025-08-04 19:38:34.9508 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AutoCompleteServer/eDoktor.Install.dll 
2025-08-04 19:38:34.9508 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AutoCompleteServer/server_service_installer.bat 
2025-08-04 19:38:34.9508 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/AutoCompleteServer/server_service_uninstaller.bat 
2025-08-04 19:38:34.9600 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/ExBSecurity/ExBSecurity.dll 
2025-08-04 19:38:34.9600 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/ExBSecurity/ex_com_installer.bat 
2025-08-04 19:38:34.9600 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/ExBSecurity/ex_com_uninstaller.bat 
2025-08-04 19:38:34.9600 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/ExBSecurityTest/ExBSecurityTest.exe 
2025-08-04 19:38:35.0765 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/ExBSecurityTest/ExBSecurityTest.exe.config 
2025-08-04 19:38:35.1064 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/RemoteServer/eDoktor.Common.dll 
2025-08-04 19:38:35.1204 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/RemoteServer/eDoktor.Install.dll 
2025-08-04 19:38:35.1204 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/RemoteServer/eDoktor.Taikoban.RemoteInterprocess.dll 
2025-08-04 19:38:35.1204 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/RemoteServer/eDoktor.Taikoban.RemoteInterprocess.dll.config 
2025-08-04 19:38:35.1340 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/RemoteServer/eDoktor.Taikoban.RemoteServer.dll 
2025-08-04 19:38:35.1340 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/RemoteServer/eDoktor.Taikoban.RemoteServer.dll.config 
2025-08-04 19:38:35.1340 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/RemoteServer/eDoktor.Taikoban.RemoteServerService.exe 
2025-08-04 19:38:35.1340 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/RemoteServer/eDoktor.Taikoban.RemoteServerService.exe.config 
2025-08-04 19:38:35.1340 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/RemoteServer/eDoktor.Taikoban.RSACrypto.dll 
2025-08-04 19:38:35.1340 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/RemoteServer/remoteserver_installer.bat 
2025-08-04 19:38:35.1479 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/RemoteServer/remoteserver_uninstaller.bat 
2025-08-04 19:38:35.1740 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/ReportService/eDoktor.Common.dll 
2025-08-04 19:38:35.1919 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/ReportService/eDoktor.Install.dll 
2025-08-04 19:38:35.1980 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/ReportService/eDoktor.Taikoban.AppCommon.dll 
2025-08-04 19:38:35.1980 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/ReportService/eDoktor.Taikoban.AppCommon.dll.config 
2025-08-04 19:38:35.1980 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/ReportService/eDoktor.Taikoban.Report.dll 
2025-08-04 19:38:35.1980 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/ReportService/eDoktor.Taikoban.Report.dll.config 
2025-08-04 19:38:35.2101 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/ReportService/eDoktor.Taikoban.ReportService.exe 
2025-08-04 19:38:35.2101 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/ReportService/eDoktor.Taikoban.ReportService.exe.config 
2025-08-04 19:38:35.2101 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/ReportService/TaikobanReportService_installer.bat 
2025-08-04 19:38:35.2101 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/ReportService/TaikobanReportService_uninstaller.bat 
2025-08-04 19:38:35.2101 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/Common.ps1 
2025-08-04 19:38:35.2101 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/Config.ps1 
2025-08-04 19:38:35.2101 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/ConfirmIsAlive.cmd 
2025-08-04 19:38:35.2101 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/ConfirmIsAlive.ps1 
2025-08-04 19:38:35.2101 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/ConfirmLicense.cmd 
2025-08-04 19:38:35.2267 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/ConfirmLicense.ps1 
2025-08-04 19:38:35.2267 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/DeleteAuthlog.cmd 
2025-08-04 19:38:35.2267 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/DeleteAuthlog.ps1 
2025-08-04 19:38:35.2537 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/eDoktor.Common.dll 
2025-08-04 19:38:35.2537 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/eDoktor.Taikoban.RSACrypto.dll 
2025-08-04 19:38:35.2618 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/ExecAutoBackup.cmd 
2025-08-04 19:38:35.2618 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/ExecAutoBackup.sql 
2025-08-04 19:38:35.2618 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/ExecShrinkLog.sql 
2025-08-04 19:38:35.2618 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/GraphedTerminalUsage.cmd 
2025-08-04 19:38:35.2618 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/GraphedTerminalUsage.ps1 
2025-08-04 19:38:35.2757 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/GraphedTerminalUsage.xlsm 
2025-08-04 19:38:35.2757 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/MonthlyLendingList.cmd 
2025-08-04 19:38:35.2757 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/MonthlyLendingList.ps1 
2025-08-04 19:38:35.2757 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/MonthlyReleaseLicenseList.cmd 
2025-08-04 19:38:35.2757 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/MonthlyReleaseLicenseList.ps1 
2025-08-04 19:38:35.2757 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/ShutdownExec.cmd 
2025-08-04 19:38:35.2757 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/TerminalUsage.cmd 
2025-08-04 19:38:35.2897 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/TerminalUsage.ps1 
2025-08-04 19:38:35.2897 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/リリースノート.txt 
2025-08-04 19:38:35.2897 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/AutomaticFailback/AutomaticFailback.bat 
2025-08-04 19:38:35.2897 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/AutomaticFailback/Failback.sql 
2025-08-04 19:38:35.3177 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/UpdateValidPeriods/eDoktor.Common.dll 
2025-08-04 19:38:35.3207 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/UpdateValidPeriods/UpdateValidPeriods.exe 
2025-08-04 19:38:35.3207 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Scripts/UpdateValidPeriods/UpdateValidPeriods.exe.config 
2025-08-04 19:38:35.3207 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Server/eDoktor.Auth.AuthenticationSchemes.dll 
2025-08-04 19:38:35.3207 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Server/eDoktor.Auth.AuthenticationSchemes.dll.config 
2025-08-04 19:38:35.3382 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Server/eDoktor.Auth.Database.dll 
2025-08-04 19:38:35.3382 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Server/eDoktor.Auth.Database.dll.config 
2025-08-04 19:38:35.3382 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Server/eDoktor.Auth.ExternalDependencies.dll 
2025-08-04 19:38:35.3382 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Server/eDoktor.Auth.ExternalDependencies.dll.config 
2025-08-04 19:38:35.3583 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Server/eDoktor.Auth.Interprocess.dll 
2025-08-04 19:38:35.3583 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Server/eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:38:35.3583 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Server/eDoktor.Auth.Licensing.dll 
2025-08-04 19:38:35.3662 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Server/eDoktor.Auth.Licensing.dll.config 
2025-08-04 19:38:35.3662 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Server/eDoktor.Auth.Server.dll 
2025-08-04 19:38:35.3662 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Server/eDoktor.Auth.Server.dll.config 
2025-08-04 19:38:35.3962 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Server/eDoktor.Common.dll 
2025-08-04 19:38:35.4012 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Server/eDoktor.Install.dll 
2025-08-04 19:38:35.4012 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Server/eDoktor.Licensing.dll 
2025-08-04 19:38:35.4153 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Server/eDoktor.Taikoban.LicenseActivator.exe 
2025-08-04 19:38:35.4153 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Server/eDoktor.Taikoban.ServerService.exe 
2025-08-04 19:38:35.4290 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Server/eDoktor.Taikoban.ServerService.exe.config 
2025-08-04 19:38:35.4290 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Server/server_service_installer.bat 
2025-08-04 19:38:35.4290 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/Server/server_service_uninstaller.bat 
2025-08-04 19:38:35.4290 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/SmartCardBulkRegister/eDoktor.Taikoban.SmartCardBulkRegister.cmd 
2025-08-04 19:38:35.4290 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/SmartCardBulkRegister/eDoktor.Taikoban.SmartCardBulkRegister.vbe 
2025-08-04 19:38:35.4290 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/SmartCardBulkRegister/ServerAddress.txt 
2025-08-04 19:38:35.4290 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/SmartCardBulkRegister/version.txt 
2025-08-04 19:38:35.4669 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/WitnessService/eDoktor.Common.dll 
2025-08-04 19:38:35.4829 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/WitnessService/eDoktor.Install.dll 
2025-08-04 19:38:35.4829 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/WitnessService/eDoktor.Taikoban.AppCommon.dll 
2025-08-04 19:38:35.4829 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/WitnessService/eDoktor.Taikoban.AppCommon.dll.config 
2025-08-04 19:38:35.4919 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/WitnessService/eDoktor.Taikoban.Report.dll 
2025-08-04 19:38:35.4919 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/WitnessService/eDoktor.Taikoban.Report.dll.config 
2025-08-04 19:38:35.4919 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/WitnessService/eDoktor.Taikoban.WitnessService.exe 
2025-08-04 19:38:35.4919 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/WitnessService/eDoktor.Taikoban.WitnessService.exe.config 
2025-08-04 19:38:35.4919 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/WitnessService/TaikobanWitnessService_installer.bat 
2025-08-04 19:38:35.4919 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktor/Taikoban/WitnessService/TaikobanWitnessService_uninstaller.bat 
2025-08-04 19:38:35.4919 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/CopyToClientInstallerScripts.cmd 
2025-08-04 19:38:35.5070 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/_配信系.txt 
2025-08-04 19:38:35.5070 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/★Scripts更新時はClientInstallerにも適用すること.txt 
2025-08-04 19:38:35.5070 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/version.txt 
2025-08-04 19:38:35.5070 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/ContextExtensions.txt 
2025-08-04 19:38:35.5070 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/eDoktor.Auth.ClientExtension.dll 
2025-08-04 19:38:35.5070 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/eDoktor.Auth.ClientExtension.dll.config 
2025-08-04 19:38:35.5316 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/eDoktor.Auth.Interprocess.dll 
2025-08-04 19:38:35.5316 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:38:35.5537 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/eDoktor.Common.dll 
2025-08-04 19:38:35.5537 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/eDoktor.PCSC.dll 
2025-08-04 19:38:35.6311 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/eDoktor.Taikoban.AdminConsole.exe 
2025-08-04 19:38:35.6351 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/eDoktor.Taikoban.AdminConsole.exe.config 
2025-08-04 19:38:35.6351 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/eDoktor.Taikoban.AdminConsoleExtension.dll 
2025-08-04 19:38:35.6351 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/eDoktor.Taikoban.AdminConsoleExtension.dll.config 
2025-08-04 19:38:35.6351 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/eDoktor.Taikoban.AppCommon.dll 
2025-08-04 19:38:35.6351 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/eDoktor.Taikoban.AppCommon.dll.config 
2025-08-04 19:38:35.6511 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/eDoktor.Taikoban.AppExtension.dll 
2025-08-04 19:38:35.6511 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/eDoktor.Taikoban.AppExtension.dll.config 
2025-08-04 19:38:35.6511 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/eDoktor.Taikoban.RemoteCommon.dll 
2025-08-04 19:38:35.6511 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/eDoktor.Taikoban.RemoteCommon.dll.config 
2025-08-04 19:38:35.6651 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/eDoktor.Taikoban.RemoteInterprocess.dll 
2025-08-04 19:38:35.6651 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/eDoktor.Taikoban.RemoteInterprocess.dll.config 
2025-08-04 19:38:35.6821 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/eDoktor.Taikoban.RemoteTool.exe 
2025-08-04 19:38:35.6821 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/eDoktor.Taikoban.RemoteTool.exe.config 
2025-08-04 19:38:35.6821 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/eDoktor.Taikoban.RSACrypto.dll 
2025-08-04 19:38:35.6821 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/Menu/MenuCntl_01.xml 
2025-08-04 19:38:35.6821 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/Menu/MenuCntl_02.xml 
2025-08-04 19:38:35.6962 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/Menu/MenuCntl_05.xml 
2025-08-04 19:38:35.6962 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/Menu/MenuCntl_06.xml 
2025-08-04 19:38:35.6962 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/Menu/MenuCntl_07.xml 
2025-08-04 19:38:35.6962 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/1_1/Resource/2844.ico 
2025-08-04 19:38:35.6962 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/Starter/eDoktor.Taikoban.AdminConsole.cmd 
2025-08-04 19:38:35.7102 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/Starter/eDoktor.Taikoban.AdminConsole.vbe 
2025-08-04 19:38:35.7102 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/Starter/ServerAddress.txt 
2025-08-04 19:38:35.7102 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/Starter/version.txt 
2025-08-04 19:38:35.7102 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/AdminConsole/Starter/Capture/Thumbs.db 
2025-08-04 19:38:35.7102 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/TaikobanClinetSetupExec.cmd 
2025-08-04 19:38:38.4313 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Drivers/NFCPortWithDriver.exe 
2025-08-04 19:38:38.7162 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Runtime/vcredist_x64.exe 
2025-08-04 19:38:39.0553 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Runtime/vcredist_x86.exe 
2025-08-04 19:38:39.2755 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Shortcut/Desktop/ログアウト.lnk 
2025-08-04 19:38:39.3970 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Shortcut/Desktop/院内情報Web.lnk 
2025-08-04 19:38:39.3990 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/AdminConsole/eDoktor.Taikoban.AdminConsole.cmd 
2025-08-04 19:38:39.3990 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/AdminConsole/eDoktor.Taikoban.AdminConsole.vbe 
2025-08-04 19:38:39.3990 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/AdminConsole/ServerAddress.txt 
2025-08-04 19:38:39.3990 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/AdminConsole/version.txt 
2025-08-04 19:38:39.3990 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/AdminConsole/Capture/Thumbs.db 
2025-08-04 19:38:39.3990 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/SmartCardBulkRegister/eDoktor.Taikoban.SmartCardBulkRegister.cmd 
2025-08-04 19:38:39.4131 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/SmartCardBulkRegister/eDoktor.Taikoban.SmartCardBulkRegister.vbe 
2025-08-04 19:38:39.4131 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/SmartCardBulkRegister/ServerAddress.txt 
2025-08-04 19:38:39.4131 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/SmartCardBulkRegister/version.txt 
2025-08-04 19:38:39.4131 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/SmartCardRegister/eDoktor.Taikoban.SmartCardRegister.cmd 
2025-08-04 19:38:39.4131 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/SmartCardRegister/eDoktor.Taikoban.SmartCardRegister.vbe 
2025-08-04 19:38:39.4131 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/SmartCardRegister/ServerAddress.txt 
2025-08-04 19:38:39.4131 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/SmartCardRegister/version.txt 
2025-08-04 19:38:39.4131 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/TemporaryAllowOneFactorAuthentication/eDoktor.Taikoban.TemporaryAllowOneFactorAuthentication.cmd 
2025-08-04 19:38:39.4131 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/TemporaryAllowOneFactorAuthentication/eDoktor.Taikoban.TemporaryAllowOneFactorAuthentication.vbe 
2025-08-04 19:38:39.4306 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/TemporaryAllowOneFactorAuthentication/ServerAddress.txt 
2025-08-04 19:38:39.4306 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/TemporaryAllowOneFactorAuthentication/version.txt 
2025-08-04 19:38:39.4306 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/TemporaryCardIssuer/eDoktor.Taikoban.TemporaryCardIssuer.cmd 
2025-08-04 19:38:39.4306 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/TemporaryCardIssuer/eDoktor.Taikoban.TemporaryCardIssuer.vbe 
2025-08-04 19:38:39.4306 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/TemporaryCardIssuer/ServerAddress.txt 
2025-08-04 19:38:39.4306 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Taikoban/TemporaryCardIssuer/version.txt 
2025-08-04 19:38:39.4446 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstaller/Task/Taikoban認証画面.xml 
2025-08-04 19:38:39.4446 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstallerStarter/ServerAddress.txt 
2025-08-04 19:38:39.4446 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstallerStarter/TaikobanClinetSetup.cmd 
2025-08-04 19:38:39.4446 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/ClientInstallerStarter/TaikobanClinetSetup.vbe 
2025-08-04 19:38:39.4446 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/CopyToClientInstallerRemoteAgent.cmd 
2025-08-04 19:38:39.4446 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/eDoktor.Taikoban.RemoteSetup.exe.config 
2025-08-04 19:38:39.4446 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/★更新時はClientInstallerにも適用すること.txt 
2025-08-04 19:38:39.4817 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/eDoktor.Common.dll 
2025-08-04 19:38:39.4916 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/eDoktor.Install.dll 
2025-08-04 19:38:39.4916 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/eDoktor.Taikoban.CaptureScreen.exe 
2025-08-04 19:38:39.4916 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/eDoktor.Taikoban.ClientExtension.dll 
2025-08-04 19:38:39.4916 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/eDoktor.Taikoban.ClientExtension.dll.config 
2025-08-04 19:38:39.5216 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/eDoktor.Taikoban.Interprocess.dll 
2025-08-04 19:38:39.5241 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/eDoktor.Taikoban.Interprocess.dll.config 
2025-08-04 19:38:39.5241 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/eDoktor.Taikoban.Messenger.exe 
2025-08-04 19:38:39.5241 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/eDoktor.Taikoban.RemoteAgent.dll 
2025-08-04 19:38:39.5241 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/eDoktor.Taikoban.RemoteAgent.dll.config 
2025-08-04 19:38:39.5241 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/eDoktor.Taikoban.RemoteAgentService.exe 
2025-08-04 19:38:39.5381 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/eDoktor.Taikoban.RemoteAgentService.exe.config 
2025-08-04 19:38:39.5381 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/eDoktor.Taikoban.RemoteInterprocess.dll 
2025-08-04 19:38:39.5381 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/eDoktor.Taikoban.RemoteInterprocess.dll.config 
2025-08-04 19:38:39.5561 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/eDoktor.Taikoban.RemoteSetup.exe 
2025-08-04 19:38:39.5561 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/eDoktor.Taikoban.RemoteSetup.exe.config 
2025-08-04 19:38:39.5561 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/eDoktor.Taikoban.RSACrypto.dll 
2025-08-04 19:38:39.5561 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/eDoktor.Taikoban.TerminalOperator.exe 
2025-08-04 19:38:39.5561 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/eDoktor.Taikoban.TerminalOperator.exe.config 
2025-08-04 19:38:39.5701 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/logo.bmp 
2025-08-04 19:38:39.5701 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/preUninstall_RemoteAgent.bat 
2025-08-04 19:38:39.5701 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/remoteagent_installer.bat 
2025-08-04 19:38:39.5701 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/RemoteAgent/101/remoteagent_uninstaller.bat 
2025-08-04 19:38:39.5701 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/APITest.cmd 
2025-08-04 19:38:39.5701 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/APITest.vbe 
2025-08-04 19:38:39.5701 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ChangeRemoteAgentDelivery.cmd 
2025-08-04 19:38:39.5861 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ChangeRemoteAgentDelivery.vbe 
2025-08-04 19:38:39.5861 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ChangeScriptsDelivery.cmd 
2025-08-04 19:38:39.6735 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ChangeTaikobanDelivery.cmd 
2025-08-04 19:38:39.6735 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ChangeTaikobanDelivery.vbe 
2025-08-04 19:38:39.6735 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ConfirmTaikoban.cmd 
2025-08-04 19:38:39.6795 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ConfirmTaikoban.vbe 
2025-08-04 19:38:39.6795 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/InstallPath.txt 
2025-08-04 19:38:39.6795 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/NoDisplayStarter.vbs 
2025-08-04 19:38:39.6795 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ScriptUpdater.cmd 
2025-08-04 19:38:39.6795 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ScriptUpdater.txt 
2025-08-04 19:38:39.6795 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ScriptUpdater.vbe 
2025-08-04 19:38:39.6795 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ServerAddress.txt 
2025-08-04 19:38:39.6965 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/StartTaikobanUpdateService.cmd 
2025-08-04 19:38:39.6965 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/StartTaikobanUpdateService.vbs 
2025-08-04 19:38:39.6965 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/StopTaikobanUpdateService.cmd 
2025-08-04 19:38:39.6965 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/StopTaikobanUpdateService.vbs 
2025-08-04 19:38:39.6965 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/Taikoban.disable.cmd 
2025-08-04 19:38:39.6965 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/Taikoban.enable.cmd 
2025-08-04 19:38:39.6965 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/Taikoban.enable.NewGetFlexible.cmd 
2025-08-04 19:38:39.7115 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/Taikoban.enable.NewGetFlexible.vbe 
2025-08-04 19:38:39.7115 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/Taikoban.enable.NewGetFlexible.WithoutReboot.cmd 
2025-08-04 19:38:39.7115 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/Taikoban.enable.WithoutReboot.cmd 
2025-08-04 19:38:39.7115 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/TaikobanRun.cmd 
2025-08-04 19:38:39.7115 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/Taikobanを最新版で有効化.cmd 
2025-08-04 19:38:39.7115 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/Taikobanを有効化.cmd 
2025-08-04 19:38:39.7115 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/Taikobanを無効化.cmd 
2025-08-04 19:38:39.7261 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ToolExit.cmd 
2025-08-04 19:38:39.7261 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ToolExit.vbs 
2025-08-04 19:38:39.7261 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/UpdateTaikoban.cmd 
2025-08-04 19:38:39.7261 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/UploadLogs.cmd 
2025-08-04 19:38:39.7261 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/UploadLogs.vbe 
2025-08-04 19:38:39.7261 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/ApplicationManage.exe 
2025-08-04 19:38:39.7421 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/ApplicationManage.exe.config 
2025-08-04 19:38:39.7421 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/AutomationSpy.exe 
2025-08-04 19:38:39.7421 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/AutomationSpy.exe.config 
2025-08-04 19:38:39.7421 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/CommonInfo.exe 
2025-08-04 19:38:39.7581 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/CommonInfo.exe.config 
2025-08-04 19:38:39.7581 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Auth.Agent.dll 
2025-08-04 19:38:39.7581 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Auth.Agent.dll.config 
2025-08-04 19:38:39.7781 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Auth.AuthModule.dll 
2025-08-04 19:38:39.7781 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Auth.ClientExtension.dll 
2025-08-04 19:38:39.7781 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Auth.ClientExtension.dll.config 
2025-08-04 19:38:39.7911 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Auth.Desktop.dll 
2025-08-04 19:38:39.7911 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Auth.Desktop.dll.config 
2025-08-04 19:38:39.7911 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Auth.Interprocess.dll 
2025-08-04 19:38:39.8061 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:38:39.8061 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Auth.Setup.dll 
2025-08-04 19:38:39.8061 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Auth.Setup.dll.config 
2025-08-04 19:38:39.8246 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Auth.SharedMemory.dll 
2025-08-04 19:38:39.8456 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Common.dll 
2025-08-04 19:38:39.8557 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Install.dll 
2025-08-04 19:38:39.9272 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Taikoban.AdminConsole.exe 
2025-08-04 19:38:39.9312 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Taikoban.AdminConsole.exe.config 
2025-08-04 19:38:39.9312 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Taikoban.AdminConsoleExtension.dll 
2025-08-04 19:38:39.9312 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Taikoban.AdminConsoleExtension.dll.config 
2025-08-04 19:38:39.9312 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Taikoban.AppCommon.dll 
2025-08-04 19:38:39.9312 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Taikoban.AppCommon.dll.config 
2025-08-04 19:38:39.9452 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Taikoban.AppExtension.dll 
2025-08-04 19:38:39.9452 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Taikoban.AppExtension.dll.config 
2025-08-04 19:38:39.9452 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Taikoban.ClientExtension.dll 
2025-08-04 19:38:39.9742 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Taikoban.Interprocess.dll 
2025-08-04 19:38:39.9772 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ApplicationManage/eDoktor.Taikoban.RSACrypto.dll 
2025-08-04 19:38:39.9772 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/AutoCompleteAgent/eDoktor.Auth.ClientExtension.dll 
2025-08-04 19:38:39.9772 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/AutoCompleteAgent/eDoktor.Auth.ClientExtension.dll.config 
2025-08-04 19:38:39.9982 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/AutoCompleteAgent/eDoktor.Auth.Interprocess.dll 
2025-08-04 19:38:39.9982 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/AutoCompleteAgent/eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:38:40.0082 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/AutoCompleteAgent/eDoktor.AutoComplete.Agent.exe 
2025-08-04 19:38:40.0082 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/AutoCompleteAgent/eDoktor.AutoComplete.Agent.exe.config 
2025-08-04 19:38:40.0232 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/AutoCompleteAgent/eDoktor.AutoComplete.AuthModule.dll 
2025-08-04 19:38:40.0232 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/AutoCompleteAgent/eDoktor.AutoComplete.AuthModuleTester.exe 
2025-08-04 19:38:40.0232 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/AutoCompleteAgent/eDoktor.AutoComplete.AuthModuleTester.exe.config 
2025-08-04 19:38:40.0527 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/AutoCompleteAgent/eDoktor.AutoComplete.AuthUI.dll 
2025-08-04 19:38:40.0557 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/AutoCompleteAgent/eDoktor.AutoComplete.AuthUI.dll.config 
2025-08-04 19:38:40.0557 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/AutoCompleteAgent/eDoktor.AutoComplete.AuthUIServer.exe 
2025-08-04 19:38:40.0697 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/AutoCompleteAgent/eDoktor.AutoComplete.AuthUIServer.exe.config 
2025-08-04 19:38:40.0697 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/AutoCompleteAgent/eDoktor.AutoComplete.ExternalAuth.dll 
2025-08-04 19:38:40.0697 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/AutoCompleteAgent/eDoktor.AutoComplete.ExternalAuth.dll.config 
2025-08-04 19:38:40.0697 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/AutoCompleteAgent/eDoktor.AutoComplete.Interprocess.dll 
2025-08-04 19:38:40.0867 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/AutoCompleteAgent/eDoktor.AutoComplete.Interprocess.dll.config 
2025-08-04 19:38:40.1107 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/AutoCompleteAgent/eDoktor.Common.dll 
2025-08-04 19:38:40.1237 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/AutoCompleteAgent/eDoktor.UI.dll 
2025-08-04 19:38:40.1237 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/CallAuthScreen/CallAuthenticationScreenF.exe 
2025-08-04 19:38:40.1323 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/CallAuthScreen/CallAuthenticationScreenF.exe.config 
2025-08-04 19:38:40.1323 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/CallAuthScreen/CallAuthScreen.cmd 
2025-08-04 19:38:40.1323 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/CallAuthScreen/CallAuthScreenF.cmd 
2025-08-04 19:38:40.1323 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/CallAuthScreen/DisableAuthScreen.cmd 
2025-08-04 19:38:40.1323 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/CallAuthScreen/eDoktor.Auth.ClientExtension.dll 
2025-08-04 19:38:40.1503 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/CallAuthScreen/eDoktor.Auth.ClientExtension.dll.config 
2025-08-04 19:38:40.1633 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/CallAuthScreen/eDoktor.Auth.Interprocess.dll 
2025-08-04 19:38:40.1633 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/CallAuthScreen/eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:38:40.1926 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/CallAuthScreen/eDoktor.Common.dll 
2025-08-04 19:38:40.1956 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/CallAuthScreen/LockLeavingSeat.cmd 
2025-08-04 19:38:40.1956 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/CallAuthScreen/LockUserSwitching.cmd 
2025-08-04 19:38:40.1956 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/CallAuthScreen/NoLock_.txt 
2025-08-04 19:38:40.1956 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/CallAuthScreen/TaikobanLogout.cmd 
2025-08-04 19:38:40.1956 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ClientExtensionTool/ClientExtensionTool.exe 
2025-08-04 19:38:40.2116 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ClientExtensionTool/ClientExtensionTool.exe.config 
2025-08-04 19:38:40.2116 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ClientExtensionTool/eDoktor.Auth.ClientExtension.dll 
2025-08-04 19:38:40.2116 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ClientExtensionTool/eDoktor.Auth.ClientExtension.dll.config 
2025-08-04 19:38:40.2367 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ClientExtensionTool/eDoktor.Auth.Interprocess.dll 
2025-08-04 19:38:40.2427 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ClientExtensionTool/eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:38:40.2667 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ClientExtensionTool/eDoktor.Common.dll 
2025-08-04 19:38:40.2667 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/ClientExtensionTool/ReadMe.txt 
2025-08-04 19:38:40.2747 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/CloseApplication/CloseApplication.exe 
2025-08-04 19:38:40.2747 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/CloseApplication/CloseApplication.exe.config 
2025-08-04 19:38:40.3007 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/CloseApplication/eDoktor.Common.dll 
2025-08-04 19:38:40.3067 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/HXAgency/eDoktor.Auth.ClientExtension.dll 
2025-08-04 19:38:40.3067 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/HXAgency/eDoktor.Auth.ClientExtension.dll.config 
2025-08-04 19:38:40.3227 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/HXAgency/eDoktor.Auth.Interprocess.dll 
2025-08-04 19:38:40.3227 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/HXAgency/eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:38:40.3512 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/HXAgency/eDoktor.Common.dll 
2025-08-04 19:38:40.3512 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/HXAgency/HXAgency.exe 
2025-08-04 19:38:40.3512 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/HXAgency/HXAgency.exe.config 
2025-08-04 19:38:40.3882 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/HXScreenSaverMonitor/eDoktor.Common.dll 
2025-08-04 19:38:40.3882 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/HXScreenSaverMonitor/HXScreenSaverMonitor.exe 
2025-08-04 19:38:40.3882 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/HXScreenSaverMonitor/HXScreenSaverMonitor.exe.config 
2025-08-04 19:38:40.4002 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/InterfaceComTester/test.vbs 
2025-08-04 19:38:40.4002 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/InterfaceComTester/test_changepassword.vbs 
2025-08-04 19:38:40.4002 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/InterfaceComTester/test_event.vbs 
2025-08-04 19:38:40.4002 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/InterfaceComTester/test_lock.vbs 
2025-08-04 19:38:40.4002 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/InterfaceComTester/test_logout.vbs 
2025-08-04 19:38:40.4152 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Scripts/Media/CardFound.wav 
2025-08-04 19:38:40.4152 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardBulkRegister/version.txt 
2025-08-04 19:38:41.0048 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardBulkRegister/1/eDoktor.Auth.ClientExtension.dll 
2025-08-04 19:38:41.2534 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardBulkRegister/1/eDoktor.Auth.ClientExtension.dll.config 
2025-08-04 19:38:41.4815 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardBulkRegister/1/eDoktor.Auth.Interprocess.dll 
2025-08-04 19:38:41.4815 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardBulkRegister/1/eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:38:41.5016 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardBulkRegister/1/eDoktor.Common.dll 
2025-08-04 19:38:41.5016 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardBulkRegister/1/eDoktor.Taikoban.AdminConsoleExtension.dll 
2025-08-04 19:38:41.5075 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardBulkRegister/1/eDoktor.Taikoban.AdminConsoleExtension.dll.config 
2025-08-04 19:38:41.5075 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardBulkRegister/1/eDoktor.Taikoban.AppCommon.dll 
2025-08-04 19:38:41.5075 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardBulkRegister/1/eDoktor.Taikoban.AppCommon.dll.config 
2025-08-04 19:38:41.5075 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardBulkRegister/1/eDoktor.Taikoban.AppExtension.dll 
2025-08-04 19:38:41.5075 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardBulkRegister/1/eDoktor.Taikoban.AppExtension.dll.config 
2025-08-04 19:38:41.5245 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardBulkRegister/1/eDoktor.Taikoban.RSACrypto.dll 
2025-08-04 19:38:41.5245 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardBulkRegister/1/eDoktor.Taikoban.SmartCardBulkRegister.exe 
2025-08-04 19:38:41.5245 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardBulkRegister/1/eDoktor.Taikoban.SmartCardBulkRegister.exe.config 
2025-08-04 19:38:41.5391 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardBulkRegister/1/【Taioban】カード一括取込サンプル.csv 
2025-08-04 19:38:41.5391 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardBulkRegister/1/【Taioban】カード一括取込サンプル.zip 
2025-08-04 19:38:41.5391 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardBulkRegister/Starter/eDoktor.Taikoban.SmartCardBulkRegister.cmd 
2025-08-04 19:38:41.5391 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardBulkRegister/Starter/eDoktor.Taikoban.SmartCardBulkRegister.vbe 
2025-08-04 19:38:41.5391 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardBulkRegister/Starter/ServerAddress.txt 
2025-08-04 19:38:41.5551 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardBulkRegister/Starter/version.txt 
2025-08-04 19:38:41.5551 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardRegister/version.txt 
2025-08-04 19:38:41.5551 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardRegister/1/eDoktor.Auth.ClientExtension.dll 
2025-08-04 19:38:41.5551 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardRegister/1/eDoktor.Auth.ClientExtension.dll.config 
2025-08-04 19:38:41.5751 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardRegister/1/eDoktor.Auth.Interprocess.dll 
2025-08-04 19:38:41.5751 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardRegister/1/eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:38:41.5950 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardRegister/1/eDoktor.Common.dll 
2025-08-04 19:38:41.6010 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardRegister/1/eDoktor.PCSC.dll 
2025-08-04 19:38:41.6010 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardRegister/1/eDoktor.Taikoban.AdminConsoleExtension.dll 
2025-08-04 19:38:41.6010 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardRegister/1/eDoktor.Taikoban.AdminConsoleExtension.dll.config 
2025-08-04 19:38:41.6010 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardRegister/1/eDoktor.Taikoban.AppCommon.dll 
2025-08-04 19:38:41.6010 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardRegister/1/eDoktor.Taikoban.AppCommon.dll.config 
2025-08-04 19:38:41.6171 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardRegister/1/eDoktor.Taikoban.AppExtension.dll 
2025-08-04 19:38:41.6171 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardRegister/1/eDoktor.Taikoban.AppExtension.dll.config 
2025-08-04 19:38:41.6171 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardRegister/1/eDoktor.Taikoban.RSACrypto.dll 
2025-08-04 19:38:41.6171 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardRegister/1/eDoktor.Taikoban.SmartCardRegister.exe 
2025-08-04 19:38:41.6171 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardRegister/1/eDoktor.Taikoban.SmartCardRegister.exe.config 
2025-08-04 19:38:41.6171 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardRegister/1/ope.png 
2025-08-04 19:38:41.6336 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardRegister/Starter/eDoktor.Taikoban.SmartCardRegister.cmd 
2025-08-04 19:38:41.6336 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardRegister/Starter/eDoktor.Taikoban.SmartCardRegister.vbe 
2025-08-04 19:38:41.6336 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardRegister/Starter/ServerAddress.txt 
2025-08-04 19:38:41.6336 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/SmartCardRegister/Starter/version.txt 
2025-08-04 19:38:41.6336 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/CopyToClientInstallerTaikoban.cmd 
2025-08-04 19:38:41.6336 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/eDoktor.Auth.Setup.dll.config 
2025-08-04 19:38:41.6336 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/TaikobanRun.cmd 
2025-08-04 19:38:41.6336 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/★更新時はClientInstallerにも適用すること.txt 
2025-08-04 19:38:41.6476 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/client_uninstaller.bat 
2025-08-04 19:38:41.6476 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.Agent.dll 
2025-08-04 19:38:41.6476 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.Agent.dll.config 
2025-08-04 19:38:41.6476 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.Api.dll 
2025-08-04 19:38:41.6476 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.Api.dll.config 
2025-08-04 19:38:41.6476 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.AuthenticationSchemes.dll 
2025-08-04 19:38:41.6636 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.AuthenticationSchemes.dll.config 
2025-08-04 19:38:41.6636 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.Client.dll 
2025-08-04 19:38:41.6636 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.Client.dll.config 
2025-08-04 19:38:41.6806 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.ClientExtension.dll 
2025-08-04 19:38:41.6806 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.ClientExtension.dll.config 
2025-08-04 19:38:41.6976 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.CredentialProvider32.dll 
2025-08-04 19:38:41.7187 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.CredentialProvider64.dll 
2025-08-04 19:38:41.7187 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.Desktop.dll 
2025-08-04 19:38:41.7272 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.Desktop.dll.config 
2025-08-04 19:38:41.7272 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.ExternalDependencies.dll 
2025-08-04 19:38:41.7272 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.ExternalDependencies.dll.config 
2025-08-04 19:38:41.7272 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.Interprocess.dll 
2025-08-04 19:38:41.7422 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:38:41.7422 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.LocalDatabase.dll 
2025-08-04 19:38:41.7422 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.Setup.dll 
2025-08-04 19:38:41.7422 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.Setup.dll.config 
2025-08-04 19:38:41.7642 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.SharedMemory.dll 
2025-08-04 19:38:41.7733 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.TimeRecorder.exe 
2025-08-04 19:38:41.7733 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.TimeRecorder.exe.config 
2025-08-04 19:38:41.7733 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.Update.dll 
2025-08-04 19:38:41.7733 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Auth.Update.dll.config 
2025-08-04 19:38:41.8607 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Common.dll 
2025-08-04 19:38:41.8607 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.ContentDisplay.API.exe 
2025-08-04 19:38:41.8607 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.ContentDisplay.API.exe.config 
2025-08-04 19:38:41.8677 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.ContentDispMessage.dll 
2025-08-04 19:38:41.8677 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Install.dll 
2025-08-04 19:38:41.8677 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.LocalServer.dll 
2025-08-04 19:38:41.8677 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.PCSC.dll 
2025-08-04 19:38:41.8867 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Taikoban.Agent.exe 
2025-08-04 19:38:41.8867 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Taikoban.Agent.exe.config 
2025-08-04 19:38:41.8867 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Taikoban.ClientService.exe 
2025-08-04 19:38:41.8977 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Taikoban.ClientService.exe.config 
2025-08-04 19:38:41.8977 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Taikoban.Interface.exe 
2025-08-04 19:38:41.8977 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Taikoban.Interface.exe.config 
2025-08-04 19:38:41.9227 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Taikoban.Setup.exe 
2025-08-04 19:38:41.9227 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Taikoban.Setup.exe.config 
2025-08-04 19:38:41.9323 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Taikoban.UpdateService.exe 
2025-08-04 19:38:41.9323 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Taikoban.UpdateService.exe.config 
2025-08-04 19:38:41.9323 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.Taikoban.VeinAuthMessage.dll 
2025-08-04 19:38:41.9323 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.VeinAuthClient.API.exe 
2025-08-04 19:38:41.9323 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.VeinAuthClient.API.exe.config 
2025-08-04 19:38:41.9323 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/eDoktor.VeinAuthLocalServer.dll 
2025-08-04 19:38:41.9453 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/InstallPath.txt 
2025-08-04 19:38:41.9453 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/logo.png 
2025-08-04 19:38:41.9453 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/register_contentDisplayComServer.bat 
2025-08-04 19:38:42.0013 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/SQLite.Interop.dll 
2025-08-04 19:38:42.0173 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/System.Data.SQLite.dll 
2025-08-04 19:38:42.0368 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/System.Data.SQLite.xml 
2025-08-04 19:38:42.0458 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/TaikobanAPI.exe 
2025-08-04 19:38:42.0458 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/TaikobanAPI.exe.config 
2025-08-04 19:38:42.0988 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/tile_v1_10.bmp 
2025-08-04 19:38:42.1404 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/tile_v1_7.bmp 
2025-08-04 19:38:42.1814 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/tile_v1_8.bmp 
2025-08-04 19:38:42.1814 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/unregister_contentDisplayComServer.bat 
2025-08-04 19:38:42.2279 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/wallpaper.png 
2025-08-04 19:38:42.2499 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/NonAuthorizer/eDoktor.Common.dll 
2025-08-04 19:38:42.2629 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/NonAuthorizer/eDoktor.Taikoban.NonAuthorizer.exe 
2025-08-04 19:38:42.2629 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/NonAuthorizer/eDoktor.Taikoban.NonAuthorizer.exe.config 
2025-08-04 19:38:42.2629 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/NonAuthorizer/eDoktor.Taikoban.VeinAuthMessage.dll 
2025-08-04 19:38:42.2799 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/ExternalAuth/NonAuthorizer/eDoktor.UI.dll 
2025-08-04 19:38:42.2799 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/LockDetailMessage/eDoktor.Auth.ClientExtension.dll 
2025-08-04 19:38:42.2799 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/LockDetailMessage/eDoktor.Auth.ClientExtension.dll.config 
2025-08-04 19:38:42.2989 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/LockDetailMessage/eDoktor.Auth.Interprocess.dll 
2025-08-04 19:38:42.2989 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/LockDetailMessage/eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:38:42.3219 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/LockDetailMessage/eDoktor.Common.dll 
2025-08-04 19:38:42.3219 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/LockDetailMessage/eDoktor.Taikoban.ContentDispMessage.dll 
2025-08-04 19:38:42.3219 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/LockDetailMessage/eDoktor.Taikoban.LockDetailMessage.exe 
2025-08-04 19:38:42.3219 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/LockDetailMessage/eDoktor.Taikoban.LockDetailMessage.exe.config 
2025-08-04 19:38:42.3354 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/Taikoban/101/DisplayAPI/LockDetailMessage/goDisplay.bat 
2025-08-04 19:38:42.3354 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryAllowOneFactorAuthentication/version.txt 
2025-08-04 19:38:42.3354 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryAllowOneFactorAuthentication/1/eDoktor.Auth.ClientExtension.dll 
2025-08-04 19:38:42.3354 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryAllowOneFactorAuthentication/1/eDoktor.Auth.ClientExtension.dll.config 
2025-08-04 19:38:42.3564 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryAllowOneFactorAuthentication/1/eDoktor.Auth.Interprocess.dll 
2025-08-04 19:38:42.3564 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryAllowOneFactorAuthentication/1/eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:38:42.3795 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryAllowOneFactorAuthentication/1/eDoktor.Common.dll 
2025-08-04 19:38:42.3824 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryAllowOneFactorAuthentication/1/eDoktor.Taikoban.AdminConsoleExtension.dll 
2025-08-04 19:38:42.3824 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryAllowOneFactorAuthentication/1/eDoktor.Taikoban.AdminConsoleExtension.dll.config 
2025-08-04 19:38:42.3824 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryAllowOneFactorAuthentication/1/eDoktor.Taikoban.AppCommon.dll 
2025-08-04 19:38:42.3824 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryAllowOneFactorAuthentication/1/eDoktor.Taikoban.AppCommon.dll.config 
2025-08-04 19:38:42.3824 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryAllowOneFactorAuthentication/1/eDoktor.Taikoban.AppExtension.dll 
2025-08-04 19:38:42.3824 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryAllowOneFactorAuthentication/1/eDoktor.Taikoban.AppExtension.dll.config 
2025-08-04 19:38:42.3994 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryAllowOneFactorAuthentication/1/eDoktor.Taikoban.RSACrypto.dll 
2025-08-04 19:38:42.4164 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryAllowOneFactorAuthentication/1/eDoktor.Taikoban.TemporaryAllowOneFactorAuthentication.exe 
2025-08-04 19:38:42.4164 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryAllowOneFactorAuthentication/1/eDoktor.Taikoban.TemporaryAllowOneFactorAuthentication.exe.config 
2025-08-04 19:38:42.4164 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryAllowOneFactorAuthentication/Starter/eDoktor.Taikoban.TemporaryAllowOneFactorAuthentication.cmd 
2025-08-04 19:38:42.4164 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryAllowOneFactorAuthentication/Starter/eDoktor.Taikoban.TemporaryAllowOneFactorAuthentication.vbe 
2025-08-04 19:38:42.4164 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryAllowOneFactorAuthentication/Starter/ServerAddress.txt 
2025-08-04 19:38:42.4164 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryAllowOneFactorAuthentication/Starter/version.txt 
2025-08-04 19:38:42.4300 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryCardIssuer/version.txt 
2025-08-04 19:38:42.4300 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryCardIssuer/1/eDoktor.Auth.ClientExtension.dll 
2025-08-04 19:38:42.4300 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryCardIssuer/1/eDoktor.Auth.ClientExtension.dll.config 
2025-08-04 19:38:42.4490 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryCardIssuer/1/eDoktor.Auth.Interprocess.dll 
2025-08-04 19:38:42.4490 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryCardIssuer/1/eDoktor.Auth.Interprocess.dll.config 
2025-08-04 19:38:42.4720 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryCardIssuer/1/eDoktor.Common.dll 
2025-08-04 19:38:42.4780 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryCardIssuer/1/eDoktor.PCSC.dll 
2025-08-04 19:38:42.4780 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryCardIssuer/1/eDoktor.Taikoban.AdminConsoleExtension.dll 
2025-08-04 19:38:42.4780 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryCardIssuer/1/eDoktor.Taikoban.AdminConsoleExtension.dll.config 
2025-08-04 19:38:42.4780 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryCardIssuer/1/eDoktor.Taikoban.AppCommon.dll 
2025-08-04 19:38:42.4780 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryCardIssuer/1/eDoktor.Taikoban.AppCommon.dll.config 
2025-08-04 19:38:42.4930 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryCardIssuer/1/eDoktor.Taikoban.AppExtension.dll 
2025-08-04 19:38:42.4930 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryCardIssuer/1/eDoktor.Taikoban.AppExtension.dll.config 
2025-08-04 19:38:42.4930 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryCardIssuer/1/eDoktor.Taikoban.RSACrypto.dll 
2025-08-04 19:38:42.5200 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryCardIssuer/1/eDoktor.Taikoban.TemporaryCardIssuer.exe 
2025-08-04 19:38:42.5240 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryCardIssuer/1/eDoktor.Taikoban.TemporaryCardIssuer.exe.config 
2025-08-04 19:38:42.5240 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryCardIssuer/1/ErrorImage.jpg 
2025-08-04 19:38:42.5240 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryCardIssuer/Starter/eDoktor.Taikoban.TemporaryCardIssuer.cmd 
2025-08-04 19:38:42.5240 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryCardIssuer/Starter/eDoktor.Taikoban.TemporaryCardIssuer.vbe 
2025-08-04 19:38:42.5240 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryCardIssuer/Starter/ServerAddress.txt 
2025-08-04 19:38:42.5395 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/01.サーバー/eDoktorUpdater/TemporaryCardIssuer/Starter/version.txt 
2025-08-04 19:38:42.5395 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/02.クライアント/ServerAddress.txt 
2025-08-04 19:38:42.5395 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/02.クライアント/TaikobanClinetSetup.cmd 
2025-08-04 19:38:42.5395 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/02.クライアント/TaikobanClinetSetup.vbe 
2025-08-04 19:38:42.5395 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/リリースノート.txt 
2025-08-04 19:38:42.5395 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/00.DBName/DBName.txt 
2025-08-04 19:38:42.5395 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/00.DBName/localhost.txt 
2025-08-04 19:38:42.5545 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/01.DB作成/CreateDB_Taikoban_2019.sql 
2025-08-04 19:38:42.5545 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/01.DB作成/CreateDB_Taikoban_2022.sql 
2025-08-04 19:38:42.5545 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/01.DB作成/db_create_win_2019.bat 
2025-08-04 19:38:42.5545 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/01.DB作成/db_create_win_2022.bat 
2025-08-04 19:38:42.5545 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_accounts.sql 
2025-08-04 19:38:42.5545 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_account_groups.sql 
2025-08-04 19:38:42.5695 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_account_invalidlogon.sql 
2025-08-04 19:38:42.5695 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_apis.sql 
2025-08-04 19:38:42.5695 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_api_authority.sql 
2025-08-04 19:38:42.5695 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_applications.sql 
2025-08-04 19:38:42.5695 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_autocomplete_settings.sql 
2025-08-04 19:38:42.5695 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_commands.sql 
2025-08-04 19:38:42.5695 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_command_holders.sql 
2025-08-04 19:38:42.5855 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_configs.sql 
2025-08-04 19:38:42.5855 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_deleted_terminal_statuses.sql 
2025-08-04 19:38:42.5855 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_enumeration_types.sql 
2025-08-04 19:38:42.5855 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_errors.sql 
2025-08-04 19:38:42.5855 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_events.sql 
2025-08-04 19:38:42.5855 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_jobs.sql 
2025-08-04 19:38:42.5855 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_lendings.sql 
2025-08-04 19:38:42.5855 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_licenses.sql 
2025-08-04 19:38:42.6035 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_log.sql 
2025-08-04 19:38:42.6035 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_password_history.sql 
2025-08-04 19:38:42.6035 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_privileges.sql 
2025-08-04 19:38:42.6035 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_privilege_templates.sql 
2025-08-04 19:38:42.6035 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_prohibited_passwords.sql 
2025-08-04 19:38:42.6035 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_services_status.sql 
2025-08-04 19:38:42.6175 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_smart_cards.sql 
2025-08-04 19:38:42.6175 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_smart_card_holders.sql 
2025-08-04 19:38:42.6175 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_sso_accounts.sql 
2025-08-04 19:38:42.6175 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_sso_account_systems.sql 
2025-08-04 19:38:42.6175 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_temp_valid_periods.sql 
2025-08-04 19:38:42.6175 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_terminals.sql 
2025-08-04 19:38:42.6321 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_terminal_configs.sql 
2025-08-04 19:38:42.6321 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_terminal_configs_backup.sql 
2025-08-04 19:38:42.6321 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_terminal_config_ties.sql 
2025-08-04 19:38:42.6321 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_terminal_groups.sql 
2025-08-04 19:38:42.6321 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_terminal_groups_properties.sql 
2025-08-04 19:38:42.6321 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_terminal_statuses.sql 
2025-08-04 19:38:42.6491 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_timerecorder_configs.sql 
2025-08-04 19:38:42.6491 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_usb_control_log.sql 
2025-08-04 19:38:42.6491 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_usb_control_master.sql 
2025-08-04 19:38:42.6491 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_users.sql 
2025-08-04 19:38:42.6491 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/create_t_vda_startup.sql 
2025-08-04 19:38:42.6491 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/02.テーブル作成/make_table_win.bat 
2025-08-04 19:38:42.6631 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_delete_t_terminal_statuses.sql 
2025-08-04 19:38:42.6631 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_insert_t_accounts.sql 
2025-08-04 19:38:42.6631 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_insert_t_log_invalidlogon.sql 
2025-08-04 19:38:42.6631 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_insert_t_log_logout.sql 
2025-08-04 19:38:42.6631 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_insert_t_password_history.sql 
2025-08-04 19:38:42.6631 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_insert_t_terminal_statuses.sql 
2025-08-04 19:38:42.6631 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_accounts.sql 
2025-08-04 19:38:42.6631 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_account_groups.sql 
2025-08-04 19:38:42.6791 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_apis.sql 
2025-08-04 19:38:42.6791 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_api_authority.sql 
2025-08-04 19:38:42.6791 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_applications.sql 
2025-08-04 19:38:42.6791 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_commands.sql 
2025-08-04 19:38:42.6791 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_command_holders.sql 
2025-08-04 19:38:42.6791 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_configs.sql 
2025-08-04 19:38:42.6791 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_errors.sql 
2025-08-04 19:38:42.6791 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_jobs.sql 
2025-08-04 19:38:42.6961 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_lendings.sql 
2025-08-04 19:38:42.6961 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_licenses.sql 
2025-08-04 19:38:42.6961 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_privileges.sql 
2025-08-04 19:38:42.6961 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_privilege_templates.sql 
2025-08-04 19:38:42.6961 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_services_status.sql 
2025-08-04 19:38:42.6961 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_smart_cards.sql 
2025-08-04 19:38:42.6961 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_smart_card_holders.sql 
2025-08-04 19:38:42.7111 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_terminals.sql 
2025-08-04 19:38:42.7111 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_terminal_configs.sql 
2025-08-04 19:38:42.7111 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_terminal_config_ties.sql 
2025-08-04 19:38:42.7111 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_terminal_groups.sql 
2025-08-04 19:38:42.7111 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_terminal_statuses.sql 
2025-08-04 19:38:42.7111 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_users.sql 
2025-08-04 19:38:42.7111 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/create_TR_update_t_vda_startup.sql 
2025-08-04 19:38:42.7266 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/03.トリガー作成/make_trigger.bat 
2025-08-04 19:38:42.7266 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/04.データ作成/insert_account_groups.sql 
2025-08-04 19:38:42.7266 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/04.データ作成/insert_admin_account.sql 
2025-08-04 19:38:42.7266 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/04.データ作成/insert_apis.sql 
2025-08-04 19:38:42.7266 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/04.データ作成/insert_applications.sql 
2025-08-04 19:38:42.7266 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/04.データ作成/insert_config.sql 
2025-08-04 19:38:42.7266 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/04.データ作成/insert_enumeration_types.sql 
2025-08-04 19:38:42.7266 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/04.データ作成/insert_jobs.sql 
2025-08-04 19:38:42.7426 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/04.データ作成/insert_privileges.sql 
2025-08-04 19:38:42.7426 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/04.データ作成/insert_terminal_groups.sql 
2025-08-04 19:38:42.7426 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/04.データ作成/insert_timerecorder_config.sql 
2025-08-04 19:38:42.7426 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/04.データ作成/make_data.bat 
2025-08-04 19:38:42.7426 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/04.データ作成/注意.txt 
2025-08-04 19:38:42.7426 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/05.ユーザー作成/CreateLogin_Taikoban.sql 
2025-08-04 19:38:42.7426 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/05.ユーザー作成/make_user.bat 
2025-08-04 19:38:42.7426 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/06.動作ポリシー作成/insert_terminal_config.sql 
2025-08-04 19:38:42.7426 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/06.動作ポリシー作成/insert_terminal_config_ties.sql 
2025-08-04 19:38:42.7586 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/06.動作ポリシー作成/make_terminal_config.bat 
2025-08-04 19:38:42.7586 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/07.ミラーリング/backup_primary.bat 
2025-08-04 19:38:42.7586 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/07.ミラーリング/backup_primary.sql 
2025-08-04 19:38:42.7586 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/07.ミラーリング/restore_secondary.bat 
2025-08-04 19:38:42.7586 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/01.Taikoban/07.ミラーリング/restore_secondary.sql 
2025-08-04 19:38:42.7586 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/旧バージョンからの更新の場合/Ver2.4.0.0から更新/alter_t_terminal_configs.txt 
2025-08-04 19:38:42.7586 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/旧バージョンからの更新の場合/Ver2.4.0.0から更新/alter_t_terminal_configs_backup.txt 
2025-08-04 19:38:42.7586 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/旧バージョンからの更新の場合/Ver2.4.0.0より前バージョンから更新/alter_t_terminal_configs.txt 
2025-08-04 19:38:42.7736 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/04.DBスクリプト/旧バージョンからの更新の場合/Ver2.4.0.0より前バージョンから更新/alter_t_terminal_configs_backup.txt 
2025-08-04 19:38:42.9487 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/05.Tools/sakura_install2-4-1-2849-x86.exe 
2025-08-04 19:38:43.2836 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/05.Tools/WinMerge-2.16.16-jp-4-x64-Setup.exe 
2025-08-04 19:38:43.3016 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/05.Tools/CryptoTool/eDoktor.Common.dll 
2025-08-04 19:38:43.3016 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/05.Tools/CryptoTool/NewTaikobanEncDecTOOL.exe 
2025-08-04 19:38:43.5503 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/05.Tools/runtime/vcredist_x64.exe 
2025-08-04 19:38:43.8166 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/05.Tools/runtime/vcredist_x86.exe 
2025-08-04 19:38:43.8471 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/99.ドキュメント/【Taikoban】代理入力検証ツール 操作マニュアル.xlsx 
2025-08-04 19:38:44.0864 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/99.ドキュメント/【Taikoban】構築手順書_v1.6.pdf 
2025-08-04 19:38:44.0864 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/99.ドキュメント/【Taikoban】設定メモ.txt 
2025-08-04 19:38:44.0864 [DEBUG] Dispenser.Utils.Logger - ファイルをZIPに追加: Taikoban/Taikoban_Ver.*******/ModuleBackup/BackupModule.cmd 
2025-08-04 19:38:44.0864 [INFO] Dispenser.Utils.Logger - ZIPファイルを作成しました。サイズ: 2310899 KB 
2025-08-04 19:38:44.0864 [INFO] Dispenser.Utils.Logger - パッケージ作成が完了しました。出力先: Output\20250804_永井検証サーバ_構築資源.zip 
2025-08-04 19:38:44.0864 [INFO] Dispenser.Utils.Logger - PDF収集処理を開始します 
2025-08-04 19:38:44.0864 [INFO] Dispenser.Utils.Logger - PDFテンプレートディレクトリ: N:\work\VSCode\Dispenser\resource\Manual 
2025-08-04 19:38:44.1023 [INFO] Dispenser.Utils.Logger - 必須PDFフォルダを追加: N:\work\VSCode\Dispenser\resource\Manual\Base 
2025-08-04 19:38:44.1023 [INFO] Dispenser.Utils.Logger - 条件付きPDFフォルダを追加: N:\work\VSCode\Dispenser\resource\Manual\Face (認証要素に「顔」が含まれているため) 
2025-08-04 19:38:44.1023 [INFO] Dispenser.Utils.Logger - 電子カルテ対応PDFフォルダを追加: N:\work\VSCode\Dispenser\resource\Manual\HX (連携電子カルテ: HX) 
2025-08-04 19:38:44.1023 [INFO] Dispenser.Utils.Logger - PDFファイル収集開始: N:\work\VSCode\Dispenser\resource\Manual\Base (1ファイル) 
2025-08-04 19:38:44.1728 [DEBUG] Dispenser.Utils.Logger - PDFファイルをコピー: 【Taikoban】構築手順書_v1.7.2.pdf → Base_【Taikoban】構築手順書_v1.7.2.pdf 
2025-08-04 19:38:44.1728 [INFO] Dispenser.Utils.Logger - フォルダ処理完了: N:\work\VSCode\Dispenser\resource\Manual\Base (1ファイル収集) 
2025-08-04 19:38:44.1728 [INFO] Dispenser.Utils.Logger - PDFファイル収集開始: N:\work\VSCode\Dispenser\resource\Manual\Face (1ファイル) 
2025-08-04 19:38:44.1878 [DEBUG] Dispenser.Utils.Logger - PDFファイルをコピー: 【Taikoban顔認証】サーバ構築手順書_v1.5.pdf → Face_【Taikoban顔認証】サーバ構築手順書_v1.5.pdf 
2025-08-04 19:38:44.1878 [INFO] Dispenser.Utils.Logger - フォルダ処理完了: N:\work\VSCode\Dispenser\resource\Manual\Face (1ファイル収集) 
2025-08-04 19:38:44.1878 [INFO] Dispenser.Utils.Logger - 情報: N:\work\VSCode\Dispenser\resource\Manual\HX 内にPDFファイルが見つかりませんでした 
2025-08-04 19:38:44.1948 [INFO] Dispenser.Utils.Logger - PDF収集結果サマリーをログファイルに出力しました 
2025-08-04 19:38:44.1948 [INFO] Dispenser.Utils.Logger - PDF収集が完了しました。出力先: Output\20250804_永井検証サーバ\手順書 
2025-08-04 19:38:44.1948 [INFO] Dispenser.Utils.Logger - Excel出力処理を開始します 
2025-08-04 19:38:44.7048 [INFO] Dispenser.Utils.Logger - Excel出力が完了しました。出力先: Output\20250804_永井検証サーバ\20250804_永井検証サーバ_案件管理情報.xlsx 
2025-08-04 19:38:46.7761 [INFO] Dispenser.Utils.Logger - 処理が正常に完了しました 
