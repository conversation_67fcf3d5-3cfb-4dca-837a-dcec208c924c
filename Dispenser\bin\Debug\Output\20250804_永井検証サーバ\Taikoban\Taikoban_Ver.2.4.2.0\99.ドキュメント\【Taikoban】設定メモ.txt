＜設定値＞
１．下記予約語を環境に合わせて一括置換する
　・sOEqwRggeOs5J+mrA1PTQP03iS2JvKkkwovlMRimh7NIGBkA6vsDZOSli1ja2eqMXHwU1svdQpoPpl+cAiF71W5rxEQCPpq4d1gvrg7XgSWUOxpzgXIYISoUrCN+yEURta0qlL+UpBogoGF6fBt4Nmo40KsnimIn6wmRkbT0ScNmukl9T8237Gw5Z3A5jBlOw71SlKGl0maMbRVGzGH8J5zm175t4SnPg1pNJkVvp+0= ⇒ SQLServer接続文字列(暗号化) (例)sOEqwRggeOs5J+mrA1PTQP03iS2JvKkkwovlMRimh7PioJrGO2N0LQdCrjcrbaEiWd5SqIGS4AaZWxBgwS/TmI0NGAmCYrH0j1X14G2kFVX/pnk/kfUdxcYY63ZXSa2MeMI9l8eOvEn3hAz09WQLH/ndQwPZ8qgvUgeEy3wRdMaP+/9xculNqgLmjv1T0uiv85y78ymkPIJoyy12h/V7YEuxp1z/dZL4pXcT5zsVBZw=
　・Network=DBMSSOCN;Data Source=*************;failover partner=*************;Initial Catalog=Taikoban;Integrated Security=False;User ID=Taikoban;Password=0Ax4CC(-U-gM ⇒ SQLServer接続文字列(平文) (例)Network=DBMSSOCN;Data Source=************;failover partner=************;Initial Catalog=Taikoban;Integrated Security=False;User ID=Taikoban;Password=0Ax4CC(-U-gM
　・*************|************* ⇒ 1号機、2号機のIPアドレス (例)************|************
　・************* ⇒ 1号機のIPアドレス
　・************* ⇒ 2号機のIPアドレス
　・************* ⇒ 3号機のIPアドレス
　・SV_Personal_nagai ⇒ 1号機のホスト名
　・SV_Personal_nagai. ⇒ 2号機のホスト名
　・D ⇒ インストールドライブ (例)D
　・D:\eDoktor\Taikoban ⇒ サーバーインストールフォルダ (例)D:\eDoktor\Taikoban
　・D:\eDoktor\Taikoban ⇒ クライアントインストールフォルダ (例)D:\eDoktor\Taikoban

　※バックアップファイルが作成された場合は削除する
