﻿// PromptPage.cs

using System;
using System.Windows.Forms;

namespace eDoktor.AutoComplete.AuthUI
{
	using Common.EventExtensions;

	partial class PromptPage : PageBase
	{
		#region Events
		public event EventHandler ShutdownButtonClick;
		public event EventHandler RestartButtonClick;
		#endregion

		#region Constructors
		public PromptPage()
		{
			InitializeComponent();
			ClickThroughControls = new Control[] { this, messageLabel };
		}
		#endregion

		#region Protected Methods
		protected override void OnVisibleChanged(EventArgs e)
		{
			base.OnVisibleChanged(e);

			if (Visible)
			{
				shutdownButton.Select();
			}
		}

		protected override bool ProcessDialogKey(Keys keyData)
		{
			try
			{
				if (((keyData & Keys.KeyCode) == Keys.Space) && ((keyData & (Keys.Alt | Keys.Control)) == Keys.None) && (ActiveControl is IButtonControl))
				{
					(ActiveControl as IButtonControl).PerformClick();
					return true;
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}

			return base.ProcessDialogKey(keyData);
		}
		#endregion

		#region Private Methods
		private void OnShutdownButtonClick(object sender, EventArgs e)
		{
			try
			{
				ShutdownButtonClick.SafeInvoke(this, e);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnRestartButtonClick(object sender, EventArgs e)
		{
			try
			{
				RestartButtonClick.SafeInvoke(this, e);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion
	}
}
