using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Automation;
using System.Runtime.InteropServices;

namespace eDoktor.AutoComplete.Agent
{
    public class AutomationElementScope : IDisposable
    {
        private readonly List<AutomationElement> _elements = new List<AutomationElement>();
        private readonly List<WeakReference> _weakReferences = new List<WeakReference>();
        private bool _disposed = false;
        private readonly int _maxElements;
        private static int _instanceCounter = 0;
        private readonly int _instanceId;

        public AutomationElementScope(int maxElements = 100)
        {
            _maxElements = maxElements;
            _instanceId = System.Threading.Interlocked.Increment(ref _instanceCounter);
            Common.Trace.OutputDebugTrace("[001] AutomationElementScope作成: ID={0}, MaxElements={1}", _instanceId, maxElements);
        }

        public void AddElement(AutomationElement element)
        {
            if (element != null && !_disposed)
            {
                Common.Trace.OutputDebugTrace("[002] 要素追加開始: ID={0}, 現在要素数={1}, 追加要素={2}", 
                    _instanceId, _elements.Count, element.GetHashCode());
                
                // より積極的な要素数制限
                if (_elements.Count >= _maxElements)
                {
                    Common.Trace.OutputDebugTrace("[003] 要素数上限到達: ID={0}, 削除実行前要素数={1}", 
                        _instanceId, _elements.Count);
                    RemoveOldestElements(_maxElements / 2);
                    Common.Trace.OutputDebugTrace("[004] 古い要素削除完了: ID={0}, 削除後要素数={1}", 
                        _instanceId, _elements.Count);
                }
                
                _elements.Add(element);
                _weakReferences.Add(new WeakReference(element));
                
                Common.Trace.OutputDebugTrace("[005] 要素追加完了: ID={0}, 総要素数={1}, 弱参照数={2}", 
                    _instanceId, _elements.Count, _weakReferences.Count);
                
                // 定期的なクリーンアップを追加
                if (_elements.Count % 50 == 0)
                {
                    Common.Trace.OutputDebugTrace("[006] 定期クリーンアップ開始: ID={0}, 要素数={1}", 
                        _instanceId, _elements.Count);
                    CleanupDeadReferences();
                    Common.Trace.OutputDebugTrace("[007] 定期クリーンアップ完了: ID={0}, 要素数={1}", 
                        _instanceId, _elements.Count);
                }
            }
            else
            {
                Common.Trace.OutputDebugTrace("[008] 要素追加スキップ: ID={0}, element=null={1}, disposed={2}", 
                    _instanceId, element == null, _disposed);
            }
        }

        public void AddElements(IEnumerable<AutomationElement> elements)
        {
            if (elements != null && !_disposed)
            {
                var elementArray = elements.Where(e => e != null).ToArray();
                Common.Trace.OutputDebugTrace("[009] 複数要素追加開始: ID={0}, 追加数={1}, 現在要素数={2}", 
                    _instanceId, elementArray.Length, _elements.Count);
                
                foreach (var element in elementArray)
                {
                    AddElement(element);
                }
                
                Common.Trace.OutputDebugTrace("[010] 複数要素追加完了: ID={0}, 最終要素数={1}", 
                    _instanceId, _elements.Count);
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                Common.Trace.OutputDebugTrace("[011] AutomationElementScope解放開始: ID={0}, 要素数={1}, 弱参照数={2}", 
                    _instanceId, _elements.Count, _weakReferences.Count);
                
                int comObjectCount = 0;
                int nonComObjectCount = 0;
                int releaseErrorCount = 0;
                
                for (int i = 0; i < _elements.Count; i++)
                {
                    var element = _elements[i];
                    try
                    {
                        if (element != null)
                        {
                            bool isComObject = System.Runtime.InteropServices.Marshal.IsComObject(element);
                            Common.Trace.OutputDebugTrace("[012] 要素解放処理: ID={0}, Index={1}, Hash={2}, IsCOM={3}", 
                                _instanceId, i, element.GetHashCode(), isComObject);
                            
                            if (isComObject)
                            {
                                comObjectCount++;
                                try
                                {
                                    int refCount = 0;
                                    while ((refCount = System.Runtime.InteropServices.Marshal.ReleaseComObject(element)) > 0)
                                    {
                                        Common.Trace.OutputDebugTrace("[013] COM解放中: ID={0}, Index={1}, RefCount={2}", 
                                            _instanceId, i, refCount);
                                    }
                                    Common.Trace.OutputDebugTrace("[014] COM解放完了: ID={0}, Index={1}", _instanceId, i);
                                }
                                catch (System.Runtime.InteropServices.InvalidComObjectException)
                                {
                                    Common.Trace.OutputDebugTrace("[015] COM既解放済み: ID={0}, Index={1}", _instanceId, i);
                                }
                            }
                            else
                            {
                                nonComObjectCount++;
                                try
                                {
                                    // 要素の有効性確認（内部リソース解放のため）
                                    var processId = element.Current.ProcessId;
                                    Common.Trace.OutputDebugTrace("[016] 非COM要素確認: ID={0}, Index={1}, ProcessId={2}", 
                                        _instanceId, i, processId);
                                    
                                    // リフレクションを使用してIDisposableかどうかを実行時に確認
                                    var elementType = element.GetType();
                                    var disposableInterface = elementType.GetInterface("System.IDisposable");
                                    if (disposableInterface != null)
                                    {
                                        var disposeMethod = elementType.GetMethod("Dispose", Type.EmptyTypes);
                                        if (disposeMethod != null)
                                        {
                                            disposeMethod.Invoke(element, null);
                                            Common.Trace.OutputDebugTrace("[017] 非COM要素Dispose実行: ID={0}, Index={1}", _instanceId, i);
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Common.Trace.OutputDebugTrace("[018] 非COM要素処理エラー: ID={0}, Index={1}, Error={2}", 
                                        _instanceId, i, ex.Message);
                                }
                            }
                        }
                        else
                        {
                            Common.Trace.OutputDebugTrace("[019] null要素スキップ: ID={0}, Index={1}", _instanceId, i);
                        }
                    }
                    catch (Exception ex)
                    {
                        releaseErrorCount++;
                        Common.Trace.OutputDebugTrace("[020] AutomationElement解放エラー: ID={0}, Index={1}, Error={2}", 
                            _instanceId, i, ex.Message);
                    }
                }
                
                Common.Trace.OutputDebugTrace("[021] 要素解放統計: ID={0}, COM={1}, 非COM={2}, エラー={3}", 
                    _instanceId, comObjectCount, nonComObjectCount, releaseErrorCount);
                
                _elements.Clear();
                _weakReferences.Clear();
                
                Common.Trace.OutputDebugTrace("[022] リスト解放完了: ID={0}", _instanceId);
                
                // 強制的なGC実行
                var beforeMemory = GC.GetTotalMemory(false);
                Common.Trace.OutputDebugTrace("[023] GC実行前メモリ: ID={0}, Memory={1}KB", 
                    _instanceId, beforeMemory / 1024);
                
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                
                var afterMemory = GC.GetTotalMemory(false);
                Common.Trace.OutputDebugTrace("[024] GC実行後メモリ: ID={0}, Memory={1}KB, 削減={2}KB", 
                    _instanceId, afterMemory / 1024, (beforeMemory - afterMemory) / 1024);
                
                Common.Trace.OutputDebugTrace("[025] AutomationElementScope解放完了: ID={0}", _instanceId);
                _disposed = true;
            }
        }

        public void CleanupDeadReferences()
        {
            if (_disposed) return;
            
            int beforeCount = _weakReferences.Count;
            Common.Trace.OutputDebugTrace("[026] 弱参照クリーンアップ開始: ID={0}, 弱参照数={1}", 
                _instanceId, beforeCount);
            
            int removedCount = 0;
            for (int i = _weakReferences.Count - 1; i >= 0; i--)
            {
                if (!_weakReferences[i].IsAlive)
                {
                    _weakReferences.RemoveAt(i);
                    if (i < _elements.Count)
                    {
                        _elements.RemoveAt(i);
                    }
                    removedCount++;
                }
            }
            
            Common.Trace.OutputDebugTrace("[027] 弱参照クリーンアップ完了: ID={0}, 削除数={1}, 残り弱参照={2}, 残り要素={3}", 
                _instanceId, removedCount, _weakReferences.Count, _elements.Count);
        }

        private void RemoveOldestElements(int count)
        {
            if (_elements.Count <= count) return;
            
            Common.Trace.OutputDebugTrace("[028] 古い要素削除開始: ID={0}, 削除予定数={1}, 現在要素数={2}", 
                _instanceId, count, _elements.Count);
            
            int actualRemoved = 0;
            for (int i = 0; i < count && _elements.Count > 0; i++)
            {
                var element = _elements[0];
                _elements.RemoveAt(0);
                actualRemoved++;
                
                Common.Trace.OutputDebugTrace("[029] 古い要素削除中: ID={0}, Index={1}, Hash={2}", 
                    _instanceId, i, element?.GetHashCode() ?? 0);
                
                TryReleaseElement(element);
            }
            
            // 対応する弱参照も削除
            if (_weakReferences.Count > count)
            {
                _weakReferences.RemoveRange(0, Math.Min(count, _weakReferences.Count));
            }
            
            Common.Trace.OutputDebugTrace("[030] 古い要素削除完了: ID={0}, 実削除数={1}, 残り要素数={2}", 
                _instanceId, actualRemoved, _elements.Count);
        }

        private void TryReleaseElement(AutomationElement element)
        {
            try
            {
                if (element != null)
                {
                    bool isComObject = System.Runtime.InteropServices.Marshal.IsComObject(element);
                    Common.Trace.OutputDebugTrace("[031] 個別要素解放開始: Hash={0}, IsCOM={1}", 
                        element.GetHashCode(), isComObject);
                    
                    if (isComObject)
                    {
                        var refCount = System.Runtime.InteropServices.Marshal.ReleaseComObject(element);
                        Common.Trace.OutputDebugTrace("[032] 個別COM解放: Hash={0}, RefCount={1}", 
                            element.GetHashCode(), refCount);
                    }
                    else
                    {
                        try
                        {
                            var processId = element.Current.ProcessId;
                            Common.Trace.OutputDebugTrace("[033] 個別非COM確認: Hash={0}, ProcessId={1}", 
                                element.GetHashCode(), processId);
                        }
                        catch (Exception ex)
                        {
                            Common.Trace.OutputDebugTrace("[034] 個別非COM確認エラー: Hash={0}, Error={1}", 
                                element.GetHashCode(), ex.Message);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Common.Trace.OutputDebugTrace("[035] 個別要素解放エラー: Hash={0}, Error={1}", 
                    element?.GetHashCode() ?? 0, ex.Message);
            }
        }
    }
}












