﻿// Server.cs

namespace eDoktor.AutoComplete.Interprocess
{
	using Common.EventExtensions;
	using System.Linq;

	public class Server : System.IDisposable
	{
		#region Fields
		private readonly object SyncRoot = new object();
		private TcpServer _tcpServer;
		private int _portNumber;
		private int _backlog;
		private int _maxAcceptableClientCount;
		#endregion

		#region Events
		public event System.EventHandler ClientConnected;
		public event System.EventHandler ClientDisconnected;
		public event System.EventHandler<Common.PacketEventArgs> PacketReceived;
		#endregion

		#region Constructors
		public Server(int portNumber, int backlog, int maxAcceptableClientCount)
		{
			_portNumber = portNumber;
			_backlog = backlog;
			_maxAcceptableClientCount = maxAcceptableClientCount;
		}
		#endregion

		#region IDisposable
		~Server()
		{
			Dispose(false);
		}

		public void Dispose()
		{
			Dispose(true);
			System.GC.SuppressFinalize(this);
		}

		protected virtual void Dispose(bool disposing)
		{
			Stop();

			if (disposing)
			{
				ClientConnected = null;
				ClientDisconnected = null;
				PacketReceived = null;
			}
		}
		#endregion

		#region Public Methods
		public void Start()
		{
			try
			{
				using (Common.TimedLock.Lock(SyncRoot))
				{
					Stop();

					_tcpServer = new TcpServer();
					_tcpServer.ClientConnected += OnClientConnected;
					_tcpServer.ClientDisconnected += OnClientDisconnected;
					_tcpServer.NonResponsePacketReceived += OnPacketReceived;

					if (_maxAcceptableClientCount > 0)
					{
						_tcpServer.MaxAcceptableClientCount = _maxAcceptableClientCount;
					}

					_tcpServer.Listen(_portNumber, _backlog, true, false, Configuration.ServerKeepAliveTime, Common.Network.DefaultKeepAliveInterval);
				}
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		public void Stop()
		{
			try
			{
				using (Common.TimedLock.Lock(SyncRoot))
				{
					if (_tcpServer != null)
					{
						_tcpServer.Dispose();
						_tcpServer = null;
					}
				}
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		// 生成済みのパケットのインスタンスを受け取ってブロードキャストするようなクロージャをactionに指定しないこと
		// （複数のクライアントに対してTranceiveする度に単一のパケットのインスタンスのシーケンス番号がインクリメントされ正常に返信を受信できない）
		public static void ForEach(System.Collections.Generic.IEnumerable<Common.TcpClient> clients, System.Action<Common.TcpClient> action)
		{
			if ((clients == null) || (!clients.Any()))
			{
				Common.Trace.OutputDebugTrace("clients is empty.");

				return;
			}
			
			if (action == null)
			{
				Common.Trace.OutputDebugTrace("action is empty.");

				return;
			}

			Common.TcpServer.ForEach(clients, action);
		}
		#endregion

		#region Private Methods
		private void OnClientConnected(object sender, System.EventArgs e)
		{
			ClientConnected.SafeInvoke(sender, e);
		}

		private void OnClientDisconnected(object sender, System.EventArgs e)
		{
			ClientDisconnected.SafeInvoke(sender, e);
		}

		private void OnPacketReceived(object sender, Common.PacketEventArgs e)
		{
			PacketReceived.SafeInvoke(sender, e);
		}
		#endregion
	}
}
