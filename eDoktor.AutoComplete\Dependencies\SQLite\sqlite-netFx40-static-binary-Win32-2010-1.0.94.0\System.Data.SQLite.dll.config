<?xml version="1.0"?>
<!--
 *
 * System.Data.SQLite.dll.config -
 *
 * Written by <PERSON>.
 * Released to the public domain, use at your own risk!
 *
-->
<configuration>
  <appSettings>
    <!--
        NOTE: If this configuration variable is set [to anything], the SQLite
              logging subsystem may be initialized in a non-default application
              domain.  By default, this is not allowed due to the potential
              for application domain unloading issues.
    -->
    <!--
    <add key="Force_SQLiteLog" value="1" />
    -->

    <!--
        NOTE: If this configuration variable is set [to anything], the native
              library pre-loading functionality will be disabled.  By default,
              the native library pre-loading will attempt to load the native
              SQLite library from architecture-specific (e.g. "x86", "amd64",
              "x64") or platform-specific (e.g. "Win32") directories that
              reside underneath the application base directory.
    -->
    <!--
    <add key="No_PreLoadSQLite" value="1" />
    -->

    <!--
        NOTE: If this configuration variable is set [to anything], the new
              connection string parsing algorithm will not be used.  This
              environment variable is intended for use with legacy code only.
    -->
    <!--
    <add key="No_SQLiteConnectionNewParser" value="1" />
    -->

    <!--
        NOTE: If this configuration variable is set [to anything], the initial
              search for types in all loaded assemblies that are tagged with
              the SQLiteFunction attribute will be skipped.  Normally, this
              search is conducted only once per application domain by the
              static constructor of the SQLiteFunction class; however, these
              implementation details are subject to change.
    -->
    <!--
    <add key="No_SQLiteFunctions" value="1" />
    -->

    <!--
        NOTE: If this configuration variable is set [to anything], it will be
              used instead of the application base directory by the native
              library pre-loader.  This environment variable can be especially
              useful in ASP.NET and other hosted environments where direct
              control of the location of the managed assemblies is not under
              the control of the application.
    -->
    <!--
    <add key="PreLoadSQLite_BaseDirectory" value="" />
    -->

    <!--
        NOTE: If this configuration variable is set [to anything], it will be
              used instead of the processor architecture value contained in the
              PROCESSOR_ARCHITECTURE environment variable to help build the
              path of the native library to pre-load.
    -->
    <!--
    <add key="PreLoadSQLite_ProcessorArchitecture" value="x86" />
    -->

    <!--
        NOTE: If this environment variable is set [to anything], the native
              library pre-loading code will skip conducting a search for the
              native library to pre-load.  By default, the search starts in the
              location of the currently executing assembly (i.e. the assembly
              containing all the managed components for System.Data.SQLite) and
              then falls back to the application domain base directory.
    -->
    <!--
    <add key="PreLoadSQLite_NoSearchForDirectory" value="1" />
    -->

    <!--
        NOTE: If this configuration variable is set [to anything], the location
              of the currently executing assembly (i.e. the one containing all
              the managed components for System.Data.SQLite) will be used as
              the basis for locating the the native library to pre-load (i.e.
              instead of using the application domain base directory).
    -->
    <!--
    <add key="PreLoadSQLite_UseAssemblyDirectory" value="1" />
    -->

    <!--
        NOTE: This configuration variable is normally set by the operating
              system itself and should reflect the native processor
              architecture of the current process (e.g. a 32-bit x86
              application running on a 64-bit x64 operating system should have
              the value "x86").
    -->
    <!--
    <add key="PROCESSOR_ARCHITECTURE" value="%PROCESSOR_ARCHITECTURE%" />
    -->

    <!--
        NOTE: If this environment variable is set [to anything], it will be
              used by the System.Data.SQLite.SQLiteFactory class as the type
              name containing the System.Data.Common.DbProviderServices
              implementation that should be used.
    -->
    <!--
    <add key="TypeName_SQLiteProviderServices" value="" />
    -->

    <!--
        NOTE: If this environment variable is set [to anything], it will be
              used by the System.Data.SQLite.SQLiteConvert class as the default
              DbType value that should be used when a per-connection value is
              not available.
    -->
    <!--
    <add key="Use_SQLiteConvert_DefaultDbType" value="Object" />
    -->

    <!--
        NOTE: If this environment variable is set [to anything], it will be
              used by the System.Data.SQLite.SQLiteConvert class as the default
              type name that should be used when a per-connection value is not
              available.
    -->
    <!--
    <add key="Use_SQLiteConvert_DefaultTypeName" value="" />
    -->
  </appSettings>
</configuration>
