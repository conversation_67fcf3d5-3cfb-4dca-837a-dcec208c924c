﻿namespace eDoktor.AutoComplete.AuthUI
{
	partial class PromptPage
	{
		/// <summary> 
		/// 必要なデザイナー変数です。
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary> 
		/// 使用中のリソースをすべてクリーンアップします。
		/// </summary>
		/// <param name="disposing">マネージ リソースが破棄される場合 true、破棄されない場合は false です。</param>
		protected override void Dispose(bool disposing)
		{
			if (disposing && (components != null))
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		#region コンポーネント デザイナーで生成されたコード

		/// <summary> 
		/// デザイナー サポートに必要なメソッドです。このメソッドの内容を 
		/// コード エディターで変更しないでください。
		/// </summary>
		private void InitializeComponent()
		{
			this.messageLabel = new System.Windows.Forms.Label();
			this.restartButton = new eDoktor.UI.ImageButton();
			this.shutdownButton = new eDoktor.UI.ImageButton();
			this.SuspendLayout();
			// 
			// messageLabel
			// 
			this.messageLabel.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
			this.messageLabel.Font = new System.Drawing.Font("Yu Gothic UI", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
			this.messageLabel.Location = new System.Drawing.Point(50, 70);
			this.messageLabel.Margin = new System.Windows.Forms.Padding(0);
			this.messageLabel.Name = "messageLabel";
			this.messageLabel.Size = new System.Drawing.Size(500, 220);
			this.messageLabel.TabIndex = 0;
			this.messageLabel.Text = "Enterキーを押してください。";
			this.messageLabel.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
			// 
			// restartButton
			// 
			this.restartButton.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
			this.restartButton.BackColor = System.Drawing.Color.Transparent;
			this.restartButton.BackgroundImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_4;
			this.restartButton.DefaultImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_4;
			this.restartButton.DefaultTextColor = System.Drawing.Color.White;
			this.restartButton.DialogResult = System.Windows.Forms.DialogResult.None;
			this.restartButton.DisabledImage = null;
			this.restartButton.DisabledTextColor = System.Drawing.Color.Empty;
			this.restartButton.FocusImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_5;
			this.restartButton.FocusTextColor = System.Drawing.Color.White;
			this.restartButton.ForeColor = System.Drawing.SystemColors.ControlText;
			this.restartButton.HoverImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_6;
			this.restartButton.HoverTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(140)))), ((int)(((byte)(177)))));
			this.restartButton.LabelText = "再起動";
			this.restartButton.Location = new System.Drawing.Point(325, 286);
			this.restartButton.Margin = new System.Windows.Forms.Padding(0);
			this.restartButton.Name = "restartButton";
			this.restartButton.PressedImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_6;
			this.restartButton.PressedTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(140)))), ((int)(((byte)(177)))));
			this.restartButton.Size = new System.Drawing.Size(195, 44);
			this.restartButton.TabIndex = 2;
			this.restartButton.TextFont = new System.Drawing.Font("Yu Gothic UI", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
			this.restartButton.TextMargin = new System.Windows.Forms.Padding(0);
			this.restartButton.Click += new System.EventHandler(this.OnRestartButtonClick);
			// 
			// shutdownButton
			// 
			this.shutdownButton.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.shutdownButton.BackColor = System.Drawing.Color.Transparent;
			this.shutdownButton.BackgroundImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_1;
			this.shutdownButton.DefaultImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_1;
			this.shutdownButton.DefaultTextColor = System.Drawing.Color.White;
			this.shutdownButton.DialogResult = System.Windows.Forms.DialogResult.None;
			this.shutdownButton.DisabledImage = null;
			this.shutdownButton.DisabledTextColor = System.Drawing.Color.Empty;
			this.shutdownButton.FocusImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_2;
			this.shutdownButton.FocusTextColor = System.Drawing.Color.White;
			this.shutdownButton.ForeColor = System.Drawing.SystemColors.ControlText;
			this.shutdownButton.HoverImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_3;
			this.shutdownButton.HoverTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(140)))), ((int)(((byte)(177)))));
			this.shutdownButton.LabelText = "シャットダウン";
			this.shutdownButton.Location = new System.Drawing.Point(80, 286);
			this.shutdownButton.Margin = new System.Windows.Forms.Padding(0);
			this.shutdownButton.Name = "shutdownButton";
			this.shutdownButton.PressedImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_3;
			this.shutdownButton.PressedTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(140)))), ((int)(((byte)(177)))));
			this.shutdownButton.Size = new System.Drawing.Size(195, 44);
			this.shutdownButton.TabIndex = 1;
			this.shutdownButton.TextFont = new System.Drawing.Font("Yu Gothic UI", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
			this.shutdownButton.TextMargin = new System.Windows.Forms.Padding(0);
			this.shutdownButton.Click += new System.EventHandler(this.OnShutdownButtonClick);
			// 
			// PromptPage
			// 
			this.Controls.Add(this.restartButton);
			this.Controls.Add(this.shutdownButton);
			this.Controls.Add(this.messageLabel);
			this.Name = "PromptPage";
			this.ResumeLayout(false);

		}

		#endregion

		private System.Windows.Forms.Label messageLabel;
		private UI.ImageButton shutdownButton;
		private UI.ImageButton restartButton;
	}
}
