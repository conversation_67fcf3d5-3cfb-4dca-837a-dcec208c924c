<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <appSettings>
        <add key="ResponseWaitingTimeout" value="10000"/>
        <!-- FaceServer -->
		<!-- 認証サーバー -->
        <add key="FaceServerAddressList" value="*************"/>
		<!-- ローカルサーバー -->
		<!-- 
		<add key="FaceServerAddressList" value="127.0.0.1"/>
		-->
        <add key="FaceServerPortNumber" value="49601"/>
        <add key="FaceServerBacklog" value="10"/>
        <add key="FaceServerMaxAcceptableClientCount" value="2500"/>
    </appSettings>
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2"/>
    </startup>
</configuration>
