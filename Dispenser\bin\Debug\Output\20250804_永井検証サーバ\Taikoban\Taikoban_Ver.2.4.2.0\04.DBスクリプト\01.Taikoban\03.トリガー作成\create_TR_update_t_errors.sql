USE [$(Param1)]
GO

/****** Object:  Trigger [dbo].[TR_update_t_errors]    Script Date: 10/06/2014 15:45:32 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*** CREATE TRIGGER ***/
IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[TR_update_t_errors]') AND type in (N'TR'))
DROP TRIGGER [dbo].[TR_update_t_errors]
GO

CREATE TRIGGER [dbo].[TR_update_t_errors] ON [dbo].[t_errors]
FOR UPDATE
AS 
BEGIN
UPDATE t_errors
SET modification_datetime = GETDATE()
WHERE id IN (SELECT id FROM inserted)
END
RETURN


GO

