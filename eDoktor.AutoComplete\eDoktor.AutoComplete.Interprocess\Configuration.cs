﻿// Configuration.cs

namespace eDoktor.AutoComplete.Interprocess
{
	public static class Configuration
	{
		#region Fields
		private const int DefaultResponseWaitingTimeout = 2000;
		private const int DefaultLoginResponseWaitingTimeout = 10000;
		private const bool DefaultEnableVdiClientStatusCheck = false;
		private const int DefaultServerPortNumber = 49001;
		private const int DefaultServerBacklog = 100;
		private const int DefaultServerMaxAcceptableClientCount = 2500;
		private const int DefaultServerKeepAliveTime = 60000;
		private const int DefaultClientPortNumber = 49002;
		private const int DefaultClientBacklog = 10;
		private const int DefaultClientMaxAcceptableClientCount = 20;
		private const int DefaultClientKeepAliveTime = 60000;
		private static int _responseWaitingTimeout;
		private static int _loginResponseWaitingTimeout;
		private static bool _enableVdiClientStatusCheck;
		private static MachineNameType _machineNameType;
		private static string _machineName;
		private static string[] _serverAddressList;
		private static int _serverPortNumber;
		private static int _serverBacklog;
		private static int _serverMaxAcceptableClientCount;
		private static uint _serverKeepAliveTime;
		private static int _clientPortNumber;
		private static int _clientBacklog;
		private static int _clientMaxAcceptableClientCount;
		private static uint _clientKeepAliveTime;
		#endregion

		#region Properties
		public static int ResponseWaitingTimeout
		{
			get { return _responseWaitingTimeout; }
		}

		public static int LoginResponseWaitingTimeout
		{
			get { return _loginResponseWaitingTimeout; }
		}

		public static bool EnableVdiClientStatusCheck
		{
			get { return _enableVdiClientStatusCheck; }
		}

		public static MachineNameType MachineNameType
		{
			get { return _machineNameType; }
		}

		public static string MachineName
		{
			get { return _machineName; }
		}

		public static string[] ServerAddressList
		{
			get { return _serverAddressList; }
		}

		public static int ServerPortNumber
		{
			get { return _serverPortNumber; }
		}

		public static int ServerBacklog
		{
			get { return _serverBacklog; }
		}

		public static int ServerMaxAcceptableClientCount
		{
			get { return _serverMaxAcceptableClientCount; }
		}

		public static uint ServerKeepAliveTime
		{
			get { return _serverKeepAliveTime; }
		}

		public static int ClientPortNumber
		{
			get { return _clientPortNumber; }
		}

		public static int ClientBacklog
		{
			get { return _clientBacklog; }
		}

		public static int ClientMaxAcceptableClientCount
		{
			get { return _clientMaxAcceptableClientCount; }
		}

		public static uint ClientKeepAliveTime
		{
			get { return _clientKeepAliveTime; }
		}
		#endregion

		#region Constructors
		static Configuration()
		{
			try
			{
				_responseWaitingTimeout = Common.Configuration.AppSetting("ResponseWaitingTimeout", DefaultResponseWaitingTimeout);
				_loginResponseWaitingTimeout = Common.Configuration.AppSetting("LoginResponseWaitingTimeout", DefaultLoginResponseWaitingTimeout);
				_enableVdiClientStatusCheck = Common.Configuration.AppSetting("EnableVdiClientStatusCheck", DefaultEnableVdiClientStatusCheck);
				_machineNameType = (Interprocess.MachineNameType)Common.Configuration.AppSetting("MachineNameType", (int)Interprocess.MachineNameType.Fqdn);

				switch (_machineNameType)
				{
					case Interprocess.MachineNameType.HostName:
						_machineName = Common.Network.GetHostName();
						break;
					case Interprocess.MachineNameType.NetBiosName:
						_machineName = Common.Network.GetNetBiosName();
						break;
					default:
						_machineName = Common.Network.GetFQDNOrNetBIOSName();
						break;
				}

				_serverAddressList = Common.Configuration.AppSettingArray("ServerAddressList", Common.Configuration.DefaultSeparator, System.StringSplitOptions.RemoveEmptyEntries);
				_serverPortNumber = Common.Configuration.AppSetting("ServerPortNumber", DefaultServerPortNumber);
				_serverBacklog = Common.Configuration.AppSetting("ServerBacklog", DefaultServerBacklog);
				_serverMaxAcceptableClientCount = Common.Configuration.AppSetting("ServerMaxAcceptableClientCount", DefaultServerMaxAcceptableClientCount);
				_serverKeepAliveTime = (uint)Common.Configuration.AppSetting("ServerKeepAliveTime", DefaultServerKeepAliveTime);
				_clientPortNumber = Common.Configuration.AppSetting("ClientPortNumber", DefaultClientPortNumber);
				_clientBacklog = Common.Configuration.AppSetting("ClientBacklog", DefaultClientBacklog);
				_clientMaxAcceptableClientCount = Common.Configuration.AppSetting("ClinetMaxAcceptableClientCount", DefaultClientMaxAcceptableClientCount);
				_clientKeepAliveTime = (uint)Common.Configuration.AppSetting("ClientKeepAliveTime", DefaultClientKeepAliveTime);
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion
	}
}
