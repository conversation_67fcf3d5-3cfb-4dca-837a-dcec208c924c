<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<configSections>
		<sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
			<section name="eDoktor.Taikoban.FaceAuthServer.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
		</sectionGroup>
	</configSections>
	<applicationSettings>
		<eDoktor.Taikoban.FaceAuthServer.Properties.Settings>
			<setting name="ConnectionStringSettingsLabel" serializeAs="String">
				<value>Taikoban</value>
			</setting>
		</eDoktor.Taikoban.FaceAuthServer.Properties.Settings>
	</applicationSettings>
	<connectionStrings>
		<!-- 顔認証サーバー -->
		<add name="Taikoban" connectionString="m4kpKqc/ty+QumI4nu+5dOAUnv2K8/Hbn/b8vLK9jJ6+3f/fNuv4wRZkTaIdSXDHBclD8UFXikkGk+ZinEZ76xK4CiBGF6TrjEtDXa7htTtv3ysVnlasXJAfpZmkamYhmkMTC8ENzGdt02jd/U0xVA==" providerName="System.Data.SqlClient"/>
		
		<!-- ローカルサーバー 
		<add name="Taikoban" connectionString="+ap8Z5YfhTkJu570YsTJa3fOPf+3IhLwu1IlPxpgsMtc2Y/b/yOLjxmqIJG5QzKFkuHQWoHC6zoCWgnMMp/13LqXznkvwGWVAU3D5NV08Jg=" providerName="System.Data.SqlClient"/>
		-->
	</connectionStrings>
	<appSettings>

		<!-- DBコネクトリトライ回数 -->
		<add key="MaxFaceRetryCount" value="3"/>

		<!-- パスワード暗号化有無 -->
		<add key="PasswordEncryption" value="true"/>

		<!-- DBの認証ログにテンプレートデータを登録するかどうか -->
		<add key="IsRegistThumbnailLog" value="false"/>

		<!-- マスクありと判断するマスクスコア値 ※DBの設定値に含めたい-->
		<add key="MaskScoreThresholdForWearingMask" value="50"/>

		<add key="ClientSettingsProvider.ServiceUri" value=""/>
	</appSettings>
	<startup>
		<supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2"/>
	</startup>
	<system.web>
		<membership defaultProvider="ClientAuthenticationMembershipProvider">
			<providers>
				<add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri=""/>
			</providers>
		</membership>
		<roleManager defaultProvider="ClientRoleProvider" enabled="true">
			<providers>
				<add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400"/>
			</providers>
		</roleManager>
	</system.web>
</configuration>
