﻿// AuthModuleBase.cs

using System;
using System.Threading;

namespace eDoktor.AutoComplete.AuthModule
{
	public abstract class AuthModuleBase : MarshalByRefObject, IAuthModule, IDisposable
	{
		#region Fields
		private readonly object SyncRoot = new object();
		private CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();
		private volatile bool _performingAuthentication;
		#endregion

		#region IDisposable
		~AuthModuleBase()
		{
			Dispose(false);
		}

		public void Dispose()
		{
			Dispose(true);
			GC.SuppressFinalize(this);
		}

		protected virtual void Dispose(bool disposing)
		{
			try
			{
				using (Common.TimedLock.Lock(SyncRoot))
				{
					if (_cancellationTokenSource != null)
					{
						_cancellationTokenSource.Dispose();
						_cancellationTokenSource = null;
					}
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion

		#region IAuthModule
		public AuthResponse PerformAuthentication(AuthRequest authRequest)
		{
			AuthResponse authResponse = null;

			try
			{
				var cancellationToken = CancellationToken.None;

				lock (SyncRoot)
				{
					if (_performingAuthentication)
					{
						throw new InvalidOperationException();
					}

					if (_cancellationTokenSource == null)
					{
						throw new ObjectDisposedException(typeof(AuthModuleBase).FullName);
					}

					_performingAuthentication = true;
					cancellationToken = _cancellationTokenSource.Token;
				}

				authResponse = DoPerformAuthentication(authRequest, cancellationToken);
			}
			catch (Exception ex)
			{
				if (ex.GetType().IsSerializable)
				{
					throw;
				}

				authResponse = new AuthResponse() { Result = AuthResultType.SystemFault };
			}
			finally
			{
				lock (SyncRoot)
				{
					_performingAuthentication = false;
				}
			}

			return authResponse;
		}

		public void Abort()
		{
			try
			{
				lock (SyncRoot)
				{
					if (_cancellationTokenSource != null)
					{
						_cancellationTokenSource.Cancel();
					}
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion

		#region Protected Methods
		public override object InitializeLifetimeService()
		{
			return null;
		}

		protected abstract AuthResponse DoPerformAuthentication(AuthRequest authRequest, CancellationToken cancellationToken);
		#endregion
	}
}
