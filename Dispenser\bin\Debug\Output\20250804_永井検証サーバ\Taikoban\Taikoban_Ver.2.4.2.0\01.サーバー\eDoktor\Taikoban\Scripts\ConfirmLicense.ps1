# �萔�ݒ�
[int]$sourceNumber = 15
[double]$margin = 1.1
[int]$debug = 1
[string]$connectionString = "Network=DBMSSOCN;Data Source=*************;failover partner=*************;Initial Catalog=Taikoban;Integrated Security=False;User ID=<PERSON><PERSON>ban;Password=************"   # �ڑ�������
[string]$executionPath = "D:\eDoktor\Taikoban\Scripts\" # ���s�p�X
[string]$logPath = $executionPath + "Logs\confirm_license.log" # ���O�t�@�C���p�X

# �ϐ���ݒ肷��
[string]$errorMessage = ""  # �G���[���b�Z�[�W

if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " �����J�n") | Out-File $logPath }

try
{
    # ��ƃf�B���N�g����ړ�����
    Set-Location $executionPath

    # �֐���ǂݍ���
    . ".\Common.ps1"

    # DLL�Q�Ƃ�ǉ�����
    Add-Type -Path (Convert-Path ".\eDoktor.Taikoban.RSACrypto.dll")
}
catch [Exception]
{
    # �G���[�ʒm�ɓo�^
    if ($_.exception -ne "") { $errorMessage = $_.exception }
    Notificate-Error $sourceNumber ([string]::Format("�����ݒ�ŃG���[���������܂���: Exception=" + $errorMessage))
    
    return
}

if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " ������������") | Out-File $logPath -Append }

# �R���t�B�O���烉�C�Z���X���擾����
try
{
    [System.Data.DataTable]$configs = Get-DatabaseData $connectionString "SELECT * FROM t_configs ORDER BY id DESC"
    [string]$licensees = $configs.Rows[0]["licensees"]
}
catch [Exception]
{
    # �G���[�ʒm�ɓo�^
    if ($_.exception -ne "") { $errorMessage = $_.exception }
    Notificate-Error $sourceNumber ([string]::Format("���C�Z���X��擾�ŃG���[���������܂���: Exception=" + $errorMessage))
    
    return
}

if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " ���C�Z���X��擾����") | Out-File $logPath -Append }

# �X�e�[�^�X�e�[�u�����烉�C�Z���X�Ώے[������擾����
[System.Data.DataTable]$statuses = Get-DatabaseData $connectionString "SELECT COUNT(*) AS target_count FROM t_terminal_statuses"
$targetCount = $statuses.Rows[0]["target_count"]

# ���C�Z���X�f�[�^��擾����
[System.Data.DataTable]$licenses = Get-DatabaseData $connectionString "SELECT * FROM t_licenses ORDER BY id"

# �L�����C�Z���X����v�Z����
try
{
    [int]$licenseNumberSum = 0
    $identifier = @()
    foreach ($license in $licenses)
    {
        [string]$licenseString = $license["license"]
        
        try
        {
            [string]$licenseDecrypted = [eDoktor.Taikoban.RSA.Crypto]::Decryptstring($licenseString)
        }
        catch [Exception]
        {
            # �G���[�ʒm�ɓo�^
            if ($_.exception -ne "") { $errorMessage = $_.exception }
            Notificate-Error $sourceNumber ([string]::Format("���C�Z���X�f�[�^�̕������ŃG���[���������܂���: Exception=" + $errorMessage))
            
            continue
        }
        
        [array]$licenseSplited = $licenseDecrypted -Split ","
        
        # ���ʎq�m�F
        if ($identifier -contains $licenseSplited[0])
        {
            continue
        }
        else
        {
            $identifier += $licenseSplited[0]
        }
        
        # ���C�Z���X��m�F
        if ($licenseSplited[1] -ne $licensees)
        {
            continue
        }
        
        # �����m�F
        [Datetime]$startDate = [Datetime]::MinValue
        [Datetime]$endDate = [Datetime]::MinValue
        if (-not [System.Datetime]::TryParse($licenseSplited[2], [ref]$startDate) )
        {
            continue
        }
        
        if (-not [System.Datetime]::TryParse($licenseSplited[3], [ref]$endDate))
        {
            continue
        }
        
        if ((Get-Date) -le (Get-Date -Date $startDate.ToString("yyyy/MM/dd")) -or (Get-Date -Date $endDate.AddDays(1).ToString("yyyy/MM/dd")) -lt (Get-Date))
        {
            continue
        }
        
        # ���C�Z���X���m�F
        [int]$licenseNumber = 0
        if (-not [int]::TryParse([string]$licenseSplited[4], [ref]$licenseNumber))
        {
            continue
        }
        
        $licenseNumberSum += $licenseNumber
    }
}
catch [Exception]
{
    # �G���[�ʒm�ɓo�^
    if ($_.exception -ne "") { $errorMessage = $_.exception }
    Notificate-Error $sourceNumber ([string]::Format("�L�����C�Z���X���v�Z�ŃG���[���������܂���: Exception=" + $errorMessage))
    
    return
}

if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " �L�����C�Z���X���擾����(" + $targetCount + "/" + $licenseNumberSum + ")") | Out-File $logPath -Append }

# ���C�Z���X������؂���
try
{
    if ($targetCount -gt $licenseNumberSum * $margin)
    {
        Notificate-Error $sourceNumber ([string]::Format("�ғ��[�������C�Z���X����I�[�o�[���Ă��܂�: �ғ��[����={0}, �L�����C�Z���X��={1}", $targetCount, $licenseNumberSum))
    }
}
catch [Exception]
{
    # �G���[�ʒm�ɓo�^
    if ($_.exception -ne "") { $errorMessage = $_.exception }
    Notificate-Error $sourceNumber ([string]::Format("���C�Z���X�����؂ŃG���[���������܂���: Exception=" + $errorMessage))
    
    return
}

if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " ���C�Z���X�����؊���") | Out-File $logPath -Append }
