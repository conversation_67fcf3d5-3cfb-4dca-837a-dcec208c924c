﻿// Crypto.cs

namespace eDoktor.Taikoban.AccountImporter
{
	public static class Crypto
	{

        #region Fields
        private static readonly eDoktor.Common.CryptoParams CryptoParams;
        #endregion

        #region Constructors
        static Crypto()
        {
            try
            {
                const string algorithmName = "Rijndael";
                const string password = "=o8^2a<~\"/R*";
                const string salt = "E-/~mo?Z";
                const int iterationCount = 1226;
                const int keySize = 256;
                const int blockSize = 128;

                byte[] key = null;
                byte[] iv = null;
                eDoktor.Common.Crypto.DeriveKeyFromPassword(password, salt, iterationCount, keySize, out key, blockSize, out iv);
                CryptoParams = new eDoktor.Common.CryptoParams();
                CryptoParams.AlgorithmName = algorithmName;
                CryptoParams.KeySize = keySize;
                CryptoParams.Key = key;
                CryptoParams.BlockSize = blockSize;
                CryptoParams.IV = iv;
            }
            catch (System.Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }
        #endregion

        public static string DecryptString(string text)
        {
            return eDoktor.Common.Crypto.Decrypt(text, CryptoParams);
        }
        public static string EncryptString(string text)
        {
            return eDoktor.Common.Crypto.Encrypt(text, CryptoParams);
        }
	}
}
