﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <connectionStrings>
        <!-- 別々のnameを指定する事で複数の接続文字列を定義できます。appSettingsのConnectionStringNameのvalueに実際に使用する接続文字列のnameを指定してください。 -->
		<add name ="Taikoban" connectionString="+ap8Z5YfhTkJu570YsTJa97eWDO/MZw3Qy8ksleTyKxcl3K5z0zJWpmZrp7p/0B4n1VK71G7p0xaEnOIQ90gsZV+fXYcU1KBJdROv3rrfPI=" providerName="System.Data.SqlClient" />
		<add name ="Taikoban2" connectionString="+ap8Z5YfhTkJu570YsTJa97eWDO/MZw3Qy8ksleTyKxcl3K5z0zJWpmZrp7p/0B4h1iriHAlC29Eta7HqanDLAgRu9SBRQvFKexoMs2Pp9FhKZuBqxigMueN1FXksOZx" providerName="System.Data.SqlClient" />
	</connectionStrings>
    <appSettings>
        <!-- EncryptedPasswords -->
        <add key="AccountPasswordEncryptionEnabled" value="false" />
        <add key="SsoPasswordEncryptionEnabled" value="false" />
        <add key="TerminalPasswordEncryptionEnabled" value="false" />
        <add key="TerminalConfigPasswordEncryptionEnabled" value="false" />
        <add key="CooperativePasswordEncryptionEnabled" value="false" />
        <!-- Connection String -->
        <add key="ConnectionStringName" value="Taikoban2" />
        <!-- LDAP -->
        <add key="LdapServer" value="" />
        <!-- <add key="LdapDomain" value="DC=edoktor,DC=local" /> -->
        <add key="LdapDomain" value="" />
        <!-- 出退勤打刻 -->
        <add key="TimeClockEnabled" value="false" />
    </appSettings>
</configuration>