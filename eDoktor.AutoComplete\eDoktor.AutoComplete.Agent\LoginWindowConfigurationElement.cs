﻿// LoginWindowConfigurationElement.cs

using System.Configuration;

namespace eDoktor.AutoComplete.Agent
{
	class LoginWindowConfigurationElement : ConfigurationElement
	{
		[ConfigurationProperty("name")]
		public string Name
		{
			get { return (string)this["name"]; }
			set { this["name"] = value; }
		}

		[ConfigurationProperty("processName")]
		public string ProcessName
		{
			get { return (string)this["processName"]; }
			set { this["processName"] = value; }
		}

		[ConfigurationProperty("targetWindowClassName")]
		public string TargetWindowClassName
		{
			get { return (string)this["targetWindowClassName"]; }
			set { this["targetWindowClassName"] = value; }
		}

		[ConfigurationProperty("targetWindowCaption")]
		public string TargetWindowCaption
		{
			get { return (string)this["targetWindowCaption"]; }
			set { this["targetWindowCaption"] = value; }
		}

		[ConfigurationProperty("userElementIndex")]
		public int UserElementIndex
		{
			get { return (int)this["userElementIndex"]; }
			set { this["userElementIndex"] = value; }
		}

		[ConfigurationProperty("userElementAutomationId")]
		public string UserElementAutomationId
		{
			get { return (string)this["userElementAutomationId"]; }
			set { this["userElementAutomationId"] = value; }
		}

		[ConfigurationProperty("userElementClassName")]
		public string UserElementClassName
		{
			get { return (string)this["userElementClassName"]; }
			set { this["userElementClassName"] = value; }
		}

		[ConfigurationProperty("userElementCaption")]
		public string UserElementCaption
		{
			get { return (string)this["userElementCaption"]; }
			set { this["userElementCaption"] = value; }
		}

		[ConfigurationProperty("passwordElementIndex")]
		public int PasswordElementIndex
		{
			get { return (int)this["passwordElementIndex"]; }
			set { this["passwordElementIndex"] = value; }
		}

		[ConfigurationProperty("passwordElementAutomationId")]
		public string PasswordElementAutomationId
		{
			get { return (string)this["passwordElementAutomationId"]; }
			set { this["passwordElementAutomationId"] = value; }
		}

		[ConfigurationProperty("passwordElementClassName")]
		public string PasswordElementClassName
		{
			get { return (string)this["passwordElementClassName"]; }
			set { this["passwordElementClassName"] = value; }
		}

		[ConfigurationProperty("passwordElementCaption")]
		public string PasswordElementCaption
		{
			get { return (string)this["passwordElementCaption"]; }
			set { this["passwordElementCaption"] = value; }
		}

		[ConfigurationProperty("passwordElementIsPassword")]
		public bool PasswordElementIsPassword
		{
			get { return (bool)this["passwordElementIsPassword"]; }
			set { this["passwordElementIsPassword"] = value; }
		}

		[ConfigurationProperty("buttonElementIndex")]
		public int ButtonElementIndex
		{
			get { return (int)this["buttonElementIndex"]; }
			set { this["buttonElementIndex"] = value; }
		}

		[ConfigurationProperty("buttonElementAutomationId")]
		public string ButtonElementAutomationId
		{
			get { return (string)this["buttonElementAutomationId"]; }
			set { this["buttonElementAutomationId"] = value; }
		}

		[ConfigurationProperty("buttonElementClassName")]
		public string ButtonElementClassName
		{
			get { return (string)this["buttonElementClassName"]; }
			set { this["buttonElementClassName"] = value; }
		}

		[ConfigurationProperty("buttonElementCaption")]
		public string ButtonElementCaption
		{
			get { return (string)this["buttonElementCaption"]; }
			set { this["buttonElementCaption"] = value; }
		}

		[ConfigurationProperty("buttonClickMethod")]
		public int ButtonClickMethod
		{
			get { return (int)this["buttonClickMethod"]; }
			set { this["buttonClickMethod"] = value; }
		}

		[ConfigurationProperty("buttonClickKeyCode")]
		public int ButtonClickKeyCode
		{
			get { return (int)this["buttonClickKeyCode"]; }
			set { this["buttonClickKeyCode"] = value; }
		}

		[ConfigurationProperty("buttonClickX")]
		public int ButtonClickX
		{
			get { return (int)this["buttonClickX"]; }
			set { this["buttonClickX"] = value; }
		}

		[ConfigurationProperty("buttonClickY")]
		public int ButtonClickY
		{
			get { return (int)this["buttonClickY"]; }
			set { this["buttonClickY"] = value; }
		}

		[ConfigurationProperty("variable")]
		public bool Variable
		{
			get { return (bool)this["variable"]; }
			set { this["variable"] = value; }
		}
	}
}
