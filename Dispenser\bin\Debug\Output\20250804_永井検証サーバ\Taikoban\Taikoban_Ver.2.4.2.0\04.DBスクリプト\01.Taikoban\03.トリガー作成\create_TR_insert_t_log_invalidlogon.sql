USE [$(Param1)]
GO

/****** Object:  Trigger [dbo].[TR_insert_t_log_invalidlogon]    Script Date: 2018/02/22 11:14:08 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*** CREATE TRIGGER ***/
IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[TR_insert_t_log_invalidlogon]') AND type in (N'TR'))
DROP TRIGGER [dbo].[TR_insert_t_log_invalidlogon]
GO

CREATE TRIGGER [dbo].[TR_insert_t_log_invalidlogon] ON [dbo].[t_log]
FOR INSERT
AS 
BEGIN
    /* 
     * パラメータ
     */
    -- アカウントロックの認証エラー閾値(5を設定すると認証エラー5回目でアカウントロックを行います)
    DECLARE @LockTh int

   -- 認証エラーの保持時間（秒）
    DECLARE @KeepRecSec int
    
    -- 管理設定から設定値を取得する
    SELECT TOP(1) @LockTh = auth_error_threshold, @KeepRecSec = auth_error_retention_period FROM t_configs ORDER BY id DESC

    /* 
     * メイン処理
     */
    DECLARE @Logon_Id nvarchar(255)
    DECLARE @Logon_Datetime datetime2(7)
    DECLARE @Log_Type int
    DECLARE @Log_Sub_Type int
    DECLARE @Org_Id nvarchar(255)
    DECLARE @User_Name nvarchar(255)
    DECLARE @Error_Count int = -1
    DECLARE @PrevError_Datetime datetime2(7) = GETDATE()

    -- =========================================================================
    -- 初期処理
    -- =========================================================================
    -- トリガーの値を変数に格納
    SELECT @Logon_Id = logon_id
          ,@Logon_Datetime = output_datetime
          ,@Log_Type = log_type
          ,@Log_Sub_Type = log_sub_type
      FROM inserted

    -- 組織ID、氏名を取得
    SELECT @Org_Id = organizational_id
          ,@User_Name = name
      FROM t_users t1
          ,t_accounts t2
     WHERE t1.id = t2.user_id
       AND t2.logon_id = @Logon_Id

    -- 現在の認証エラー回数を取得
    SELECT @Error_Count = error_count
          ,@PrevError_Datetime = update_datetime
      FROM t_account_invalidlogon
     WHERE logon_id = @Logon_Id

    -- テーブルに該当ユーザーのログがあるかをチェック
    IF (@Error_Count = -1 And @Logon_Id <> '')
    BEGIN
        -- レコードないので件数0でデータ登録（回数チェックは以降で実施）
        SET @Error_Count = 0
        INSERT INTO t_account_invalidlogon 
            (logon_id, error_count, update_datetime)
        VALUES
            (@Logon_Id, @Error_Count, @Logon_Datetime)
    END

    -- =========================================================================
    -- 認証OKの場合の処理
    -- =========================================================================
    -- ログイン、ロック解除、モバイルログインの場合は認証エラー回数をリセット
    --   3:ログイン
    --   7:ロック解除
    --  18:モバイルログイン
    IF (@Log_Type in (3, 7, 18))
    BEGIN
        -- 認証エラー回数をリセット
        SET @Error_Count = 0

        -- 認証エラー回数を更新
        UPDATE t_account_invalidlogon 
           SET error_count = @Error_Count
              ,update_datetime = @Logon_Datetime
         WHERE logon_id = @Logon_Id
    END

    -- =========================================================================
    -- 認証NGの場合の処理
    -- =========================================================================
    -- パスワード認証エラーのみ対象
    --  171:パスワード間違い(Windows)
    --  173:パスワード間違い(iOS)
    IF (@Log_Sub_Type in (171, 173))
    BEGIN
        -- 前回の認証エラーが認証エラー保持期間を超えていた場合はエラー回数をリセット
        IF (DATEADD(second, @KeepRecSec, @PrevError_Datetime) < GETDATE())
        BEGIN
            SET @Error_Count = 0
        END

        -- エラーをカウントアップしロック条件のチェックを行う。
        SET @Error_Count = @Error_Count +1

        IF (@Error_Count >= @LockTh)
        BEGIN
            -- アカウントをロックします。
            UPDATE t_accounts
               SET invalidated = 1 -- 1:無効
                  ,invalidation_datetime = @Logon_Datetime
                  ,invalidated_by = 1 -- 1:edoktor(システムユーザ)
             WHERE logon_id = @Logon_Id

            -- エラーテーブル(t_errors)にログを出力
            INSERT INTO t_errors
                ([registration_datetime]
                ,[registered_by]
                ,[modification_datetime]
                ,[modified_by]
                ,[source]
                ,[status]
                ,[alarm_flg]
                ,[message]
                ,[notes])
            VALUES
                (@Logon_Datetime
                ,1         -- 1:edoktor(システムユーザ)
                ,@Logon_Datetime
                ,0         -- 0:t_errorsの既存データより0が登録されているようなので。
                ,101       -- 101:アカウントロックを表します。100番以降を順次利用してます。定義表はWikiへ
                ,0         -- 0:未処理
                ,0         -- 0:t_errorsの既存データより0が登録されているようなので。
                ,'【アカウントロック】規定回数の認証に失敗したためアカウントをロックしました。[職員ID]' + @Org_Id + '[ユーザID]' + @Logon_Id + '[氏名]' + @User_Name
                ,'')

            -- 認証エラー回数をリセット
            SET @Error_Count = 0
        END

        -- 認証エラー回数を更新
        IF (@Error_Count <= 1)
            UPDATE t_account_invalidlogon 
               SET error_count = @Error_Count
                  ,update_datetime = @Logon_Datetime
             WHERE logon_id = @Logon_Id
        ELSE
            UPDATE t_account_invalidlogon 
               SET error_count = @Error_Count
             WHERE logon_id = @Logon_Id
    END
END


GO