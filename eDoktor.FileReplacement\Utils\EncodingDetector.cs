using System;
using System.IO;
using System.Text;

namespace eDoktor.FileReplacement.Utils
{
    /// <summary>
    /// ファイルの文字コード検出ユーティリティ
    /// </summary>
    public static class EncodingDetector
    {
        /// <summary>
        /// ファイルの文字コードを検出
        /// </summary>
        /// <param name="filePath">ファイルパス</param>
        /// <returns>検出された文字コード</returns>
        public static Encoding DetectEncoding(string filePath)
        {
            try
            {
                var bytes = File.ReadAllBytes(filePath);

                if (bytes.Length == 0)
                    return Encoding.GetEncoding("Shift_JIS"); // 空ファイルもShift_JISとして扱う

                // BOMチェックで文字コードを判定
                if (bytes.Length >= 3 && bytes[0] == 0xEF && bytes[1] == 0xBB && bytes[2] == 0xBF)
                {
                    return Encoding.UTF8;
                }
                else if (bytes.Length >= 2 && bytes[0] == 0xFF && bytes[1] == 0xFE)
                {
                    return Encoding.Unicode; // UTF-16 LE
                }
                else if (bytes.Length >= 2 && bytes[0] == 0xFE && bytes[1] == 0xFF)
                {
                    return Encoding.BigEndianUnicode; // UTF-16 BE
                }
                else if (bytes.Length >= 4 && bytes[0] == 0x00 && bytes[1] == 0x00 && bytes[2] == 0xFE && bytes[3] == 0xFF)
                {
                    return Encoding.UTF32; // UTF-32 BE
                }
                else if (bytes.Length >= 4 && bytes[0] == 0xFF && bytes[1] == 0xFE && bytes[2] == 0x00 && bytes[3] == 0x00)
                {
                    return new UTF32Encoding(false, true); // UTF-32 LE
                }

                // BOMがない場合の詳細判定
                var encoding = DetectEncodingWithoutBom(bytes);
                return encoding;
            }
            catch (Exception)
            {
                return Encoding.GetEncoding("Shift_JIS"); // エラー時もShift_JISを返す
            }
        }

        /// <summary>
        /// BOMなしファイルの文字コード検出
        /// </summary>
        /// <param name="bytes">ファイルのバイト配列</param>
        /// <returns>検出された文字コード</returns>
        private static Encoding DetectEncodingWithoutBom(byte[] bytes)
        {
            // 1. UTF-8の検証（より厳密に）
            if (IsValidUtf8Strict(bytes))
            {
                return new UTF8Encoding(false); // UTF-8 without BOM
            }

            // 2. 日本語文字の存在チェック
            if (ContainsJapaneseCharacters(bytes))
            {
                // 日本語が含まれている場合、Shift_JISの可能性が高い
                if (IsValidShiftJis(bytes))
                {
                    return Encoding.GetEncoding("Shift_JIS");
                }

                // Shift_JISで読めない場合はEUC-JPを試す
                if (IsValidEucJp(bytes))
                {
                    return Encoding.GetEncoding("EUC-JP");
                }
            }

            // 3. ASCII文字のみの場合
            if (IsAsciiOnly(bytes))
            {
                return Encoding.ASCII;
            }

            // 4. デフォルトはShift_JIS（日本語環境）
            return Encoding.GetEncoding("Shift_JIS");
        }

        /// <summary>
        /// バイト配列が有効なUTF-8かどうかを判定（厳密版）
        /// </summary>
        /// <param name="bytes">バイト配列</param>
        /// <returns>有効なUTF-8の場合true</returns>
        private static bool IsValidUtf8Strict(byte[] bytes)
        {
            try
            {
                var decoder = Encoding.UTF8.GetDecoder();
                var chars = new char[bytes.Length];
                decoder.Convert(bytes, 0, bytes.Length, chars, 0, chars.Length, true, out _, out _, out bool completed);

                if (!completed) return false;

                // UTF-8として読めても、実際に日本語文字が含まれているかチェック
                var text = Encoding.UTF8.GetString(bytes);

                // 制御文字や不正文字が多い場合はUTF-8ではない可能性
                var controlCharCount = 0;
                foreach (char c in text)
                {
                    if (char.IsControl(c) && c != '\r' && c != '\n' && c != '\t')
                    {
                        controlCharCount++;
                    }
                }

                // 制御文字が10%以上ある場合は不正とみなす
                return (double)controlCharCount / text.Length < 0.1;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 日本語文字が含まれているかチェック
        /// </summary>
        /// <param name="bytes">バイト配列</param>
        /// <returns>日本語文字が含まれている場合true</returns>
        private static bool ContainsJapaneseCharacters(byte[] bytes)
        {
            // Shift_JISの日本語文字範囲をチェック
            for (int i = 0; i < bytes.Length - 1; i++)
            {
                var b1 = bytes[i];
                var b2 = bytes[i + 1];

                // Shift_JISの2バイト文字範囲
                if ((b1 >= 0x81 && b1 <= 0x9F) || (b1 >= 0xE0 && b1 <= 0xFC))
                {
                    if ((b2 >= 0x40 && b2 <= 0x7E) || (b2 >= 0x80 && b2 <= 0xFC))
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// Shift_JISとして有効かチェック
        /// </summary>
        /// <param name="bytes">バイト配列</param>
        /// <returns>有効なShift_JISの場合true</returns>
        private static bool IsValidShiftJis(byte[] bytes)
        {
            try
            {
                var sjis = Encoding.GetEncoding("Shift_JIS");
                var text = sjis.GetString(bytes);

                // 再エンコードして元のバイト配列と比較
                var reencoded = sjis.GetBytes(text);

                // 完全一致でなくても、大部分が一致していればOK
                int matchCount = 0;
                int minLength = Math.Min(bytes.Length, reencoded.Length);

                for (int i = 0; i < minLength; i++)
                {
                    if (bytes[i] == reencoded[i])
                        matchCount++;
                }

                return (double)matchCount / minLength > 0.9; // 90%以上一致
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// EUC-JPとして有効かチェック
        /// </summary>
        /// <param name="bytes">バイト配列</param>
        /// <returns>有効なEUC-JPの場合true</returns>
        private static bool IsValidEucJp(byte[] bytes)
        {
            try
            {
                var eucjp = Encoding.GetEncoding("EUC-JP");
                var text = eucjp.GetString(bytes);
                var reencoded = eucjp.GetBytes(text);

                int matchCount = 0;
                int minLength = Math.Min(bytes.Length, reencoded.Length);

                for (int i = 0; i < minLength; i++)
                {
                    if (bytes[i] == reencoded[i])
                        matchCount++;
                }

                return (double)matchCount / minLength > 0.9;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// ASCII文字のみかチェック
        /// </summary>
        /// <param name="bytes">バイト配列</param>
        /// <returns>ASCII文字のみの場合true</returns>
        private static bool IsAsciiOnly(byte[] bytes)
        {
            foreach (byte b in bytes)
            {
                if (b > 127) return false;
            }
            return true;
        }
    }
}
