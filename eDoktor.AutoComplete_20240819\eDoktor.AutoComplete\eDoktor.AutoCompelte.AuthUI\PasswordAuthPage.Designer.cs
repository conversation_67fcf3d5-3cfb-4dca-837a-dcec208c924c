﻿namespace eDoktor.AutoComplete.AuthUI
{
    partial class PasswordAuthPage
    {
        /// <summary> 
        /// 必要なデザイナー変数です。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 使用中のリソースをすべてクリーンアップします。
        /// </summary>
        /// <param name="disposing">マネージ リソースが破棄される場合 true、破棄されない場合は false です。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region コンポーネント デザイナーで生成されたコード

        /// <summary> 
        /// デザイナー サポートに必要なメソッドです。このメソッドの内容を 
        /// コード エディターで変更しないでください。
        /// </summary>
        private void InitializeComponent()
        {
            this.passwordTextBox = new eDoktor.UI.ImageTextBox();
            this.cancelButton = new eDoktor.UI.ImageButton();
            this.okButton = new eDoktor.UI.ImageButton();
            this.label1 = new System.Windows.Forms.Label();
            this.userNameLabel = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.capsLockLabel = new System.Windows.Forms.Label();
            this.SuspendLayout();
            // 
            // passwordTextBox
            // 
            this.passwordTextBox.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Left | System.Windows.Forms.AnchorStyles.Right)));
            this.passwordTextBox.BackColor = System.Drawing.Color.Transparent;
            this.passwordTextBox.BackgroundImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_form_2;
            this.passwordTextBox.DefaultImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_form_2;
            this.passwordTextBox.DefaultTextColor = System.Drawing.Color.Empty;
            this.passwordTextBox.DisabledImage = null;
            this.passwordTextBox.DisabledTextColor = System.Drawing.Color.Empty;
            this.passwordTextBox.FocusImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_form_2;
            this.passwordTextBox.FocusTextColor = System.Drawing.Color.Empty;
            this.passwordTextBox.Location = new System.Drawing.Point(113, 196);
            this.passwordTextBox.Margin = new System.Windows.Forms.Padding(0);
            this.passwordTextBox.Name = "passwordTextBox";
            this.passwordTextBox.PasswordChar = '●';
            this.passwordTextBox.PlaceholderText = "パスワード";
            this.passwordTextBox.Size = new System.Drawing.Size(374, 50);
            this.passwordTextBox.TabIndex = 1;
            this.passwordTextBox.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.passwordTextBox.TextFont = new System.Drawing.Font("Yu Gothic UI", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
            this.passwordTextBox.TextMargin = new System.Windows.Forms.Padding(50, 0, 0, 0);
            this.passwordTextBox.UseSystemPasswordChar = true;
            // 
            // cancelButton
            // 
            this.cancelButton.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.cancelButton.BackColor = System.Drawing.Color.Transparent;
            this.cancelButton.BackgroundImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_7;
            this.cancelButton.DefaultImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_7;
            this.cancelButton.DefaultTextColor = System.Drawing.Color.White;
            this.cancelButton.DialogResult = System.Windows.Forms.DialogResult.None;
            this.cancelButton.DisabledImage = null;
            this.cancelButton.DisabledTextColor = System.Drawing.Color.Empty;
            this.cancelButton.FocusImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_8;
            this.cancelButton.FocusTextColor = System.Drawing.Color.White;
            this.cancelButton.ForeColor = System.Drawing.SystemColors.ControlText;
            this.cancelButton.HoverImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_9;
            this.cancelButton.HoverTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(140)))), ((int)(((byte)(177)))));
            this.cancelButton.LabelText = "キャンセル";
            this.cancelButton.Location = new System.Drawing.Point(325, 286);
            this.cancelButton.Margin = new System.Windows.Forms.Padding(0);
            this.cancelButton.Name = "cancelButton";
            this.cancelButton.PressedImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_9;
            this.cancelButton.PressedTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(140)))), ((int)(((byte)(177)))));
            this.cancelButton.Size = new System.Drawing.Size(195, 44);
            this.cancelButton.TabIndex = 3;
            this.cancelButton.TextFont = new System.Drawing.Font("Yu Gothic UI", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
            this.cancelButton.TextMargin = new System.Windows.Forms.Padding(0);
            this.cancelButton.Click += new System.EventHandler(this.OnCancelButtonClick);
            // 
            // okButton
            // 
            this.okButton.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.okButton.BackColor = System.Drawing.Color.Transparent;
            this.okButton.BackgroundImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_7;
            this.okButton.DefaultImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_7;
            this.okButton.DefaultTextColor = System.Drawing.Color.White;
            this.okButton.DialogResult = System.Windows.Forms.DialogResult.None;
            this.okButton.DisabledImage = null;
            this.okButton.DisabledTextColor = System.Drawing.Color.Empty;
            this.okButton.FocusImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_8;
            this.okButton.FocusTextColor = System.Drawing.Color.White;
            this.okButton.ForeColor = System.Drawing.SystemColors.ControlText;
            this.okButton.HoverImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_9;
            this.okButton.HoverTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(140)))), ((int)(((byte)(177)))));
            this.okButton.LabelText = "OK";
            this.okButton.Location = new System.Drawing.Point(80, 286);
            this.okButton.Margin = new System.Windows.Forms.Padding(0);
            this.okButton.Name = "okButton";
            this.okButton.PressedImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_9;
            this.okButton.PressedTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(140)))), ((int)(((byte)(177)))));
            this.okButton.Size = new System.Drawing.Size(195, 44);
            this.okButton.TabIndex = 2;
            this.okButton.TextFont = new System.Drawing.Font("Yu Gothic UI", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
            this.okButton.TextMargin = new System.Windows.Forms.Padding(0);
            this.okButton.Click += new System.EventHandler(this.OnOKButtonClick);
            // 
            // label1
            // 
            this.label1.Font = new System.Drawing.Font("Yu Gothic UI", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
            this.label1.Location = new System.Drawing.Point(0, 40);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(600, 51);
            this.label1.TabIndex = 4;
            this.label1.Text = "ユーザー情報を入力してください。";
            this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.label1.Visible = false;
            // 
            // userNameLabel
            // 
            this.userNameLabel.Font = new System.Drawing.Font("Yu Gothic UI", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
            this.userNameLabel.Location = new System.Drawing.Point(168, 127);
            this.userNameLabel.Name = "userNameLabel";
            this.userNameLabel.Size = new System.Drawing.Size(319, 32);
            this.userNameLabel.TabIndex = 5;
            this.userNameLabel.Text = "ここにユーザー名が入ります";
            // 
            // label2
            // 
            this.label2.Font = new System.Drawing.Font("Yu Gothic UI", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
            this.label2.Location = new System.Drawing.Point(113, 127);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(58, 32);
            this.label2.TabIndex = 6;
            this.label2.Text = "ID : ";
            // 
            // capsLockLabel
            // 
            this.capsLockLabel.AutoSize = true;
            this.capsLockLabel.Font = new System.Drawing.Font("Yu Gothic UI", 9F, System.Drawing.FontStyle.Bold);
            this.capsLockLabel.Location = new System.Drawing.Point(215, 255);
            this.capsLockLabel.Name = "capsLockLabel";
            this.capsLockLabel.Size = new System.Drawing.Size(169, 15);
            this.capsLockLabel.TabIndex = 7;
            this.capsLockLabel.Text = "CapsLockキーがオンになっています";
            // 
            // PasswordAuthPage
            // 
            this.Controls.Add(this.capsLockLabel);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.userNameLabel);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.okButton);
            this.Controls.Add(this.cancelButton);
            this.Controls.Add(this.passwordTextBox);
            this.Name = "PasswordAuthPage";
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private UI.ImageTextBox passwordTextBox;
        private UI.ImageButton cancelButton;
        private UI.ImageButton okButton;
        private System.Windows.Forms.Label label1;
        public System.Windows.Forms.Label userNameLabel;
        public System.Windows.Forms.Label label2;
        public System.Windows.Forms.Label capsLockLabel;
    }
}
