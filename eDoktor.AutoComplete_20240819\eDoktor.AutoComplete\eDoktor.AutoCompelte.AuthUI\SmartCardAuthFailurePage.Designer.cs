﻿namespace eDoktor.AutoComplete.AuthUI
{
    partial class SmartCardAuthFailurePage
    {
        /// <summary> 
        /// 必要なデザイナー変数です。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 使用中のリソースをすべてクリーンアップします。
        /// </summary>
        /// <param name="disposing">マネージ リソースが破棄される場合 true、破棄されない場合は false です。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region コンポーネント デザイナーで生成されたコード

        /// <summary> 
        /// デザイナー サポートに必要なメソッドです。このメソッドの内容を 
        /// コード エディターで変更しないでください。
        /// </summary>
        private void InitializeComponent()
        {
            this.messageLabel = new System.Windows.Forms.Label();
            this.okButton = new eDoktor.UI.ImageButton();
            this.SuspendLayout();
            // 
            // messageLabel
            // 
            this.messageLabel.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.messageLabel.Font = new System.Drawing.Font("Yu Gothic UI", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
            this.messageLabel.Location = new System.Drawing.Point(50, 70);
            this.messageLabel.Margin = new System.Windows.Forms.Padding(0);
            this.messageLabel.Name = "messageLabel";
            this.messageLabel.Size = new System.Drawing.Size(500, 220);
            this.messageLabel.TabIndex = 0;
            this.messageLabel.Text = "無効なカードです。";
            this.messageLabel.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // okButton
            // 
            this.okButton.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.okButton.BackColor = System.Drawing.Color.Transparent;
            this.okButton.BackgroundImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_7;
            this.okButton.DefaultImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_7;
            this.okButton.DefaultTextColor = System.Drawing.Color.White;
            this.okButton.DialogResult = System.Windows.Forms.DialogResult.None;
            this.okButton.DisabledImage = null;
            this.okButton.DisabledTextColor = System.Drawing.Color.Empty;
            this.okButton.FocusImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_8;
            this.okButton.FocusTextColor = System.Drawing.Color.White;
            this.okButton.ForeColor = System.Drawing.SystemColors.ControlText;
            this.okButton.HoverImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_9;
            this.okButton.HoverTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(140)))), ((int)(((byte)(177)))));
            this.okButton.LabelText = "OK";
            this.okButton.Location = new System.Drawing.Point(203, 286);
            this.okButton.Margin = new System.Windows.Forms.Padding(0);
            this.okButton.Name = "okButton";
            this.okButton.PressedImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_9;
            this.okButton.PressedTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(140)))), ((int)(((byte)(177)))));
            this.okButton.Size = new System.Drawing.Size(195, 44);
            this.okButton.TabIndex = 1;
            this.okButton.TextFont = new System.Drawing.Font("Yu Gothic UI", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
            this.okButton.TextMargin = new System.Windows.Forms.Padding(0);
            this.okButton.Click += new System.EventHandler(this.OnOKButtonClick);
            // 
            // SmartCardAuthFailurePage
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.okButton);
            this.Controls.Add(this.messageLabel);
            this.Name = "SmartCardAuthFailurePage";
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label messageLabel;
        private UI.ImageButton okButton;
    }
}
