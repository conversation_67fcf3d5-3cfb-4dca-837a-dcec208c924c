﻿using System;
using System.Configuration;

namespace eDoktor.Taikoban.AccountImporter.Configuration
{
    class ContentConfigurationElement : ConfigurationElement
    {
        #region Properties
        [ConfigurationProperty("name", IsRequired = true)]
        public string Name
        {
            get { return this["name"] as string; }
            set { this["name"] = value; }
        }

        [ConfigurationProperty("offset", IsRequired = true)]
        public int Offset
        {
            get { return (int)this["offset"]; }
            set { this["offset"] = value; }
        }

        [ConfigurationProperty("length", IsRequired = true)]
        public int Lenght
        {
            get { return (int)this["length"]; }
            set { this["length"] = value; }
        }

        [ConfigurationProperty("propertyName", IsRequired = true)]
        public string PropertyName
        {
            get { return this["propertyName"] as string; }
            set { this["propertyName"] = value; }
        }

        [ConfigurationProperty("prefix")]
        public string Prefix
        {
            get { return this["prefix"] as string; }
            set { this["prefix"] = value; }
        }

        [ConfigurationProperty("suffix")]
        public string Suffix
        {
            get { return this["suffix"] as string; }
            set { this["suffix"] = value; }
        }

        [ConfigurationProperty("isKey")]
        public bool IsKey
        {
            get { return (bool)this["isKey"]; }
            set { this["isKey"] = value; }
        }
        #endregion
    }
}
