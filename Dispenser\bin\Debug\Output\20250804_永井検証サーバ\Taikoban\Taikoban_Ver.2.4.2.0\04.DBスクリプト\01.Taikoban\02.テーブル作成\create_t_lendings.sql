USE [$(Param1)]
GO
/****** Object:  Table [dbo].[t_lendings]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_lendings]') AND type in (N'U'))
DROP TABLE [dbo].[t_lendings]
GO

CREATE TABLE [dbo].[t_lendings](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[registered_by] [int] NOT NULL,
	[modification_datetime] [datetime2](7) NOT NULL,
	[modified_by] [int] NOT NULL,
	[rental_date] [datetime2](7) NOT NULL,
	[return_due_date] [datetime2](7) NOT NULL,
	[return_date] [datetime2](7) NULL,
	[rentl_action_account_id] [int] NOT NULL,
	[account_id] [int] NOT NULL,
	[smart_card_id] [int] NOT NULL,
	[holder_id] [int] NOT NULL,
 CONSTRAINT [PK_t_rentals] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_lendings] ADD  CONSTRAINT [DF_t_rentals_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO
ALTER TABLE [dbo].[t_lendings] ADD  CONSTRAINT [DF_t_rentals_registered_by]  DEFAULT ((0)) FOR [registered_by]
GO
ALTER TABLE [dbo].[t_lendings] ADD  CONSTRAINT [DF_t_rentals_modification_datetime]  DEFAULT (getdate()) FOR [modification_datetime]
GO
ALTER TABLE [dbo].[t_lendings] ADD  CONSTRAINT [DF_t_rentals_modified_by]  DEFAULT ((0)) FOR [modified_by]
GO
ALTER TABLE [dbo].[t_lendings] ADD  CONSTRAINT [DF_t_rentals_rental_date]  DEFAULT (getdate()) FOR [rental_date]
GO
ALTER TABLE [dbo].[t_lendings] ADD  CONSTRAINT [DF_t_rentals_return_due_date]  DEFAULT (getdate()) FOR [return_due_date]
GO
ALTER TABLE [dbo].[t_lendings] ADD  CONSTRAINT [DF_t_rentals_rentl_action_account_id]  DEFAULT ((0)) FOR [rentl_action_account_id]
GO
ALTER TABLE [dbo].[t_lendings] ADD  CONSTRAINT [DF_t_rentals_account_id]  DEFAULT ((0)) FOR [account_id]
GO
ALTER TABLE [dbo].[t_lendings] ADD  CONSTRAINT [DF_t_rentals_smart_card_id]  DEFAULT ((0)) FOR [smart_card_id]
GO
ALTER TABLE [dbo].[t_lendings] ADD  CONSTRAINT [DF_t_rentals_holder_id]  DEFAULT ((0)) FOR [holder_id]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_lendings', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_lendings', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_lendings', @level2type=N'COLUMN',@level2name=N'registered_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_lendings', @level2type=N'COLUMN',@level2name=N'modification_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_lendings', @level2type=N'COLUMN',@level2name=N'modified_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'貸出日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_lendings', @level2type=N'COLUMN',@level2name=N'rental_date'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'返却予定日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_lendings', @level2type=N'COLUMN',@level2name=N'return_due_date'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'返却日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_lendings', @level2type=N'COLUMN',@level2name=N'return_date'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'貸出処理実行者' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_lendings', @level2type=N'COLUMN',@level2name=N'rentl_action_account_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'貸出アカウントID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_lendings', @level2type=N'COLUMN',@level2name=N'account_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'貸出カードID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_lendings', @level2type=N'COLUMN',@level2name=N'smart_card_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'アカウントとスマートカードの関連付け' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_lendings', @level2type=N'COLUMN',@level2name=N'holder_id'
GO
