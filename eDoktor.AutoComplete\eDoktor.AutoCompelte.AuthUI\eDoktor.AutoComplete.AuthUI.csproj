﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{58B67F7E-D947-4B7E-8287-EE5D1A9A15B6}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>eDoktor.AutoComplete.AuthUI</RootNamespace>
    <AssemblyName>eDoktor.AutoComplete.AuthUI</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>None</ResolveAssemblyWarnOrErrorOnTargetArchitectureMismatch>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\bin\$(Platform)\$(Configuration)\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\bin\$(Platform)\$(Configuration)\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\bin\$(Platform)\$(Configuration)\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>..\bin\$(Platform)\$(Configuration)\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\bin\$(Platform)\$(Configuration)\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>..\bin\$(Platform)\$(Configuration)\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>edoktor.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <Win32Resource>app.res</Win32Resource>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="eDoktor.Auth.ClientExtension">
      <HintPath>..\Dependencies\eDoktor\x86\Release\eDoktor.Auth.ClientExtension.dll</HintPath>
    </Reference>
    <Reference Include="eDoktor.Auth.Interprocess">
      <HintPath>..\Dependencies\eDoktor\x86\Release\eDoktor.Auth.Interprocess.dll</HintPath>
    </Reference>
    <Reference Include="eDoktor.Common, Version=1.0.0.0, Culture=neutral, PublicKeyToken=0ff7bf58c14a5b51, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\eDoktor\$(Platform)\$(Configuration)\eDoktor.Common.dll</HintPath>
    </Reference>
    <Reference Include="eDoktor.UI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=0ff7bf58c14a5b51, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\Dependencies\eDoktor\$(Platform)\$(Configuration)\eDoktor.UI.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="UIAutomationClient" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AuthModule.cs" />
    <Compile Include="CredentialProviderAuthForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CredentialProviderAuthForm.Designer.cs">
      <DependentUpon>CredentialProviderAuthForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DesktopAuthForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DesktopAuthForm.Designer.cs">
      <DependentUpon>DesktopAuthForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DeviceErrorPage.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="DeviceErrorPage.Designer.cs">
      <DependentUpon>DeviceErrorPage.cs</DependentUpon>
    </Compile>
    <Compile Include="FormShowMode.cs" />
    <Compile Include="InvalidUserNameOrPasswordPage.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="InvalidUserNameOrPasswordPage.Designer.cs">
      <DependentUpon>InvalidUserNameOrPasswordPage.cs</DependentUpon>
    </Compile>
    <Compile Include="FaceAuthFailurePage2.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="FaceAuthFailurePage2.Designer.cs">
      <DependentUpon>FaceAuthFailurePage2.cs</DependentUpon>
    </Compile>
    <Compile Include="PasswordAuthPage.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="PasswordAuthPage.Designer.cs">
      <DependentUpon>PasswordAuthPage.cs</DependentUpon>
    </Compile>
    <Compile Include="PromptPage2.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="PromptPage2.Designer.cs">
      <DependentUpon>PromptPage2.cs</DependentUpon>
    </Compile>
    <Compile Include="PasswordCredentialEventArgs.cs" />
    <Compile Include="SmartCardAuthFailurePage.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SmartCardAuthFailurePage.Designer.cs">
      <DependentUpon>SmartCardAuthFailurePage.cs</DependentUpon>
    </Compile>
    <Compile Include="UserConfirmationPage.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UserConfirmationPage.Designer.cs">
      <DependentUpon>UserConfirmationPage.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemFaultPage.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SystemFaultPage.Designer.cs">
      <DependentUpon>SystemFaultPage.cs</DependentUpon>
    </Compile>
    <Compile Include="FaceAuthFailurePage.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="FaceAuthFailurePage.Designer.cs">
      <DependentUpon>FaceAuthFailurePage.cs</DependentUpon>
    </Compile>
    <Compile Include="NetworkErrorPage.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="NetworkErrorPage.Designer.cs">
      <DependentUpon>NetworkErrorPage.cs</DependentUpon>
    </Compile>
    <Compile Include="PageBase.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="PageBase.Designer.cs">
      <DependentUpon>PageBase.cs</DependentUpon>
    </Compile>
    <Compile Include="PasswordCredentialPage.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="PasswordCredentialPage.Designer.cs">
      <DependentUpon>PasswordCredentialPage.cs</DependentUpon>
    </Compile>
    <Compile Include="PromptPage.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="PromptPage.Designer.cs">
      <DependentUpon>PromptPage.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Win32Window.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\eDoktor.AutoCompelte.AuthModule\eDoktor.AutoComplete.AuthModule.csproj">
      <Project>{1d4f08d7-c6d0-4e27-b336-95896bf6ce8b}</Project>
      <Name>eDoktor.AutoComplete.AuthModule</Name>
    </ProjectReference>
    <ProjectReference Include="..\eDoktor.AutoComplete.ExternalAuth\eDoktor.AutoComplete.ExternalAuth.csproj">
      <Project>{940aaa5c-ad71-42d3-96ab-af008e52e785}</Project>
      <Name>eDoktor.AutoComplete.ExternalAuth</Name>
    </ProjectReference>
    <ProjectReference Include="..\eDoktor.AutoComplete.Interprocess\eDoktor.AutoComplete.Interprocess.csproj">
      <Project>{82fcc6ff-259c-4cb3-b77c-b71557afb297}</Project>
      <Name>eDoktor.AutoComplete.Interprocess</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="CredentialProviderAuthForm.resx">
      <DependentUpon>CredentialProviderAuthForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DesktopAuthForm.resx">
      <DependentUpon>DesktopAuthForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DeviceErrorPage.resx">
      <DependentUpon>DeviceErrorPage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="InvalidUserNameOrPasswordPage.resx">
      <DependentUpon>InvalidUserNameOrPasswordPage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FaceAuthFailurePage2.resx">
      <DependentUpon>FaceAuthFailurePage2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PasswordAuthPage.resx">
      <DependentUpon>PasswordAuthPage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PromptPage2.resx">
      <DependentUpon>PromptPage2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SmartCardAuthFailurePage.resx">
      <DependentUpon>SmartCardAuthFailurePage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UserConfirmationPage.resx">
      <DependentUpon>UserConfirmationPage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemFaultPage.resx">
      <DependentUpon>SystemFaultPage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FaceAuthFailurePage.resx">
      <DependentUpon>FaceAuthFailurePage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NetworkErrorPage.resx">
      <DependentUpon>NetworkErrorPage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PageBase.resx">
      <DependentUpon>PageBase.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PasswordCredentialPage.resx">
      <DependentUpon>PasswordCredentialPage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PromptPage.resx">
      <DependentUpon>PromptPage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="app.manifest" />
    <None Include="edoktor.snk" />
    <None Include="Resources\B-button-1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\B-button-2.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\B-button-3.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\B-button-4.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\B-button-5.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\B-button-6.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\B-button-7.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\B-button-8.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\B-button-9.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\B-form-1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\B-form-2.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\B-form-3.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\mainB.png" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="app.res" />
    <None Include="Resources\icon-2.png" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>