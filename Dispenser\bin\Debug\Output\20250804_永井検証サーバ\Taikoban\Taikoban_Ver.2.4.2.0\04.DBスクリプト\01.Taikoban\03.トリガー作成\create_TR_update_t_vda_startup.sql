USE [$(Param1)]
GO

/****** Object:  Trigger [dbo].[TR_update_t_vda_startup]    Script Date: 10/06/2014 16:00:58 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*** CREATE TRIGGER ***/
IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[TR_update_t_vda_startup]') AND type in (N'TR'))
DROP TRIGGER [dbo].[TR_update_t_vda_startup]
GO

CREATE TRIGGER [dbo].[TR_update_t_vda_startup] ON [dbo].[t_vda_startup]
FOR UPDATE
AS 
BEGIN

UPDATE t_vda_startup
SET modification_datetime = GETDATE()
WHERE id IN (SELECT id FROM inserted)

END
RETURN


GO

