﻿// TcpClientExtensions.cs

namespace eDoktor.AutoComplete.Interprocess.TcpClientExtensions
{
	public static class TcpClientExtensions
	{
		#region Private Methods
		private static T Transceive<T, U>(this TcpClient tcpClient, Command command, U requestData)
			where T : class, new()
			where U : class, new()
		{
			return Transceive<T, U>(tcpClient, command, requestData, Configuration.ResponseWaitingTimeout);
		}

		private static T Transceive<T, U>(this TcpClient tcpClient, Command command, U requestData, int millisecondsTimeout)
			where T : class, new()
			where U : class, new()
		{
			T responseData = null;

			try
			{
				if ((tcpClient == null) || (!tcpClient.Connected))
				{
					return responseData;
				}

				Packet packet = tcpClient.Transceive(Packet.Create<U>(0, Response.ACK, command, 0, requestData), millisecondsTimeout);

				if (packet != null)
				{
					responseData = packet.GetData<T>();
				}
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}

			return responseData;
		}
		#endregion

		#region Public Methods
		// クライアント→サーバー 1～20
		public static ClientToServerAutoCompleteSettingQueryResponseData Transceive(this TcpClient tcpClient, ClientToServerAutoCompleteSettingQueryRequestData requestData)
		{
			return tcpClient.Transceive<ClientToServerAutoCompleteSettingQueryResponseData, ClientToServerAutoCompleteSettingQueryRequestData>(Command.ClientToServerAutoCompleteSettingQuery, requestData);
		}
		#endregion
	}
}
