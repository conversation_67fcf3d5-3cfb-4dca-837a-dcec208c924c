USE [$(Param1)]
GO

/****** Object:  Trigger [dbo].[TR_update_t_face_auth_messages]    Script Date: 2021/07/07 9:43:31 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[TR_update_t_face_auth_messages]') AND type in (N'TR'))
DROP TRIGGER [dbo].[TR_update_t_face_auth_messages]
GO

CREATE TRIGGER [dbo].[TR_update_t_face_auth_messages] ON [dbo].[t_face_auth_messages]
FOR UPDATE
AS 
BEGIN

UPDATE t_face_auth_messages

SET modification_datetime = GETDATE()
WHERE id IN (SELECT id FROM inserted)

END
RETURN


GO

ALTER TABLE [dbo].[t_face_auth_messages] ENABLE TRIGGER [TR_update_t_face_auth_messages]
GO


