﻿// AutomationHelper.cs

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Windows.Automation;
using System.Windows.Forms;

// 次への参照を追加してください UIAutomationClient,UIAutomationTypes,WindowsBase

namespace eDoktor.AutoComplete.Agent
{
	static class AutomationHelper
	{
		#region Public Methods
		public static AutomationElement GetMainWindowElement(Process process)
		{
			process.Refresh();
			return AutomationElement.FromHandle(process.MainWindowHandle);
		}

		public static AutomationElement GetParentWindow(AutomationElement element)
		{
			TreeWalker walker = TreeWalker.ControlViewWalker;
			AutomationElement node = element;

			do
			{
				if ((node == AutomationElement.RootElement) || (node.Current.ControlType == ControlType.Window))
				{
					return node;
				}

				var tempNode = walker.GetParent(node);

				if (tempNode.Current.ProcessId != element.Current.ProcessId)
				{
					return node;
				}

				node = tempNode;
			}
			while (true);
		}

		public static void WalkControlElements(AutomationElement rootElement, List<AutomationElement> elementList, AutomationElementScope scope = null)
		{
			WalkControlElements(rootElement, false, elementList, scope);
		}

		public static List<AutomationElement> PerformHitTest(List<AutomationElement> elementList, System.Windows.Point point)
		{
			List<AutomationElement> newElementList = null;

			foreach (var element in elementList)
			{
				if (PerformHitTest(element.Current, point))
				{
					if (newElementList == null)
					{
						newElementList = new List<AutomationElement>();
					}

					newElementList.Add(element);
				}
			}

			return newElementList;
		}

		public static bool PerformHitTest(AutomationElement.AutomationElementInformation elementInformation, System.Windows.Point point)
		{
			return elementInformation.BoundingRectangle.Contains(point);
		}

		public static AutomationElement FindFirstElement(AutomationElement rootElement, AutomationProperty property, string value)
		{
			return rootElement.FindFirst(TreeScope.Element | TreeScope.Descendants, new PropertyCondition(property, value));
		}

		public static AutomationElement FindElementById(AutomationElement rootElement, string value)
		{
			return FindFirstElement(rootElement, AutomationElement.AutomationIdProperty, value);
		}

		public static AutomationElement FindElementsByName(AutomationElement rootElement, string value)
		{
			return FindFirstElement(rootElement, AutomationElement.NameProperty, value);
		}

		public static AutomationElement FindElementsByHelpText(AutomationElement rootElement, string value)
		{
			return FindFirstElement(rootElement, AutomationElement.HelpTextProperty, value);
		}

		public static void SetText(AutomationElement element, string value)
		{
			// ▼ MODIFY パスワード入力方式追加対応 2024/08/07 eDoktor Y.Kihara
			//if (element == null)
			//{
			//	throw new ArgumentNullException("element");
			//}

			//if (value == null)
			//{
			//	throw new ArgumentNullException("value");
			//}

			//if (!element.Current.IsEnabled)
			//{
			//	throw new InvalidOperationException("The control with an AutomationID of " + element.Current.AutomationId.ToString() + " is not enabled.");
			//}

			//if (!element.Current.IsKeyboardFocusable)
			//{
			//	throw new InvalidOperationException("The control with an AutomationID of " + element.Current.AutomationId.ToString() + "is read-only.");
			//}

			//object pattern = null;

			//if (!element.TryGetCurrentPattern(ValuePattern.Pattern, out pattern))
			//{
			//	element.SetFocus();

			//	Thread.Sleep(100);

			//	SendKeys.SendWait("^{HOME}");   // Move to start of control
			//	SendKeys.SendWait("^+{END}");   // Select everything
			//	SendKeys.SendWait("{DEL}");     // Delete selection
			//	SendKeys.SendWait(value);
			//}
			//else
			//{
			//	element.SetFocus();
			//	(pattern as ValuePattern).SetValue(value);
			//}
			SetText(element, value, null);
			// ▲ MODIFY パスワード入力方式追加対応 2024/08/07 eDoktor Y.Kihara
		}

		// ▼ ADD パスワード入力方式追加対応 2024/08/07 eDoktor Y.Kihara
		public static void SetText(AutomationElement element, string value, Interprocess.AutoCompleteSetting setting)
		{
			if (element == null)
			{
				throw new ArgumentNullException("element");
			}

			if (value == null)
			{
				throw new ArgumentNullException("value");
			}

			if (!element.Current.IsEnabled)
			{
				throw new InvalidOperationException("The control with an AutomationID of " + element.Current.AutomationId.ToString() + " is not enabled.");
			}

			if (!element.Current.IsKeyboardFocusable)
			{
				throw new InvalidOperationException("The control with an AutomationID of " + element.Current.AutomationId.ToString() + "is read-only.");
			}

			object pattern = null;

			var passwordFocusingWait = 0;
			var passwordInputInterval = 0;
			if (setting != null)
			{
				passwordFocusingWait = setting.PasswordFocusingWait;
				if (passwordFocusingWait < 0)
				{
					passwordFocusingWait = 0;
				}
				passwordInputInterval = setting.PasswordInputInterval;
				if (passwordInputInterval < 0)
				{
					passwordInputInterval = 0;
				}
			}

			if (setting != null && setting.PasswordInputMethod != Interprocess.PasswordInputMethodType.Automation)
			{
				element.SetFocus();

				Thread.Sleep(passwordFocusingWait);

				SendKeys.SendWait("^{HOME}");   // Move to start of control
				SendKeys.SendWait("^+{END}");   // Select everything
				SendKeys.SendWait("{DEL}");     // Delete selection

				foreach (var c in value)
				{
					Thread.Sleep(passwordInputInterval);

					//***debug***
					//eDoktor.Common.Trace.OutputDebugTrace(c.ToString());
					//***debug***
					SendKeys.SendWait(c.ToString());
				}
			}
			else if (!element.TryGetCurrentPattern(ValuePattern.Pattern, out pattern))
			{
				element.SetFocus();

				Thread.Sleep(100);

				SendKeys.SendWait("^{HOME}");   // Move to start of control
				SendKeys.SendWait("^+{END}");   // Select everything
				SendKeys.SendWait("{DEL}");     // Delete selection
				SendKeys.SendWait(value);
			}
			else
			{
				element.SetFocus();
				(pattern as ValuePattern).SetValue(value);
			}
		}
		// ▲ ADD パスワード入力方式追加対応 2024/08/07 eDoktor Y.Kihara

		public static void PushButton(AutomationElement element)
		{
			var pattern = element.GetCurrentPattern(InvokePattern.Pattern) as InvokePattern;
			pattern.Invoke();
		}
		#endregion

		#region Private Methods
		private static void WalkControlElements(AutomationElement rootElement, bool treeValidationExceptionShouldFail, List<AutomationElement> elementList, AutomationElementScope scope = null)
		{
			try
			{
				if (!elementList.Contains(rootElement))
				{
					elementList.Add(rootElement);
					scope?.AddElement(rootElement);
				}

				var elementCollection = rootElement.FindAll(TreeScope.Children, Automation.ControlViewCondition);

				foreach (var node in elementCollection)
				{
					var elementNode = node as AutomationElement;
					if (elementNode != null)
					{
						scope?.AddElement(elementNode);
						
						if (rootElement == TreeWalker.ControlViewWalker.GetParent(elementNode))
						{
							WalkControlElements(elementNode, treeValidationExceptionShouldFail, elementList, scope);
						}
						else
						{
							WalkControlElements(elementNode, true, elementList, scope);
						}
					}
				}
			}
			catch (ElementNotAvailableException ex)
			{
				Common.Trace.OutputDebugTrace("要素が利用できません: {0}", ex.Message);
				if (treeValidationExceptionShouldFail)
				{
					throw;
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
				if (treeValidationExceptionShouldFail)
				{
					throw;
				}
			}
		}
		#endregion
	}
}
