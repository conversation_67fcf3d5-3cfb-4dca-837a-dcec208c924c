﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
      <section name="eDoktor.Taikoban.SmartCardBulkRegister.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <applicationSettings>
    <eDoktor.Taikoban.SmartCardBulkRegister.Properties.Settings>
      <setting name="ConnectionStringSettingsLabel" serializeAs="String">
        <value>Taikoban</value>
      </setting>
    </eDoktor.Taikoban.SmartCardBulkRegister.Properties.Settings>
  </applicationSettings>
  <connectionStrings>
    <add name="Taikoban" connectionString="sOEqwRggeOs5J+mrA1PTQP03iS2JvKkkwovlMRimh7NIGBkA6vsDZOSli1ja2eqMXHwU1svdQpoPpl+cAiF71W5rxEQCPpq4d1gvrg7XgSWUOxpzgXIYISoUrCN+yEURta0qlL+UpBogoGF6fBt4Nmo40KsnimIn6wmRkbT0ScNmukl9T8237Gw5Z3A5jBlOw71SlKGl0maMbRVGzGH8J5zm175t4SnPg1pNJkVvp+0=" providerName="System.Data.SqlClient" />
  </connectionStrings>

  <appSettings>
    <!-- Trace -->
    <add key="EnableTrace" value="true" />
    <add key="EnableDebugTrace" value="true" />
    <add key="EnableDetailedExceptionTrace" value="true" />
    <add key="ContainingFolderPath" value=".\Logs\%USER%\"/>
    <add key="TraceTermInDays" value="30" />
    
    <!-- SSO -->
    <add key="EncryptedSystemId" value="yxkT3DQ104BwmiNRagij8A=="/>

    <!-- ImportFile -->
    <add key="FileEncode" value="Shift-JIS"/>
    <add key="Delimiter" value=","/>
    <add key="DatetimeFormat" value="yyyy/MM/dd"/>
    <!-- 1行目を無視するかどうか true=無視する,false=無視しない -->
    <add key="IgnoreFirstRow" value="true"/>
    <!-- CSVの項目の必要数。必要数未満のデータはエラーとする -->
    <add key="RequiredNumberOfItems" value="4"/>
    <!-- 組織内IDの項目順番。必須項目 -->
    <add key="OrganizationalIdIndex" value="0"/>
    <!-- 氏名の項目順番。-1以下の場合は項目なし -->
    <add key="NameIndex" value="1"/>
    <!-- カナ氏名の項目順番。-1以下の場合は項目なし -->
    <add key="KanaIndex" value="2"/>
    <!-- ログオンIDの項目順番。必須項目 -->
    <add key="LogonIdIndex" value="0"/>
    <!-- パスワードの項目順番。-1以下の場合は項目なし。固定値を利用する -->
    <add key="PasswordIndex" value="-1"/>
    <!-- カードユニークIDの項目順番。-1以下の場合は項目なし。0以上の場合は必須項目 -->
    <add key="UniqueIdIndex" value="3"/>
    <!-- カードユニークIDの記載にハイフンが含まれているか。 true=含まれている。false=含まれていない。 -->
    <add key="IncludeHyphen" value="false" />
    <!-- 有効開始日の項目順番。-1以下の場合は項目なし -->
    <add key="UsageStartDatetimeIndex" value="-4"/>
    <!-- 有効終了日の項目順番。-1以下の場合は項目なし -->
    <add key="UsageEndDatetimeIndex" value="-5"/>
    <add key="CheckLicense" value="false"/>
    <add key="AdminUsers" value="9tHpTd+8smKBK0asMSE+Zi6XL5O7Eny9W1T+eJMmnGE="/>
    
    <!-- Const -->
    <add key="InitialPassword" value="Pt9mRXimjrPfFBQSCg7K5Q=="/>
    <add key="InitialEncryptedPassword" value="ｩｩﾙrrqqf"/>
    <add key="LogonTo" value="hhx"/>
    <add key="InitialPasswordUpdateDatetime" value="2023-01-01"/>
    <!-- カードタイプ 0:未定義 1:FeliCa 2:MIFARE 3:PicoPass -->
    <add key="CardType" value="1"/>

  </appSettings>

  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5.2" />
  </startup>
</configuration>