<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <log4net>

    <!-- 通常ログ -->
    <appender name="OperationLogDailyAppender" type="log4net.Appender.RollingFileAppender">
      <param name="File" value=".\Logs\Log" />
      <param name="RollingStyle" value="date" />
      <param name="DatePattern" value='"_"yyyyMMdd".log"' />
      <param name="StaticLogFileName" value="false" />
      <param name="AppendToFile" value="true" />

      <filter type="log4net.Filter.LevelRangeFilter">
        <param name="LevelMax" value="INFO" />
        <param name="LevelMin" value="DEBUG" />
      </filter>

      <param name="MaximumFileSize" value="10MB" />
      <param name="MaxSizeRollBackups" value="90" />

      <layout type="log4net.Layout.PatternLayout">
        <ConversionPattern value="[%date %-5p] thread[%t] %m%n" />
      </layout>
    </appender>

    <!-- エラーログ -->
    <appender name="ErrorLogDailyAppender" type="log4net.Appender.RollingFileAppender">
      <param name="File" value=".\ErrorLogs\Error" />
      <param name="RollingStyle" value="date" />
      <param name="DatePattern" value='"_"yyyyMMdd".log"' />
      <param name="StaticLogFileName" value="false" />
      <param name="AppendToFile" value="true" />

      <filter type="log4net.Filter.LevelRangeFilter">
        <param name="LevelMax" value="FATAL" />
        <param name="LevelMin" value="WARN" />
      </filter>

      <param name="MaximumFileSize" value="10MB" />

      <layout type="log4net.Layout.PatternLayout">
        <ConversionPattern value="[%date %-5p] thread[%t] %m%n" />
      </layout>
    </appender>

    <root>
      <!-- TRACE以上のログを記録 -->
      <level value="TRACE" />
      <!-- 使用する Appender -->
      <appender-ref ref="OperationLogDailyAppender" />
      <appender-ref ref="ErrorLogDailyAppender" />
    </root>

  </log4net>
</configuration>