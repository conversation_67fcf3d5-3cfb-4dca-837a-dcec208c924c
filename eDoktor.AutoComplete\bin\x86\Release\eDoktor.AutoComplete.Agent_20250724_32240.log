﻿1 21:02:41.821 eDoktor.AutoComplete.Agent.Program.Main                              eDoktor.AutoComplete.Agent, Version=1.0.0.0, Culture=neutral, PublicKeyToken=0ff7bf58c14a5b51
0 21:02:41.842 eDoktor.AutoComplete.Agent.Program.Main                              elevation=False
0 21:02:42.441 eDoktor.Common.TcpClient.OnSocketConnected                           192.168.1.93:49901
0 21:02:42.492 eDoktor.AutoComplete.Interprocess.TcpClient.GetNextSequence          192.168.1.93:49901 sequence=1
0 21:02:42.512 eDoktor.Common.TcpClient.OnPacketTransmitted                         192.168.1.93:49901 [65-44-6F-6B-74-6F-72-20-00-01-01-00-01-00-A0-00-DF-AE-BF-65-45-BC-58-A0-94-8F-60-0A-44-51-96-09-C6-EC-27-8F-22-99-4A-F7-ED-D6-46-1E-13-28-A4-5D-1A-85-F6-B5-A0-60-32-72-91-AA-5F-DE-EC-8E-61-E1-05-DA-02-2A-2F-59-19-9B-72-B2-C1-BD-E4-A8-A5-CA-5D-2B-9D-21-B6-3B-BD-72-9D-47-38-B5-26-91-C0-8D-73-EA-7C-7D-AA-D7-81-CC-AB-63-8F-53-6C-48-1D-10-25-A4-DD-27-40-B2-65-53-AA-05-E0-E4-68-DB-34-29-E0-90-8C-1C-B9-DA-4C-8C-0F-46-B4-36-3C-3B-4D-A8-DF-ED-27-42-30-5B-13-30-9F-95-84-89-3F-22-65-65-F0-44-A6-CA-37-FD-B3-45-9C-0E-B3-3D-06-CD-06-95-0D-0A]
0 21:02:42.527 eDoktor.Common.TcpClient.OnPacketReceived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
0 21:02:42.549 eDoktor.AutoComplete.Agent.ApplicationContext.get_DefaultAutoCompleteInfo AutoCompleteInfoDataStoreDirectoryPath=D:\eDoktor\Taikoban\Logs\AutoCompleteAgent\
4 21:25:45.356 eDoktor.Common.TcpClient.OnExceptionOccurred                         System.IO.IOException: 転送接続からデータを読み取れません: 接続済みの呼び出し先が一定の時間を過ぎても正しく応答しなかったため、接続できませんでした。または接続済みのホストが応答しなかったため、確立された接続は失敗しました。。 ---> System.Net.Sockets.SocketException: 接続済みの呼び出し先が一定の時間を過ぎても正しく応答しなかったため、接続できませんでした。または接続済みのホストが応答しなかったため、確立された接続は失敗しました。
   場所 System.Net.Sockets.Socket.EndReceive(IAsyncResult asyncResult)
   場所 System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   --- 内部例外スタック トレースの終わり ---
   場所 System.Net.Sockets.NetworkStream.EndRead(IAsyncResult asyncResult)
   場所 eDoktor.Common.PacketTransceiver.BeginReceiveCallback(IAsyncResult asyncResult)
0 21:25:45.365 eDoktor.Common.TcpClient.OnSocketDisconnected                        192.168.1.93:49901
0 21:25:50.373 eDoktor.Common.TcpClient.OnSocketConnected                           192.168.1.93:49901
0 21:25:50.376 eDoktor.AutoComplete.Interprocess.TcpClient.GetNextSequence          192.168.1.93:49901 sequence=2
0 21:25:50.378 eDoktor.Common.TcpClient.OnPacketTransmitted                         192.168.1.93:49901 [65-44-6F-6B-74-6F-72-20-00-01-01-00-02-00-A0-00-DF-AE-BF-65-45-BC-58-A0-94-8F-60-0A-44-51-96-09-C6-EC-27-8F-22-99-4A-F7-ED-D6-46-1E-13-28-A4-5D-1A-85-F6-B5-A0-60-32-72-91-AA-5F-DE-EC-8E-61-E1-05-DA-02-2A-2F-59-19-9B-72-B2-C1-BD-E4-A8-A5-CA-5D-2B-9D-21-B6-3B-BD-72-9D-47-38-B5-26-91-C0-8D-73-EA-7C-7D-AA-D7-81-CC-AB-63-8F-53-6C-48-1D-10-25-A4-DD-27-40-B2-65-53-AA-05-E0-E4-68-DB-34-29-E0-90-8C-1C-B9-DA-4C-8C-0F-46-B4-36-3C-3B-4D-A8-DF-ED-27-42-30-5B-13-30-9F-95-84-89-3F-22-65-65-F0-44-A6-CA-37-FD-B3-45-9C-0E-B3-3D-06-CD-06-95-0D-0A]
0 21:25:50.391 eDoktor.Common.TcpClient.OnPacketReceived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
4 21:33:09.946 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__33_0 System.InvalidOperationException: プロセスは終了しているため、要求された情報は利用できません。
   場所 System.Diagnostics.Process.EnsureState(State state)
   場所 System.Diagnostics.Process.get_ProcessName()
   場所 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__33_0(IntPtr hWnd, IntPtr lParam) 場所 N:\work\VSCode\eDoktor.AutoComplete\eDoktor.AutoComplete.Agent\LoginWindowMonitor.cs:行 904
