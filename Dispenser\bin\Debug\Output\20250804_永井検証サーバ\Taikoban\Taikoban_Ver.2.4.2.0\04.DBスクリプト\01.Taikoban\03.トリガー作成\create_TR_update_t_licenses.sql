USE [$(Param1)]
GO

/****** Object:  Trigger [dbo].[TR_update_t_licenses]    Script Date: 10/06/2014 15:51:18 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*** CREATE TRIGGER ***/
IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[TR_update_t_licenses]') AND type in (N'TR'))
DROP TRIGGER [dbo].[TR_update_t_licenses]
GO

CREATE TRIGGER [dbo].[TR_update_t_licenses] ON [dbo].[t_licenses]
FOR UPDATE
AS 
BEGIN
UPDATE t_licenses
SET modification_datetime = GETDATE()
WHERE id IN (SELECT id FROM inserted)
END
RETURN


GO

