﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <!-- Trace -->
    <add key="EnableTrace" value="true" />
    <add key="EnableDebugTrace" value="true" />
    <add key="EnableDetailedExceptionTrace" value="true" />
    <add key="TraceTermInDays" value="30" />
    <add key="TraceContainingFolderPath" value="D:\eDoktor\Taikoban\AutoCompleteAgent\"/>
    <!-- DataStore -->
    <add key="DataStoreDirectoryPath" value="D:\eDoktor\Taikoban\AutoCompleteAgent\"/>

    <!-- Auth Module -->
    <add key="AuthModuleModeEx" value="true" />
    <add key="AuthModuleAssemblyNameOrPath" value="eDoktor.AutoComplete.AuthUI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=0ff7bf58c14a5b51" />
    <add key="AuthModuleTypeName" value="eDoktor.AutoComplete.AuthUI.AuthModule" />
    <add key="AuthModuleTopMost" value="false" />
    <add key="AuthModuleTopMessage" value="ICカードをかざすか$NL$認証開始ボタンを押下してください"/>
    <!-- LoginWindowMonitor -->
    <add key="LoginWindowMonitorMuteTimeout" value="5000" />
    <add key="LoginWindowMonitorTimerPeriod" value="500" />
    <add key="LoginWindowMonitorUseFindWindow" value="true" />
  </appSettings>
</configuration>