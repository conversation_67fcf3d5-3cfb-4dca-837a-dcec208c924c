USE [$(Param1)]
GO

/****** Object:  Trigger [dbo].[TR_insert_t_accounts]    Script Date: 10/06/2014 16:00:58 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*** CREATE TRIGGER ***/
IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[TR_insert_t_accounts]') AND type in (N'TR'))
DROP TRIGGER [dbo].[TR_insert_t_accounts]
GO

CREATE TRIGGER [dbo].[TR_insert_t_accounts] ON [dbo].[t_accounts]
FOR INSERT
AS 
BEGIN

INSERT INTO t_password_history (logon_id, password) SELECT logon_id, password FROM inserted WHERE password IS NOT NULL AND password <> '' AND password <> 'IISPqVAtKzsTtc/kgrvdFw=='

UPDATE t_accounts SET password = 'rLJvHjUBvA6guNrmEhzO1A==' WHERE id IN (SELECT id FROM inserted) AND password IS NULL OR password = '' OR password = 'IISPqVAtKzsTtc/kgrvdFw=='

END
RETURN


GO

