USE [$(Param1)]
GO
/****** Object:  Table [dbo].[t_accounts]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_accounts]') AND type in (N'U'))
DROP TABLE [dbo].[t_accounts]
GO

CREATE TABLE [dbo].[t_accounts](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[registered_by] [int] NOT NULL,
	[modification_datetime] [datetime2](7) NOT NULL,
	[modified_by] [int] NOT NULL,
	[invalidated] [bit] NOT NULL,
	[invalidation_datetime] [datetime2](7) NULL,
	[invalidated_by] [int] NOT NULL,
	[logically_deleted] [bit] NOT NULL,
	[logical_deletion_datetime] [datetime2](7) NULL,
	[logically_deleted_by] [int] NOT NULL,
	[synchronized] [bit] NOT NULL,
	[usage_start_datetime] [datetime2](7) NULL,
	[usage_end_datetime] [datetime2](7) NULL,
	[user_id] [int] NOT NULL,
	[authority] [int] NOT NULL,
	[group_id] [int] NOT NULL,
	[privilege_template_id] [int] NOT NULL,
	[logon_id] [nvarchar](255) NOT NULL,
	[password] [nvarchar](255) NULL,
	[encrypted_password] [nvarchar](255) NULL,
	[logon_to] [nvarchar](255) NOT NULL,
	[password_update_datetime] [datetime2](7) NULL,
	[notes] [nvarchar](255) NOT NULL,
 CONSTRAINT [PK_t_accounts] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [IX_t_accounts] UNIQUE NONCLUSTERED 
(
	[logon_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_accounts] ADD  CONSTRAINT [DF_t_accounts_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO
ALTER TABLE [dbo].[t_accounts] ADD  CONSTRAINT [DF_t_accounts_registered_by]  DEFAULT ((0)) FOR [registered_by]
GO
ALTER TABLE [dbo].[t_accounts] ADD  CONSTRAINT [DF_t_accounts_modification_datetime]  DEFAULT (getdate()) FOR [modification_datetime]
GO
ALTER TABLE [dbo].[t_accounts] ADD  CONSTRAINT [DF_t_accounts_modified_by]  DEFAULT ((0)) FOR [modified_by]
GO
ALTER TABLE [dbo].[t_accounts] ADD  CONSTRAINT [DF_t_accounts_invalidated]  DEFAULT ('false') FOR [invalidated]
GO
ALTER TABLE [dbo].[t_accounts] ADD  CONSTRAINT [DF_t_accounts_invalidated_by]  DEFAULT ((0)) FOR [invalidated_by]
GO
ALTER TABLE [dbo].[t_accounts] ADD  CONSTRAINT [DF_t_accounts_logically_deleted]  DEFAULT ('false') FOR [logically_deleted]
GO
ALTER TABLE [dbo].[t_accounts] ADD  CONSTRAINT [DF_t_accounts_logically_deleted_by]  DEFAULT ((0)) FOR [logically_deleted_by]
GO
ALTER TABLE [dbo].[t_accounts] ADD  CONSTRAINT [DF_t_accounts_synchronized]  DEFAULT ('false') FOR [synchronized]
GO
ALTER TABLE [dbo].[t_accounts] ADD  CONSTRAINT [DF_t_accounts_user_id]  DEFAULT ((0)) FOR [user_id]
GO
ALTER TABLE [dbo].[t_accounts] ADD  CONSTRAINT [DF_t_accounts_authority]  DEFAULT ((0)) FOR [authority]
GO
ALTER TABLE [dbo].[t_accounts] ADD  CONSTRAINT [DF_t_accounts_group_id]  DEFAULT ((0)) FOR [group_id]
GO
ALTER TABLE [dbo].[t_accounts] ADD  CONSTRAINT [DF_t_accounts_privilege_template_id]  DEFAULT ((0)) FOR [privilege_template_id]
GO
ALTER TABLE [dbo].[t_accounts] ADD  CONSTRAINT [DF_t_accounts_logon_id]  DEFAULT ('') FOR [logon_id]
GO
ALTER TABLE [dbo].[t_accounts] ADD  CONSTRAINT [DF_t_accounts_password]  DEFAULT ('') FOR [password]
GO
ALTER TABLE [dbo].[t_accounts] ADD  CONSTRAINT [DF_t_accounts_logon_to]  DEFAULT ('') FOR [logon_to]
GO
ALTER TABLE [dbo].[t_accounts] ADD  CONSTRAINT [DF_t_accounts_notes]  DEFAULT ('') FOR [notes]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'registered_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'modification_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'modified_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'invalidated'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効化日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'invalidation_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効化を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'invalidated_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'logically_deleted'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'logical_deletion_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'logically_deleted_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'連携データ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'synchronized'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'有効期間開始日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'usage_start_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'有効期間終了日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'usage_end_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ユーザID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'user_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'権限' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'authority'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'所属するアカウントグループのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'group_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'権限テンプレートのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'privilege_template_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ログオンID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'logon_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'password'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ログオン先' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'logon_to'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード更新日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'password_update_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'注記' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_accounts', @level2type=N'COLUMN',@level2name=N'notes'
GO
