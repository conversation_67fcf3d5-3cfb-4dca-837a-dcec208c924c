USE [$(Param1)]
GO

TRUNCATE TABLE t_face_auth_settings;

INSERT [dbo].[t_face_auth_settings] ([auth_mode], [retry_count], [face_template_regist_max_count], [face_search_wait_time], [auth_start_wait_time], [time_out], [mask_score_threshold], [template_quality_threshold_auth], [template_quality_threshold_insert], [face_auth_threshold], [face_recog_data_type], [is_estimate_gender_age], [face_recog_quality_threshold], [face_search_count_max], [face_search_scale_min], [face_search_scale_max], [input_image_width], [input_image_height], [is_hold_template_data], [template_get_wait_time], [is_display_auth_user_daialog], [is_display_auth_image], [registration_datetime], [registered_by], [modification_datetime], [modified_by], [is_use_eye_blink], [is_use_face_for_blinking], [face_size_max], [face_size_min], [eye_size_max], [eye_size_min], [blink_waiting_time], [blink_count], [blinking_time], [blink_area_x], [blink_area_y], [blink_area_width], [blink_area_height], [is_display_blink_message], [blink_message], [is_display_face_guide]) VALUES (0, 5, 5, 2000, 500, 3000, 100, 0, 0, 70, 1, 0, 2, 1, 25, 150, 0, 0, 0, 60, 1, 0, CAST(N'2022-10-14T17:54:10.2200000' AS DateTime2), 0, CAST(N'2023-07-24T20:05:53.8370000' AS DateTime2), 0, 0, 0, 150000, 10000, 5000, 500, 500, 3, 200, 20, 20, 60, 40, 1, N'', 0)


GO