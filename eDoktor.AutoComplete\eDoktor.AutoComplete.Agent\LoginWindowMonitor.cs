﻿// LoginWindowMonitor.cs

//#define VERBOSE_DEBUG_TRACE

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Windows.Automation;
using System.Reflection;

namespace eDoktor.AutoComplete.Agent
{
	using Interprocess.ObjectValidationExtensions;

	class LoginWindowMonitor : IDisposable
	{
		#region Handle
		class Handle : IDisposable
		{
			#region Properties
			public IntPtr Value { get; set; }
			public uint ProcessId { get; set; }
			public string ProcessName { get; set; }
			public bool Variable { get; set; }
			public bool Exists { get; set; }
			#endregion

			#region IDisposable
			~Handle()
			{
				Dispose(false);
			}

			public void Dispose()
			{
				Dispose(true);
				GC.SuppressFinalize(this);
			}

			protected virtual void Dispose(bool disposing)
			{

			}
			#endregion
		}

		class HandleComparer : IComparer<Handle>
		{
			public int Compare(Handle x, Handle y)
			{
				if (x == null)
				{
					if (y == null)
					{
						return 0;
					}
					else
					{
						return -1;
					}
				}
				else
				{
					if (y == null)
					{
						return 1;
					}
					else
					{
						return x.Value.ToInt64().CompareTo(y.Value.ToInt64());
					}
				}
			}
		}

		class ProcessingHandle : Handle
		{
			#region Fields
			private const int ThreadStartMillisecondsTimeout = 5000;
            private const int ThreadJoinMillisecondsTimeout = 5000;
            // ▼ ADD EX対応 2023/09/30 eDoktor Y.Kihara
            private const string NewLineReseredWord = "$NL$";
            private static readonly bool _authModuleModeEx;
            private static readonly string _authModuleAssemblyNameOrPath;
			private static readonly string _authModuleTypeName;
            private static readonly bool _authModuleTopMost;
            private static readonly string _authModuleTopMessage;
            // ▲ ADD EX対応 2023/09/30 eDoktor Y.Kihara
            
			private readonly object SyncRoot = new object();
			private volatile bool _finished;
			private Thread _thread;
			private AutoResetEvent _startEvent;
			private DateTime _lastSsoDateTime = DateTime.MinValue;
            // ▼ ADD EX対応 2023/09/30 eDoktor Y.Kihara
			private AuthModule.AuthModuleLoader _loader;
            // ▲ ADD EX対応 2023/09/30 eDoktor Y.Kihara
			#endregion

			#region Properties
			public Interprocess.AutoCompleteSetting LoginWindow { get; set; }
			public AutomationElement UserElement { get; set; }
			public AutomationElement PasswordElement { get; set; }
			public AutomationElement ButtonElement { get; set; }
			public bool AbortRequested { get; set; }
			public bool Finished
			{
				get { return _finished; }
				set { _finished = value; }
			}
			public DateTime LastSsoDateTime
			{
				get { return _lastSsoDateTime; }
				set { _lastSsoDateTime = value; }
			}
			#endregion

			#region Constructors
			static ProcessingHandle()
			{
	            // ▼ ADD EX対応 2023/09/30 eDoktor Y.Kihara
                _authModuleModeEx = Common.Configuration.AppSetting("AuthModuleModeEx", false);
				_authModuleAssemblyNameOrPath = Common.Configuration.AppSetting("AuthModuleAssemblyNameOrPath");
				_authModuleTypeName = Common.Configuration.AppSetting("AuthModuleTypeName");
                _authModuleTopMost = Common.Configuration.AppSetting("AuthModuleTopMost", false);
                _authModuleTopMessage = Common.Configuration.AppSetting("AuthModuleTopMessage").Replace(NewLineReseredWord, System.Environment.NewLine);
	            // ▲ ADD EX対応 2023/09/30 eDoktor Y.Kihara
			}
			#endregion

			#region Public Methods
			public void Start()
			{
				try
				{
					using (Common.TimedLock.Lock(SyncRoot))
					{
						if ((_thread != null) || (_startEvent != null))
						{
							Finished = true;

							return;
						}

						if (DateTime.Now < (_lastSsoDateTime + Configuration.LoginWindowMonitorMuteTimeout))
						{
							Finished = true;

							return;
						}

						_thread = new Thread(ThreadMain);
						_startEvent = new AutoResetEvent(false);
						_thread.SetApartmentState(System.Threading.ApartmentState.STA);
						_thread.Start();

						if (!_startEvent.WaitOne(ThreadStartMillisecondsTimeout))
						{
							throw new TimeoutException();
						}
					}
				}
				catch (Exception ex)
				{
					try
					{
						Common.Trace.OutputExceptionTrace(ex);

						var thread = _thread;

						if ((thread != null) && (thread.IsAlive))
						{
							Abort();

							if (!thread.Join(ThreadJoinMillisecondsTimeout))
							{
								thread.Abort();
								_thread = null;
							}
						}
					}
					catch (Exception ex2)
					{
						Common.Trace.OutputExceptionTrace(ex2);
					}
				}
				finally
				{
					try
					{
						if (_startEvent != null)
						{
							_startEvent.Dispose();
							_startEvent = null;
						}
					}
					catch (Exception ex)
					{
						Common.Trace.OutputExceptionTrace(ex);
					}
				}
			}

			public void Abort()
			{
				try
				{
					using (Common.TimedLock.Lock(SyncRoot))
					{
			            // ▼ ADD EX対応 2023/09/30 eDoktor Y.Kihara
						if (_loader != null)
						{
							_loader.Abort();
						}
			            // ▲ ADD EX対応 2023/09/30 eDoktor Y.Kihara

						AbortRequested = true;
					}
				}
				catch (Exception ex)
				{
					Common.Trace.OutputExceptionTrace(ex);
				}
			}
			#endregion

			#region Protected Methods
			protected override void Dispose(bool disposing)
			{
				try
				{
					base.Dispose(disposing);
					Abort();
				}
				catch (Exception ex)
				{
					Common.Trace.OutputExceptionTrace(ex);
				}
			}
			#endregion

			#region Private Methods
			private void ThreadMain(object state)
			{
				try
				{
#if VERBOSE_DEBUG_TRACE
					Common.Trace.OutputDebugTrace("handle={0}", Value.ToInt64());
#endif
		            // ▼ MODIFY EX対応 2023/09/30 eDoktor Y.Kihara
		            if (_authModuleModeEx == true)
		            {
						var authRequest = new AuthModule.AuthRequest()
						{
							Context = AuthModule.AuthContextType.Desktop,
							Mode = AuthModule.AuthModeType.FindUser,
							OwnerHandle = Value,
							UserName = string.Empty,
							DisplayPosition = AuthModule.DisplayPositionType.Center,
							Margin = System.Windows.Forms.Padding.Empty,
							TopMost = _authModuleTopMost,
	                        Draggable = true,
	                        TopMessage = _authModuleTopMessage,
	                        EnableDonotAutoLoginCheckbox = LoginWindow.EnableDonotAutoLoginCheckbox,
	                        DonotAutoLoginCheckboxMessage = LoginWindow.DonotAutoLoginCheckboxMessage,
							NotAllowedOneFactorAuthentication = LoginWindow.NotAllowedOneFactorAuthentication,
							// ▼ ADD 閉じるボタンの表示設定追加対応 2024/07/22 eDoktor Y.Kihara
							HiddenCloseButton = LoginWindow.HiddenCloseButton,
							// ▲ ADD 閉じるボタンの表示設定追加対応 2024/07/22 eDoktor Y.Kihara
						};
						string assemblyNameOrPath = _authModuleAssemblyNameOrPath;
						string typeName = _authModuleTypeName;
						_loader = new AuthModule.AuthModuleLoader();
	
						var startEvent = _startEvent;
	
						if (startEvent != null)
						{
							startEvent.Set();
						}
	
						var authResponse = _loader.PerformAuthentication(assemblyNameOrPath, typeName, authRequest);
	
						if ((authResponse != null) && (authResponse.Result == AuthModule.AuthResultType.Success) && (!string.IsNullOrWhiteSpace(authResponse.UserName)))
						{
							var credential = QueryUser(authResponse.UserName);
	
							if (credential.IsValid)
	                        {
	                            if (SetIdAndPassword(credential, authResponse.DonotAutoLogin))
								{
									// 処理成功
								}
							}
	                    }
	                    else if ((authResponse != null) && (authResponse.Result == AuthModule.AuthResultType.UserAborted))
	                    {
	                        if (LoginWindow.ActionOnExit == Interprocess.ActionOnExitType.CloseMainWindow)
	                        {
	                            // 対象ログイン画面に対してウィンドウクローズ
	                            uint processId = 0;
	                            uint threadId = GetWindowThreadProcessId(Value, out processId);
	
	                            using (var process = Process.GetProcessById((int)processId))
	                            {
	                                eDoktor.Common.Trace.OutputTrace("対象ログイン画面にクローズメッセージを送信(PID={0})", processId);
	                                process.CloseMainWindow();
	                            }
	                        }
	                        else if (LoginWindow.ActionOnExit == Interprocess.ActionOnExitType.KillProcess)
	                        {
	                            // 対象プロセスをキル
	                            uint processId = 0;
	                            uint threadId = GetWindowThreadProcessId(Value, out processId);
	
	                            using (var process = Process.GetProcessById((int)processId))
	                            {
	                                eDoktor.Common.Trace.OutputTrace("対象プロセスをキル(PID={0})", processId);
	                                process.Kill();
	                            }
	                        }
	                    }
					}
					else
					{
						var startEvent = _startEvent;
	
						if (startEvent != null)
						{
							startEvent.Set();
						}
	
						string userName = string.Empty;
						string password = string.Empty;
						var result = GetUserWithPassword(this.LoginWindow.SystemKey, this.LoginWindow.SecurityKey, out userName, out password);
	
						eDoktor.Common.Trace.OutputDebugTrace("UserName={0}", userName);
	
						if (!string.IsNullOrWhiteSpace(userName))
						{
							if (SetIdAndPassword(userName, password))
							{
								// 処理成功
								_lastSsoDateTime = DateTime.Now;
	
								//***debug***
								eDoktor.Common.Trace.OutputDebugTrace("_lastSsoDateTime={0}", _lastSsoDateTime);
							}
						}
					}
		            // ▲ MODIFY EX対応 2023/09/30 eDoktor Y.Kihara
		            

				}
				catch (Exception ex)
				{
					Common.Trace.OutputExceptionTrace(ex);
				}
				finally
				{
					Finished = true;
		            // ▼ ADD EX対応 2023/09/30 eDoktor Y.Kihara
					_loader = null;
		            // ▲ ADD EX対応 2023/09/30 eDoktor Y.Kihara
					_thread = null;
				}
			}

            // ▼ ADD EX対応 2023/09/30 eDoktor Y.Kihara
			private Auth.Interprocess.Credential QueryUser(string userName)
			{
				using (var client = new Auth.Interprocess.Client(Auth.Interprocess.ConnectionType.ToRemoteServer, false))
				{
					client.Start();
					var request = new Auth.Interprocess.ClientToServerUserQueryRequestData() { HostName = Auth.Interprocess.Configuration.MachineName, UserName = userName };
					var response = client.Transceive(request);
					return ((response != null) && (response.Credential.IsValid)) ? response.Credential : null;
				};
			}
            // ▲ ADD EX対応 2023/09/30 eDoktor Y.Kihara

			private bool SetIdAndPassword(string userName, string password)
			{
				Auth.Interprocess.Credential credential = new Auth.Interprocess.Credential() { UserName = userName, Password = password };

				return SetIdAndPassword(credential, false);
			}

            // ▼ ADD EX対応 2023/09/30 eDoktor Y.Kihara
			private bool SetIdAndPassword(Auth.Interprocess.Credential credential, bool notAutoLogin)
			{
				AutomationHelper.SetText(UserElement, credential.UserName);
				// ▼ MODIFY パスワード入力方式追加対応 2024/08/07 eDoktor Y.Kihara
				//AutomationHelper.SetText(PasswordElement, credential.Password);
				if (LoginWindow.PasswordInputMethod == Interprocess.PasswordInputMethodType.Automation)
				{
					AutomationHelper.SetText(PasswordElement, credential.Password);
				}
				else
				{
					AutomationHelper.SetText(PasswordElement, credential.Password, LoginWindow);
				}
				// ▲ MODIFY パスワード入力方式追加対応 2024/08/07 eDoktor Y.Kihara

				if (notAutoLogin)
                {
                    return true;
                }

                if (LoginWindow.ButtonClickMethod == Interprocess.ButtonClickMethodType.Automation)
				{
					AutomationHelper.PushButton(ButtonElement);
				}
				else
				{
					var tempHandleValue = ButtonElement.Current.NativeWindowHandle;
					Common.Trace.OutputTrace("tempHandleValue : {0}", tempHandleValue);

					if (tempHandleValue <= 0)
					{
						return false;
					}

					IntPtr buttonHandle = (IntPtr)tempHandleValue;

                    if (LoginWindow.ButtonClickMethod == Interprocess.ButtonClickMethodType.Click)
					{
						var wParam = new IntPtr(MK_LBUTTON);
						var lParam2 = MakeLParam(LoginWindow.ButtonClickX, LoginWindow.ButtonClickY);
						PostMessage(buttonHandle, WM_LBUTTONDOWN, wParam, lParam2);
						PostMessage(buttonHandle, WM_LBUTTONUP, wParam, lParam2);

                    }
					else if (LoginWindow.ButtonClickMethod == Interprocess.ButtonClickMethodType.Key)
					{
						ForceActivate(Value);
						Common.Trace.OutputTrace("buttonHandle : {0}", buttonHandle);
						PostKeyDownAndKeyUp(buttonHandle, LoginWindow.ButtonClickKeyCode);
					}
					else
					{
						Common.Trace.OutputErrorTrace("サポートされていないボタン押下方法({0})が指定されています。", LoginWindow.ButtonClickMethod);

						return false;
					}
				}

				return true;
			}
			#endregion
		}
		#endregion

		#region Fields
		private const int EnumWindowsTimeout = 30000;
		private const int AbortTimeout = 10000;
		private const int DefaultTimerPeriod = 100;
		private readonly object SyncRoot = new object();
		private Timer _timer;
		private readonly int _timerPeriod;
		private readonly int _muteTimeout;
		private volatile bool _stopped;
        // ▼ MODIFY 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
		//private readonly LoginWindow[] _loginWindows;
        private readonly Interprocess.AutoCompleteSetting[] _loginWindows;
        // ▲ MODIFY 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
		private readonly List<Handle> _ignorableHandleList = new List<Handle>();
		private readonly List<ProcessingHandle> _variableHandleList = new List<ProcessingHandle>();
		private readonly List<ProcessingHandle> _processingHandleList = new List<ProcessingHandle>();
		private readonly HandleComparer _handleComparer = new HandleComparer();
		#endregion

        #region Constructors
        // ▼ MODIFY 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
		//public LoginWindowMonitor()
		public LoginWindowMonitor(Interprocess.AutoCompleteInfo autoCompleteInfo)
        // ▲ MODIFY 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
		{
            _timerPeriod = Common.Configuration.AppSetting("LoginWindowMonitorTimerPeriod", DefaultTimerPeriod);
			// ▼ MODIFY 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
			//_loginWindows = LoginWindow.Load();
			_loginWindows = autoCompleteInfo.AutoCompleteSettings;
            // ▲ MODIFY 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
		}
		#endregion

		#region IDisposable
		~LoginWindowMonitor()
		{
			Dispose(false);
		}

		public void Dispose()
		{
			Dispose(true);
			GC.SuppressFinalize(this);
		}

		protected virtual void Dispose(bool disposing)
		{
			Stop();
		}
		#endregion

		#region Public Methods
		public void Start()
		{
			StartTimer();
		}

		public void Stop()
		{
			try
			{
				using (Common.TimedLock.Lock(SyncRoot, EnumWindowsTimeout))
				{
					_stopped = true;
					StopTimer();
					Abort();
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
        #endregion
        private bool FindUserElement(List<AutomationElement> elementList, Interprocess.AutoCompleteSetting loginWindow, out AutomationElement userElement)
        {
            userElement = null;
            try
            {
                if ((loginWindow.UserElementIndex >= 0) && (loginWindow.UserElementIndex < elementList.Count))
                {
                    var element = elementList[loginWindow.UserElementIndex];
                    var elementInformation = element.Current;

                    if ((!string.IsNullOrWhiteSpace(loginWindow.UserElementAutomationId)) &&
                        (!loginWindow.UserElementAutomationId.Equals(elementInformation.AutomationId, StringComparison.OrdinalIgnoreCase)))
                    {
                        return false;
                    }

                    if ((!string.IsNullOrWhiteSpace(loginWindow.UserElementClassName)) &&
                        (!loginWindow.UserElementClassName.Equals(elementInformation.ClassName, StringComparison.OrdinalIgnoreCase)))
                    {
                        return false;
                    }

                    userElement = element;
                    return true;
                }

                // インデックス指定がない場合は既存のロジックを使用
                foreach (var element in elementList)
                {
                    var elementInformation = element.Current;

                    if ((!string.IsNullOrWhiteSpace(loginWindow.UserElementAutomationId)) &&
                        (!loginWindow.UserElementAutomationId.Equals(elementInformation.AutomationId, StringComparison.OrdinalIgnoreCase)))
                    {
                        continue;
                    }

                    if ((!string.IsNullOrWhiteSpace(loginWindow.UserElementClassName)) &&
                        (!loginWindow.UserElementClassName.Equals(elementInformation.ClassName, StringComparison.OrdinalIgnoreCase)))
                    {
                        continue;
                    }

                    userElement = element;
                    return true;
                }

                return false;
            }
            catch (ElementNotAvailableException ex)
            {
                Common.Trace.OutputDebugTrace("ユーザー要素検索エラー: {0}", ex.Message);
                return false;
            }
            catch (Exception ex)
            {
                Common.Trace.OutputExceptionTrace(ex);
                return false;
            }
        }

        private bool FindPasswordElement(List<AutomationElement> elementList, Interprocess.AutoCompleteSetting loginWindow, out AutomationElement passwordElement)
        {
            passwordElement = null;
            try
            {
                if ((loginWindow.PasswordElementIndex >= 0) && (loginWindow.PasswordElementIndex < elementList.Count))
                {
                    var element = elementList[loginWindow.PasswordElementIndex];
                    var elementInformation = element.Current;

                    if ((!string.IsNullOrWhiteSpace(loginWindow.PasswordElementAutomationId)) &&
                        (!loginWindow.PasswordElementAutomationId.Equals(elementInformation.AutomationId, StringComparison.OrdinalIgnoreCase)))
                    {
                        return false;
                    }

                    if ((!string.IsNullOrWhiteSpace(loginWindow.PasswordElementClassName)) &&
                        (!loginWindow.PasswordElementClassName.Equals(elementInformation.ClassName, StringComparison.OrdinalIgnoreCase)))
                    {
                        return false;
                    }

                    passwordElement = element;
                    return true;
                }

                // インデックス指定がない場合は既存のロジックを使用
                foreach (var element in elementList)
                {
                    var elementInformation = element.Current;

                    if ((!string.IsNullOrWhiteSpace(loginWindow.PasswordElementAutomationId)) &&
                        (!loginWindow.PasswordElementAutomationId.Equals(elementInformation.AutomationId, StringComparison.OrdinalIgnoreCase)))
                    {
                        continue;
                    }

                    if ((!string.IsNullOrWhiteSpace(loginWindow.PasswordElementClassName)) &&
                        (!loginWindow.PasswordElementClassName.Equals(elementInformation.ClassName, StringComparison.OrdinalIgnoreCase)))
                    {
                        continue;
                    }

                    passwordElement = element;
                    return true;
                }

                return false;
            }
            catch (ElementNotAvailableException ex)
            {
                Common.Trace.OutputDebugTrace("パスワード要素検索エラー: {0}", ex.Message);
                return false;
            }
            catch (Exception ex)
            {
                Common.Trace.OutputExceptionTrace(ex);
                return false;
            }
        }

        private bool FindButtonElement(List<AutomationElement> elementList, Interprocess.AutoCompleteSetting loginWindow, out AutomationElement buttonElement)
        {
            buttonElement = null;
            try
            {
                if ((loginWindow.ButtonElementIndex >= 0) && (loginWindow.ButtonElementIndex < elementList.Count))
                {
                    var element = elementList[loginWindow.ButtonElementIndex];
                    var elementInformation = element.Current;

                    if ((!string.IsNullOrWhiteSpace(loginWindow.ButtonElementAutomationId)) &&
                        (!loginWindow.ButtonElementAutomationId.Equals(elementInformation.AutomationId, StringComparison.OrdinalIgnoreCase)))
                    {
                        return false;
                    }

                    if ((!string.IsNullOrWhiteSpace(loginWindow.ButtonElementClassName)) &&
                        (!loginWindow.ButtonElementClassName.Equals(elementInformation.ClassName, StringComparison.OrdinalIgnoreCase)))
                    {
                        return false;
                    }

                    buttonElement = element;
                    return true;
                }

                // インデックス指定がない場合は既存のロジックを使用
                foreach (var element in elementList)
                {
                    var elementInformation = element.Current;

                    if ((!string.IsNullOrWhiteSpace(loginWindow.ButtonElementAutomationId)) &&
                        (!loginWindow.ButtonElementAutomationId.Equals(elementInformation.AutomationId, StringComparison.OrdinalIgnoreCase)))
                    {
                        continue;
                    }

                    if ((!string.IsNullOrWhiteSpace(loginWindow.ButtonElementClassName)) &&
                        (!loginWindow.ButtonElementClassName.Equals(elementInformation.ClassName, StringComparison.OrdinalIgnoreCase)))
                    {
                        continue;
                    }

                    buttonElement = element;
                    return true;
                }

                return false;
            }
            catch (ElementNotAvailableException ex)
            {
                Common.Trace.OutputDebugTrace("ボタン要素検索エラー: {0}", ex.Message);
                return false;
            }
            catch (Exception ex)
            {
                Common.Trace.OutputExceptionTrace(ex);
                return false;
            }
        }
        #region Private Methods
        private void StartTimer()
		{
			using (Common.TimedLock.Lock(SyncRoot))
			{
				StopTimer();

				if (_stopped)
				{
					return;
				}

				_timer = new Timer(OnTimer, null, _timerPeriod, Timeout.Infinite);
			}
		}

		private void StopTimer()
		{
			try
			{
				using (Common.TimedLock.Lock(SyncRoot, EnumWindowsTimeout))
				{
					if (_timer != null)
					{
						_timer.Dispose();
						_timer = null;
					}
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnTimer(object state)
		{
			try
			{
				StopTimer();
				PerformMainRoutine();
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
			finally
			{
				try
				{
					StartTimer();
				}
				catch (Exception ex)
				{
					Common.Trace.OutputExceptionTrace(ex);
				}
			}
		}

		private void InsertIgnorableHandleToList(Handle handle)
		{
			int index = _ignorableHandleList.BinarySearch(handle, _handleComparer);

			if (index < 0)
			{
				index = ~index;
			}

			_ignorableHandleList.Insert(index, handle);
		}

		private void InsertIgnorableHandleToList(int index, Handle handle)
		{
			if (index < 0)
			{
				index = ~index;
			}

			_ignorableHandleList.Insert(index, handle);
		}

		private void InsertVariableHandleToList(ProcessingHandle handle)
		{
			int index = _variableHandleList.BinarySearch(handle, _handleComparer);

			if (index < 0)
			{
				index = ~index;
			}

			_variableHandleList.Insert(index, handle);
		}

		private void InsertVariableHandleToList(int index, ProcessingHandle handle)
		{
			if (index < 0)
			{
				index = ~index;
			}

			_variableHandleList.Insert(index, handle);
		}

		private void InsertProcessiongHandleToList(int index, ProcessingHandle handle)
		{
			if (index < 0)
			{
				index = ~index;
			}

			_processingHandleList.Insert(index, handle);
		}

		private void PerformMainRoutine()
		{
			using (Common.TimedLock.Lock(SyncRoot, AbortTimeout))
			{
				if (_stopped)
				{
					return;
				}

				foreach (var handle in _ignorableHandleList)
				{
					handle.Exists = false;
				}

				foreach (var handle in _processingHandleList)
				{
					handle.Exists = false;
				}

				bool result = EnumWindows((hWnd, lParam) =>
				{
					AutomationElementScope elementScope = null;
					try
					{
						var handle = new ProcessingHandle() { Value = hWnd, Exists = true };
						int ignorableHandleListIndex = -1;
						int processingHandleListIndex = -1;
						int variableHandleListIndex = -1;

						// 無視できるハンドルか確認する
						if (_ignorableHandleList.Count > 0)
						{
							ignorableHandleListIndex = _ignorableHandleList.BinarySearch(handle, _handleComparer);
#if VERBOSE_DEBUG_TRACE
							Common.Trace.OutputDebugTrace("handle={0} ignorableHandleListIndex={1}", hWnd, ignorableHandleListIndex);
#endif
							if (ignorableHandleListIndex >= 0)
							{
								_ignorableHandleList[ignorableHandleListIndex].Exists = true;
								return true;
							}
						}

						// 処理中のハンドルか確認する
						if (_processingHandleList.Count > 0)
						{
							processingHandleListIndex = _processingHandleList.BinarySearch(handle, _handleComparer);
#if VERBOSE_DEBUG_TRACE
							Common.Trace.OutputDebugTrace("handle={0} processingHandleListIndex={1}", hWnd, processingHandleListIndex);
#endif
							if (processingHandleListIndex >= 0)
							{
								_processingHandleList[processingHandleListIndex].Exists = true;
								return true;
							}
						}

						if (_variableHandleList.Count > 0)
						{
							variableHandleListIndex = _variableHandleList.BinarySearch(handle, _handleComparer);

							if (variableHandleListIndex >= 0)
							{
								_variableHandleList[variableHandleListIndex].Exists = true;
								handle = _variableHandleList[variableHandleListIndex];
							}
						}

						if (variableHandleListIndex < 0)
						{
							// 新たに発見されたハンドルなので素性を確認する				
							uint processId = 0;
							uint threadId = GetWindowThreadProcessId(hWnd, out processId);

							using (var process = Process.GetProcessById((int)processId))
							{
								handle.ProcessId = processId;
								handle.ProcessName = process.ProcessName;
#if VERBOSE_DEBUG_TRACE
								Common.Trace.OutputDebugTrace("handle={0} processName={1}", hWnd, processName);
#endif
							}
						}

						string caption = null;
						string className = null;
						bool ignore = true;

						foreach (var loginWindow in _loginWindows)
						{
							try
							{
								if (!handle.ProcessName.Equals(loginWindow.ProcessName, StringComparison.OrdinalIgnoreCase))
								{
									continue;
								}

								if (!string.IsNullOrWhiteSpace(loginWindow.TargetWindowClassName))
								{
									if (className == null)
									{
										className = GetClassName(hWnd);
									}

									if (!className.StartsWith(loginWindow.TargetWindowClassName, StringComparison.OrdinalIgnoreCase))
									{
										continue;
									}
								}

								if ((loginWindow.Variable) && (!handle.Variable))
								{
									// キャプションはコンテンツに応じて変わる可能性を想定して無視し、プロセス名とクラス名が一致している場合に監視が必要とされるウィンドウと同一であり得ると判断する
									handle.Variable = true;
								}

								if (!string.IsNullOrWhiteSpace(loginWindow.TargetWindowCaption))
								{
									if (caption == null)
									{
										caption = GetWindowText(hWnd);
									}

									if (!caption.StartsWith(loginWindow.TargetWindowCaption, StringComparison.OrdinalIgnoreCase))
									{
										continue;
									}
								}

								AutomationElement rootElement = null;
								try
								{
									rootElement = AutomationElement.FromHandle(hWnd);
									if (rootElement == null)
									{
										Common.Trace.OutputDebugTrace("[036] ルート要素取得失敗: hWnd={0}", hWnd);
										return true;
									}
									Common.Trace.OutputDebugTrace("[037] ルート要素取得成功: hWnd={0}, Hash={1}", hWnd, rootElement.GetHashCode());
								}
								catch (ElementNotAvailableException ex)
								{
									Common.Trace.OutputDebugTrace("[038] ルート要素取得失敗: hWnd={0}, Error={1}", hWnd, ex.Message);
									return true;
								}

								elementScope = new AutomationElementScope();
								elementScope.AddElement(rootElement);

								var elementList = new List<AutomationElement>();
								
								try
								{
									Common.Trace.OutputDebugTrace("[039] WalkControlElements開始: hWnd={0}", hWnd);
									AutomationHelper.WalkControlElements(rootElement, elementList, elementScope);
									Common.Trace.OutputDebugTrace("[040] WalkControlElements完了: hWnd={0}, 発見要素数={1}", hWnd, elementList.Count);
								}
								catch (Exception ex)
								{
									Common.Trace.OutputDebugTrace("[041] WalkControlElements失敗: hWnd={0}, Error={1}", hWnd, ex.Message);
								}

								if (elementList.Count <= 0)
								{
									return true;
								}

								AutomationElement userElement = null;
								AutomationElement passwordElement = null;
								AutomationElement buttonElement = null;

								// 要素検索時の例外処理強化
								if (!FindUserElement(elementList, loginWindow, out userElement) ||
									!FindPasswordElement(elementList, loginWindow, out passwordElement) ||
									!FindButtonElement(elementList, loginWindow, out buttonElement))
								{
									continue;
								}

								// 対象のウィンドウを発見
								handle.LoginWindow = loginWindow;
								handle.UserElement = userElement;
								handle.PasswordElement = passwordElement;
								handle.ButtonElement = buttonElement;
								handle.Finished = false;

								// 状態確認
								if (!IsWindowVisible(handle.Value))
								{
									handle.Finished = true;
									break;
								}

								eDoktor.Common.Trace.OutputDebugTrace("対象ウィンドウ発見[{0}:{1}]", handle.LoginWindow.Name, handle.Value);

								if (variableHandleListIndex >= 0)
								{
									_variableHandleList.RemoveAt(variableHandleListIndex);
								}

								InsertProcessiongHandleToList(processingHandleListIndex, handle);
								ignore = false;
								handle.Start();
								break;
							}
							catch (ElementNotAvailableException ex)
							{
								Common.Trace.OutputDebugTrace("要素アクセスエラー: {0}", ex.Message);
								continue;
							}
							catch (Exception ex)
							{
								Common.Trace.OutputExceptionTrace(ex);
								continue;
							}
						}

						if (handle.Variable)
						{
							InsertVariableHandleToList(variableHandleListIndex, handle);
						}
						else if (ignore)
						{
							InsertIgnorableHandleToList(ignorableHandleListIndex, handle);
						}
					}
					catch (Exception ex)
					{
						Common.Trace.OutputExceptionTrace(ex);
					}
					finally
					{
						// 確実にリソース解放
						if (elementScope != null)
						{
							Common.Trace.OutputDebugTrace("[042] elementScope解放開始: hWnd={0}", hWnd);
							elementScope.Dispose();
							Common.Trace.OutputDebugTrace("[043] elementScope解放完了: hWnd={0}", hWnd);
						}
					}

					return true;
				}, IntPtr.Zero);

				if (_processingHandleList.Count > 0)
				{
					for (int i = _processingHandleList.Count - 1; i >= 0; --i)
					{
						var handle = _processingHandleList[i];

						if (handle.Finished)
						{
							_processingHandleList.RemoveAt(i);

							if (handle.Variable)
							{
								InsertVariableHandleToList(handle);
							}
							else
							{
								InsertIgnorableHandleToList(handle);
							}
							continue;
						}

						if ((!handle.Exists) && (!handle.AbortRequested))
						{
							handle.Abort();
						}
					}
				}

				if (_variableHandleList.Count > 0)
				{
					for (int i = _variableHandleList.Count - 1; i >= 0; --i)
					{
						var handle = _variableHandleList[i];

						if (!handle.Exists)
						{
							_variableHandleList.RemoveAt(i);
						}
					}
				}

				if (_ignorableHandleList.Count > 0)
				{
					for (int i = _ignorableHandleList.Count - 1; i >= 0; --i)
					{
						var handle = _ignorableHandleList[i];

						if (!handle.Exists)
						{
							_ignorableHandleList.RemoveAt(i);
						}
					}
				}
			}
		}

		private void Abort()
		{
			try
			{
				using (Common.TimedLock.Lock(SyncRoot, EnumWindowsTimeout))
				{
					foreach (var handle in _processingHandleList)
					{
						handle.Abort();
					}
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private static string GetWindowText(IntPtr hWnd)
		{
			var size = SendMessage(hWnd, WM_GETTEXTLENGTH, 0, 0).ToInt32();

			if (size > 0)
			{
				var buf = new StringBuilder(size + 1);
				SendMessage(hWnd, (int)WM_GETTEXT, buf.Capacity, buf);
				return buf.ToString();
			}

			return string.Empty;
		}

		private static string GetWindowText2(IntPtr hWnd)
		{
			int textLen = GetWindowTextLength(hWnd);

			if (0 < textLen)
			{
				var buf = new StringBuilder(textLen + 1);
				GetWindowText(hWnd, buf, buf.Capacity);
				return buf.ToString();
			}

			return string.Empty;
		}

		private static string GetClassName(IntPtr hWnd)
		{
			const int MaxClassNameLength = 256;
			var buf = new StringBuilder(MaxClassNameLength);
			int ret = GetClassName(hWnd, buf, buf.Capacity);
			return (ret >= 0) ? buf.ToString() : string.Empty;
		}

		private static void ThrowLastWin32ErrorException()
		{
			throw new Win32Exception(Marshal.GetLastWin32Error());
		}

		private static IntPtr MakeLParam(int loWord, int hiWord)
		{
			return new IntPtr((hiWord << 16) | (loWord & 0xffff));
		}

		private static void ForceActivate(IntPtr handle)
		{
			if (IsIconic(handle))
			{
				ShowWindowAsync(handle, SW_RESTORE);
			}

			var currentTmeout = IntPtr.Zero;
			uint foregroundWindowThreadId = 0;
			uint targetWindowThreadId = 0;
			var foregroundWindowHandle = GetForegroundWindow();

			if (foregroundWindowHandle != handle)
			{
				uint processId;
				foregroundWindowThreadId = GetWindowThreadProcessId(foregroundWindowHandle, out processId);
				targetWindowThreadId = GetWindowThreadProcessId(handle, out processId);
				AttachThreadInput(targetWindowThreadId, foregroundWindowThreadId, true);
				var tempTimeout = IntPtr.Zero;
				// 起動中の他のアプリケーションにより本来の値とは違う値が現在値として取得される場合がある
				SystemParametersInfo(SPI_GETFOREGROUNDLOCKTIMEOUT, 0, currentTmeout, 0);
				SystemParametersInfo(SPI_SETFOREGROUNDLOCKTIMEOUT, 0, tempTimeout, 0);
			}

			SetForegroundWindow(handle);

			if (foregroundWindowHandle != handle)
			{
				// 本来の値と違う値を書き込んでしまう可能性があるためSPIF_UPDATEINIFILE | SPIF_SENDCHANGEは使用しない
				SystemParametersInfo(SPI_SETFOREGROUNDLOCKTIMEOUT, 0, currentTmeout, 0/*SPIF_UPDATEINIFILE | SPIF_SENDCHANGE*/);
				AttachThreadInput(targetWindowThreadId, foregroundWindowThreadId, false);
			}
		}

		private static void PostKeyDownAndKeyUp(IntPtr handle, int keyCode)
		{
			uint repeatCount = 0;
			uint scanCode = 0;
			uint extended = 0;
			uint context = 0;
			uint previousState = 0;
			uint transition = 0;

			scanCode = 65;
			uint lParamDown = repeatCount
				| (scanCode << 16)
				| (extended << 24)
				| (context << 29)
				| (previousState << 30)
				| (transition << 31);
			previousState = 1;
			transition = 1;
			uint lParamUp = repeatCount
				| (scanCode << 16)
				| (extended << 24)
				| (context << 29)
				| (previousState << 30)
				| (transition << 31);
			PostMessage(handle, WM_KEYDOWN, (IntPtr)keyCode, unchecked((IntPtr)(int)lParamDown));
			PostMessage(handle, WM_KEYUP, (IntPtr)keyCode, unchecked((IntPtr)(int)lParamUp));
		}


		private static int GetUserWithPassword(int systemId, string securityKey, out string user, out string password)
		{
			user = string.Empty;
			password = string.Empty;

			try
			{
				Type t = Type.GetTypeFromProgID("eDoktor.Taikoban");
				object taikoban = Activator.CreateInstance(t);

				object[] args = new object[] { systemId, securityKey, null, null };
				ParameterModifier mod = new ParameterModifier(4);
				mod[0] = false;
				mod[1] = false;
				mod[2] = true;
				mod[3] = true;
				ParameterModifier[] mods = { mod };
				object result = t.InvokeMember("GetUserWithPassword", BindingFlags.InvokeMethod,
				null, taikoban, args, mods, null, null);

				user = (args[2] != null) ? args[2].ToString() : string.Empty;
				password = (args[3] != null) ? args[3].ToString() : string.Empty;

				return (int)result;
			}
			catch (Exception ex)
			{
				eDoktor.Common.Trace.OutputExceptionTrace(ex);

				return -3;
			}
		}
		#endregion

		#region P/Invoke
		delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

		const int WM_GETTEXT = 0x000D;
		const int WM_GETTEXTLENGTH = 0x000E;
		const int WM_KEYDOWN = 0x0100;
		const int WM_KEYUP = 0x0101;
		const int SW_RESTORE = 9;
		const int MK_LBUTTON = 0x0001;
		const int WM_LBUTTONDOWN = 0x0201;
		const int WM_LBUTTONUP = 0x0202;
		const uint SPI_GETFOREGROUNDLOCKTIMEOUT = 0x2000;
		const uint SPI_SETFOREGROUNDLOCKTIMEOUT = 0x2001;

		[DllImport("user32.dll")]
		[return: MarshalAs(UnmanagedType.Bool)]
		static extern bool EnumWindows(EnumWindowsProc lpEnumFunc, IntPtr lParam);

		[DllImport("user32.dll", SetLastError = true)]
		static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint lpdwProcessId);

		[DllImport("user32.dll", EntryPoint = "SendMessage", CharSet = CharSet.Auto, SetLastError = true)]
		static extern bool SendMessage(IntPtr hWnd, uint Msg, int wParam, StringBuilder lParam);

		[DllImport("user32.dll", SetLastError = true)]
		static extern IntPtr SendMessage(IntPtr hWnd, int Msg, int wparam, int lparam);

		[DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
		static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

		[DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
		static extern int GetWindowTextLength(IntPtr hWnd);

		[DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
		static extern int GetClassName(IntPtr hWnd, StringBuilder lpClassName, int nMaxCount);

		[DllImport("User32.dll", CharSet = CharSet.Auto, SetLastError = true)]
		static extern bool PostMessage(IntPtr hWnd, int Msg, IntPtr wParam, IntPtr lParam);

		[DllImport("user32.dll")]
		static extern bool IsIconic(IntPtr hWnd);

		[DllImport("user32.dll")]
		static extern bool ShowWindowAsync(IntPtr hWnd, int nCmdShow);

		[DllImport("user32.dll")]
		static extern IntPtr GetForegroundWindow();

		[DllImport("user32.dll")]
		[return: MarshalAs(UnmanagedType.Bool)]
		static extern bool SetForegroundWindow(IntPtr hWnd);

		[DllImport("user32.dll")]
		static extern bool AttachThreadInput(uint idAttach, uint idAttachTo, bool fAttach);

		[DllImport("user32.dll", SetLastError = true)]
		static extern bool SystemParametersInfo(uint uiAction, uint uiParam, IntPtr pvParam, uint fWinIni);

		[DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
		static extern IntPtr FindWindow(string lpClassName, string lpWindowName);

		[DllImport("user32.dll")]
		static extern bool IsWindowVisible(IntPtr hWnd);
		#endregion
	}
}
