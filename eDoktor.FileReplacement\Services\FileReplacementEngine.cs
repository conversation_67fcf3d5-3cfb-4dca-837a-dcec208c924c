using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using eDoktor.FileReplacement.Models;
using eDoktor.FileReplacement.Utils;

namespace eDoktor.FileReplacement.Services
{
    /// <summary>
    /// 文字コードを保持したまま文字列置き換えを行うエンジン
    /// </summary>
    public class FileReplacementEngine
    {
        private readonly ILogger _logger;
        private readonly string[] _excludeExtensions = {
            ".msi", ".exe", ".obj", ".pdb", ".ilk", ".res", ".pch", ".iobj", ".ipdb",
            ".dll", ".zip", ".rar", ".7z", ".tar", ".gz", ".bmp", ".jpg", ".jpeg",
            ".png", ".gif", ".ico", ".pdf", ".doc", ".docx", ".xls", ".xlsx"
        };

        /// <summary>
        /// コンストラクタ
        /// </summary>
        /// <param name="logger">ログ出力インターフェース（nullの場合はConsoleLoggerを使用）</param>
        public FileReplacementEngine(ILogger logger = null)
        {
            _logger = logger ?? new ConsoleLogger();
        }

        /// <summary>
        /// 指定フォルダ内のファイルに対して文字列置き換えを実行
        /// </summary>
        /// <param name="targetFolders">処理対象フォルダ配列</param>
        /// <param name="outputPath">出力先パス</param>
        /// <param name="ruleSet">置き換えルールセット</param>
        /// <returns>処理結果</returns>
        public async Task<ReplacementResult> ProcessReplacementAsync(string[] targetFolders, string outputPath, ReplacementRuleSet ruleSet)
        {
            var result = new ReplacementResult();

            try
            {
                _logger.Info("文字列置き換え処理を開始します");
                result.StartTime = DateTime.Now;

                // 出力ディレクトリの準備
                PrepareOutputDirectory(outputPath);

                // 各対象フォルダを処理
                foreach (var targetFolder in targetFolders)
                {
                    await ProcessTargetFolderAsync(targetFolder, outputPath, ruleSet, result);
                }

                result.EndTime = DateTime.Now;
                result.IsSuccess = true;

                _logger.Info($"文字列置き換え処理が完了しました。処理時間: {result.ProcessingTime.TotalSeconds:F2}秒");
                return result;
            }
            catch (Exception ex)
            {
                result.EndTime = DateTime.Now;
                result.IsSuccess = false;
                result.ErrorMessage = ex.Message;

                _logger.Error($"文字列置き換え処理中にエラーが発生しました: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 対象フォルダを処理
        /// </summary>
        /// <param name="targetFolder">対象フォルダ</param>
        /// <param name="outputPath">出力パス</param>
        /// <param name="ruleSet">置き換えルールセット</param>
        /// <param name="result">処理結果</param>
        private async Task ProcessTargetFolderAsync(string targetFolder, string outputPath, ReplacementRuleSet ruleSet, ReplacementResult result)
        {
            try
            {
                if (!Directory.Exists(targetFolder))
                {
                    var message = $"警告: 対象フォルダが見つかりません: {targetFolder}";
                    _logger.Warn(message);
                    result.Warnings.Add(message);
                    return;
                }

                _logger.Info($"対象フォルダの処理を開始: {targetFolder}");

                // フォルダ名を取得（出力先のサブフォルダ名として使用）
                var folderName = Path.GetFileName(targetFolder);
                var destinationPath = Path.Combine(outputPath, folderName);

                // テンプレートフォルダから出力フォルダにコピー
                await CopyDirectoryAsync(targetFolder, destinationPath);

                // 出力フォルダ内のファイルを置換処理
                await ProcessFilesInDirectoryAsync(destinationPath, ruleSet, result);

                _logger.Info($"対象フォルダの処理が完了: {targetFolder} → {destinationPath}");
            }
            catch (Exception ex)
            {
                var message = $"対象フォルダの処理中にエラーが発生しました: {targetFolder} - {ex.Message}";
                _logger.Error(message, ex);
                result.Errors.Add(message);
            }
        }

        /// <summary>
        /// ディレクトリを再帰的にコピー
        /// </summary>
        /// <param name="sourceDir">ソースディレクトリ</param>
        /// <param name="destDir">出力先ディレクトリ</param>
        private async Task CopyDirectoryAsync(string sourceDir, string destDir)
        {
            await Task.Run(() =>
            {
                // 出力先ディレクトリを作成
                Directory.CreateDirectory(destDir);
                _logger.Debug($"ディレクトリを作成: {destDir}");

                // ファイルをコピー
                foreach (var file in Directory.GetFiles(sourceDir))
                {
                    var fileName = Path.GetFileName(file);
                    var destFile = Path.Combine(destDir, fileName);
                    File.Copy(file, destFile, true);
                    _logger.Debug($"ファイルをコピー: {file} → {destFile}");
                }

                // サブディレクトリを再帰的にコピー
                foreach (var subDir in Directory.GetDirectories(sourceDir))
                {
                    var subDirName = Path.GetFileName(subDir);
                    var destSubDir = Path.Combine(destDir, subDirName);
                    _logger.Debug($"サブディレクトリを処理: {subDir} → {destSubDir}");
                    CopyDirectoryRecursive(subDir, destSubDir);
                }
            });
        }

        /// <summary>
        /// ディレクトリを再帰的にコピー（同期版）
        /// </summary>
        /// <param name="sourceDir">ソースディレクトリ</param>
        /// <param name="destDir">出力先ディレクトリ</param>
        private void CopyDirectoryRecursive(string sourceDir, string destDir)
        {
            Directory.CreateDirectory(destDir);

            foreach (var file in Directory.GetFiles(sourceDir))
            {
                var fileName = Path.GetFileName(file);
                var destFile = Path.Combine(destDir, fileName);
                File.Copy(file, destFile, true);
            }

            foreach (var subDir in Directory.GetDirectories(sourceDir))
            {
                var subDirName = Path.GetFileName(subDir);
                var destSubDir = Path.Combine(destDir, subDirName);
                CopyDirectoryRecursive(subDir, destSubDir);
            }
        }

        /// <summary>
        /// ディレクトリ内のファイルを処理
        /// </summary>
        /// <param name="directoryPath">ディレクトリパス</param>
        /// <param name="ruleSet">置き換えルールセット</param>
        /// <param name="result">処理結果</param>
        private async Task ProcessFilesInDirectoryAsync(string directoryPath, ReplacementRuleSet ruleSet, ReplacementResult result)
        {
            try
            {
                // 対象ファイルの取得
                var targetFiles = GetTargetFiles(directoryPath);

                _logger.Info($"見つかった対象ファイル数: {targetFiles.Length}");

                if (targetFiles.Length == 0)
                {
                    var message = $"警告: {directoryPath} 内に処理対象となるファイルが見つかりませんでした。";
                    _logger.Warn(message);
                    result.Warnings.Add(message);
                    return;
                }

                // 各ファイルを処理
                foreach (var file in targetFiles)
                {
                    await ProcessFileAsync(file, ruleSet, result);
                }
            }
            catch (Exception ex)
            {
                var message = $"ディレクトリ処理中にエラーが発生しました: {directoryPath} - {ex.Message}";
                _logger.Error(message, ex);
                result.Errors.Add(message);
            }
        }

        /// <summary>
        /// 対象ファイルを取得
        /// </summary>
        /// <param name="folderPath">フォルダパス</param>
        /// <returns>対象ファイル配列</returns>
        private FileInfo[] GetTargetFiles(string folderPath)
        {
            var directory = new DirectoryInfo(folderPath);
            var allFiles = directory.GetFiles("*", SearchOption.AllDirectories);

            return allFiles.Where(file =>
                !_excludeExtensions.Contains(file.Extension.ToLower())
            ).ToArray();
        }

        /// <summary>
        /// ファイルを処理
        /// </summary>
        /// <param name="file">ファイル情報</param>
        /// <param name="ruleSet">置き換えルールセット</param>
        /// <param name="result">処理結果</param>
        private async Task ProcessFileAsync(FileInfo file, ReplacementRuleSet ruleSet, ReplacementResult result)
        {
            try
            {
                // 文字コードを検出
                var encoding = EncodingDetector.DetectEncoding(file.FullName);
                _logger.Debug($"ファイル: {file.Name}, 検出された文字コード: {encoding.EncodingName}");

                // ファイル内容を読み込み
                var content = File.ReadAllText(file.FullName, encoding);
                var originalContent = content;

                // 読み込み内容の検証（日本語文字化けチェック）
                if (ContainsGarbledText(content))
                {
                    _logger.Warn($"文字化けの可能性があります: {file.Name}");
                    // Shift_JISで再試行
                    try
                    {
                        var sjisEncoding = Encoding.GetEncoding("Shift_JIS");
                        content = File.ReadAllText(file.FullName, sjisEncoding);
                        originalContent = content;
                        encoding = sjisEncoding;
                        _logger.Info($"Shift_JISで再読み込みしました: {file.Name}");
                    }
                    catch (Exception ex)
                    {
                        _logger.Warn($"Shift_JIS再読み込み失敗: {file.Name} - {ex.Message}");
                    }
                }

                // 文字列置き換えを実行
                var replacementsFound = false;
                foreach (var rule in ruleSet.Rules)
                {
                    if (!string.IsNullOrEmpty(rule.Pattern) && !string.IsNullOrEmpty(rule.Replacement))
                    {
                        if (content.Contains(rule.Pattern))
                        {
                            content = content.Replace(rule.Pattern, rule.Replacement);
                            result.AddReplacementResult(rule.Pattern, file.FullName);
                            replacementsFound = true;
                        }
                    }
                }

                // 変更があった場合のみ、元の文字コードで書き込み
                if (content != originalContent)
                {
                    File.WriteAllText(file.FullName, content, encoding);
                    result.ProcessedFiles++;
                    _logger.Debug($"ファイルを更新しました: {file.FullName}");
                }
                else
                {
                    result.SkippedFiles++;
                }
            }
            catch (Exception ex)
            {
                var message = $"ファイル処理中にエラーが発生しました: {file.FullName} - {ex.Message}";
                _logger.Error(message, ex);
                result.Errors.Add(message);
            }
        }

        /// <summary>
        /// 出力ディレクトリを準備
        /// </summary>
        /// <param name="outputPath">出力パス</param>
        private void PrepareOutputDirectory(string outputPath)
        {
            if (Directory.Exists(outputPath))
            {
                Directory.Delete(outputPath, true);
            }

            Directory.CreateDirectory(outputPath);
        }

        /// <summary>
        /// 文字化けしたテキストが含まれているかチェック
        /// </summary>
        /// <param name="text">チェック対象のテキスト</param>
        /// <returns>文字化けの可能性がある場合true</returns>
        private bool ContainsGarbledText(string text)
        {
            if (string.IsNullOrEmpty(text)) return false;

            // 文字化けの典型的なパターンをチェック
            var garbledPatterns = new[]
            {
                "縺", "繧", "繝", "縲", "蟄", "蜿", "蠎", "蟇", "蟆", "蟄", // よくある文字化けパターン
                "�", // 置換文字（U+FFFD）
                "??" // 不明文字の連続
            };

            int garbledCount = 0;
            foreach (var pattern in garbledPatterns)
            {
                garbledCount += CountOccurrences(text, pattern);
            }

            // 制御文字の異常な出現もチェック
            int controlCharCount = 0;
            foreach (char c in text)
            {
                if (char.IsControl(c) && c != '\r' && c != '\n' && c != '\t')
                {
                    controlCharCount++;
                }
            }

            // 文字化けパターンが多い、または制御文字が異常に多い場合
            return garbledCount > 0 || (double)controlCharCount / text.Length > 0.05;
        }

        /// <summary>
        /// 文字列内の指定パターンの出現回数をカウント
        /// </summary>
        /// <param name="text">対象文字列</param>
        /// <param name="pattern">検索パターン</param>
        /// <returns>出現回数</returns>
        private int CountOccurrences(string text, string pattern)
        {
            int count = 0;
            int index = 0;
            while ((index = text.IndexOf(pattern, index)) != -1)
            {
                count++;
                index += pattern.Length;
            }
            return count;
        }
    }
}
