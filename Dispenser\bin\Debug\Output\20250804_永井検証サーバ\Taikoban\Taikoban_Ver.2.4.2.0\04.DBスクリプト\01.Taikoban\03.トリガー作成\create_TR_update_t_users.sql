USE [$(Param1)]
GO

/****** Object:  Trigger [dbo].[TR_update_t_users]    Script Date: 10/06/2014 16:00:58 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*** CREATE TRIGGER ***/
IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[TR_update_t_users]') AND type in (N'TR'))
DROP TRIGGER [dbo].[TR_update_t_users]
GO

CREATE TRIGGER [dbo].[TR_update_t_users] ON [dbo].[t_users]
FOR UPDATE
AS 
BEGIN

UPDATE t_users
SET modification_datetime = GETDATE()
WHERE id IN (SELECT id FROM inserted)

END
RETURN


GO

