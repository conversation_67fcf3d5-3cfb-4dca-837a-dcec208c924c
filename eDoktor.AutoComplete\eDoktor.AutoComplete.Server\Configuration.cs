﻿// Configuration.cs

namespace eDoktor.AutoComplete.Server
{
	static class Configuration
	{
		#region Properties
		public static bool QueryTerminalConfigWithRemoteClientName { get; private set; }
		public static bool QueryVdaStartup { get; private set; }
		public static bool UpdateActiveDirectoryPassword { get; private set; }
		public static bool LoadLicenseFile { get; private set; }
		#endregion

		#region Constructors
		static Configuration()
		{
			try
			{
				QueryTerminalConfigWithRemoteClientName = Common.Configuration.AppSetting("QueryTerminalConfigWithRemoteClientName", false);
				QueryVdaStartup = Common.Configuration.AppSetting("QueryVdaStartup", false);
				UpdateActiveDirectoryPassword = Common.Configuration.AppSetting("UpdateActiveDirectoryPassword", false);
				LoadLicenseFile = true;
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion
	}
}
