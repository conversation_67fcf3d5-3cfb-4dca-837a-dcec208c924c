USE [$(Param1)]
GO

/****** Object:  Table [dbo].[t_face_auth_use_devices]    Script Date: 2021/07/07 9:54:21 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_face_auth_use_devices]') AND type in (N'U'))
DROP TABLE [dbo].[t_face_auth_use_devices]
GO

CREATE TABLE [dbo].[t_face_auth_use_devices](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[device] [nvarchar](max) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[registered_by] [int] NOT NULL,
	[modification_datetime] [datetime2](7) NOT NULL,
	[modified_by] [int] NOT NULL,
	[invalidated] [bit] NOT NULL,
	[invalidation_datetime] [datetime2](7) NULL,
	[invalidated_by] [int] NOT NULL,
	[logically_deleted] [bit] NOT NULL,
	[logical_deletion_datetime] [datetime2](7) NULL,
	[logically_deleted_by] [int] NOT NULL,
 CONSTRAINT [PK_t_face_auth_use_devices] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[t_face_auth_use_devices] ADD  CONSTRAINT [DF_t_face_auth_use_devices_device]  DEFAULT ((0)) FOR [device]
GO

ALTER TABLE [dbo].[t_face_auth_use_devices] ADD  CONSTRAINT [DF_t_face_auth_use_devices_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO

ALTER TABLE [dbo].[t_face_auth_use_devices] ADD  CONSTRAINT [DF_t_face_auth_use_devices_registered_by]  DEFAULT ((0)) FOR [registered_by]
GO

ALTER TABLE [dbo].[t_face_auth_use_devices] ADD  CONSTRAINT [DF_t_face_auth_use_devices_modification_datetime]  DEFAULT (getdate()) FOR [modification_datetime]
GO

ALTER TABLE [dbo].[t_face_auth_use_devices] ADD  CONSTRAINT [DF_t_face_auth_use_devices_modified_by]  DEFAULT ((0)) FOR [modified_by]
GO

ALTER TABLE [dbo].[t_face_auth_use_devices] ADD  CONSTRAINT [DF_t_face_auth_use_devices_invalidated]  DEFAULT ((0)) FOR [invalidated]
GO

ALTER TABLE [dbo].[t_face_auth_use_devices] ADD  CONSTRAINT [DF_t_face_auth_use_devices_invalidated_by]  DEFAULT ((0)) FOR [invalidated_by]
GO

ALTER TABLE [dbo].[t_face_auth_use_devices] ADD  CONSTRAINT [DF_t_face_auth_use_devices_logically_deleted]  DEFAULT ((0)) FOR [logically_deleted]
GO

ALTER TABLE [dbo].[t_face_auth_use_devices] ADD  CONSTRAINT [DF_t_face_auth_use_devices_logically_deleted_by]  DEFAULT ((0)) FOR [logically_deleted_by]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_use_devices', @level2type=N'COLUMN',@level2name=N'id'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'デバイス' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_use_devices', @level2type=N'COLUMN',@level2name=N'device'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_use_devices', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_use_devices', @level2type=N'COLUMN',@level2name=N'registered_by'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_use_devices', @level2type=N'COLUMN',@level2name=N'modification_datetime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_use_devices', @level2type=N'COLUMN',@level2name=N'modified_by'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効フラグ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_use_devices', @level2type=N'COLUMN',@level2name=N'invalidated'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_use_devices', @level2type=N'COLUMN',@level2name=N'invalidation_datetime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効登録者' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_use_devices', @level2type=N'COLUMN',@level2name=N'invalidated_by'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_use_devices', @level2type=N'COLUMN',@level2name=N'logically_deleted'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_use_devices', @level2type=N'COLUMN',@level2name=N'logical_deletion_datetime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_face_auth_use_devices', @level2type=N'COLUMN',@level2name=N'logically_deleted_by'
GO


