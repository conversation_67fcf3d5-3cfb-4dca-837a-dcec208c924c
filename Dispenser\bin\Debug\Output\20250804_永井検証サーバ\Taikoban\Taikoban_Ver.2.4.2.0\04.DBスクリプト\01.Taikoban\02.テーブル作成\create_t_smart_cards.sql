USE [$(Param1)]
GO
/****** Object:  Table [dbo].[t_smart_cards]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_smart_cards]') AND type in (N'U'))
DROP TABLE [dbo].[t_smart_cards]
GO

CREATE TABLE [dbo].[t_smart_cards](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[registered_by] [int] NOT NULL,
	[modification_datetime] [datetime2](7) NOT NULL,
	[modified_by] [int] NOT NULL,
	[invalidated] [bit] NOT NULL,
	[invalidation_datetime] [datetime2](7) NULL,
	[invalidated_by] [int] NOT NULL,
	[logically_deleted] [bit] NOT NULL,
	[logical_deletion_datetime] [datetime2](7) NULL,
	[logically_deleted_by] [int] NOT NULL,
	[usage_start_datetime] [datetime2](7) NULL,
	[usage_end_datetime] [datetime2](7) NULL,
	[unique_id] [nvarchar](255) NOT NULL,
	[card_type] [int] NOT NULL,
	[usage_type] [int] NOT NULL,
	[lending_id] [int] NOT NULL,
	[notes] [nvarchar](255) NOT NULL,
 CONSTRAINT [PK_t_smart_cards] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [IX_t_smart_cards] UNIQUE NONCLUSTERED 
(
	[card_type] ASC,
	[unique_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_smart_cards] ADD  CONSTRAINT [DF_t_smart_cards_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO
ALTER TABLE [dbo].[t_smart_cards] ADD  CONSTRAINT [DF_t_smart_cards_registered_by]  DEFAULT ((0)) FOR [registered_by]
GO
ALTER TABLE [dbo].[t_smart_cards] ADD  CONSTRAINT [DF_t_smart_cards_modification_datetime]  DEFAULT (getdate()) FOR [modification_datetime]
GO
ALTER TABLE [dbo].[t_smart_cards] ADD  CONSTRAINT [DF_t_smart_cards_modified_by]  DEFAULT ((0)) FOR [modified_by]
GO
ALTER TABLE [dbo].[t_smart_cards] ADD  CONSTRAINT [DF_t_smart_cards_invalidated]  DEFAULT ('false') FOR [invalidated]
GO
ALTER TABLE [dbo].[t_smart_cards] ADD  CONSTRAINT [DF_t_smart_cards_invalidated_by]  DEFAULT ((0)) FOR [invalidated_by]
GO
ALTER TABLE [dbo].[t_smart_cards] ADD  CONSTRAINT [DF_t_smart_cards_logically_deleted]  DEFAULT ('false') FOR [logically_deleted]
GO
ALTER TABLE [dbo].[t_smart_cards] ADD  CONSTRAINT [DF_t_smart_cards_logically_deleted_by]  DEFAULT ((0)) FOR [logically_deleted_by]
GO
ALTER TABLE [dbo].[t_smart_cards] ADD  CONSTRAINT [DF_t_smart_cards_unique_id]  DEFAULT ('') FOR [unique_id]
GO
ALTER TABLE [dbo].[t_smart_cards] ADD  CONSTRAINT [DF_t_smart_cards_card_type]  DEFAULT ((0)) FOR [card_type]
GO
ALTER TABLE [dbo].[t_smart_cards] ADD  DEFAULT ((0)) FOR [usage_type]
GO
ALTER TABLE [dbo].[t_smart_cards] ADD  DEFAULT ((0)) FOR [lending_id]
GO
ALTER TABLE [dbo].[t_smart_cards] ADD  CONSTRAINT [DF_t_smart_cards_notes]  DEFAULT ('') FOR [notes]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_smart_cards', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_smart_cards', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_smart_cards', @level2type=N'COLUMN',@level2name=N'registered_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_smart_cards', @level2type=N'COLUMN',@level2name=N'modification_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_smart_cards', @level2type=N'COLUMN',@level2name=N'modified_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_smart_cards', @level2type=N'COLUMN',@level2name=N'invalidated'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効化日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_smart_cards', @level2type=N'COLUMN',@level2name=N'invalidation_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効化を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_smart_cards', @level2type=N'COLUMN',@level2name=N'invalidated_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_smart_cards', @level2type=N'COLUMN',@level2name=N'logically_deleted'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_smart_cards', @level2type=N'COLUMN',@level2name=N'logical_deletion_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_smart_cards', @level2type=N'COLUMN',@level2name=N'logically_deleted_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'有効期間開始日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_smart_cards', @level2type=N'COLUMN',@level2name=N'usage_start_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'有効期間終了日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_smart_cards', @level2type=N'COLUMN',@level2name=N'usage_end_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'固有ID (FeliCa IDm/MIFARE UIDなど)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_smart_cards', @level2type=N'COLUMN',@level2name=N'unique_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'カードタイプ 0:未定義 1:FeliCa 2:MIFARE 3:PicoPass' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_smart_cards', @level2type=N'COLUMN',@level2name=N'card_type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'用途タイプ 0:未定義 1:本カード 2:仮カード' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_smart_cards', @level2type=N'COLUMN',@level2name=N'usage_type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'仮カード発行ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_smart_cards', @level2type=N'COLUMN',@level2name=N'lending_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'注記' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_smart_cards', @level2type=N'COLUMN',@level2name=N'notes'
GO
