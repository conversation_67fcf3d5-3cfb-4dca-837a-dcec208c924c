@echo off
REM ##### �f�[�^�x�[�X��o�b�N�A�b�v���� #####

REM �萔�錾
SET OUTPUT=D:\eDoktor\Taikoban\DatabaseBackup
SET SERVER1=SV_Personal_nagai
SET SERVER2=SV_Personal_nagai.
SET HOST1=SV_Personal_nagai
SET HOST2=SV_Personal_nagai.
SET BACKUP=D:\backup\TADB\%COMPUTERNAME%
SET LOGPATH=D:\eDoktor\Taikoban\Scripts\Logs\ExecAutoBackup\ExecAutoBackup_%DATE:~-2%.log
SET SQL=SELECT COUNT('X') FROM sys.database_mirroring WHERE mirroring_guid IS NOT NULL AND DB_NAME(database_id) = 'Taikoban' AND mirroring_role_desc = 'PRINCIPAL';


REM �J�����g�f�B���N�g����ݒ�
cd /d %~dp0

echo %DATE% %TIME% �X�N���v�g�J�n > %LOGPATH%
echo.>> %LOGPATH%
echo.>> %LOGPATH%

REM ����Ǘ���s��
del %OUTPUT%\Taikoban_db_3.bak /F /Q >> %LOGPATH%
ren %OUTPUT%\Taikoban_db_2.bak Taikoban_db_3.bak >> %LOGPATH%
ren %OUTPUT%\Taikoban_db_1.bak Taikoban_db_2.bak >> %LOGPATH%

REM �v�����p���T�[�o�[��擾����
SQLCMD -S %SERVER1% -E -H %HOST1% -Q "EXIT(set nocount on;%SQL%)" -h -1

IF %ERRORLEVEL% EQU 1 (
  SET SERVER=%SERVER1%
  SET HOST=%HOST1%

  GOTO SERVER
)

SQLCMD -S %SERVER2% -E -H %HOST2% -Q "EXIT(set nocount on;%SQL%)" -h -1

IF %ERRORLEVEL% EQU 1 (
  SET SERVER=%SERVER2%
  SET HOST=%HOST2%
) ELSE (
  SET SERVER=localhost
  SET HOST=localhost
)

:SERVER

echo �v�����V�p���T�[�o�[�F%SERVER%,%HOST% >> %LOGPATH%

echo.>> %LOGPATH%
echo.>> %LOGPATH%

echo %DATE% %TIME% �f�[�^�x�[�X�̃o�b�N�A�b�v�J�n >> %LOGPATH%
REM ���S�o�b�N�A�b�v��擾����
SQLCMD -S %SERVER% -E -H %HOST% -i ExecAutoBackup.sql -v Param1=%COMPUTERNAME% -b >> %LOGPATH%

REM �f�[�^�x�[�X�̃g�����U�N�V�������O��V�������N����
rem IF "%DATE:~8,2%" EQU "01" (
  echo.>> %LOGPATH%
  echo.>> %LOGPATH%
  echo %DATE% %TIME% �f�[�^�x�[�X�̃g�����U�N�V�������O��V�������N�J�n >> %LOGPATH%
  SQLCMD -S %SERVER% -E -H %HOST% -i ExecShrinkLog.sql -b >> %LOGPATH%
rem )

REM �o�b�N�A�b�v�t�@�C����o�b�N�A�b�v����
robocopy %OUTPUT% %BACKUP% /R:3 /W:10 >> %LOGPATH%

echo.>> %LOGPATH%
echo.>> %LOGPATH%
echo %DATE% %TIME% �X�N���v�g�I�� >> %LOGPATH%