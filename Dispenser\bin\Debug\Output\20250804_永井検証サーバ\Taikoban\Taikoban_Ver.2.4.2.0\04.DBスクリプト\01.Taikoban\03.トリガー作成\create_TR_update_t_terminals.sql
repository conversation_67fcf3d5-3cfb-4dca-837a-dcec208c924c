USE [$(Param1)]
GO

/****** Object:  Trigger [dbo].[TR_update_t_terminals]    Script Date: 10/06/2014 16:00:05 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[TR_update_t_terminals]') AND type in (N'TR'))
DROP TRIGGER [dbo].[TR_update_t_terminals]
GO

/*** CREATE TRIGGER ***/
CREATE TRIGGER [dbo].[TR_update_t_terminals] ON [dbo].[t_terminals]
FOR UPDATE
AS 
BEGIN

UPDATE t_terminals
SET modification_datetime = GETDATE()
WHERE id IN (SELECT id FROM inserted)

DELETE FROM t_terminal_statuses 
WHERE terminal_name IN (SELECT name FROM inserted WHERE logically_deleted = 1)

END
RETURN


GO

