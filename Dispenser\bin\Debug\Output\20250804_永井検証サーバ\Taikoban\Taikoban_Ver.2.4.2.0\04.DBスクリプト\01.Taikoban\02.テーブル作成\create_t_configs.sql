USE [$(Param1)]
GO
/****** Object:  Table [dbo].[t_configs]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_configs]') AND type in (N'U'))
DROP TABLE [dbo].[t_configs]
GO

CREATE TABLE [dbo].[t_configs](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[registered_by] [int] NOT NULL,
	[modification_datetime] [datetime2](7) NOT NULL,
	[modified_by] [int] NOT NULL,
	[password_term] [int] NOT NULL,
	[password_term_unit] [int] NOT NULL,
	[password_expiration_warning_term] [int] NOT NULL,
	[password_expiration_warning_term_unit] [int] NOT NULL,
	[unit_of_expiration] [int] NOT NULL,
	[password_expiration_warning_message] [nvarchar](1024) NOT NULL,
	[password_expiration_message] [nvarchar](1024) NOT NULL,
	[smartcard_expiration_message] [nvarchar](1024) NOT NULL,
	[additional_error_message] [nvarchar](1024) NOT NULL,
	[authentication_without_card_message] [nvarchar](1024) NOT NULL,
	[log_retention_period] [int] NOT NULL,
	[log_retention_period_unit] [int] NOT NULL,
	[licensees] [nvarchar](255) NOT NULL,
	[use_shared_account] [int] NOT NULL,
	[password_min_length] [int] NOT NULL,
	[password_max_length] [int] NOT NULL,
	[password_available_characters] [nvarchar](255) NOT NULL,
	[password_complexity] [int] NOT NULL,
	[password_including_birthdate] [int] NOT NULL,
	[password_generation_management] [int] NOT NULL,
	[auth_error_threshold] [int] NOT NULL,
	[auth_error_retention_period] [int] NOT NULL,
	[disable_multiterminal_login] [int] NOT NULL,
 CONSTRAINT [PK_t_configs] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_registered_by]  DEFAULT ((0)) FOR [registered_by]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_modification_datetime]  DEFAULT (getdate()) FOR [modification_datetime]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_modified_by]  DEFAULT ((0)) FOR [modified_by]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_password_term]  DEFAULT ((0)) FOR [password_term]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_password_term_unit]  DEFAULT ((0)) FOR [password_term_unit]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_password_expiration_warning_term]  DEFAULT ((0)) FOR [password_expiration_warning_term]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_password_expiration_warning_term_unit]  DEFAULT ((0)) FOR [password_expiration_warning_term_unit]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_unit_of_expiration]  DEFAULT ((1)) FOR [unit_of_expiration]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_password_expiration_warning_message]  DEFAULT ('') FOR [password_expiration_warning_message]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_password_expiration_message]  DEFAULT ('') FOR [password_expiration_message]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_smartcard_expiration_message]  DEFAULT ('') FOR [smartcard_expiration_message]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_additional_error_message]  DEFAULT ('') FOR [additional_error_message]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_authentication_without_card_message]  DEFAULT ('') FOR [authentication_without_card_message]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_log_retention_period]  DEFAULT ((0)) FOR [log_retention_period]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_log_retention_period_unit]  DEFAULT ((0)) FOR [log_retention_period_unit]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_licensees]  DEFAULT ('') FOR [licensees]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_use_shared_account]  DEFAULT ((0)) FOR [use_shared_account]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_password_min_length]  DEFAULT ((0)) FOR [password_min_length]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_password_max_length]  DEFAULT ((0)) FOR [password_max_length]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_password_available_characters]  DEFAULT ('') FOR [password_available_characters]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_password_complexity]  DEFAULT ((0)) FOR [password_complexity]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_password_including_birthdate]  DEFAULT ((0)) FOR [password_including_birthdate]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_password_generation_management]  DEFAULT ((0)) FOR [password_generation_management]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_auth_error_threshold]  DEFAULT ((0)) FOR [auth_error_threshold]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_auth_error_retention_period]  DEFAULT ((0)) FOR [auth_error_retention_period]
GO
ALTER TABLE [dbo].[t_configs] ADD  CONSTRAINT [DF_t_configs_disable_multiterminal_login]  DEFAULT ((0)) FOR [disable_multiterminal_login]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'registered_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'modification_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'modified_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード有効期間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'password_term'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード有効期間の単位　1:年 2:月 3:週 4:日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'password_term_unit'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード期限切れの警告を出す期間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'password_expiration_warning_term'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード期限切れの警告を出す期間の単位　1:年 2:月 3:週 4:日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'password_expiration_warning_term_unit'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'有効期限で考慮する時間　1:日のみ 2:時分まで' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'unit_of_expiration'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード有効期限切れ警告のメッセージ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'password_expiration_warning_message'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード有効期限切れメッセージ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'password_expiration_message'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ICカード有効期限切れメッセージ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'smartcard_expiration_message'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'エラー時の追加メッセージ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'additional_error_message'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ICカードなし認証時のメッセージ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'authentication_without_card_message'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ログ保存期間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'log_retention_period'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ログ保存期間の単位　1:年 2:月 3:週 4:日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'log_retention_period_unit'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ライセンス先' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'licensees'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'使用共用アカウント 0:動作ポリシー 1:端末マスタ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'use_shared_account'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード最小桁数' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'password_min_length'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード最大桁数' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'password_max_length'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード利用可能文字' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'password_available_characters'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワードの複雑性 0:制限なし 1:英字、数字必須' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'password_complexity'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'生年月日を含むパスワード 0:許可する 1:許可しない' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'password_including_birthdate'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'パスワード履歴管理' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'password_generation_management'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'認証エラー閾値' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'auth_error_threshold'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'認証エラー保持期間' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'auth_error_retention_period'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'同一ユーザによる複数端末へのログイン可否 0:可 1:不可' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_configs', @level2type=N'COLUMN',@level2name=N'disable_multiterminal_login'
GO
