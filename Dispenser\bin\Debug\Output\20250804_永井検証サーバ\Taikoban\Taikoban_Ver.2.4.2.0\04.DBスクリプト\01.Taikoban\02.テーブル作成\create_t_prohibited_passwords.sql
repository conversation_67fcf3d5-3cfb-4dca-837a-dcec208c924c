USE [$(Param1)]
GO
/****** Object:  Table [dbo].[t_prohibited_passwords]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_prohibited_passwords]') AND type in (N'U'))
DROP TABLE [dbo].[t_prohibited_passwords]
GO

CREATE TABLE [dbo].[t_prohibited_passwords](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[prohibited_password] [nvarchar](255) NOT NULL,
 CONSTRAINT [PK_t_prohibited_passwords] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_prohibited_passwords] ADD  CONSTRAINT [DF_t_prohibited_passwords]  DEFAULT ('') FOR [prohibited_password]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_prohibited_passwords', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'禁止パスワード' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_prohibited_passwords', @level2type=N'COLUMN',@level2name=N'prohibited_password'
GO
