﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace eDoktor.Taikoban.AccountImporter.Model
{
    class SsoAccount
    {
        #region Properties
        //項目
        public int Id { get; set; }
        public bool Invalidated { get; set; }
        public bool LogicallyDeleted { get; set; }
        public DateTime? UsageStartDatetime { get; set; }
        public DateTime? UsageEndDatetime { get; set; }
        public string AccountLogonId { get; set; }
        public int SsoAccountSystemId { get; set; }
        public string SsoLogonId { get; set; }
        public string SsoPassword { get; set; }

        //更新チェック用フラグ
        public Boolean IsInvalidatedChanged { get; set; }
        public Boolean IsLogicallyDeletedChanged { get; set; }
        public Boolean IsPasswordChanged { get; set; }
        #endregion

        #region Constructors
        public SsoAccount()
        {
            Id = -1;
            
            Invalidated = false;
            LogicallyDeleted = false;
            UsageStartDatetime = null;
            UsageEndDatetime = null;
            AccountLogonId = string.Empty;
            SsoAccountSystemId = 0;
            SsoLogonId = string.Empty;
            SsoPassword = string.Empty;
        
            IsInvalidatedChanged = false;
            IsLogicallyDeletedChanged = false;
            IsPasswordChanged = false;
        }

        public SsoAccount(LineAnalyzer line)
        {
            Id = -1;

            Invalidated = false;
            LogicallyDeleted = false;
            UsageStartDatetime = null;
            UsageEndDatetime = null;
            AccountLogonId = line.LogonId;
            SsoAccountSystemId = Configuration.Configuration.TargetSsoAccountSystemId;
            SsoLogonId = line.SsoLogonId;
            SsoPassword = line.SsoPassword;

            IsInvalidatedChanged = false;
            IsLogicallyDeletedChanged = false;
            IsPasswordChanged = false;
        }
        #endregion
    }
}
