USE [$(Param1)]
GO
/****** Object:  Table [dbo].[t_commands]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_commands]') AND type in (N'U'))
DROP TABLE [dbo].[t_commands]
GO

CREATE TABLE [dbo].[t_commands](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[registration_datetime] [datetime2](7) NOT NULL,
	[registered_by] [int] NOT NULL,
	[modification_datetime] [datetime2](7) NOT NULL,
	[modified_by] [int] NOT NULL,
	[invalidated] [bit] NOT NULL,
	[invalidation_datetime] [datetime2](7) NULL,
	[invalidated_by] [int] NOT NULL,
	[logically_deleted] [bit] NOT NULL,
	[logical_deletion_datetime] [datetime2](7) NULL,
	[logically_deleted_by] [int] NOT NULL,
	[name] [nvarchar](255) NOT NULL,
	[description] [nvarchar](255) NOT NULL,
	[file_name] [nvarchar](1024) NOT NULL,
	[arguments] [nvarchar](max) NOT NULL,
	[create_no_window] [bit] NOT NULL,
	[use_shell_execute] [bit] NOT NULL,
	[window_style] [int] NOT NULL,
	[working_directory] [nvarchar](1024) NOT NULL,
	[runas] [int] NOT NULL,
	[wait_for_exit] [bit] NOT NULL,
	[timeout] [int] NOT NULL,
	[notes] [nvarchar](255) NOT NULL,
 CONSTRAINT [PK_t_commands] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 CONSTRAINT [IX_t_commands] UNIQUE NONCLUSTERED 
(
	[name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_commands] ADD  CONSTRAINT [DF_t_commands_registration_datetime]  DEFAULT (getdate()) FOR [registration_datetime]
GO
ALTER TABLE [dbo].[t_commands] ADD  CONSTRAINT [DF_t_commands_registered_by]  DEFAULT ((0)) FOR [registered_by]
GO
ALTER TABLE [dbo].[t_commands] ADD  CONSTRAINT [DF_t_commands_modification_datetime]  DEFAULT (getdate()) FOR [modification_datetime]
GO
ALTER TABLE [dbo].[t_commands] ADD  CONSTRAINT [DF_t_commands_modified_by]  DEFAULT ((0)) FOR [modified_by]
GO
ALTER TABLE [dbo].[t_commands] ADD  CONSTRAINT [DF_t_commands_invalidated]  DEFAULT ('false') FOR [invalidated]
GO
ALTER TABLE [dbo].[t_commands] ADD  CONSTRAINT [DF_t_commands_invalidated_by]  DEFAULT ((0)) FOR [invalidated_by]
GO
ALTER TABLE [dbo].[t_commands] ADD  CONSTRAINT [DF_t_commands_logically_deleted]  DEFAULT ('false') FOR [logically_deleted]
GO
ALTER TABLE [dbo].[t_commands] ADD  CONSTRAINT [DF_t_commands_logically_deleted_by]  DEFAULT ((0)) FOR [logically_deleted_by]
GO
ALTER TABLE [dbo].[t_commands] ADD  CONSTRAINT [DF_t_commands_name]  DEFAULT ('') FOR [name]
GO
ALTER TABLE [dbo].[t_commands] ADD  CONSTRAINT [DF_t_commands_description]  DEFAULT ('') FOR [description]
GO
ALTER TABLE [dbo].[t_commands] ADD  CONSTRAINT [DF_t_commands_file_name]  DEFAULT ('') FOR [file_name]
GO
ALTER TABLE [dbo].[t_commands] ADD  CONSTRAINT [DF_t_commands_arguments]  DEFAULT ('') FOR [arguments]
GO
ALTER TABLE [dbo].[t_commands] ADD  CONSTRAINT [DF_t_commands_create_no_window]  DEFAULT ((0)) FOR [create_no_window]
GO
ALTER TABLE [dbo].[t_commands] ADD  CONSTRAINT [DF_t_commands_use_shell_execute]  DEFAULT ((0)) FOR [use_shell_execute]
GO
ALTER TABLE [dbo].[t_commands] ADD  CONSTRAINT [DF_t_commands_window_style]  DEFAULT ((0)) FOR [window_style]
GO
ALTER TABLE [dbo].[t_commands] ADD  CONSTRAINT [DF_t_commands_working_directory]  DEFAULT ('') FOR [working_directory]
GO
ALTER TABLE [dbo].[t_commands] ADD  CONSTRAINT [DF_t_commands_runas]  DEFAULT ((0)) FOR [runas]
GO
ALTER TABLE [dbo].[t_commands] ADD  CONSTRAINT [DF_t_commands_wait_for_exit]  DEFAULT ((0)) FOR [wait_for_exit]
GO
ALTER TABLE [dbo].[t_commands] ADD  CONSTRAINT [DF_t_commands_timeout]  DEFAULT ((0)) FOR [timeout]
GO
ALTER TABLE [dbo].[t_commands] ADD  CONSTRAINT [DF_t_commands_notes]  DEFAULT ('') FOR [notes]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'registration_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'登録を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'registered_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'modification_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'modified_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'invalidated'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効化日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'invalidation_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'無効化を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'invalidated_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'logically_deleted'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'logical_deletion_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'論理削除を行ったユーザーのID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'logically_deleted_by'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'コマンド名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'説明' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'description'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'実行ファイル名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'file_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'引数' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'arguments'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'プロセスを新しいウィンドウで起動するかどうか 0:ウィンドウを作成する 1:ウィンドウを作成しない' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'create_no_window'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'プロセスの起動にオペレーションシステムのシェルを使用するかどうか 0:使用しない 1:使用する' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'use_shell_execute'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'プロセスを起動する時に使用するウィンドウの状態 0:Normal 1:Hidden 2:Minimized 3:Maxmized' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'window_style'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'作業ディレクトリ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'working_directory'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'プロセスを実行するユーザ 0:System 1:User' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'runas'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'プロセスが終了するまで待機するか 0:待機しない 1:待機する' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'wait_for_exit'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'プロセスが終了するまでの最大待機時間 （ミリ秒）' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'timeout'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'注記' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_commands', @level2type=N'COLUMN',@level2name=N'notes'
GO
