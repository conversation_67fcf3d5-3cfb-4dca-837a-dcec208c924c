﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace eDoktor.Taikoban.AccountImporter.Configuration
{
    class Configuration
    {
		#region Enum
		public enum DataTypeEnum
		{
			/// <summary>固定長</summary>
			Fixed = 0,
			/// <summary>可変長</summary>
			Variable = 1,
		}

		public enum PasswordDataDecryptMethodEnum
		{
			/// <summary>復号化しない</summary>
			None = 0,
			/// <summary>GX方式</summary>
			GX = 1,
		}
		#endregion

        #region Fields

        #endregion

        #region Properties
		// ファイル取得
        public static int FileGetMethod { get; private set; }
        public static string ConnectUser { get; private set; }
        public static string ConnectPassword { get; private set; }
        public static string SourceFileUri { get; private set; }
		//public static string SourceDirectoryPath { get; private set; }
        public static int MaxRetryCount { get; private set; }
        public static int RetryInterval { get; private set; }
        public static string LocalFilePath { get; private set; }
        public static string FileBackupDirectory { get; private set; }
        public static string FileBackupName { get; private set; }
        public static int FileTermInDays { get; private set; }
		// ファイル内容
		public static System.Text.Encoding FileEncoding { get; private set; }
		public static DataTypeEnum DataType { get; private set; }
        public static int LineLength { get; private set; }
		public static string Delimiter { get; private set;}
        public static bool IgnoreFirstRow { get; private set; }
		// 非アクティブ
        public static int InvalidatedDataOffset { get; private set; }
		public static int InvalidatedDataLength { get; private set; }
		// 削除
        public static int LogicallyDeletedDataOffset { get; private set; }
		public static int LogicallyDeletedDataLength { get; private set; }
		// 有効開始日時
        public static int UsageStartDatetimeDataOffset { get; private set; }
		public static int UsageStartDatetimeDataLength { get; private set; }
        public static string UsageStartDatetimeDataFormat { get; private set; }
        public static string[] UsageStartDatetimeDataNullList { get; private set; }
		// 有効終了日
        public static int UsageEndDatetimeDataOffset { get; private set; }
		public static int UsageEndDatetimeDataLength { get; private set; }
        public static string UsageEndDatetimeDataFormat { get; private set; }
        public static string[] UsageEndDatetimeDataNullList { get; private set; }
		// 組織内ID
        public static int OrganizationalIdDataOffset { get; private set; }
		public static int OrganizationalIdDataLength { get; private set; }
		// 氏名
        public static int NameDataOffset { get; private set; }
		public static int NameDataLength { get; private set; }
		// カナ氏名
        public static int KanaDataOffset { get; private set; }
		public static int KanaDataLength { get; private set; }
		// 職種コード
		public static int JobCodeDataOffset { get; private set; }
		public static int JobCodeDataLength { get; private set; }
		// 職種名
		public static int JobNameDataOffset { get; private set; }
		public static int JobNameDataLength { get; private set; }
		// 生年月日
        public static int BirthdateDataOffset { get; private set; }
		public static int BirthdateDataLength { get; private set; }
        public static string BirthdateDataFormat { get; private set; }
        public static string[] BirthdateDataNullList { get; private set; }
		// ログオンID
        public static int LogonIdDataOffset { get; private set; }
		public static int LogonIdDataLength { get; private set; }
		// パスワード
        public static int PasswordDataOffset { get; private set; }
        public static int PasswordDataLength { get; private set; }
		public static bool PasswordDataIsEncrypted { get; private set; }
		public static PasswordDataDecryptMethodEnum PasswordDataDecryptMethod { get; private set; }
		public static string PasswordDataInitial { get; private set; }
		// パスワード更新日
        public static int PasswordUpdateDatetimeDataOffset { get; private set; }
        public static int PasswordUpdateDatetimeDataLength { get; private set; }
        public static string PasswordUpdateDatetimeDataFormat { get; private set; }
        public static string[] PasswordUpdateDatetimeDataNullList { get; private set; }
        public static bool PasswordUpdateDatetimeIsUsedForNewAccount { get; private set; }
		// カードID
		public static int CardIdDataOffset { get; private set; }
		public static int CardIdDataLength { get; private set; }
		public static bool CardIdWithHyphen { get; private set; }
        public static int CardType { get; private set; }
		// SSOログオンID
		public static int SsoLogonIdDataOffset { get; private set; }
		public static int SsoLogonIdDataLength { get; private set; }
		// SSOパスワード
		public static int SsoPasswordDataOffset { get; private set; }
		public static int SsoPasswordDataLength { get; private set; }
        public static PasswordDataDecryptMethodEnum SsoPasswordDataDecryptMethod { get; private set; }
		public static string SsoPasswordDataInitial { get; private set; }
        // SSOアカウント体系
        public static int TargetSsoAccountSystemId { get; private set; }

		// 固定値
        public static string LogonTo { get; private set; }
        public static string PasswordUpdateDatetimeToUseForNewAccount { get; private set; }
        public static int PractitionerID { get; private set; }
        public static int DefaultAccountGroupID { get; private set; }
        public static int DefaultPrivilegeTemplateID { get; private set; }
		public static bool RegistErrorTable { get; private set; }
		public static int ErrorSource { get; private set; }

		// AD連携
        public static bool SyncronizeAd { get; private set; }
        public static string LdapUrl { get; private set; }
        public static string LdapUser { get; private set; }
        public static string LdapPassword { get; private set; }
        public static System.DirectoryServices.AuthenticationTypes LdapAuthenticationTypes { get; private set; }
        public static string SharePath { get; private set; }
        public static string ShareUser { get; private set; }
        public static string SharePassword { get; private set; }
        public static string InitialPassword { get; private set; }
        public static string ShareLocalDrive { get; private set; }
        public static int ShareRetryCount { get; private set; }
        public static int ShareRetryInterval { get; private set; }
        public static string ShareDomain { get; private set; }
        public static string[] ShareUnnecessaryUserList { get; private set; }
        public static string[] ExcludedAccountList { get; private set; }
        public static ContentConfigurationElementCollection Contents { get; private set; }
        #endregion

        #region Public Methods
        public static void Load()
        {
			// ファイル取得
            SetIntProperty(NameOf(() => FileGetMethod), 0, 2);
            string encryptedConnectUser = eDoktor.Common.Configuration.AppSetting("EncryptedConnectUser");
            ConnectUser = Crypto.DecryptString(encryptedConnectUser);
            string encryptedConnectPassword = eDoktor.Common.Configuration.AppSetting("EncryptedConnectPassword");
            ConnectPassword = Crypto.DecryptString(encryptedConnectPassword);
            SetStringProperty(NameOf(() => SourceFileUri));
			//SetStringProperty(NameOf(() => SourceDirectoryPath));
            SetIntProperty(NameOf(() => MaxRetryCount));
            SetIntProperty(NameOf(() => RetryInterval));
            SetStringProperty(NameOf(() => LocalFilePath));
            SetStringProperty(NameOf(() => FileBackupDirectory));
            SetStringProperty(NameOf(() => FileBackupName));
            SetIntProperty(NameOf(() => FileTermInDays));
			// ファイル内容
            string strFileEncoding = eDoktor.Common.Configuration.AppSetting("FileEncoding");
			if (strFileEncoding == "UTF8")
			{
				FileEncoding = System.Text.Encoding.UTF8;
			}
			else
			{
				FileEncoding = System.Text.Encoding.GetEncoding(strFileEncoding);
			}
			SetIntProperty(NameOf(() => DataType), 0, 1);
            SetIntProperty(NameOf(() => LineLength));
			SetStringProperty(NameOf(() => Delimiter));
            SetBoolProperty(NameOf(() => IgnoreFirstRow), false);
			// 非アクティブ
            SetIntProperty(NameOf(() => InvalidatedDataOffset), -1);
            SetIntProperty(NameOf(() => InvalidatedDataLength));
			// 削除
			SetIntProperty(NameOf(() => LogicallyDeletedDataOffset), -1);
            SetIntProperty(NameOf(() => LogicallyDeletedDataLength));
			// 有効開始日時
			SetIntProperty(NameOf(() => UsageStartDatetimeDataOffset), -1);
            SetIntProperty(NameOf(() => UsageStartDatetimeDataLength));
            SetStringProperty(NameOf(() => UsageStartDatetimeDataFormat));
            SetStringListProperty(NameOf(() => UsageStartDatetimeDataNullList));
			// 有効終了日時
			SetIntProperty(NameOf(() => UsageEndDatetimeDataOffset), -1);
            SetIntProperty(NameOf(() => UsageEndDatetimeDataLength));
            SetStringProperty(NameOf(() => UsageEndDatetimeDataFormat));
            SetStringListProperty(NameOf(() => UsageEndDatetimeDataNullList));
			// 組織内ID
            SetIntProperty(NameOf(() => OrganizationalIdDataOffset));
            SetIntProperty(NameOf(() => OrganizationalIdDataLength));
			// 氏名
            SetIntProperty(NameOf(() => NameDataOffset));
            SetIntProperty(NameOf(() => NameDataLength));
			// カナ氏名
			SetIntProperty(NameOf(() => KanaDataOffset), -1);
            SetIntProperty(NameOf(() => KanaDataLength));
			// 職種コード
			SetIntProperty(NameOf(() => JobCodeDataOffset), -1);
			SetIntProperty(NameOf(() => JobCodeDataLength));
			// 職種名
			SetIntProperty(NameOf(() => JobNameDataOffset), -1);
			SetIntProperty(NameOf(() => JobNameDataLength));
			// 生年月日
			SetIntProperty(NameOf(() => BirthdateDataOffset), -1);
            SetIntProperty(NameOf(() => BirthdateDataLength));
            SetStringProperty(NameOf(() => BirthdateDataFormat));
            SetStringListProperty(NameOf(() => BirthdateDataNullList));
			// ログオンID
            SetIntProperty(NameOf(() => LogonIdDataOffset));
            SetIntProperty(NameOf(() => LogonIdDataLength));
			// パスワード
			SetIntProperty(NameOf(() => PasswordDataOffset), -1);
            SetIntProperty(NameOf(() => PasswordDataLength));
            SetBoolProperty(NameOf(() => PasswordDataIsEncrypted));
			SetIntProperty(NameOf(() => PasswordDataDecryptMethod), 0, 1);
			SetStringProperty(NameOf(() => PasswordDataInitial));
			// パスワード更新日
			SetIntProperty(NameOf(() => PasswordUpdateDatetimeDataOffset), -1);
            SetIntProperty(NameOf(() => PasswordUpdateDatetimeDataLength));
            SetStringProperty(NameOf(() => PasswordUpdateDatetimeDataFormat));
			//***debug***
			eDoktor.Common.Trace.OutputDebugTrace("PasswordUpdateDatetimeDataFormat={0}", PasswordUpdateDatetimeDataFormat);
			//***debug***
            SetStringListProperty(NameOf(() => PasswordUpdateDatetimeDataNullList));
            SetBoolProperty(NameOf(() => PasswordUpdateDatetimeIsUsedForNewAccount));
            // カードID
			SetIntProperty(NameOf(() => CardIdDataOffset), -1);
			SetIntProperty(NameOf(() => CardIdDataLength));
			SetBoolProperty(NameOf(() => CardIdWithHyphen));
            SetIntProperty(NameOf(() => CardType), 0, 2);
			// SSOログオンID
			SetIntProperty(NameOf(() => SsoLogonIdDataOffset), -1);
			SetIntProperty(NameOf(() => SsoLogonIdDataLength));
			// SSOパスワード
			SetIntProperty(NameOf(() => SsoPasswordDataOffset), -1);
			SetIntProperty(NameOf(() => SsoPasswordDataLength));
			SetIntProperty(NameOf(() => SsoPasswordDataDecryptMethod), 0, 1);
			SetStringProperty(NameOf(() => SsoPasswordDataInitial));
            // SSOアカウント体系ID
            SetIntProperty(NameOf(() => TargetSsoAccountSystemId));

			// 固定値
            SetStringProperty(NameOf(() => LogonTo), false);
            SetStringProperty(NameOf(() => PasswordUpdateDatetimeToUseForNewAccount));
            SetIntProperty(NameOf(() => PractitionerID));
            SetIntProperty(NameOf(() => DefaultAccountGroupID));
            SetIntProperty(NameOf(() => DefaultPrivilegeTemplateID));
			SetBoolProperty(NameOf(() => RegistErrorTable));
			SetIntProperty(NameOf(() => ErrorSource));

			// AD
            SetBoolProperty(NameOf(() => SyncronizeAd));
            SetStringProperty(NameOf(() => LdapUrl), SyncronizeAd);
            if (SyncronizeAd)
            {
                SetStringProperty(NameOf(() => LdapUrl));
                string encryptedLdapUser = eDoktor.Common.Configuration.AppSetting("EncryptedLdapUser");
                LdapUser = Crypto.DecryptString(encryptedLdapUser);
                string encryptedLdapPassword = eDoktor.Common.Configuration.AppSetting("EncryptedLdapPassword");
                LdapPassword = Crypto.DecryptString(encryptedLdapPassword);
                int ldapAuthenticationTypes = eDoktor.Common.Configuration.AppSetting("LdapAuthenticationTypes", -1);
                LdapAuthenticationTypes = (System.DirectoryServices.AuthenticationTypes)ldapAuthenticationTypes;
                SetStringProperty(NameOf(() => SharePath));
                string encryptedShareUser = eDoktor.Common.Configuration.AppSetting("EncryptedShareUser");
                ShareUser = Crypto.DecryptString(encryptedShareUser);
                string encryptedSharePassword = eDoktor.Common.Configuration.AppSetting("EncryptedSharePassword");
                SharePassword = Crypto.DecryptString(encryptedSharePassword);
                string encryptedInitialPassword = eDoktor.Common.Configuration.AppSetting("EncryptedInitialPassword");
                InitialPassword = Crypto.DecryptString(encryptedInitialPassword);
                SetStringProperty(NameOf(() => ShareLocalDrive));
                SetIntProperty(NameOf(() => ShareRetryCount));
                SetIntProperty(NameOf(() => ShareRetryInterval));
                SetStringProperty(NameOf(() => ShareDomain), false);
                SetStringListProperty(NameOf(() => ShareUnnecessaryUserList));
                SetStringListProperty(NameOf(() => ExcludedAccountList));
                Contents = LdapConfiguration.Load();
            }

			// 設定値ログ出力
			eDoktor.Common.Trace.OutputTrace("DataType={0}", DataType);
        }
        #endregion

        #region Private Methods
        private static string NameOf<T>(System.Linq.Expressions.Expression<Func<T>> e)
        {
            var member = (System.Linq.Expressions.MemberExpression)e.Body;

            return member.Member.Name;
        }

        private static void SetIntProperty(string name, int min = 0, int max = -1)
        {
            int value = eDoktor.Common.Configuration.AppSetting(name, -1);

            if (value < min || (max > -1 && value > max))
            {
                throw new System.FormatException(name);
            }

            var configurationType = typeof(Configuration);

            var property = configurationType.GetProperty(name);

            property.SetValue(configurationType, value, null);
        }

        private static void SetStringProperty(string name, bool isRequired = true)
        {
            string value = eDoktor.Common.Configuration.AppSetting(name);

            if (isRequired && string.IsNullOrWhiteSpace(value))
            {
                throw new System.FormatException(name);
            }

            var configurationType = typeof(Configuration);

            var property = configurationType.GetProperty(name);

            property.SetValue(configurationType, value, null);
        }

        private static void SetStringListProperty(string name)
        {
            string[] value = eDoktor.Common.Configuration.AppSettingArray(name, eDoktor.Common.Configuration.DefaultSeparator, StringSplitOptions.RemoveEmptyEntries);

            var configurationType = typeof(Configuration);

            var property = configurationType.GetProperty(name);

            property.SetValue(configurationType, value, null);
        }

        private static void SetBoolProperty(string name, bool valueIfNull = false)
        {
            bool value = eDoktor.Common.Configuration.AppSetting(name, valueIfNull);

            var configurationType = typeof(Configuration);

            var property = configurationType.GetProperty(name);

            property.SetValue(configurationType, value, null);
        }
        #endregion
    }
}
