﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<configSections>
		<sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
			<section name="eDoktor.Taikoban.AdminConsole.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
		</sectionGroup>
	</configSections>
	<applicationSettings>
		<eDoktor.Taikoban.AdminConsole.Properties.Settings>
			<setting name="ConnectionStringSettingsLabel" serializeAs="String">
				<value>Taikoban</value>
			</setting>
		</eDoktor.Taikoban.AdminConsole.Properties.Settings>
	</applicationSettings>
	<connectionStrings>
		<add name="Taikoban" connectionString="sOEqwRggeOs5J+mrA1PTQP03iS2JvKkkwovlMRimh7NIGBkA6vsDZOSli1ja2eqMXHwU1svdQpoPpl+cAiF71W5rxEQCPpq4d1gvrg7XgSWUOxpzgXIYISoUrCN+yEURta0qlL+UpBogoGF6fBt4Nmo40KsnimIn6wmRkbT0ScNmukl9T8237Gw5Z3A5jBlOw71SlKGl0maMbRVGzGH8J5zm175t4SnPg1pNJkVvp+0=" providerName="System.Data.SqlClient" />
	</connectionStrings>
	<appSettings>
		<!-- Trace -->
		<add key="EnableTrace" value="true" />
		<add key="EnableDebugTrace" value="true" />
		<add key="EnableDetailedExceptionTrace" value="true" />
		<add key="ContainingFolderPath" value="..\logs\%USER%\"/>
		<add key="TraceTermInDays" value="30" />
		<!-- 動作モード 0:Taikoban 1:TaikobanLX -->
		<add key="Mode" value="0"/>
		<!-- ウィンドウ背景色 -->
		<add key="WindowBackground" value="AliceBlue" />
		<!-- タイトル文字色 -->
		<add key="TitleForeground" value="White" />
		<!-- タイトル背景色 -->
		<add key="TitleBackground" value="Black" />
		<!-- 新規登録時のログオン先のデフォルト値 -->
		<add key="Logon To" value="domain" />
		<!-- 新規ユーザーの作成フラグ                        true:作成可能 以外:不可 -->
		<add key="User Create" value="true" />
		<!-- ユーザーのパスワードの更新は可能フラグ          true:更新可能 以外:不可 -->
		<add key="User Password Update" value="false" />
		<!-- Active Directoryのパスワードの更新フラグ        true:更新する 以外:更新しない -->
		<!--    <add key="Active Directory Password Update" value="false" />  -->
		<!-- ユーザーの同期フラグの更新可能フラグ            true:更新可能 以外:不可 -->
		<add key="User Synchronized Update" value="false" />
		<!-- ユーザーのパスワード更新日更新フラグ            true:更新可能 以外:不可 -->
		<add key="User Logon Password Last Update Date Update" value="true" />
		<!-- 論理削除データの管理可能フラグ                  true:管理可能 以外:不可 -->
		<add key="Logically Deleted Management" value="true" />
		<!-- 富士通連携でのパスワード暗号化フラグ true:暗号化されている 以外:それ以外（富士通連携無しの場合含む） -->
		<add key="UseFujitsuEncryptedPassword" value="false" />
		<!-- デフォルト時刻                                  HH:mm:ss形式 -->
		<add key="Default Time" value="18:00:00" />
		<!-- エラー通知機能で監視するTaikobanレポートサービス監視フラグ true:監視する 以外:監視しない -->
		<add key="TaikobanReportService monitor" value="true" />
		<!-- エラー通知機能で監視するTaikobanレポートサービスの動作サーバー -->
		<add key="TaikobanReportService machine" value="" />
		<!-- ステータス参照 自動更新間隔(ms) 未設定時は「60000」 -->
		<add key="StatusReference Auto Update" value="10000" />
		<!--CSVファイルの区切り文字 設定例)タブ:9 カンマ:44 (未設定、エラーの場合「タブ:9」になります) -->
		<add key="CsvDelimiter" value="44" />
		<!-- CSVファイルのエンコード 設定例)Shift_JIS -->
		<add key="CsvEncoding" value="Shift_JIS" />
		<!-- スマートカードマスタ CSVファイルのデフォルトファイル名 -->
		<add key="SmartCardManageCsvPath" value="SmartCard.csv" />
		<!-- 認証ログ CSVファイルのデフォルトファイル名 -->
		<add key="LogManageCsvPath" value="Log.csv" />
		<!-- 貸出履歴 CSVファイルのデフォルトファイル名 -->
		<add key="LendingManageCsvPath" value="Lending.csv" />
		<!-- 登録済みのカード紐づけへの上書き登録を許可する false:許可しない true:許可する 省略値=false -->
		<add key="AllowOverwrite" value="true" />
		<!-- ステータス参照のリストの背景色 -->
		<add key="NotAuth_Logoff" value="Gray" />
		<add key="NotAuth_Lock" value="Gainsboro" />
		<add key="NotAuth_Logon" value="LightBlue" />
		<add key="Auth_Logoff" value="Gray" />
		<add key="Auth_Lock" value="Gold" />
		<add key="Auth_Logon" value="White" />
		<add key="Unknown" value="Red" />
		<!-- ステータスコンテキストメニュー -->
		<add key="ContextMenuFileCount" value="3" />
		<add key="ContextMenuFilePath1" value=".\ContextExtensions.txt" />
		<add key="ContextMenuFilePath2" value="D:\eDoktor\Taikoban\AdminConsole\SContextExtension\ContextExtensions.txt" />
		<add key="ContextMenuFilePath3" value="D:\eDoktor\Taikoban\AdminConsole\SContextExtension\SContextExtensions.txt" />
		<!-- プロセス監視 -->
		<add key="ProcessControlDatabaseName" value="Taikoban.hosp.kobe-u.ac.jp" />
		<!-- データベース名 -->
		<add key="ProcessControlServiceCount" value="0" />
		<!-- 監視するサービス数、ここで指定した数だけ、サービス名、サービス表示名を連番で記述する事！ -->
		<add key="ProcessControlProcessName1" value="TaikobanReportService" />
		<!-- 監視するサービス名 -->
		<add key="ProcessControlProcessDisplayName1" value="レポート監視" />
		<!-- 監視するサービス表示名 -->
		<add key="ProcessControlProcessName2" value="TaikobanDataSyncService" />
		<!-- 監視するサービス名 -->
		<add key="ProcessControlProcessDisplayName2" value="ＩＤ連携" />
		<!-- 監視するサービス表示名 -->
		<add key="SystemId" value="kk6f8JBMqWSadYvMpZ56oA==" />
		<add key="SecurityKey" value="oIO7QNCSWLbPzzTMlZ06cCPTjRVCbFBmf5zhS3sovtw=" />
		<!-- 端末動作設定 非表示フラグ -->
		<add key="TerminalConfigsIsHidded" value="false" />
		<!-- 端末動作管理登録 入力項目可否設定 -->
		<add key="UpdateTerminalConfigName" value="true" />
		<!-- 端末動作管理名の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateDescription" value="true" />
		<!-- 説明の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateInvalidated" value="true" />
		<!-- 非アクティブの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateNotes" value="true" />
		<!-- 注記の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateWindowsLogonType" value="true" />
		<!-- Windowsログオン方法の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateAutoLogon" value="true" />
		<!-- 自動ログオンの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateSharedAccountUser" value="true" />
		<!-- 共用アカウントのユーザの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateSharedAccountPassword" value="true" />
		<!-- 共用アカウントのユーザパスワードの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateSharedAccountDomain" value="true" />
		<!-- 共用アカウントのドメインの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateAuthenticateExceptTaikoban" value="true" />
		<!-- ユーザをTaikoban以外で認証するの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateAuthenticationOtherTaikoban" value="true" />
		<!-- Taikoban以外の認証方法の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateLdapUrl" value="true" />
		<!-- LDAP　URLの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateAdsiAuthenticationCode" value="true" />
		<!-- ADSI　認証コードの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateSearchFilter" value="true" />
		<!-- 検索フィルターの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateDnOfUserToBind" value="true" />
		<!-- バインドするユーザのDNの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdatePasswordOfUserToBind" value="true" />
		<!-- バインドするユーザのパスワードの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateLoninWhenFailedExceptTaikoban" value="true" />
		<!-- 障害時にTaikoban以外の認証がNGだった場合の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdatePasswordAuthentication" value="true" />
		<!-- パスワード認証の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateUseAuthenticateWithoutIcCard" value="true" />
		<!-- ICカード無し認証画面の利用の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateCombinationKeyAuthenticateWithoutIcCard" value="true" />
		<!-- ICカード無し認証画面の呼出しコンビネーションキーの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateOperationCardOmissionInAuthentication" value="true" />
		<!-- スマートカード認証ポリシーの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateSmartCardAuthentication" value="true"/>
		<!-- 標準SAS認証ポリシーの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateSASAuthentication" value="true"/>
		<!-- 標準SASキーコンビネーションの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateCombinationKeyStandardSASAuthentication" value="true"/>
		<!-- 管理者SASキーコンビネーションの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateCombinationKeyAdministratorSASAuthentication" value="true"/>
		<!-- 認証時にカード抜けが発生した場合の動作の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateManagePasswordExpiration" value="true" />
		<!-- パスワード有効期限管理をするの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateUsePasswordUpdateScreen" value="true" />
		<!-- パスワード変更画面の利用の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateOperationCardOmissionInPasswordUpdate" value="true" />
		<!-- 有効期限切れ又は有効期限間近の際のパスワード変更画面でのカード抜けの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateLoginPasswordExpired" value="true" />
		<!-- パスワード有効期限切れでのログインの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateCommandWhenPasswordExpired" value="true" />
		<!-- パスワード有効期限切れの場合に実行するコマンドの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateWindowsOperationCardOmission" value="true" />
		<!-- カード抜け動作（Windowsセッション）の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateTaikobanOperationCardOmission" value="true" />
		<!-- カード抜け動作（Taikoban認証）の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateGraceTimeOfUpToCardOmission" value="true" />
		<!-- カード抜けアクションの猶予時間の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateStartOfWarningTimerOfUpToCardOmission" value="true" />
		<!-- カード抜けアクションの猶予時間の警告タイマー表示開始時間の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateShortcutKeyForLock" value="true" />
		<!-- ロック操作ショートカットキーの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateWindowsGraceTimeOfUpToLockNoOperations" value="true" />
		<!-- 端末無操作時のロックまでの猶予時間（Windowsセッション）の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateWindowsGraceTimeOfUpToLogoffNoOparations" value="true" />
		<!-- 端末無操作時ログオフまでの猶予時間（Windowsセッション）の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateTaikobanGraceTimeOfUpToLogoutNoOparations" value="true" />
		<!-- 端末無操作時のログアウトまでの猶予時間（Taikoban認証）の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateWindowsGraceTimeOfUpToLogoffAfterLock" value="true" />
		<!-- ロック後に自動ログオフするまでの猶予時間（Windowsセッション）の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateTaikobanGraceTimeOfUpToLogoutAfterLock" value="true" />
		<!-- ロック後に自動ログアウトするまでの猶予時間（Taikoban認証）の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateDisplayGoToDesktopLink" value="true" />
		<!-- デスクトップへ移動ボタン表示の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateOperationWhenAuthenticationNotifiedFromSbc" value="true" />
		<!-- SBC端末からの認証通知時の動作の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateCommandWhenAuthenticationNotifiedFromSbc" value="true" />
		<!-- SBC端末からの認証通知時の動作 実行コマンドの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateProcessToKillWhenAuthenticationNotifiedFromSbc" value="true" />
		<!-- SBC端末からの認証通知時の動作 KILLするプロセスの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateWindowToCloseWhenAuthenticationNotifiedFromSbc" value="true" />
		<!-- SBC端末からの認証通知時の動作 クローズするメインウィンドウの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateOperationWhenAuthenticationNotifiedFromNonSbc" value="true" />
		<!-- 非SBC端末からの認証通知時動作の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateCommandWhenAuthenticationNotifiedFromNonSbc" value="true" />
		<!-- 非SBC端末からの認証通知時動作 実行コマンドの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateProcessToKillWhenAuthenticationNotifiedFromNonSbc" value="true" />
		<!-- 非SBC端末からの認証通知時動作 KILLするプロセスの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateWindowToCloseWhenAuthenticationNotifiedFromNonSbc" value="true" />
		<!-- 非SBC端末からの認証通知時動作 クローズするメインウィンドウの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateCommandWhenTaikobanLogin" value="true" />
		<!-- Taikobanログイン時の動作 実行コマンドの更新可能フラグ true:更新可能 以外:不可 -->


		<!-- ユーザ再認証時（ログイン時、「デスクトップへ移動」時は含まない）の動作 実行コマンドの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateCommandWhenUserReauthentication" value="true" />


		<add key="UpdateCommandWhenTaikobanLogout" value="true" />
		<!-- Taikobanログアウト時の動作 実行コマンドの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateCommandWhenAuthenticatedTaikobanUserChanged" value="true" />
		<!-- Taikobanの認証ユーザに変化があった時の動作 実行コマンドの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateProcessToKillWhenAuthenticatedTaikobanUserChanged" value="true" />
		<!-- Taikobanの認証ユーザに変化があった時の動作 KILLするプロセスの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateWindowToCloseWhenAuthenticatedTaikobanUserChanged" value="true" />
		<!-- Taikobanの認証ユーザに変化があった時の動作 クローズするメインウィンドウの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateDisplayTerminalIsLoggedIn" value="true" />
		<!-- 利用中の端末以外にログインしている端末を表示するの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateAddressOfTaikobanServerToConnect" value="true" />
		<!-- 接続先TaikobanサーバIPアドレスの更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateWorkedWithCitrixReceiver" value="true" />
		<!-- レシーバ連携の更新可能フラグ true:更新可能 以外:不可 -->

		<!-- シャットダウンボタン表示の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateShutdownButtonDisplay" value="true" />
		<!-- ログアウト時動作の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateLogoutOperation" value="true" />

		<!-- 離席ロック画面利用の更新可能フラグ true:更新可能 以外:不可 -->
		<add key="UpdateUseLeavingSeatLock" value="true" />

		<!-- 端末動作管理登録 項目説明 -->
		<add key="TerminalConfigEntry01" value="grdTerminalConfigName,動作ポリシー名" />
		<add key="TerminalConfigEntry02" value="grdDescription,説明" />
		<add key="TerminalConfigEntry03" value="grdInvalidated,非アクティブ" />
		<add key="TerminalConfigEntry04" value="grdNotes,注記" />
		<!-- ログイン -->
		<add key="TerminalConfigEntry05" value="grdWindowsLogonType,Windowsログオン方法" />
		<add key="TerminalConfigEntry06" value="grdAutoLogon,自動ログオン" />
		<add key="TerminalConfigEntry07" value="grdSharedAccountUser,共用アカウントのユーザ" />
		<add key="TerminalConfigEntry08" value="grdSharedAccountPassword,共用アカウントのユーザパスワード" />
		<add key="TerminalConfigEntry09" value="grdSharedAccountDomain,共用アカウントのドメイン" />
		<add key="TerminalConfigEntry10" value="grdAuthenticateExceptTaikoban,ユーザをTaikoban以外で認証する" />
		<add key="TerminalConfigEntry11" value="grdAuthenticationOtherTaikoban,Taikoban以外の認証方法" />
		<add key="TerminalConfigEntry12" value="grdLdapUrl,LDAP　URL" />
		<add key="TerminalConfigEntry13" value="grdAdsiAuthenticationCode,ADSI　認証コード" />
		<add key="TerminalConfigEntry14" value="grdSearchFilter,検索フィルター" />
		<add key="TerminalConfigEntry15" value="grdDnOfUserToBind,バインドするユーザのDN" />
		<add key="TerminalConfigEntry16" value="grdPasswordOfUserToBind,バインドするユーザのパスワード" />
		<add key="TerminalConfigEntry17" value="grdLoninWhenFailedExceptTaikoban,障害時にTaikoban以外の認証がNGだった場合" />
		<!-- 認証画面 -->
		<add key="TerminalConfigEntry18" value="grdPasswordAuthentication,パスワード認証" />
		<add key="TerminalConfigEntry19" value="grdUseAuthenticateWithoutIcCard,ICカード無し認証画面の利用" />
		<add key="TerminalConfigEntry20" value="grdCombinationKeyAuthenticateWithoutIcCard,ICカード無し認証画面の呼出しコンビネーションキー" />
		<add key="TerminalConfigEntry59" value="grdSmartCardAuthentication,スマートカード認証ポリシー"/>
		<add key="TerminalConfigEntry60" value="grdSASAuthentication,標準SAS認証ポリシー"/>
		<add key="TerminalConfigEntry61" value="grdCombinationKeyStandardSASAuthentication,標準SASキーコンビネーション"/>
		<add key="TerminalConfigEntry62" value="grdCombinationKeyAdministratorSASAuthentication,管理者SASキーコンビネーション"/>
		<add key="TerminalConfigEntry21" value="grdOperationCardOmissionInAuthentication,認証時にカード抜けが発生した場合の動作" />
		<add key="TerminalConfigEntry22" value="grdManagePasswordExpiration,パスワード有効期限管理をする" />
		<add key="TerminalConfigEntry23" value="grdUsePasswordUpdateScreen,パスワード変更画面の利用" />
		<add key="TerminalConfigEntry24" value="grdOperationCardOmissionInPasswordUpdate,有効期限切れ又は有効期限間近の際のパスワード変更画面でのカード抜け" />
		<add key="TerminalConfigEntry25" value="grdLoginPasswordExpired,パスワード有効期限切れでのログイン" />
		<add key="TerminalConfigEntry26" value="grdCommandWhenPasswordExpired,パスワード有効期限切れの場合に実行するコマンド" />
		<!-- ログイン中 -->
		<add key="TerminalConfigEntry27" value="grdWindowsOperationCardOmission,カード抜け動作（Windowsセッション）" />
		<add key="TerminalConfigEntry28" value="grdTaikobanOperationCardOmission,カード抜け動作（Taikoban認証）" />
		<add key="TerminalConfigEntry29" value="grdGraceTimeOfUpToCardOmission,カード抜けアクションの猶予時間" />
		<add key="TerminalConfigEntry30" value="grdStartOfWarningTimerOfUpToCardOmission,カード抜けアクションの猶予時間の警告タイマー表示開始時間" />
		<!-- ロック時動作 -->
		<add key="TerminalConfigEntry31" value="grdShortcutKeyForLock,ロック操作ショートカットキー" />
		<add key="TerminalConfigEntry32" value="grdWindowsGraceTimeOfUpToLockNoOperations,端末無操作時のロックまでの猶予時間（Windowsセッション）" />
		<add key="TerminalConfigEntry33" value="grdWindowsGraceTimeOfUpToLogoffNoOparations,端末無操作時ログオフまでの猶予時間（Windowsセッション）" />
		<add key="TerminalConfigEntry34" value="grdTaikobanGraceTimeOfUpToLogoutNoOparations,端末無操作時のログアウトまでの猶予時間（Taikoban認証）" />
		<add key="TerminalConfigEntry35" value="grdWindowsGraceTimeOfUpToLogoffAfterLock,ロック後に自動ログオフするまでの猶予時間（Windowsセッション）" />
		<add key="TerminalConfigEntry36" value="grdTaikobanGraceTimeOfUpToLogoutAfterLock,ロック後に自動ログアウトするまでの猶予時間（Taikoban認証）" />
		<add key="TerminalConfigEntry37" value="grdDisplayGoToDesktopLink,デスクトップへ移動ボタン表示" />
		<!-- ローミング動作 -->
		<add key="TerminalConfigEntry38" value="grdOperationWhenAuthenticationNotifiedFromSbc,SBC端末からの認証通知時の動作" />
		<add key="TerminalConfigEntry39" value="grdCommandWhenAuthenticationNotifiedFromSbc,SBC端末からの認証通知時の動作 実行コマンド" />
		<add key="TerminalConfigEntry40" value="grdProcessToKillWhenAuthenticationNotifiedFromSbc,SBC端末からの認証通知時の動作 KILLするプロセス" />
		<add key="TerminalConfigEntry41" value="grdWindowToCloseWhenAuthenticationNotifiedFromSbc,SBC端末からの認証通知時の動作 クローズするメインウィンドウ" />
		<add key="TerminalConfigEntry42" value="grdOperationWhenAuthenticationNotifiedFromNonSbc,非SBC端末からの認証通知時動作" />
		<add key="TerminalConfigEntry43" value="grdCommandWhenAuthenticationNotifiedFromNonSbc,非SBC端末からの認証通知時動作 実行コマンド" />
		<add key="TerminalConfigEntry44" value="grdProcessToKillWhenAuthenticationNotifiedFromNonSbc,非SBC端末からの認証通知時動作 KILLするプロセス" />
		<add key="TerminalConfigEntry45" value="grdWindowToCloseWhenAuthenticationNotifiedFromNonSbc,非SBC端末からの認証通知時動作 クローズするメインウィンドウ" />
		<!-- Taikobanログイン時の動作 -->
		<add key="TerminalConfigEntry46" value="grdCommandWhenTaikobanLogin,Taikobanログイン時の動作 実行コマンド" />
		<!-- Taikobanログアウト時の動作 -->
		<add key="TerminalConfigEntry47" value="grdCommandWhenTaikobanLogout,Taikobanログアウト時の動作 実行コマンド" />
		<!-- Taikobanの認証ユーザに変化があった時の動作 -->
		<add key="TerminalConfigEntry48" value="grdCommandWhenAuthenticatedTaikobanUserChanged,Taikobanの認証ユーザに変化があった時の動作 実行コマンド" />
		<add key="TerminalConfigEntry49" value="grdProcessToKillWhenAuthenticatedTaikobanUserChanged,Taikobanの認証ユーザに変化があった時の動作 KILLするプロセス" />
		<add key="TerminalConfigEntry50" value="grdWindowToCloseWhenAuthenticatedTaikobanUserChanged,Taikobanの認証ユーザに変化があった時の動作 クローズするメインウィンドウ" />
		<!-- 認証後の動作 -->
		<add key="TerminalConfigEntry51" value="grdDisplayTerminalIsLoggedIn,利用中の端末以外にログインしている端末を表示する" />
		<!-- サーバ情報 -->
		<add key="TerminalConfigEntry52" value="grdAddressOfTaikobanServerToConnect,接続先TaikobanサーバIPアドレス" />
		<!-- Citrix連携 -->
		<add key="TerminalConfigEntry53" value="grdWorkedWithCitrixReceiver,レシーバ連携" />
		<!-- ボタン -->
		<add key="TerminalConfigEntry54" value="grdEntry,登録ボタン" />
		<add key="TerminalConfigEntry55" value="grdCancel,キャンセルボタン" />
		<!-- シャットダウンボタン表示 -->
		<add key="TerminalConfigEntry56" value="grdShutdownButtonDisplay,シャットダウンボタン表示" />
		<!-- ログアウト時動作 -->
		<add key="TerminalConfigEntry57" value="grdLogoutOperation,ログアウト時動作" />

		<!-- ユーザ再認証時（ログイン時、「デスクトップへ移動」時は含まない）の動作 -->
		<add key="TerminalConfigEntry58" value="grdCommandWhenUserReauthentication,ユーザ再認証時（ログイン時、「デスクトップへ移動」時は含まない）の動作 実行コマンド" />

		<!-- 離席画面 -->
		<add key="TerminalConfigEntry63" value="grdUseLeavingSeatLock,離席ロック画面の利用"/>
		<add key="ClientSettingsProvider.ServiceUri" value="" />

		<!-- 直近参照ログ検索時にリモート端末名も検索対象に含めるかどうか false:対象としない true:対象とする 省略時はfalse 削除(2015/12/01)-->
		<!--<add key="SearchingRemoteTerminalName" value="true" /> -->

		<!-- 直近参照ログ検索時のリモート端末名の検索タイプ 0:含む 1:前方一致 2:後方一致 3:一致 省略時は0 削除(2015/12/01)-->
		<!-- (SearchingRemoteTerminalName=trueの時のみ有効) -->
		<!--<add key="SearchingTypeForRemoteTerminalName" value="2" /> -->

		<!-- ICカード状態のSytemFalt(Taikobanサーバーと通信は出来ていないがカード状態は読み取れている時のカード状態)を無視するかどうか(true:無視する false:無視しない) -->
		<!-- Taikobanサーバーが無い状態でICカード読み取りを行う時にはtrueを設定するとカードをの内容を読み取れる。 -->
		<!--<add key="IgnoreSystemFault" value="true"/> -->

		<!-- 直近参照ログ検索用設定 2015/12/01追加-->
		<!-- 端末名から設定されているサフィックスを取り除いた文字列も端末名として検索対象にする(空白なら検索対象としない)-->
		<!-- (例) 端末名が abcd.edoktor.local サフィックスが.edoktor.localなら 検索対象端末名はabcd-->
		<add key="TerminalSuffix" value=".edoktor.local" />

		<!-- 管理者アカウント -->
		<add key="AdminUsers" value="IISPqVAtKzsTtc/kgrvdFw=="/>

		<!-- 認証ログの最長保管期間（年） -->
		<add key="MaximumDurationYearsOnStorage" value="5"/>

		<!-- PC/SC START -->
		<add key="EnablePCSC" value="false"/>	<!-- Taikoban経由せずに直接カードリーダーから情報を取得するか:false=Taikoban経由 true=直接取得 -->
		<add key="PCSCVerbose" value="false"/>
		<add key="PCSCWhiteReaderList" value="Sony FeliCa Port/PaSoRi 3.0|SONY FeliCa Port/PaSoRi 4.0"/>
		<add key="PCSCBlackReaderList" value=""/>
		<add key="PCSCMinimumPollingInterval" value="3000"/>
		<add key="PCSCAccessStartedEventMillisecondsTimeout" value="0"/>
		<!-- HPKI -->
		<add key="EnableHPKI" value="false"/>
		<add key="HPKIDataReadingDelayMilliseconds" value="1000"/>
		<!-- JPKI -->
		<add key="EnableJPKI" value="false"/>
		<add key="JPKIDataReadingDelayMilliseconds" value="1000"/>
		<!-- PC/SC END -->

	</appSettings>
	<system.web>
		<membership defaultProvider="ClientAuthenticationMembershipProvider">
			<providers>
				<add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
			</providers>
		</membership>
		<roleManager defaultProvider="ClientRoleProvider" enabled="true">
			<providers>
				<add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
			</providers>
		</roleManager>
	</system.web>
</configuration>