﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <section name="secureDesktop" type="eDoktor.Auth.Desktop.Configuration.SecureDesktopConfigurationSection,eDoktor.Auth.Desktop"/>
    </configSections>
    <!-- Layout: None = 0, Tile = 1, Center = 2, Stretch = 3, Zoom = 4 -->
    <!-- AnchorType: LeftTop = 0, CenterTop = 1, RightTop = 2, RightCenter = 3, RightBottom = 4, CenterBottom = 5, LeftBottom = 6, LeftCenter = 7, Center = 8 -->
    <secureDesktop backColor="Black" wallpaperLayout="3" vendorLogoEnabled="false" productLogoEnabled="false" releaseVersionEnabled="false" oemLogoEnabled="false" oemLogoAnchorType="5" oemLogoX="0" oemLogoY="-100" messageColor="Salmon" statusMessageColor="White"/>
    <appSettings>
        <add key="AlwaysReturnToDefaultDesktop" value="true"/>
    </appSettings>
</configuration>