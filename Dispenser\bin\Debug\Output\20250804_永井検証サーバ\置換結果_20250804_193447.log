﻿
================================================
処理結果サマリー
================================================
処理成功: はい
処理時間: 62.64秒
総ファイル数: 532
処理されたファイル数: 81
スキップされたファイル数: 451

置換キーワードごとの処理結果:
[$$INSTALL_DRIVE$$] - 置換ファイル数: 3
  置換されたファイル:
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\Batch\ServerSetup.cmd
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\99.ドキュメント\【Taikoban】設定メモ.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\ModuleBackup\BackupModule.cmd

[$$CRYPTED_CONNECTION_STRING$$] - 置換ファイル数: 16
  置換されたファイル:
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AccountImporter\eDoktor.Taikoban.AccountImporter.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AutoCompleteServer\eDoktor.AutoComplete.Database.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\RemoteServer\eDoktor.Taikoban.RemoteServer.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\ReportService\eDoktor.Taikoban.Report.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\UpdateValidPeriods\UpdateValidPeriods.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Auth.Database.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\WitnessService\eDoktor.Taikoban.Report.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\AdminConsole\1_1\eDoktor.Taikoban.AdminConsole.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ApplicationManage\ApplicationManage.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ApplicationManage\AutomationSpy.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ApplicationManage\eDoktor.Taikoban.AdminConsole.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\SmartCardBulkRegister\1\eDoktor.Taikoban.SmartCardBulkRegister.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\SmartCardRegister\1\eDoktor.Taikoban.SmartCardRegister.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\TemporaryAllowOneFactorAuthentication\1\eDoktor.Taikoban.TemporaryAllowOneFactorAuthentication.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\TemporaryCardIssuer\1\eDoktor.Taikoban.TemporaryCardIssuer.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\99.ドキュメント\【Taikoban】設定メモ.txt

[$$DELIVERY_ADDRESS$$] - 置換ファイル数: 21
  置換されたファイル:
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\ServerAddress.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\SmartCardBulkRegister\ServerAddress.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\AdminConsole\Starter\ServerAddress.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\ClientInstaller\Taikoban\AdminConsole\ServerAddress.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\ClientInstaller\Taikoban\SmartCardBulkRegister\ServerAddress.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\ClientInstaller\Taikoban\SmartCardRegister\ServerAddress.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\ClientInstaller\Taikoban\TemporaryAllowOneFactorAuthentication\ServerAddress.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\ClientInstaller\Taikoban\TemporaryCardIssuer\ServerAddress.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\ClientInstallerStarter\ServerAddress.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\RemoteAgent\eDoktor.Taikoban.RemoteSetup.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\RemoteAgent\101\eDoktor.Taikoban.RemoteSetup.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ServerAddress.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ApplicationManage\eDoktor.Auth.Setup.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\SmartCardBulkRegister\Starter\ServerAddress.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\SmartCardRegister\Starter\ServerAddress.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\eDoktor.Auth.Setup.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\101\eDoktor.Auth.Setup.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\TemporaryAllowOneFactorAuthentication\Starter\ServerAddress.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\TemporaryCardIssuer\Starter\ServerAddress.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\02.クライアント\ServerAddress.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\99.ドキュメント\【Taikoban】設定メモ.txt

[$$CLIENT_INSTALL_PATH$$] - 置換ファイル数: 19
  置換されたファイル:
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\ContextExtensions.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\AdminConsole\1_1\ContextExtensions.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\AdminConsole\1_1\eDoktor.Taikoban.AdminConsole.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\RemoteAgent\eDoktor.Taikoban.RemoteSetup.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\RemoteAgent\101\eDoktor.Taikoban.RemoteSetup.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\InstallPath.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ApplicationManage\eDoktor.Auth.Setup.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\AutoCompleteAgent\eDoktor.AutoComplete.Agent.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\CallAuthScreen\CallAuthenticationScreenF.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\CloseApplication\CloseApplication.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\HXAgency\HXAgency.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\HXScreenSaverMonitor\HXScreenSaverMonitor.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\eDoktor.Auth.Setup.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\101\eDoktor.Auth.Setup.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\101\eDoktor.ContentDisplay.API.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\101\eDoktor.VeinAuthClient.API.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\101\InstallPath.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\TemporaryAllowOneFactorAuthentication\1\eDoktor.Taikoban.TemporaryAllowOneFactorAuthentication.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\99.ドキュメント\【Taikoban】設定メモ.txt

[$$SERVER_INSTALL_PATH$$] - 置換ファイル数: 12
  置換されたファイル:
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AdminConsole\SContextExtension\SContextExtensions.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AutoCompleteServer\eDoktor.AutoComplete.ServerService.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\Config.ps1
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\ConfirmIsAlive.ps1
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\ConfirmLicense.ps1
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\DeleteAuthlog.ps1
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\ExecAutoBackup.cmd
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\GraphedTerminalUsage.ps1
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\MonthlyLendingList.ps1
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\MonthlyReleaseLicenseList.ps1
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Taikoban.ServerService.exe.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\99.ドキュメント\【Taikoban】設定メモ.txt

[$$SERVER_ADDRESS$$] - 置換ファイル数: 21
  置換されたファイル:
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\API\eDoktor.Auth.Interprocess.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\AutoCompleteServer\eDoktor.AutoComplete.Interprocess.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\RemoteServer\eDoktor.Taikoban.RemoteInterprocess.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Server\eDoktor.Auth.Interprocess.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\AdminConsole\1_1\eDoktor.Auth.Interprocess.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\AdminConsole\1_1\eDoktor.Taikoban.RemoteInterprocess.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\RemoteAgent\101\eDoktor.Taikoban.Interprocess.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\RemoteAgent\101\eDoktor.Taikoban.RemoteInterprocess.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ApplicationManage\eDoktor.Auth.Interprocess.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\AutoCompleteAgent\eDoktor.Auth.Interprocess.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\AutoCompleteAgent\eDoktor.AutoComplete.Interprocess.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\CallAuthScreen\eDoktor.Auth.Interprocess.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\ClientExtensionTool\eDoktor.Auth.Interprocess.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Scripts\HXAgency\eDoktor.Auth.Interprocess.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\SmartCardBulkRegister\1\eDoktor.Auth.Interprocess.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\SmartCardRegister\1\eDoktor.Auth.Interprocess.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\101\eDoktor.Auth.Interprocess.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\Taikoban\101\DisplayAPI\LockDetailMessage\eDoktor.Auth.Interprocess.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\TemporaryAllowOneFactorAuthentication\1\eDoktor.Auth.Interprocess.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktorUpdater\TemporaryCardIssuer\1\eDoktor.Auth.Interprocess.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\99.ドキュメント\【Taikoban】設定メモ.txt

[$$SERVER_ADDRESS_2$$] - 置換ファイル数: 5
  置換されたファイル:
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\ReportService\eDoktor.Taikoban.Report.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\AutomaticFailback\AutomaticFailback.bat
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\WitnessService\eDoktor.Taikoban.Report.dll.config
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\99.ドキュメント\【Taikoban】設定メモ.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\ModuleBackup\BackupModule.cmd

[$$CONNECTION_STRING$$] - 置換ファイル数: 8
  置換されたファイル:
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\Config.ps1
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\ConfirmIsAlive.ps1
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\ConfirmLicense.ps1
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\DeleteAuthlog.ps1
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\GraphedTerminalUsage.ps1
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\MonthlyLendingList.ps1
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\MonthlyReleaseLicenseList.ps1
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\99.ドキュメント\【Taikoban】設定メモ.txt

[$$HOST_NAME_1$$] - 置換ファイル数: 2
  置換されたファイル:
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\ExecAutoBackup.cmd
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\99.ドキュメント\【Taikoban】設定メモ.txt

[$$HOST_NAME_2$$] - 置換ファイル数: 2
  置換されたファイル:
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\ExecAutoBackup.cmd
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\99.ドキュメント\【Taikoban】設定メモ.txt

[$$SERVER_ADDRESS_1$$] - 置換ファイル数: 3
  置換されたファイル:
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\01.サーバー\eDoktor\Taikoban\Scripts\AutomaticFailback\AutomaticFailback.bat
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\99.ドキュメント\【Taikoban】設定メモ.txt
    N:\work\VSCode\Dispenser\bin\Debug\Output\20250804_永井検証サーバ\Taikoban\Taikoban_Ver.*******\ModuleBackup\BackupModule.cmd

================================================
処理終了: 2025/08/04 19:35:51
