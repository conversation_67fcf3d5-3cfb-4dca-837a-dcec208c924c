USE [$(Param1)]
GO

/****** Object:  Trigger [dbo].[TR_update_t_account_groups]    Script Date: 10/06/2014 16:00:32 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*** CREATE TRIGGER ***/
IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[TR_update_t_account_groups]') AND type in (N'TR'))
DROP TRIGGER [dbo].[TR_update_t_account_groups]
GO

CREATE TRIGGER [dbo].[TR_update_t_account_groups] ON [dbo].[t_account_groups]
FOR UPDATE
AS 
BEGIN
UPDATE t_account_groups
SET modification_datetime = GETDATE()
WHERE id IN (SELECT id FROM inserted)
END
RETURN


GO

