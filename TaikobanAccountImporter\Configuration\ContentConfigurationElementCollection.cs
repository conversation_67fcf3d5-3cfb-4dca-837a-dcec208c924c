﻿using System;
using System.Configuration;

namespace eDoktor.Taikoban.AccountImporter.Configuration
{
    [ConfigurationCollection(typeof(ContentConfigurationElement))]
    class ContentConfigurationElementCollection : ConfigurationElementCollection
    {
        protected override object GetElementKey(ConfigurationElement element)
        {
            return ((ContentConfigurationElement)element).Name;
        }

        protected override ConfigurationElement CreateNewElement()
        {
            return new ContentConfigurationElement();
        }

        public void Add(ContentConfigurationElement element)
        {
            BaseAdd(element);
        }

        public override ConfigurationElementCollectionType CollectionType
        {
            get { return ConfigurationElementCollectionType.BasicMap; }
        }

        public new ContentConfigurationElement this[string name]
        {
            get { return (ContentConfigurationElement)base.BaseGet(name); }
        }

        protected override string ElementName
        {
            get
            {
                return "content";
            }
        }
    }
}
