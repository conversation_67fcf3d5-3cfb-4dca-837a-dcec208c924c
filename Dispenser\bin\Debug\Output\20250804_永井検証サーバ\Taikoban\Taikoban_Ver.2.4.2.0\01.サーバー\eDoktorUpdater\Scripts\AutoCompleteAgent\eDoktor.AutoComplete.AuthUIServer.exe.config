﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <appSettings>
    <!-- Trace -->
    <add key="EnableTrace" value="true" />
    <add key="EnableDebugTrace" value="true" />
    <add key="EnableDetailedExceptionTrace" value="true" />
    <add key="TraceTermInDays" value="30" />
    <!-- Auth Module -->
    <add key="AuthModuleAssemblyNameOrPath" value="eDoktor.AutoComplete.AuthUI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=0ff7bf58c14a5b51" />
    <add key="AuthModuleTypeName" value="eDoktor.AutoComplete.AuthUI.AuthModule" /> 
  </appSettings>
</configuration>