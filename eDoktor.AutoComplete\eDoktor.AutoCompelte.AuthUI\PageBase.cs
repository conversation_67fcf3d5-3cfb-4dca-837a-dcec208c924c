﻿// PageBase.cs

using System;
using System.Windows.Forms;

namespace eDoktor.AutoComplete.AuthUI
{
	partial class PageBase : UserControl
	{
		#region Fields
		private Control[] _clickThroughControls;
		#endregion

		#region Properties
		public Control[] ClickThroughControls { get { return _clickThroughControls; } protected set { _clickThroughControls = value; } }
		#endregion

		#region Constructors
		public PageBase()
		{
			InitializeComponent();
		}
		#endregion
	}
}
