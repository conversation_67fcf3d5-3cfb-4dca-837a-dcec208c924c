@echo off

REM �萔��`

REM 1���@�T�[�o��
SET SERVER1_NAME=192.168.1.194
REM 2���@�T�[�o��
SET SERVER2_NAME=192.168.1.194
REM DB��
SET DB_NAME=master
REM SQL���O�C�����[�U��
SET SQLUSER=sa
REM SQL���O�C���p�X���[�h
SET SQLPASS=P@ssw0rd
REM 1���@�T�[�o�̏��
SET SERVER1_ROLE=NULL
SET SERVER2_ROLE=NULL
REM �ǂݍ��݃t�@�C���F�t�F�C���o�b�N���s
SET IMPORT_SQLFILE=%~dp0Failback.sql
REM ���O�t�@�C���p�X
SET time2=%time: =0%
SET LOGFILE_PATH=%~dp0Log\%date:/=%%time2:~0,2%%time2:~3,2%%time2:~6,2%%.log
REM �t�F�C���o�b�N���O�t�@�C��
SET FAILBACK_LOG_PATH=%~dp0FailbackResult.log

ECHO %DATE% %TIME% �����J�n>%LOGFILE_PATH%

REM 2���@�̐ڑ��m�F
PING %SERVER2_NAME% -n 2 | FIND "TTL"
IF ERRORLEVEL 1 (
	ECHO %DATE% %TIME% 2���@�T�[�o�ڑ����s>>%LOGFILE_PATH%
	REM �����I��
	ECHO %DATE% %TIME% �����I��>>%LOGFILE_PATH%
	EXIT
) ELSE (
	ECHO %DATE% %TIME% 2���@�T�[�o�ڑ�����>>%LOGFILE_PATH%
)

REM 1���@�T�[�o�̃T�[�o��Ԃ�擾�A���ʂ�ϐ��Ɋi�[
FOR /f  %%i in ('SQLCMD -S %SERVER1_NAME% -U %SQLUSER% -P %SQLPASS% -d %DB_NAME% -Q "SET nocount on;SELECT mirroring_role_desc FROM sys.database_mirroring WHERE mirroring_guid IS NOT NULL;"') do SET SERVER1_ROLE=%%i
REM 1���@�̃T�[�o���[����擾�o�������m�F
IF not %SERVER1_ROLE% == NULL (
	ECHO %DATE% %TIME% 1���@�T�[�o��Ԏ擾����>>%LOGFILE_PATH%
	ECHO %DATE% %TIME% 1���@�T�[�o�F%SERVER1_ROLE%>>%LOGFILE_PATH%
) ELSE (
	ECHO %DATE% %TIME% 1���@�T�[�o��Ԏ擾���s>>%LOGFILE_PATH%
	REM �����I��
	ECHO %DATE% %TIME% �����I��>>%LOGFILE_PATH%
	EXIT
)

REM 2���@�T�[�o�̃T�[�o��Ԃ�擾�A���ʂ�ϐ��Ɋi�[
FOR /f  %%i in ('SQLCMD -S %SERVER2_NAME% -U %SQLUSER% -P %SQLPASS% -d %DB_NAME% -Q "SET nocount on;SELECT mirroring_role_desc FROM sys.database_mirroring WHERE mirroring_guid IS NOT NULL;"') do SET SERVER2_ROLE=%%i
REM 2���@�̃T�[�o���[����擾�o�������m�F
IF not %SERVER2_ROLE% == NULL (
	ECHO %DATE% %TIME% 2���@�T�[�o��Ԏ擾����>>%LOGFILE_PATH%
	ECHO %DATE% %TIME% 2���@�T�[�o�F%SERVER2_ROLE%>>%LOGFILE_PATH%
) ELSE (
	ECHO %DATE% %TIME% 2���@�T�[�o��Ԏ擾���s>>%LOGFILE_PATH%
	REM �����I��
	ECHO %DATE% %TIME% �����I��>>%LOGFILE_PATH%
	EXIT
)

REM �P���@�T�[�o��MIRROR�ł���΃t�F�C���o�b�N���s
IF %SERVER1_ROLE% == MIRROR (
	REM �t�F�C���o�b�N���s
	ECHO %DATE% %TIME% �t�F�C���o�b�N���s>>%LOGFILE_PATH%
	SQLCMD -S %SERVER2_NAME% -U %SQLUSER% -P %SQLPASS% -d %DB_NAME% -b -i %IMPORT_SQLFILE% -o %FAILBACK_LOG_PATH% 2>>%LOGFILE_PATH%
) ELSE (
	ECHO %DATE% %TIME% �t�F�C���o�b�N�����s>>%LOGFILE_PATH%
	REM �t�F�C���o�b�N���s���ʃt�@�C���폜
	DEL %FAILBACK_LOG_PATH%
	ECHO %DATE% %TIME% �����I��>>%LOGFILE_PATH%
	EXIT
)

REM �t�F�C���o�b�N���s�����������`�F�b�N
IF %errorlevel% ==0 (
	ECHO %DATE% %TIME% �t�F�C���o�b�N���s����>>%LOGFILE_PATH%
	REM �t�F�C���o�b�N���s���Ȃ̂�20�b�҂�
	TIMEOUT /T 20
) ELSE (
	ECHO %DATE% %TIME% �t�F�C���o�b�N���s���s>>%LOGFILE_PATH%
	REM �t�F�C���o�b�N���s���ʃt�@�C���폜
	DEL %FAILBACK_LOG_PATH%
	REM �����I��
	ECHO %DATE% %TIME% �����I��>>%LOGFILE_PATH%
	EXIT
)

REM �t�F�C���o�b�N���s��̂P�A�Q���@�T�[�o�̃��[���m�F
REM �P���@
FOR /f  %%i in ('SQLCMD -S %SERVER1_NAME% -U %SQLUSER% -P %SQLPASS% -d %DB_NAME% -Q "SET nocount on;SELECT mirroring_role_desc FROM sys.database_mirroring WHERE mirroring_guid IS NOT NULL;"') do SET SERVER1_ROLE=%%i
REM �Q���@
FOR /f  %%i in ('SQLCMD -S %SERVER2_NAME% -U %SQLUSER% -P %SQLPASS% -d %DB_NAME% -Q "SET nocount on;SELECT mirroring_role_desc FROM sys.database_mirroring WHERE mirroring_guid IS NOT NULL;"') do SET SERVER2_ROLE=%%i

ECHO %DATE% %TIME% �t�F�C���o�b�N���s��1���@�T�[�o�F%SERVER1_ROLE%>>%LOGFILE_PATH%
ECHO %DATE% %TIME% �t�F�C���o�b�N���s��2���@�T�[�o�F%SERVER2_ROLE%>>%LOGFILE_PATH%
ECHO %DATE% %TIME% �����I��>>%LOGFILE_PATH%
EXIT

