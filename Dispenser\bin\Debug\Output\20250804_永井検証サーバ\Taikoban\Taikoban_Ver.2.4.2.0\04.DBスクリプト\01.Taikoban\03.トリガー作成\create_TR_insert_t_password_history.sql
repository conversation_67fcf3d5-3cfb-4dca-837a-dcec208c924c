USE [$(Param1)]
GO

/****** Object:  Trigger [dbo].[TR_insert_t_password_history]    Script Date: 10/06/2014 16:00:58 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*** CREATE TRIGGER ***/
IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[TR_insert_t_password_history]') AND type in (N'TR'))
DROP TRIGGER [dbo].[TR_insert_t_password_history]
GO

CREATE TRIGGER [dbo].[TR_insert_t_password_history] ON [dbo].[t_password_history]
FOR INSERT
AS 
BEGIN

DELETE FROM t_password_history WHERE id IN (SELECT id FROM (SELECT RANK() OVER(PARTITION BY logon_id ORDER BY id DESC) as rk, id, logon_id, password FROM t_password_history WHERE logon_id IN (SELECT logon_id FROM inserted)) as x WHERE rk > (SELECT TOP(1) password_generation_management FROM t_configs))

END
RETURN


GO

