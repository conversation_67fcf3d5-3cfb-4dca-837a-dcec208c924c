USE [$(Param1)]
GO
/****** Object:  Table [dbo].[t_log]    Script Date: 2023/06/11 14:43:22 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[t_log]') AND type in (N'U'))
DROP TABLE [dbo].[t_log]
GO

CREATE TABLE [dbo].[t_log](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[output_datetime] [datetime2](7) NOT NULL,
	[terminal_name] [nvarchar](255) NOT NULL,
	[remote_terminal_name] [nvarchar](255) NOT NULL,
	[logon_id] [nvarchar](255) NOT NULL,
	[card_id] [nvarchar](255) NOT NULL,
	[card_type] [int] NOT NULL,
	[system_key] [int] NOT NULL,
	[log_type] [int] NOT NULL,
	[log_sub_type] [int] NOT NULL,
	[terminal_config_id] [int] NOT NULL,
	[message] [nvarchar](2048) NOT NULL,
	[source] [int] NOT NULL,
 CONSTRAINT [PK_t_log] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO
ALTER TABLE [dbo].[t_log] ADD  CONSTRAINT [DF_t_log_output_datetime]  DEFAULT (getdate()) FOR [output_datetime]
GO
ALTER TABLE [dbo].[t_log] ADD  CONSTRAINT [DF_t_log_terminal_id]  DEFAULT ('') FOR [terminal_name]
GO
ALTER TABLE [dbo].[t_log] ADD  CONSTRAINT [DF_t_log_remote_terminal_id]  DEFAULT ('') FOR [remote_terminal_name]
GO
ALTER TABLE [dbo].[t_log] ADD  CONSTRAINT [DF_t_log_user_id]  DEFAULT ('') FOR [logon_id]
GO
ALTER TABLE [dbo].[t_log] ADD  CONSTRAINT [DF_t_log_card_id]  DEFAULT ('') FOR [card_id]
GO
ALTER TABLE [dbo].[t_log] ADD  CONSTRAINT [DF_t_log_card_type]  DEFAULT ((0)) FOR [card_type]
GO
ALTER TABLE [dbo].[t_log] ADD  CONSTRAINT [DF_t_log_system_id]  DEFAULT ((0)) FOR [system_key]
GO
ALTER TABLE [dbo].[t_log] ADD  CONSTRAINT [DF_t_log_log_type]  DEFAULT ((0)) FOR [log_type]
GO
ALTER TABLE [dbo].[t_log] ADD  CONSTRAINT [DF_t_log_log_sub_type]  DEFAULT ((0)) FOR [log_sub_type]
GO
ALTER TABLE [dbo].[t_log] ADD  CONSTRAINT [DF_t_log_terminal_config_id]  DEFAULT ((0)) FOR [terminal_config_id]
GO
ALTER TABLE [dbo].[t_log] ADD  CONSTRAINT [DF_t_log_message]  DEFAULT ('') FOR [message]
GO
ALTER TABLE [dbo].[t_log] ADD  DEFAULT ((0)) FOR [source]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_log', @level2type=N'COLUMN',@level2name=N'id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ログ出力日時' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_log', @level2type=N'COLUMN',@level2name=N'output_datetime'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'端末名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_log', @level2type=N'COLUMN',@level2name=N'terminal_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'リモート端末名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_log', @level2type=N'COLUMN',@level2name=N'remote_terminal_name'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ログオンID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_log', @level2type=N'COLUMN',@level2name=N'logon_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'固有ID (FeliCa IDm/MIFARE UIDなど)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_log', @level2type=N'COLUMN',@level2name=N'card_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'カードタイプ 0:未定義 1:FeliCa 2:MIFARE 3:PicoPass' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_log', @level2type=N'COLUMN',@level2name=N'card_type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'システムID（SSO）' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_log', @level2type=N'COLUMN',@level2name=N'system_key'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ログ項目' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_log', @level2type=N'COLUMN',@level2name=N'log_type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ログサブ項目' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_log', @level2type=N'COLUMN',@level2name=N'log_sub_type'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'動作ポリシーID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_log', @level2type=N'COLUMN',@level2name=N'terminal_config_id'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'メッセージ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_log', @level2type=N'COLUMN',@level2name=N'message'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'ログ発生元　1:Agent 2:Client 3:Server 4:API 5:Portal用DLL 6:CIS用DLL 7:管理コンソール用DLL' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N't_log', @level2type=N'COLUMN',@level2name=N'source'
GO
