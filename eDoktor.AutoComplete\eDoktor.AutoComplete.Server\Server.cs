﻿// Server.cs

namespace eDoktor.AutoComplete.Server
{
	using Interprocess.ObjectValidationExtensions;

	public class Server : System.IDisposable
	{
		#region Fields
		private readonly object SyncRoot = new object();
		private Interprocess.Server _server;
		int _simultaneousConnectionsLimit;
		private Database.Database _database;
		#endregion

		#region Constructors
		public Server()
		{

		}
		#endregion

		#region IDisposable
		~Server()
		{
			Dispose(false);
		}

		public void Dispose()
		{
			Dispose(true);
			System.GC.SuppressFinalize(this);
		}

		protected virtual void Dispose(bool disposing)
		{
			try
			{
				Stop();
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion

		#region Public Methods
		public void Start()
		{
			using (Common.TimedLock.Lock(SyncRoot))
			{
				Stop();

				Common.Trace.OutputDebugTrace("started");

				_database = new Database.Database();

				_server = new Interprocess.Server(Interprocess.Configuration.ServerPortNumber, Interprocess.Configuration.ServerBacklog, Interprocess.Configuration.ServerMaxAcceptableClientCount);

				_server.PacketReceived += OnPacketReceived;
				_server.ClientConnected += OnClientConnected;
				_server.ClientDisconnected += OnClientDisconnected;
				_server.Start();
			}
		}

		public void Stop()
		{
			using (Common.TimedLock.Lock(SyncRoot))
			{
				if (_server != null)
				{
					_server.Dispose();
					_server = null;

					Common.Trace.OutputDebugTrace("stopped");
				}

				_database = null;
			}
		}
		#endregion

		#region Private Methods

		private void OnClientConnected(object sender, System.EventArgs e)
		{
			try
			{
				var client = sender as Interprocess.TcpClient;

				OnClientConnected(client);
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnClientDisconnected(object sender, System.EventArgs e)
		{
			try
			{
				var client = sender as Interprocess.TcpClient;

				OnClientDisconnected(client);
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnPacketReceived(object sender, Common.PacketEventArgs e)
		{
			try
			{
				var client = sender as Interprocess.TcpClient;
				var packet = e.Packet as Interprocess.Packet;

				switch (packet.Command)
				{
					case Interprocess.Command.ClientToServerAutoCompleteSettingQuery:
						OnAutoCompleteSettingQuery(client, packet);
						break;
					default:
						Common.Trace.OutputWarningTrace("invalid packet({0}) from {1}", packet.Command, client.RemoteEndPoint);
						break;
				}
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnClientConnected(Interprocess.TcpClient client)
		{
			try
			{
				
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnClientDisconnected(Interprocess.TcpClient client)
		{
			try
			{
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnAutoCompleteSettingQuery(Interprocess.TcpClient client, Interprocess.Packet packet)
		{
			var requestData = packet.GetData<Interprocess.ClientToServerAutoCompleteSettingQueryRequestData>();
			Interprocess.ClientToServerAutoCompleteSettingQueryResponseData responseData = null;

			try
			{
                // ▼ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
                var autoCompleteInfo = _database.QueryAutoCompleteInfo();
                Common.Trace.OutputDebugTrace("autoCompleteSettings.Length={0}", (autoCompleteInfo.AutoCompleteSettings != null) ? autoCompleteInfo.AutoCompleteSettings.Length : 0);
                // ▲ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara

                const int version = 1;
                // ▼ MODIFY 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
                responseData = new Interprocess.ClientToServerAutoCompleteSettingQueryResponseData() { AutoCompleteInfo = autoCompleteInfo };
                // ▲ MODIFY 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
			}
			finally
			{
				client.Transmit(packet.CreateResponsePacket(responseData));
			}
		}
		#endregion
	}
}
