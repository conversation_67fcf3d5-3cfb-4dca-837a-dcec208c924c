﻿# 関数を定義する
# データ取得関数
function Get-DatabaseData([string]$connectionString, [string]$query)
{
    try
    {
        $connection = New-Object -TypeName System.Data.SqlClient.SqlConnection
        $connection.ConnectionString = $connectionString
        $command = $connection.CreateCommand()
        $command.CommandTimeout = 90
        $command.CommandText = $query
        $adapter = New-Object -TypeName System.Data.SqlClient.SqlDataAdapter $command
        $dataset = New-Object -TypeName System.Data.DataSet
        [void]$adapter.Fill($dataset)
        return, $dataset.Tables[0]
    }
    catch [Exception]
    {
        $errorMessage = $_.exception
    }
}

# データベース実行関数
function Invoke-DatabaseQuery([string]$connectionString, [string]$query)
{
    try
    {
        $connection = New-Object System.Data.SqlClient.SqlConnection
        $connection.ConnectionString = $connectionString
        $command = $connection.CreateCommand()
        $command.CommandTimeout = 90
        $command.CommandText = $query
        $connection.Open()
        $command.ExecuteNonQuery()
        $connection.Close()
    }
    catch [Exception]
    {
        $errorMessage = $_.exception
    }
}

# エラー通知関数
function Notificate-Error([int]$source, [string]$message)
{
    try
    {
        $query = [string]::Format("INSERT INTO t_errors (registration_datetime, registered_by, modification_datetime, modified_by, source, status, alarm_flg, message) VALUES (GETDATE(), 99990005, GETDATE(), 99990005, {0}, 0, 0, '{1}')", $source, ($message -replace "'", "''"))
    
        $result = Invoke-DatabaseQuery $connectionString $query
    }
    catch [Exception]
    {
        $errorMessage = $_.exception
    }
}