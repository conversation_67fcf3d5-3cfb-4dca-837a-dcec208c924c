# �萔�ݒ�
[int]$sourceNumber = 16
[int]$debug = 1
[string]$connectionString = "Network=DBMSSOCN;Data Source=*************;failover partner=*************;Initial Catalog=Taikoban;Integrated Security=False;User ID=Tai<PERSON>ban;Password=************"   # �ڑ�������
[string]$executionPath = "D:\eDoktor\Taikoban\Scripts\" # ���s�p�X
[string]$outputPath = $executionPath + "output\LendingList_%yyyyMM%.csv"   # �o�̓t�@�C���p�X
[string]$logPath = $executionPath + "Logs\lending_list.log" # ���O�t�@�C���p�X
[string]$delimiter = "`t"

# �ϐ���ݒ肷��
[string]$message = ""       # �o�̓��b�Z�[�W
[string]$errorMessage = ""  # �G���[���b�Z�[�W
[string]$preMonth = ""      # �O��1��
[string]$month = ""         # ����1��
[System.Text.StringBuilder]$query # SQL�N�G����

if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " �����J�n") | Out-File $logPath }

try
{
    # ��ƃf�B���N�g����ړ�����
    Set-Location $executionPath

    # �֐���ǂݍ���
    . ".\Common.ps1"
}
catch [Exception]
{
    # �G���[�ʒm�ɓo�^
    if ($_.exception -ne "") { $errorMessage = $_.exception }
    # Notificate-Error $sourceNumber ([string]::Format("�����ݒ�ŃG���[���������܂���: Exception=" + $errorMessage))
    
    return
}

if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " ������������") | Out-File $logPath -Append }

try
{
    # �O��1��
    $preMonth = ((Get-Date).AddMonths(-1).ToString("yyyy/MM") -as [DateTime]).ToString("yyyy/MM/dd")
    # ����1��
    $month = ((Get-Date).ToString("yyyy/MM") -as [DateTime]).ToString("yyyy/MM/dd")
    # �o�̓t�@�C���p�X
    $outputPath = $outputPath.Replace("%yyyyMM%", (Get-Date).AddMonths(-1).ToString("yyyyMM"))
    
    # �O���̑ݏo���X�g��擾���܂�
    $query = New-Object System.Text.StringBuilder
    $query.Append("SELECT s.unique_id, a1.logon_id, u1.name, l.rental_date, l.return_due_date, l.return_date, a2.logon_id AS action_logon_id, u2.name AS action_user_name, l.id")
    $query.Append(" FROM t_lendings AS l")
    $query.Append("  LEFT JOIN t_smart_cards AS s  ON l.smart_card_id           = s.id")
    $query.Append("  LEFT JOIN t_accounts    AS a1 ON l.account_id              = a1.id")
    $query.Append("  LEFT JOIN t_users       AS u1 ON a1.user_id                = u1.id")
    $query.Append("  LEFT JOIN t_accounts    AS a2 ON l.rentl_action_account_id = a2.id")
    $query.Append("  LEFT JOIN t_users       AS u2 ON a2.user_id                = u2.id")
    $query.AppendFormat(" WHERE (CONVERT(DateTime, '{0}', 111) <= rental_date AND rental_date < CONVERT(DateTime, '{1}', 111))", $preMonth, $month)
    $query.AppendFormat(" OR (CONVERT(DateTime, '{0}', 111) <= return_date AND return_date < CONVERT(DateTime, '{1}', 111))", $preMonth, $month)
    $query.Append(" ORDER BY l.rental_date")
    [System.Data.DataTable]$dt = Get-DatabaseData $connectionString $query.ToString()
    
    # �o�̓t�H���_��쐬���܂�
    New-Item (Split-Path $outputPath -parent) -itemType Directory -Force
    
    # �O���̑ݏo���X�g��o�͂��܂�
    $message = "�ݏo�X�}�[�g�J�[�hID"
    $message += $delimiter + "���O�C��ID"
    $message += $delimiter + "����"
    $message += $delimiter + "�ݏo��"
    $message += $delimiter + "�ԋp�\���"
    $message += $delimiter + "�ԋp��"
    $message += $delimiter + "�ݏo���s��ID"
    $message += $delimiter + "�ݏo���s�Ҏ���"
    $message | Out-File $outputPath
    $dt.Rows | ForEach-Object { 
      $message = $_["unique_id"]
      $message += $delimiter + $_["logon_id"]
      $message += $delimiter + $_["name"]
      $message += $delimiter + $_["rental_date"].ToString("yyyy/MM/dd HH:mm:ss")
      $message += $delimiter + $_["return_due_date"].ToString("yyyy/MM/dd HH:mm:ss")
      if ([String]::IsNullOrEmpty($_["return_date"]))
      {
          $message += $delimiter + ""
      }
      else
      {
          $message += $delimiter + $_["return_date"].ToString("yyyy/MM/dd HH:mm:ss")
      }
      $message += $delimiter + $_["action_logon_id"]
      $message += $delimiter + $_["action_user_name"]
      $message | Out-File $outputPath -Append
    }
}
catch [Exception]
{
    # �G���[�ʒm�ɓo�^
    if ($_.exception -ne "") { $errorMessage = $_.exception }
    # Notificate-Error $sourceNumber ([string]::Format("���J�[�h�݂��o���󋵏o�͂ŃG���[���������܂���: Exception=" + $errorMessage))
}

if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " ���J�[�h�݂��o���󋵏o�͊���") | Out-File $logPath -Append }