﻿// SsoConfigurationSection.cs

using System;
using System.Configuration;

namespace eDoktor.AutoComplete.Agent
{
	class SsoConfigurationSection : ConfigurationSection
	{
		#region Properties
		[ConfigurationProperty("loginWindows")]
		public LoginWindowConfigurationElementCollection LoginWindows
		{
			get { return (LoginWindowConfigurationElementCollection)this["loginWindows"]; }
			set { this["loginWindows"] = value; }
		}
		#endregion
	}
}
