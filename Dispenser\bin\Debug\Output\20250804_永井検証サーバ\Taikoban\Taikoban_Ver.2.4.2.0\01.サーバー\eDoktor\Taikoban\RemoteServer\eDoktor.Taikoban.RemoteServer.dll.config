﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="eDoktor.Taikoban.RemoteServer.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <applicationSettings>
    <eDoktor.Taikoban.RemoteServer.Properties.Settings>
      <setting name="ConnectionStringSettingsLabel" serializeAs="String">
        <value>Taikoban</value>
      </setting>
    </eDoktor.Taikoban.RemoteServer.Properties.Settings>
  </applicationSettings>
  <connectionStrings>
    <add name="Taikoban" connectionString="sOEqwRggeOs5J+mrA1PTQP03iS2JvKkkwovlMRimh7NIGBkA6vsDZOSli1ja2eqMXHwU1svdQpoPpl+cAiF71W5rxEQCPpq4d1gvrg7XgSWUOxpzgXIYISoUrCN+yEURta0qlL+UpBogoGF6fBt4Nmo40KsnimIn6wmRkbT0ScNmukl9T8237Gw5Z3A5jBlOw71SlKGl0maMbRVGzGH8J5zm175t4SnPg1pNJkVvp+0=" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <appSettings>
    <!-- セキュリティキー有効期間（秒） -->
    <add key="ValidityPriodOfSecurityKey" value="300"/>
    <!-- パスワード暗号化 false:暗号化なし true:暗号化あり 省略値:true -->
    <add key="PasswordEncryption" value="true" />
  </appSettings>
</configuration>