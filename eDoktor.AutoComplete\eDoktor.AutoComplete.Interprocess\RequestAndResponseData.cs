﻿// RequestAndResponseData.cs

namespace eDoktor.AutoComplete.Interprocess
{
	public class SetupInfo
	{
		#region Properties
		public System.DateTime DateTime { get; set; }
		public string OSVersion { get; set; }
		public int Version { get; set; }
		public bool Enabled { get; set; }
		public bool AutoUpdate { get; set; }
		public int CompleteStatus { get; set; }
		#endregion
	}

	// クライアント→サーバー 1～20
	public class ClientToServerAutoCompleteSettingQueryRequestData
	{
		#region Properties
		#endregion
	}

	public class ClientToServerAutoCompleteSettingQueryResponseData
	{
		#region Properties
        // ▼ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
        public AutoCompleteInfo AutoCompleteInfo { get; set; }
        // ▲ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
		#endregion
	}
}
