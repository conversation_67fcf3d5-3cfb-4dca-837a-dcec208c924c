﻿namespace eDoktor.AutoComplete.AuthUI
{
	partial class UserConfirmationPage
	{
		/// <summary> 
		/// 必要なデザイナー変数です。
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary> 
		/// 使用中のリソースをすべてクリーンアップします。
		/// </summary>
		/// <param name="disposing">マネージ リソースが破棄される場合 true、破棄されない場合は false です。</param>
		protected override void Dispose(bool disposing)
		{
			if (disposing && (components != null))
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		#region コンポーネント デザイナーで生成されたコード

		/// <summary> 
		/// デザイナー サポートに必要なメソッドです。このメソッドの内容を 
		/// コード エディターで変更しないでください。
		/// </summary>
		private void InitializeComponent()
		{
			this.messageLabel = new System.Windows.Forms.Label();
			this.noButton = new eDoktor.UI.ImageButton();
			this.yesButton = new eDoktor.UI.ImageButton();
			this.SuspendLayout();
			// 
			// messageLabel
			// 
			this.messageLabel.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
			| System.Windows.Forms.AnchorStyles.Left)
			| System.Windows.Forms.AnchorStyles.Right)));
			this.messageLabel.Font = new System.Drawing.Font("Yu Gothic UI", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
			this.messageLabel.Location = new System.Drawing.Point(50, 70);
			this.messageLabel.Margin = new System.Windows.Forms.Padding(0);
			this.messageLabel.Name = "messageLabel";
			this.messageLabel.Size = new System.Drawing.Size(500, 220);
			this.messageLabel.TabIndex = 0;
			this.messageLabel.Text = "ログインしますか？";
			this.messageLabel.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
			// 
			// noButton
			// 
			this.noButton.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
			this.noButton.BackColor = System.Drawing.Color.Transparent;
			this.noButton.BackgroundImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_7;
			this.noButton.DefaultImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_7;
			this.noButton.DefaultTextColor = System.Drawing.Color.White;
			this.noButton.DialogResult = System.Windows.Forms.DialogResult.None;
			this.noButton.DisabledImage = null;
			this.noButton.DisabledTextColor = System.Drawing.Color.Empty;
			this.noButton.FocusImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_8;
			this.noButton.FocusTextColor = System.Drawing.Color.White;
			this.noButton.ForeColor = System.Drawing.SystemColors.ControlText;
			this.noButton.HoverImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_9;
			this.noButton.HoverTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(140)))), ((int)(((byte)(177)))));
			this.noButton.LabelText = "いいえ";
			this.noButton.Location = new System.Drawing.Point(325, 286);
			this.noButton.Margin = new System.Windows.Forms.Padding(0);
			this.noButton.Name = "noButton";
			this.noButton.PressedImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_9;
			this.noButton.PressedTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(140)))), ((int)(((byte)(177)))));
			this.noButton.Size = new System.Drawing.Size(195, 44);
			this.noButton.TabIndex = 2;
			this.noButton.TextFont = new System.Drawing.Font("Yu Gothic UI", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
			this.noButton.TextMargin = new System.Windows.Forms.Padding(0);
			this.noButton.Click += new System.EventHandler(this.OnNoButtonClick);
			// 
			// yesButton
			// 
			this.yesButton.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
			this.yesButton.BackColor = System.Drawing.Color.Transparent;
			this.yesButton.BackgroundImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_7;
			this.yesButton.DefaultImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_7;
			this.yesButton.DefaultTextColor = System.Drawing.Color.White;
			this.yesButton.DialogResult = System.Windows.Forms.DialogResult.None;
			this.yesButton.DisabledImage = null;
			this.yesButton.DisabledTextColor = System.Drawing.Color.Empty;
			this.yesButton.FocusImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_8;
			this.yesButton.FocusTextColor = System.Drawing.Color.White;
			this.yesButton.ForeColor = System.Drawing.SystemColors.ControlText;
			this.yesButton.HoverImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_9;
			this.yesButton.HoverTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(140)))), ((int)(((byte)(177)))));
			this.yesButton.LabelText = "はい";
			this.yesButton.Location = new System.Drawing.Point(80, 286);
			this.yesButton.Margin = new System.Windows.Forms.Padding(0);
			this.yesButton.Name = "yesButton";
			this.yesButton.PressedImage = global::eDoktor.AutoComplete.AuthUI.Properties.Resources.B_button_9;
			this.yesButton.PressedTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(140)))), ((int)(((byte)(177)))));
			this.yesButton.Size = new System.Drawing.Size(195, 44);
			this.yesButton.TabIndex = 1;
			this.yesButton.TextFont = new System.Drawing.Font("Yu Gothic UI", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(128)));
			this.yesButton.TextMargin = new System.Windows.Forms.Padding(0);
			this.yesButton.Click += new System.EventHandler(this.OnYesButtonClick);
			// 
			// UserConfirmationPage
			// 
			this.Controls.Add(this.noButton);
			this.Controls.Add(this.yesButton);
			this.Controls.Add(this.messageLabel);
			this.Name = "UserConfirmationPage";
			this.ResumeLayout(false);

		}

		#endregion

		private System.Windows.Forms.Label messageLabel;
		private UI.ImageButton yesButton;
		private UI.ImageButton noButton;
	}
}
