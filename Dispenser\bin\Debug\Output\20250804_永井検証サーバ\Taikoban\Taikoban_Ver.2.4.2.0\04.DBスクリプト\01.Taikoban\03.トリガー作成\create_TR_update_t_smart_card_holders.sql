USE [$(Param1)]
GO

/****** Object:  Trigger [dbo].[TR_update_t_smart_card_holders]    Script Date: 10/06/2014 15:53:27 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*** CREATE TRIGGER ***/
IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[TR_update_t_smart_card_holders]') AND type in (N'TR'))
DROP TRIGGER [dbo].[TR_update_t_smart_card_holders]
GO

CREATE TRIGGER [dbo].[TR_update_t_smart_card_holders] ON [dbo].[t_smart_card_holders]
FOR UPDATE
AS 
BEGIN
UPDATE t_smart_card_holders
SET modification_datetime = GETDATE()
WHERE id IN (SELECT id FROM inserted)
END
RETURN


GO

