﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <appSettings>
    <!-- Trace -->
    <add key="EnableTrace" value="true" />
    <add key="EnableDebugTrace" value="true" />
    <add key="EnableDetailedExceptionTrace" value="true" />
    <add key="TraceTermInDays" value="30" />

    <!-- 子ウインドウexeパス -->
    <add key="ChildExe" value="D:\eDoktor\TaikobanChiba\Client\DisplayAPI\ExternalAuth\ExternalAuthDemo\eDoktor.Taikoban.ExternalAuthDemo.exe"/>
    <!-- 静脈認証終了時のメッセージ番号の定義 16進数(0x????)で定義します。0x8000以上を指定してください。省略値は0x8003-->
    <add key="WmAppAuthEnd" value="0x8003"/>
    <!-- 認証画面検索チェックタイムアウト値 単位 100ms 省略値=50(5秒) Max=200(20秒)-->
    <add key="AuthFormLoadCheckTimeout" value="50"/>
  </appSettings>
</configuration>