<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:schemaLocation="http://www.nlog-project.org/schemas/NLog.xsd NLog.xsd"
      autoReload="true"
      throwExceptions="false"
      internalLogLevel="Off" internalLogFile="c:\temp\nlog-internal.log" >

  <targets>
    <target xsi:type="File"
            name="file"
            fileName="${basedir}/logs/${date:format=yyyyMMdd}.log"
            layout="${longdate} [${threadid:padding=8}] [${uppercase:${level:padding=-5}}] ${callsite}() ${message} ${exception:format=tostring}"
            encoding="UTF-8"
            archiveFileName="${basedir}/logs/archives/archive.{#}.log"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="7"/>
  </targets>

  <rules>
    <logger name="*" minlevel="Trace" writeTo="file"/>
  </rules>
</nlog>