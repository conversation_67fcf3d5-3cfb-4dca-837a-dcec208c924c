﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <appSettings>
    <!-- Trace -->
    <add key="EnableTrace" value="true"/>
    <add key="EnableDebugTrace" value="true"/>
    <add key="EnableDetailedExceptionTrace" value="true"/>
    <add key="ContainingFolderPath" value="D:\eDoktor\Taikoban\Logs\CloseApplication\"/>
    <add key="TraceTermInDays" value="30"/>
    
    <!-- Target -->
    <add key="TargetProcessesName" value=""/>
    <add key="AllowProcessesName" value=""/>
    <add key="WaitTimeCloseMainWindow" value="1000"/>
    <add key="AllowHX" value="true"/>
		<add key="HXTargetURL" value="https://hx-nlb"/>

		<!-- Setting -->
		<add key="NoKillProcessNameList" value="msedge|explorer"/> <!-- 同一プロセスで複数のウィンドウを出しているため強制終了出来ないプロセス -->
  </appSettings>
</configuration>