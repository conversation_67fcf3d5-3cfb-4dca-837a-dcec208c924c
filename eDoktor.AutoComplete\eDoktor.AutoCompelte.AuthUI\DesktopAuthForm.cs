﻿// DesktopAuthForm.cs

using System;
using System.Drawing;
using System.Security.Permissions;
using System.Windows.Forms;
using System.Threading;

namespace eDoktor.AutoComplete.AuthUI
{
	using Interprocess.ObjectValidationExtensions;
	// TaikobanEX対応 start
	using eDoktor.Auth.ClientExtension;
	// TaikobanEX対応 end

	partial class DesktopAuthForm : AutoComplete.AuthModule.AuthFormBase
	{
		#region Enumerations
		private enum PageType
		{
			Undefined = -1,
			DeviceError,
			FaceAuthFailure,
			NetworkError,
			Prompt,
			SystemFault,
			// TaikobanEX対応 start
//			UserConfirmation
			PasswordCredential,
			InvalidUserNameOrPassword,
            PasswordAuth,
            // ▼ MODIFY 未登録カード検出時のエラー対応 2023/01/01 eDoktor Y.Kihara
            //SmartCardFaceAuthFailure,
            //SmartCardAuthFailure
			SmartCardAuthFailure,
			SmartCardFaceAuthFailure,
            // ▲ MODIFY 未登録カード検出時のエラー対応 2023/01/01 eDoktor Y.Kihara
			// TaikobanEX対応 end
		}
		#endregion

		#region Fields
		private readonly object SyncRoot = new object();
		private AutoComplete.AuthModule.AuthModuleLoader _loader;
		private PageBase[] _pages;
		private PageType _currentPage = PageType.Undefined;
		private PageType _nextPage = PageType.Undefined;

		// TaikobanEX対応 start
		private ClientExtensionCore _clientExtensionCore;
		private ClientStatus _clientStatus;
		private bool _authStartBtnPushed; // true:認証開始ボタン押下時    false:ICカード認証時
		private Auth.Interprocess.SmartCardAuthenticationPolicyType _smartCardPolicyType;
		private Auth.Interprocess.DefaultSasOneFactorAuthenticationPolicyType _sasPolicyType;
		private string _smartCardUserName;
		private string _idPasswordUserName;
		// TaikobanEX対応 end

		#endregion

		#region Properties
		protected PageBase CurrentPage { get { return _pages[(int)_currentPage]; } }
		public AutoResetEvent CloseEvent { get; set; }
		#endregion

		#region Constructors
		public DesktopAuthForm()
		{
            InitializeComponent();
            InitializePages();
		}
		#endregion

		#region Protected Methods
		protected override void OnLoad(EventArgs e)
		{
			try
			{
				base.OnLoad(e);
				//ForceActivate(Handle);
                // ▼ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
                PromptPage2 page = (PromptPage2)_pages[(int)PageType.Prompt];
                page.Message = AuthRequest.TopMessage;
                // ▼ ADD ログインボタンを自動押下しない対応 2023/01/10 eDoktor Y.Kihara
                page.donotAutoLoginCheckBox.Visible = AuthRequest.EnableDonotAutoLoginCheckbox;
                page.donotAutoLoginCheckBox.Checked = false;
                page.donotAutoLoginCheckBox.Text = AuthRequest.DonotAutoLoginCheckboxMessage;
                page.donotAutoLoginCheckBox.Left = (page.Width - page.donotAutoLoginCheckBox.Width) / 2;
                // ▲ ADD ログインボタンを自動押下しない対応 2023/01/10 eDoktor Y.Kihara
				// ▲ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
				// ▼ ADD 認証開始ボタンの表示設定追加対応 2023/03/31 eDoktor Y.Kihara
				page.NotAllowedOneFactorAuthentication = AuthRequest.NotAllowedOneFactorAuthentication;
				// ▲ ADD 認証開始ボタンの表示設定追加対応 2023/03/31 eDoktor Y.Kihara
				// ▼ ADD 閉じるボタンの表示設定追加対応 2024/07/22 eDoktor Y.Kihara
				page.HiddenCloseButton = AuthRequest.HiddenCloseButton;
				// ▲ ADD 閉じるボタンの表示設定追加対応 2024/07/22 eDoktor Y.Kihara
				// ▼ MODIFY 標準SASキー対応 2024/07/13 eDoktor Y.Kihara
				page.DefaultSasKeyCombination = _clientExtensionCore.DefaultTerminalConfig.DefaultSasKeyCombination;
				// ▲ MODIFY 標準SASキー対応 2024/07/13 eDoktor Y.Kihara
				SetPage(PageType.Prompt);

                // ▼ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
                if (_clientExtensionCore != null && _clientExtensionCore.SmartCardReaderStatus != null)
                {
                    ExecuteSmartCardReaderStatusChanged(_clientExtensionCore.SmartCardReaderStatus);
                }
                // ▲ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
				CloseInternally();
			}
		}

		protected override void OnFormClosed(FormClosedEventArgs e)
		{
			base.OnFormClosed(e);

			var closeEvent = CloseEvent;

			if (closeEvent != null)
			{
				closeEvent.Set();
			}
		}

		protected override void OnCancelRequested(EventArgs e)
		{
			try
			{
				AbortLoader();
				base.OnCancelRequested(e);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}

		}
		#endregion

		#region Private Methods
		private void InitializePages()
		{
            SuspendLayout();
            var deviceErrorPage = new DeviceErrorPage();
            var faceAuthFailurePage = new FaceAuthFailurePage2();
            var networkErrorPage = new NetworkErrorPage();
            var promptPage = new PromptPage2();
			var systemFaultPage = new SystemFaultPage();
			// TaikobanEX対応 start
			//			var userConfirmationPage = new UserConfirmationPage();
			var passwordCredentialPage = new PasswordCredentialPage();
			var invalidUserNameOrPasswordPage = new InvalidUserNameOrPasswordPage();
			var passwordAuthPage = new PasswordAuthPage();
			var smartCardAuthFailurePage = new SmartCardAuthFailurePage();
			var smartCardFaceAuthFailurePage = new FaceAuthFailurePage();
			//			_pages = new PageBase[] { deviceErrorPage, faceAuthFailurePage, networkErrorPage, promptPage, systemFaultPage, userConfirmationPage };
			_pages = new PageBase[] { deviceErrorPage, faceAuthFailurePage, networkErrorPage, promptPage, systemFaultPage, passwordCredentialPage, invalidUserNameOrPasswordPage, passwordAuthPage, smartCardAuthFailurePage, smartCardFaceAuthFailurePage };
			// TaikobanEX対応 end
			Controls.AddRange(_pages);

			foreach (var page in _pages)
			{
				page.Visible = false;
				AddClickThroughControls(page.ClickThroughControls);
			}

			deviceErrorPage.OKButtonClick += OnDeviceErrorPageOKButtonClick;
			faceAuthFailurePage.OKButtonClick += OnFaceAuthFailurePageOKButtonClick;
			networkErrorPage.OKButtonClick += OnNetworkErrorPageOKButtonClick;
			promptPage.StartButtonClick += OnPromptPageStartButtonClick;
			promptPage.CloseButtonClick += OnPromptPageCloseButtonClick;
			systemFaultPage.OKButtonClick += OnSystemFaultPageOKButtonClick;

			// TaikobanEX対応 start
			//userConfirmationPage.YesButtonClick += OnUserConfirmationPageYesButtonClick;
			//userConfirmationPage.NoButtonClick += OnUserConfirmationPageNoButtonClick;
			passwordCredentialPage.AuthenticateUser += PasswordCredentialPage_AuthenticateUser;
			passwordCredentialPage.CancelButtonClick += PasswordCredentialPage_CancelButtonClick;

			invalidUserNameOrPasswordPage.OKButtonClick += InvalidUserNameOrPasswordPage_OKButtonClick;

			passwordAuthPage.AuthenticateUser += PasswordAuthPage_AuthenticateUser;
			passwordAuthPage.CancelButtonClick += PasswordAuthPage_CancelButtonClick;

            smartCardAuthFailurePage.OKButtonClick += SmartCardAuthFailurePage_OKButtonClick;
            smartCardFaceAuthFailurePage.OKButtonClick += SmartCardFaceAuthFailurePage_OKButtonClick;

			// ICカードリーダー対応
			try
			{
				_clientExtensionCore = new ClientExtensionCore();
				_clientExtensionCore.ClientStatusChanged += _clientExtensionCore_ClientStatusChanged;
				_clientExtensionCore.RoamingOccurred += _clientExtensionCore_RoamingOccurred;
				_clientExtensionCore.PasswordChanged += _clientExtensionCore_PasswordChanged;
				_clientExtensionCore.SmartCardReaderStatusChanged += _clientExtensionCore_SmartCardReaderStatusChanged;
				_clientExtensionCore.Start();

				using (Common.TimedLock.Lock(SyncRoot))
				{
					_clientStatus = _clientExtensionCore.ClientStatus;
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}


			_authStartBtnPushed = false;
			_smartCardPolicyType = Auth.Interprocess.SmartCardAuthenticationPolicyType.SmartCardOnly;
			_sasPolicyType = Auth.Interprocess.DefaultSasOneFactorAuthenticationPolicyType.NotAllowed;
			_smartCardUserName = "";
			_idPasswordUserName = "";
			// TaikobanEX対応 end

			this.ResumeLayout(false);
		}

		// TaikobanEX対応 start
		// ICカード認証後、顔認証に失敗した場合の処理
        private void SmartCardFaceAuthFailurePage_OKButtonClick(object sender, EventArgs e)
        {
			// 生体認証のみ
			if (_smartCardPolicyType == Auth.Interprocess.SmartCardAuthenticationPolicyType.BiometricAuthenticationOnly)
			{
				// 認証待機画面に遷移する
				SetPage(PageType.Prompt);
			}
			// 生体認証を優先(ICカードの場合)
			else if (_smartCardPolicyType == Auth.Interprocess.SmartCardAuthenticationPolicyType.PrioritizeBiometricAuthentication)
			{
				// 画面遷移する前にパスワード認証画面のユーザー名にログイン名を設定しておく
				var page = GetPage(PageType.PasswordAuth) as PasswordAuthPage;
				page.userNameLabel.Text = _smartCardUserName;
				Common.Trace.OutputTrace("page.userNameLabel.Text : {0}", page.userNameLabel.Text);

				// パスワード認証画面に遷移する
				SetPage(PageType.PasswordAuth);
			}
			else
			{
                Common.Trace.OutputErrorTrace("Error！！ _smartCardPolicyType : {0}", _smartCardPolicyType);

                // ▼ ADD 未登録カード検出時のエラー対応 2023/01/01 eDoktor Y.Kihara
                // 認証待機画面に遷移する
                SetPage(PageType.Prompt);
                // ▲ ADD 未登録カード検出時のエラー対応 2023/01/01 eDoktor Y.Kihara
			}
		}

		// 無効なカードです。画面表示時のOKボタン押下処理
		private void SmartCardAuthFailurePage_OKButtonClick(object sender, EventArgs e)
        {
			// 認証開始画面に遷移する
			SetPage(PageType.Prompt);
		}

		private void _clientExtensionCore_ClientStatusChanged(object sender, ClientStatusEventArgs e)
		{
			Common.Trace.OutputTrace("_clientExtension_ClientStatusChanged() call");
			try
			{
				using (Common.TimedLock.Lock(SyncRoot))
				{
					Common.Trace.OutputTrace("セッションイベントを受け取りました。");

					// 状態の再評価
					HandleClientStatusEvent(e.ClientStatusEvent);
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
			finally
			{
				//				ShowMenu();
			}
		}

		private void _clientExtensionCore_RoamingOccurred(object sender, RoamingEventArgs e)
		{
			Common.Trace.OutputTrace("_clientExtension_RoamingOccurred() call");
			try
			{
				using (Common.TimedLock.Lock(SyncRoot))
				{
					// ローミング発生時にロックやログオフなどを行ったりローミング先に取られた仮想セッションをアンロック時に取り返すためのフラグを立てるなどを行います。
					Common.Trace.OutputTrace("ローミングイベントを受け取りました。");

					// ローミング関連情報
					Common.Trace.OutputTrace("ユーザー名={0}", e.UserName);
					Common.Trace.OutputTrace("マシン名={0}", e.ClientInfo.MachineName);
					Common.Trace.OutputTrace("SBC={0}", e.ClientInfo.Sbc);

					if ((_clientStatus == null) || (_clientStatus.AuthenticatedCredential == null) || (!_clientStatus.AuthenticatedCredential.UserName.Equals(e.UserName, StringComparison.OrdinalIgnoreCase)))
					{
						// キャッシュしている情報とユーザーが一致していない!? イベントを取りこぼした？
						Common.Trace.OutputTrace("!?");
					}

					// 状態の再評価（ローミングが発生した際にユーザーをログアウトさせたりセッションをロックしたりログオフしたりといった設定が認証システム側にされている場合があります）
					HandleClientStatusEvent(e.ClientStatusEvent);
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
			finally
			{
				//				ShowMenu();
			}
		}

		private void _clientExtensionCore_PasswordChanged(object sender, PasswordChangeEventArgs e)
		{
			Common.Trace.OutputTrace("_clientExtension_PasswordChanged() call");
			try
			{
				using (Common.TimedLock.Lock(SyncRoot))
				{
					Common.Trace.OutputTrace("パスワード変更イベントを受け取りました。");

					// パスワード変更関連情報
					Common.Trace.OutputTrace("ユーザー名={0}", e.Credential.UserName);
					Common.Trace.OutputTrace("新しいパスワード={0}", e.Credential.Password);

					// パスワードの変更に対する処理(SSOでID連携しているアプリを再認証させるなど)の前にアプリ側でログイン中としているユーザーと通知されたユーザーが一致しているか確認
					if ((_clientStatus == null) || (_clientStatus.AuthenticatedCredential == null) || (!_clientStatus.AuthenticatedCredential.UserName.Equals(e.Credential.UserName, StringComparison.OrdinalIgnoreCase)))
					{
						// キャッシュしている情報とユーザーが一致していない!? イベントを取りこぼした？
						Common.Trace.OutputTrace("!?");
					}
					else
					{
						// キャッシュしているユーザー情報のパスワードを更新
						// 以下の処理はNG パスワードの変更が発生した時点では該当ユーザーはログインしていたがこの時点(e.ClientStatusEvent)では既にログアウトしているかも知れない
						//_clientStatus.AuthenticatedCredential.Password = e.Credential.Password;
					}

					// 状態の再評価
					HandleClientStatusEvent(e.ClientStatusEvent);
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
			finally
			{
				//				ShowMenu();
			}
		}

        // ▼ ADD 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
        private void _clientExtensionCore_SmartCardReaderStatusChanged(object sender, SmartCardReaderStatusEventArgs e)
        {
            ExecuteSmartCardReaderStatusChanged(e.Status);
        }
        // ▲ ADD 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
        
		// カードリーダにICカードがかざされた場合の処理
        // ▼ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
		//private void _clientExtensionCore_SmartCardReaderStatusChanged(object sender, SmartCardReaderStatusEventArgs e)
		private void ExecuteSmartCardReaderStatusChanged(SmartCardReaderStatus status)
        // ▲ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
        {
            // ▼ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
            //Common.Trace.OutputTrace("e.Status.LastEvent : {0}", e.Status.LastEvent);
            Common.Trace.OutputTrace("Status.LastEvent : {0}", status.LastEvent);
            // ▲ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara

			_authStartBtnPushed = false;

            // カードがかざされていない場合
            // ▼ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
            //if (e.Status.LastEvent == SmartCardReaderEvent.Absent)
            if (status.LastEvent == SmartCardReaderEvent.Absent || status.LastEvent == SmartCardReaderEvent.DeviceLost || status.LastEvent == SmartCardReaderEvent.Unknown)
            // ▲ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
			{
				return;
			}

            // スマートカード認証ポリシーを取得する
            // ▼ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
            //var resuponseData = GetQueryUserResponseData(e.Status.AuthenticatedCredential.UserName);
            var responseData = GetQueryUserResponseData(status.AuthenticatedCredential.UserName);
            // ▲ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara

			if (responseData == null)
			{
				var nextPage = (IsNetworkAvailable()) ? PageType.SystemFault : PageType.NetworkError;
				SetPage(nextPage);
				return;
			}

            // 認証失敗時
            // ▼ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
            //if (e.Status.LastEvent == SmartCardReaderEvent.Invalid)
            if (status.LastEvent == SmartCardReaderEvent.Invalid)
            // ▲ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
            {
				// エラー画面に遷移する
				SetPage(PageType.SmartCardAuthFailure, true);
				return;
			}
            // 障害発生時
            // ▼ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
            //else if (e.Status.LastEvent == SmartCardReaderEvent.SystemFault || e.Status.LastEvent == SmartCardReaderEvent.LinkDown)
			else if(status.LastEvent == SmartCardReaderEvent.SystemFault || status.LastEvent == SmartCardReaderEvent.LinkDown)
			// ▲ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
			// ▲ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
			{
				// 障害発生時画面に遷移する。
				SetPage(PageType.SystemFault, true);
				return;
			}

            // ▼ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
            //if (string.IsNullOrWhiteSpace(e.Status.AuthenticatedCredential.UserName))
            if (string.IsNullOrWhiteSpace(status.AuthenticatedCredential.UserName))
            // ▲ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
			{
				SetPage(PageType.InvalidUserNameOrPassword);
				return;
			}

			if (!IsNetworkAvailable())
			{
				SetPage(PageType.NetworkError);
				return;
			}

			Common.Trace.OutputTrace("resuponseData.TerminalConfig.SmartCardAuthenticationPolicyType : {0}", responseData.TerminalConfig.SmartCardAuthenticationPolicyType);
			_smartCardPolicyType = responseData.TerminalConfig.SmartCardAuthenticationPolicyType;

			// スマートカードのみ
			if (responseData.TerminalConfig.SmartCardAuthenticationPolicyType == Auth.Interprocess.SmartCardAuthenticationPolicyType.SmartCardOnly)
			{
				try
                {
                    // ▼ MODIFY ログインボタンを自動押下しない対応 2023/01/01 eDoktor Y.Kihara
                    var page = GetPage(PageType.Prompt) as PromptPage2;
                    bool donotAutoLogin = page.donotAutoLoginCheckBox.Checked;
                    // ▲ MODIFY ログインボタンを自動押下しない対応 2023/01/01 eDoktor Y.Kihara
					// 応答を返して終了する
					AuthResponse = new AutoComplete.AuthModule.AuthResponse() { Result = AutoComplete.AuthModule.AuthResultType.Success,
                                                                        Methods = AutoComplete.AuthModule.AuthMethodTypes.SmartCard,
                                                                        // ▼ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
																		//UserName = e.Status.AuthenticatedCredential.UserName };
                                                                        // ▼ ADD ログインボタンを自動押下しない対応 2023/01/01 eDoktor Y.Kihara
                                                                        DonotAutoLogin = donotAutoLogin,
                                                                        // ▲ ADD ログインボタンを自動押下しない対応 2023/01/01 eDoktor Y.Kihara
                                                                        UserName = status.AuthenticatedCredential.UserName };
                                                                        // ▲ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
					CloseInternally();
				}
				catch (Exception ex)
				{
					Common.Trace.OutputExceptionTrace(ex);
				}
			}
			// 生体認証のみ もしくは、生体認証を優先
			else if (responseData.TerminalConfig.SmartCardAuthenticationPolicyType == Auth.Interprocess.SmartCardAuthenticationPolicyType.BiometricAuthenticationOnly ||
						responseData.TerminalConfig.SmartCardAuthenticationPolicyType == Auth.Interprocess.SmartCardAuthenticationPolicyType.PrioritizeBiometricAuthentication)
			{
                // 顔認証に失敗した場合に備えて、UserNameを保持しておく。
                // ▼ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
                //_smartCardUserName = e.Status.AuthenticatedCredential.UserName;
                _smartCardUserName = status.AuthenticatedCredential.UserName;
                // ▲ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara

				// 顔認証画面に遷移する
				StartFaceAuth();
			}
			// パスワードのみ
			else if(responseData.TerminalConfig.SmartCardAuthenticationPolicyType == Auth.Interprocess.SmartCardAuthenticationPolicyType.PasswordOnly)
            {
				// 画面遷移する前にパスワード認証画面のユーザー名にログイン名を設定しておく
                var page = GetPage(PageType.PasswordAuth) as PasswordAuthPage;
                // ▼ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
                //page.userNameLabel.Text = e.Status.AuthenticatedCredential.UserName;
                page.userNameLabel.Text = status.AuthenticatedCredential.UserName;
                // ▲ MODIFY 起動時カード置きっぱなし対応 2023/01/01 eDoktor Y.Kihara
				Common.Trace.OutputTrace("page.userNameLabel.Text : {0}", page.userNameLabel.Text);

				// パスワード認証画面に遷移する
				SetPage(PageType.PasswordAuth, true);
			}
			else
            {
				Common.Trace.OutputErrorTrace("unknown PolicyType : {0}", responseData.TerminalConfig.SmartCardAuthenticationPolicyType);
            }
		}

		// QueryUserの応答データを取得する。動作ポリシーの情報がほしいため。
		private Auth.Interprocess.ClientToServerUserQueryResponseData GetQueryUserResponseData(string userName)
		{
			using (var client = new Auth.Interprocess.Client(Auth.Interprocess.ConnectionType.ToRemoteServer, false))
			{
				client.Start();
				var request = new Auth.Interprocess.ClientToServerUserQueryRequestData() { HostName = Interprocess.Configuration.MachineName, UserName = userName };
				var response = client.Transceive(request);

				if ((response == null) || (!response.Credential.IsValid))
				{
					Common.Trace.OutputErrorTrace("response error!!");
					return null;
				}

				return response;
			}
		}

		private void HandleClientStatusEvent(ClientStatusEvent clientStatusEvent)
		{
			Common.Trace.OutputTrace("HandleClientStatusEvent() call");
			var clientStatus = clientStatusEvent.ClientStatus;
			var currentAuthenticationEvent = clientStatusEvent.CurrentAuthenticationEvent;
			var currentWindowsSessionEvent = clientStatusEvent.CurrentWindowsSessionEvent;

			if (currentWindowsSessionEvent != WindowsSessionEvent.None)
			{
				Console.WriteLine("Windowsセッションイベント={0}", currentWindowsSessionEvent);
			}

			if (currentAuthenticationEvent != AuthenticationEvent.None)
			{
				Console.WriteLine("認証イベント={0}", currentAuthenticationEvent);

			}

			if (clientStatus.AuthenticatedCredential != null)
			{
				Console.WriteLine("ユーザー名={0}", clientStatus.AuthenticatedCredential.UserName);
				Console.WriteLine("パスワード={0}", clientStatus.AuthenticatedCredential.Password);
				Console.WriteLine("ドメイン={0}", clientStatus.AuthenticatedCredential.Domain);

				if (currentAuthenticationEvent == AuthenticationEvent.Login)
				{
					if ((_clientStatus == null) || (_clientStatus.AuthenticatedCredential == null))
					{
						Console.WriteLine("新規ユーザーがログインしました。");
					}
					else if (!clientStatus.AuthenticatedCredential.UserName.Equals(_clientStatus.AuthenticatedCredential.UserName, StringComparison.OrdinalIgnoreCase))
					{
						Console.WriteLine("ユーザーの切り替えが発生しました。");
					}
					else
					{
						Console.WriteLine("ロックしたユーザーが戻りました。");
					}
				}
			}

			// アプリ側の状態の一貫性を保つためイベントで通知された状態をキャッシュしておく
			// （端末状態を変数に持たず随時_clientExtension.ClientStatusで問い合わせると一連の処理の途中で内容が変わっている可能性がある）
			_clientStatus = clientStatus;
		}

		// ICカード認証後のパスワード認証時のキャンセルボタン押下処理
		private void PasswordAuthPage_CancelButtonClick(object sender, EventArgs e)
        {
			Common.Trace.OutputTrace("PasswordAuthPage_CancelButtonClick() call start");
			try
			{
				AuthResponse = new AutoComplete.AuthModule.AuthResponse() { Result = AutoComplete.AuthModule.AuthResultType.UserAborted };
				// 認証開始画面に遷移する
				SetPage(PageType.Prompt);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		// ICカード認証後のパスワード認証時のOKボタン押下処理
		private void PasswordAuthPage_AuthenticateUser(object sender, PasswordCredentialEventArgs e)
        {
			Common.Trace.OutputTrace("PasswordAuthPage_AuthenticateUser() call");

			try
			{
				if ((string.IsNullOrWhiteSpace(e.UserName)) || (string.IsNullOrWhiteSpace(e.Password)))
				{
					SetPage(PageType.InvalidUserNameOrPassword);
					return;
				}

				if (!IsNetworkAvailable())
				{
					SetPage(PageType.NetworkError);
					return;
				}

				using (var client = new Auth.Interprocess.Client(Auth.Interprocess.ConnectionType.ToRemoteServer, false))
				{
					client.Start();
					Common.Trace.OutputTrace("ClientToServerUserQueryRequestData() call start");
					var request = new Auth.Interprocess.ClientToServerUserQueryRequestData() { HostName = Auth.Interprocess.Configuration.MachineName, UserName = e.UserName };
					var response = client.Transceive(request);
					Common.Trace.OutputTrace("ClientToServerUserQueryRequestData() call end");

					if (response == null)
					{
						var nextPage = (IsNetworkAvailable()) ? PageType.SystemFault : PageType.NetworkError;
						SetPage(nextPage);
						return;
					}

					if ((!response.Credential.IsValid) || (response.Credential.Password != e.Password))
					{
						SetPage(PageType.InvalidUserNameOrPassword);
						return;
					}

					Common.Trace.OutputTrace("response.TerminalConfig.SmartCardAuthenticationPolicyType : {0}", response.TerminalConfig.SmartCardAuthenticationPolicyType);
					_smartCardPolicyType = response.TerminalConfig.SmartCardAuthenticationPolicyType;

                    // IDパスワードのみの場合は、レスポンスを返して終了する
                    // ▼ MODIFY ログインボタンを自動押下しない対応 2023/01/01 eDoktor Y.Kihara
                    //AuthResponse = new AutoComplete.AuthModule.AuthResponse() { Result = AutoComplete.AuthModule.AuthResultType.Success, Methods = AutoComplete.AuthModule.AuthMethodTypes.Password, UserName = response.Credential.UserName };
                    var page = GetPage(PageType.Prompt) as PromptPage2;
                    bool notAutoLogin = page.donotAutoLoginCheckBox.Checked;
                    AuthResponse = new AutoComplete.AuthModule.AuthResponse() { Result = AutoComplete.AuthModule.AuthResultType.Success, Methods = AutoComplete.AuthModule.AuthMethodTypes.Password, DonotAutoLogin = notAutoLogin, UserName = response.Credential.UserName };
                    // ▲ MODIFY ログインボタンを自動押下しない対応 2023/01/01 eDoktor Y.Kihara
					CloseInternally();
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
				SetPage(PageType.SystemFault);
			}
		}

		// IDパスワード失敗時(ユーザー名またはパスワードが違います画面)のOKボタン押下処理
		private void InvalidUserNameOrPasswordPage_OKButtonClick(object sender, EventArgs e)
        {
			Common.Trace.OutputTrace("InvalidUserNameOrPasswordPage_OKButtonClick() call start");
			try
			{
                // IDパスワードによる認証の場合
                if (_authStartBtnPushed)
                {
                    // IDパスワード入力画面に遷移する
                    SetPage(PageType.PasswordCredential);
                }
                else // ICカードによる認証の場合
                {
                    // パスワード認証画面に遷移する
                    SetPage(PageType.PasswordAuth);
                }
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		// IDパスワード入力画面でOKボタン押下時の処理
		private void PasswordCredentialPage_AuthenticateUser(object sender, PasswordCredentialEventArgs e)
        {
			Common.Trace.OutputTrace("PasswordCredentialPage_AuthenticateUser() call");

			try
			{
				if ((string.IsNullOrWhiteSpace(e.UserName)) || (string.IsNullOrWhiteSpace(e.Password)))
				{
					SetPage(PageType.InvalidUserNameOrPassword);
					return;
				}

				if (!IsNetworkAvailable())
				{
					SetPage(PageType.NetworkError);
					return;
				}

				using (var client = new Auth.Interprocess.Client(Auth.Interprocess.ConnectionType.ToRemoteServer, false))
				{
					client.Start();
					Common.Trace.OutputTrace("ClientToServerUserQueryRequestData() call start");
					var request = new Auth.Interprocess.ClientToServerUserQueryRequestData() { HostName = Auth.Interprocess.Configuration.MachineName, UserName = e.UserName };
					var response = client.Transceive(request);
					Common.Trace.OutputTrace("ClientToServerUserQueryRequestData() call end");

					if (response == null)
					{
						var nextPage = (IsNetworkAvailable()) ? PageType.SystemFault : PageType.NetworkError;
						SetPage(nextPage);
						return;
					}

					if ((!response.Credential.IsValid) || (response.Credential.Password != e.Password))
					{
						SetPage(PageType.InvalidUserNameOrPassword);
						return;
					}

					//***debug***
					Common.Trace.EnableDebugTrace = true;
					Common.Trace.EnableDetailedExceptionTrace = true;
					Common.Trace.EnableTrace = true;
					//***debug***
					Common.Trace.OutputTrace("response.TerminalConfig.DefaultSasOneFactorAuthenticationPolicyType : {0}", response.TerminalConfig.DefaultSasOneFactorAuthenticationPolicyType);
					_sasPolicyType = response.TerminalConfig.DefaultSasOneFactorAuthenticationPolicyType;

					// 2要素認証必須もしくは、生体認証を優先
					if (response.TerminalConfig.DefaultSasOneFactorAuthenticationPolicyType == Auth.Interprocess.DefaultSasOneFactorAuthenticationPolicyType.NotAllowed ||
						response.TerminalConfig.DefaultSasOneFactorAuthenticationPolicyType == Auth.Interprocess.DefaultSasOneFactorAuthenticationPolicyType.PrioritizeBiometricAuthentication)
                    {
						// IDパスワード＋顔認証の場合は、顔認証を開始する
						StartFaceAuth();
					}
					// パスワードのみ
                    else if (response.TerminalConfig.DefaultSasOneFactorAuthenticationPolicyType == Auth.Interprocess.DefaultSasOneFactorAuthenticationPolicyType.PasswordOnly)
                    {
                        // ▼ ADD ログインボタンを自動押下しない対応 2023/01/10 eDoktor Y.Kihara
                        var page = GetPage(PageType.Prompt) as PromptPage2;
                        bool donotAutoLogin = page.donotAutoLoginCheckBox.Checked;
                        // ▲ ADD ログインボタンを自動押下しない対応 2023/01/10 eDoktor Y.Kihara
                        // IDパスワードのみの場合は、レスポンスを返して終了する
                        AuthResponse = new AutoComplete.AuthModule.AuthResponse()
                        {
                            Result = AutoComplete.AuthModule.AuthResultType.Success,
                            Methods = AutoComplete.AuthModule.AuthMethodTypes.Password,
                            // ▼ MODIFY ログインボタンを自動押下しない対応 2023/01/10 eDoktor Y.Kihara
                            DonotAutoLogin = donotAutoLogin,
                            // ▲ MODIFY ログインボタンを自動押下しない対応 2023/01/10 eDoktor Y.Kihara
                            UserName = response.Credential.UserName
                        };
                        CloseInternally();
                    }

					// 顔認証失敗時に生体認証を優先の場合は、そのまま代替入力を行うので、UserNameを保持しておく。
					_idPasswordUserName = response.Credential.UserName;
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
				SetPage(PageType.SystemFault);
			}
		}

		// IDパスワード入力画面でキャンセルボタン押下時の処理
        private void PasswordCredentialPage_CancelButtonClick(object sender, EventArgs e)
        {
			Common.Trace.OutputTrace("PasswordCredentialPage_CancelButtonClick() call start");
			try
			{
				AuthResponse = new AutoComplete.AuthModule.AuthResponse() { Result = AutoComplete.AuthModule.AuthResultType.UserAborted };
				// 認証開始画面に遷移する
				SetPage(PageType.Prompt);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		// TaikobanEX対応 end

		private bool AbortLoader()
		{
			using (Common.TimedLock.Lock(SyncRoot))
			{
				if (_loader != null)
				{
					_loader.Abort();
					return true;
				}

				return false;
			}
		}

		private static bool IsNetworkAvailable()
		{
			return Common.Network.IsNetworkAvailable();
		}

		private PageBase GetPage(PageType pageType)
		{
			return _pages[(int)pageType];
		}

		private void SetPage(PageType pageType, bool ignoreLoader = false)
		{
			if (pageType == _currentPage)
			{
				return;
			}

			if (InvokeRequired)
			{
				Action<PageType, bool> setPageDelegate = (tempPageType, tempIgnoreLoader) => SetPage(tempPageType, tempIgnoreLoader);
				Invoke(setPageDelegate, pageType, ignoreLoader);
				return;
			}

			_nextPage = pageType;

			if ((!ignoreLoader) && (AbortLoader()))
			{
				return;
			}

			if (_currentPage != PageType.Undefined)
			{
				_pages[(int)_currentPage].Visible = false;
			}

			_currentPage = _nextPage;
			_nextPage = PageType.Undefined;
			CurrentPage.Visible = true;
			ActiveControl = CurrentPage;
		}

		private void StartFaceAuth()
		{
			try
			{
				//***debug***
				eDoktor.Common.Trace.OutputDebugTrace("StartFaceAuth start");
				//***debug***
				using (Common.TimedLock.Lock(SyncRoot))
				{
					//***debug***
					//if (_loader != null)
					//{
					//	return;
					//}
					//***debug***

					if (!IsNetworkAvailable())
					{
						SetPage(PageType.NetworkError);
						return;
					}

					//***debug***
					//_loader = new AutoComplete.AuthModule.AuthModuleLoader();
					//***debug***
				}

				//***debug***
				//var assemblyNameOrPath = Common.Configuration.AppSetting("AuthModuleAssemblyNameOrPath");
				//var typeName = Common.Configuration.AppSetting("AuthModuleTypeName");
				//            var authRequest = AuthRequest;
				//            // ▼ MODIFY 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
				//            //var newAuthRequest = (authRequest != null) ? new AutoComplete.AuthModule.AuthRequest() { Context = authRequest.Context, Mode = authRequest.Mode, OwnerHandle = Handle, TopMost = authRequest.TopMost, Draggable = authRequest.Draggable, DisplayPosition = AutoComplete.AuthModule.DisplayPositionType.CenterOwner, Margin = authRequest.Margin, UserName = authRequest.UserName } : null;
				//            var newAuthRequest = (authRequest != null) ? new AutoComplete.AuthModule.AuthRequest() { Context = authRequest.Context, Mode = authRequest.Mode, OwnerHandle = Handle, TopMost = authRequest.TopMost, Draggable = authRequest.Draggable, DisplayPosition = AutoComplete.AuthModule.DisplayPositionType.CenterOwner, Margin = authRequest.Margin, TopMessage = authRequest.TopMessage, UserName = authRequest.UserName } : null;
				//            // ▲ MODIFY 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
				//var tempAuthResponse = _loader.PerformAuthentication(assemblyNameOrPath, typeName, newAuthRequest);
				var tempAuthResponse = new AutoComplete.AuthModule.AuthResponse();
				using (var externalAuth = new eDoktor.AutoComplete.ExternalAuth.ExternalAuth())
				{
					var authRequest = AuthRequest;
					var result = externalAuth.Authenticate(this.Handle, authRequest.UserName);

					tempAuthResponse.Methods = AutoComplete.AuthModule.AuthMethodTypes.Face;    // 一旦…
					tempAuthResponse.UserName = authRequest.UserName;

					switch (result)
					{
						case ExternalAuth.ExternalAuth.AuthResult.Success:
							tempAuthResponse.Result = AutoComplete.AuthModule.AuthResultType.Success;
							break;
						case ExternalAuth.ExternalAuth.AuthResult.Cancel:
							tempAuthResponse.Result = AutoComplete.AuthModule.AuthResultType.CallerAborted;
							break;
						case ExternalAuth.ExternalAuth.AuthResult.UserNotExist:
							tempAuthResponse.Result = AutoComplete.AuthModule.AuthResultType.NoMatchFound;
							break;
						case ExternalAuth.ExternalAuth.AuthResult.SystemError:
							tempAuthResponse.Result = AutoComplete.AuthModule.AuthResultType.NoMatchFound;
							break;
						default:
							tempAuthResponse.Result = AutoComplete.AuthModule.AuthResultType.SystemFault;
							break;
					}
				}
				//***debug***

				if (CancellationToken.IsCancellationRequested)
				{
					return;
				}

				if (_nextPage != PageType.Undefined)
				{
					SetPage(_nextPage, true);
					return;
				}

				if ((tempAuthResponse == null) || (tempAuthResponse.Result == AutoComplete.AuthModule.AuthResultType.Undefined) || (tempAuthResponse.Result == AutoComplete.AuthModule.AuthResultType.SystemFault) || ((tempAuthResponse.Result == AutoComplete.AuthModule.AuthResultType.Success) && (string.IsNullOrWhiteSpace(tempAuthResponse.UserName))))
				{
					if (!IsNetworkAvailable())
					{
						SetPage(PageType.NetworkError, true);
						return;
					}

					SetPage(PageType.SystemFault, true);
					return;
				}
				else if (tempAuthResponse.Result == AutoComplete.AuthModule.AuthResultType.Success)
				{
					using (var client = new Auth.Interprocess.Client(Auth.Interprocess.ConnectionType.ToRemoteServer, false))
					{
						client.Start();
						var request = new Auth.Interprocess.ClientToServerUserQueryRequestData() { HostName = Auth.Interprocess.Configuration.MachineName, UserName = tempAuthResponse.UserName };
						var response = client.Transceive(request);

						if ((response == null) || (!response.Credential.IsValid))
						{
							SetPage(PageType.SystemFault, true);
							return;
						}

                        // ▼ MODIFY ログインボタンを自動押下しない対応 2023/01/01 eDoktor Y.Kihara
                        var page = GetPage(PageType.Prompt) as PromptPage2;
                        bool donotAutoLogin = page.donotAutoLoginCheckBox.Checked;
                        tempAuthResponse.DonotAutoLogin = donotAutoLogin;
                        // ▲ MODIFY ログインボタンを自動押下しない対応 2023/01/01 eDoktor Y.Kihara

						// TaikobanEX対応 start
						// ログインしますか画面には遷移せず、応答を返して終了する
						//var page = GetPage(PageType.UserConfirmation) as UserConfirmationPage;
						//page.UserName = response.Credential.UserName;
						//page.DisplayName = response.Credential.DisplayName;
						//SetPage(PageType.UserConfirmation, true);
						//return;
						// TaikobanEX対応 end
					}
				}
				else if (tempAuthResponse.Result == AutoComplete.AuthModule.AuthResultType.NoMatchFound)
				{
					// TaikobanEX対応 start
					// IDパスワードによる認証の場合
					if (_authStartBtnPushed)
                    {
						// 顔認証失敗画面に遷移する
						SetPage(PageType.FaceAuthFailure, true);
					}
					// ICカードによる認証の場合
					else
					{
						// 動作ポリシーが「生体認証を優先」の場合
						if (_smartCardPolicyType == Auth.Interprocess.SmartCardAuthenticationPolicyType.PrioritizeBiometricAuthentication)
						{
                            // ICカード用の顔認証失敗画面に遷移する
                            // ▼ MODIFY 未登録カード検出時のエラー対応 2023/01/01 eDoktor Y.Kihara
                            //SetPage(PageType.SmartCardAuthFailure, true);
                            SetPage(PageType.SmartCardFaceAuthFailure, true);
                            // ▲ MODIFY 未登録カード検出時のエラー対応 2023/01/01 eDoktor Y.Kihara
						}
						else
						{
							// 顔認証失敗画面に遷移する
							SetPage(PageType.FaceAuthFailure, true);
						}
					}
					// TaikobanEX対応 end
					return;
				}
				else if (tempAuthResponse.Result == AutoComplete.AuthModule.AuthResultType.DeviceError)
				{
					SetPage(PageType.DeviceError, true);
					return;
				}
				else if (tempAuthResponse.Result == AutoComplete.AuthModule.AuthResultType.UserAborted)
				{
					return;
				}

				AuthResponse = tempAuthResponse;
				CloseInternally();
			}
			catch(Exception ex)
            {
				Common.Trace.OutputExceptionTrace(ex);
				SetPage(PageType.SystemFault, true);
            }
			finally
			{
				_loader = null;
			}
		}

		private void OnDeviceErrorPageOKButtonClick(object sender, EventArgs e)
		{
			try
			{
				SetPage(PageType.Prompt);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnFaceAuthFailurePageOKButtonClick(object sender, EventArgs e)
		{
			try
			{
				// TaikobanEX対応 start
				// 2要素認証必須
				if (_sasPolicyType == Auth.Interprocess.DefaultSasOneFactorAuthenticationPolicyType.NotAllowed)
				{
					// 認証待機画面に遷移する
					SetPage(PageType.Prompt);
				}
				// 生体認証のみ
				else if (_sasPolicyType == Auth.Interprocess.DefaultSasOneFactorAuthenticationPolicyType.BiometricAuthenticationOnly)
				{
					// 認証待機画面に遷移する
					SetPage(PageType.Prompt);
				}
				// 生体認証を優先(IDパスワードの場合)
				else if (_sasPolicyType == Auth.Interprocess.DefaultSasOneFactorAuthenticationPolicyType.PrioritizeBiometricAuthentication)
				{
                    // IDパスワード認証は成功しているので、そのまま代替入力を行う
                    // ▼ MODIFY ログインボタンを自動押下しない対応 2023/01/01 eDoktor Y.Kihara
                    var page = GetPage(PageType.Prompt) as PromptPage2;
                    bool donotAutoLogin = page.donotAutoLoginCheckBox.Checked;
                    //AuthResponse = new AutoComplete.AuthModule.AuthResponse() { Result = AutoComplete.AuthModule.AuthResultType.Success, Methods = AutoComplete.AuthModule.AuthMethodTypes.Face, UserName = _idPasswordUserName };
                    AuthResponse = new AutoComplete.AuthModule.AuthResponse() { Result = AutoComplete.AuthModule.AuthResultType.Success, Methods = AutoComplete.AuthModule.AuthMethodTypes.Face, DonotAutoLogin = donotAutoLogin, UserName = _idPasswordUserName };
                    // ▲ MODIFY ログインボタンを自動押下しない対応 2023/01/01 eDoktor Y.Kihara
					CloseInternally();
				}
				else
				{
					Common.Trace.OutputErrorTrace("Error！！ _sasPolicyType : {0}", _sasPolicyType);
				}
				// TaikobanEX対応 end
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnNetworkErrorPageOKButtonClick(object sender, EventArgs e)
		{
			try
			{
				SetPage(PageType.Prompt);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnPromptPageStartButtonClick(object sender, EventArgs e)
		{
			try
			{
				// TaikobanEX対応 start
				_authStartBtnPushed = true;

#if false
				// スマートカード認証ポリシーを取得する
				var resuponseData = GetQueryUserResponseData("edoktor");  // ここに指定する名前をどこから取得するか？ ★要確認

                if (resuponseData != null)
                {

					_smartCardPolicyType = resuponseData.TerminalConfig.SmartCardAuthenticationPolicyType;
					_sasPolicyType = resuponseData.TerminalConfig.DefaultSasOneFactorAuthenticationPolicyType;

					// 2要素認証必須
					if (_sasPolicyType == Interprocess.DefaultSasOneFactorAuthenticationPolicyType.NotAllowed)
					{
						// IDパスワード認証画面に遷移する
						SetPage(PageType.PasswordCredential, true);
					}
					// 生体認証のみ
					else if (_sasPolicyType == Interprocess.DefaultSasOneFactorAuthenticationPolicyType.BiometricAuthenticationOnly)
					{
						// 顔認証画面に遷移する
						StartFaceAuth();
					}
					// 生体認証を優先
					else if (_sasPolicyType == Interprocess.DefaultSasOneFactorAuthenticationPolicyType.PrioritizeBiometricAuthentication)
					{
						// IDパスワード認証画面に遷移する
						SetPage(PageType.PasswordCredential, true);
					}
					// パスワードのみ
					else if (_sasPolicyType == Interprocess.DefaultSasOneFactorAuthenticationPolicyType.PasswordOnly)
					{
						// IDパスワード認証画面に遷移する
						SetPage(PageType.PasswordCredential, true);
					}
					else
					{
						Common.Trace.OutputErrorTrace("Error!! sasPolicyType : {0}", _sasPolicyType);
					}
				}
#else

				// こちらの方法ではクライアントインストール時にしか動作ポリシーを取得できないが、Taikobanの仕様に合わせる為、今のところこちらを採用とする。
				// 問題は、カードリーダーにカードをかざしている状態じゃないと情報がとれない為、導入の際にカードリーダーが必須となる。★★★ 要検討
				if (_clientExtensionCore.DefaultTerminalConfig != null)
                {
					// スマートカード認証ポリシーを取得する
					_smartCardPolicyType = _clientExtensionCore.DefaultTerminalConfig.SmartCardAuthenticationPolicyType;
					_sasPolicyType = _clientExtensionCore.DefaultTerminalConfig.DefaultSasOneFactorAuthenticationPolicyType;
					Common.Trace.OutputTrace("_smartCardPolicyType : {0}", _smartCardPolicyType);
					Common.Trace.OutputTrace("_sasPolicyType       : {0}", _sasPolicyType);

					// 2要素認証必須
					if (_sasPolicyType == Auth.Interprocess.DefaultSasOneFactorAuthenticationPolicyType.NotAllowed)
					{
						// IDパスワード認証画面に遷移する
						SetPage(PageType.PasswordCredential, true);
					}
					// 生体認証のみ
					else if (_sasPolicyType == Auth.Interprocess.DefaultSasOneFactorAuthenticationPolicyType.BiometricAuthenticationOnly)
					{
						// 顔認証画面に遷移する
						StartFaceAuth();
					}
					// 生体認証を優先
					else if (_sasPolicyType == Auth.Interprocess.DefaultSasOneFactorAuthenticationPolicyType.PrioritizeBiometricAuthentication)
					{
						// IDパスワード認証画面に遷移する
						SetPage(PageType.PasswordCredential, true);
					}
					// パスワードのみ
					else if (_sasPolicyType == Auth.Interprocess.DefaultSasOneFactorAuthenticationPolicyType.PasswordOnly)
					{
						// IDパスワード認証画面に遷移する
						SetPage(PageType.PasswordCredential, true);
					}
					else
					{
						Common.Trace.OutputErrorTrace("Error!! sasPolicyType : {0}", _sasPolicyType);
					}
				}
                else
                {
					Common.Trace.OutputErrorTrace("動作ポリシーが取得できませんでした。");
                }
#endif
				// TaikobanEX対応 end
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnPromptPageCloseButtonClick(object sender, EventArgs e)
		{
			try
			{
				AuthResponse = new AutoComplete.AuthModule.AuthResponse() { Result = AutoComplete.AuthModule.AuthResultType.UserAborted };
				CloseInternally();
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnSystemFaultPageOKButtonClick(object sender, EventArgs e)
		{
			try
			{
				// ▼ MODIFY 障害時解除対応 2024/07/13 eDoktor Y.Kihara
				//SetPage(PageType.Prompt);
				// 障害時ログイン許可設定により動作を変更する
				if (_clientExtensionCore.DefaultTerminalConfig.AllowLogonOnAllFailure)
				{
					// 障害時ログイン許可のため、画面クローズ
					CloseInternally();
				}
				else
				{
					// 障害時ログイン不許可のため、プロンプト画面に戻る
					SetPage(PageType.Prompt);
				}
				// ▲ MODIFY 障害時解除対応 2024/07/13 eDoktor Y.Kihara
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnUserConfirmationPageYesButtonClick(object sender, EventArgs e)
		{
			try
			{
				var page = sender as UserConfirmationPage;
                AuthResponse = new AutoComplete.AuthModule.AuthResponse() { Result = AutoComplete.AuthModule.AuthResultType.Success, Methods = AutoComplete.AuthModule.AuthMethodTypes.Face, UserName = page.UserName };
                // ▼ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara

                // ▲ ADD 代理入力マスタ対応 2022/09/18 eDoktor Y.Kihara
				CloseInternally();
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private void OnUserConfirmationPageNoButtonClick(object sender, EventArgs e)
		{
			try
			{
				SetPage(PageType.Prompt);
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion
	}
}
