# �萔�ݒ�
[int]$sourceNumber = 17
[int]$debug = 1
[string]$connectionString = "Network=DBMSSOCN;Data Source=*************;failover partner=*************;Initial Catalog=Taikoban;Integrated Security=False;User ID=Tai<PERSON>ban;Password=************"   # �ڑ�������
[string]$executionPath = "D:\eDoktor\Taikoban\Scripts\" # ���s�p�X
[string]$outputPath = $executionPath + "output\ReleaseLicenseList_%yyyyMM%.csv"   # �o�̓t�@�C���p�X
[string]$logPath = $executionPath + "Logs\release_license_list.log" # ���O�t�@�C���p�X
[string]$delimiter = "`t"

# �ϐ���ݒ肷��
[string]$message = ""       # �o�̓��b�Z�[�W
[string]$errorMessage = ""  # �G���[���b�Z�[�W
[string]$month = ""         # ����1��
[System.Text.StringBuilder]$query # SQL�N�G����

if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " �����J�n") | Out-File $logPath }

try
{
    # ��ƃf�B���N�g����ړ�����
    Set-Location $executionPath

    # �֐���ǂݍ���
    . ".\Common.ps1"
}
catch [Exception]
{
    # �G���[�ʒm�ɓo�^
    if ($_.exception -ne "") { $errorMessage = $_.exception }
    # Notificate-Error $sourceNumber ([string]::Format("�����ݒ�ŃG���[���������܂���: Exception=" + $errorMessage))
    
    return
}

if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " ������������") | Out-File $logPath -Append }

try
{
    # ����1��
    $month = ((Get-Date).ToString("yyyy/MM") -as [DateTime]).ToString("yyyy/MM/dd")
    # �o�̓t�@�C���p�X
    $outputPath = $outputPath.Replace("%yyyyMM%", (Get-Date).AddMonths(-1).ToString("yyyyMM"))
    
    # �O���̃��C�Z���X������X�g��擾���܂�
    $query = New-Object System.Text.StringBuilder
    $query.Append("SELECT * FROM t_deleted_terminal_statuses")
    $query.AppendFormat(" WHERE deletion_datetime < CONVERT(DateTime, '{0}', 111)", $month)
    $query.Append(" ORDER BY deletion_datetime")
    [System.Data.DataTable]$dt = Get-DatabaseData $connectionString $query.ToString()
    
    # �o�̓t�H���_��쐬���܂�
    New-Item (Split-Path $outputPath -parent) -itemType Directory -Force
    
    # �O���̃��C�Z���X������X�g��o�͂��܂�
    $message = "���C�Z���X�����"
    $message += $delimiter + "�[����"
    $message | Out-File $outputPath
    $dt.Rows | ForEach-Object { 
      $message = $_["deletion_datetime"]
      $message += $delimiter + $_["terminal_name"]
      $message | Out-File $outputPath -Append
    }
    
    # �O���̃��C�Z���X������X�g��폜���܂�
    $query = New-Object System.Text.StringBuilder
    $query.Append("DELETE FROM t_deleted_terminal_statuses")
    $query.AppendFormat(" WHERE deletion_datetime < CONVERT(DateTime, '{0}', 111)", $month)
    Invoke-DatabaseQuery $connectionString $query.ToString()
}
catch [Exception]
{
    # �G���[�ʒm�ɓo�^
    if ($_.exception -ne "") { $errorMessage = $_.exception }
    # Notificate-Error $sourceNumber ([string]::Format("���C�Z���X������X�g�擾�ŃG���[���������܂���: Exception=" + $errorMessage))
}

if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " ���C�Z���X������X�g�擾����") | Out-File $logPath -Append }