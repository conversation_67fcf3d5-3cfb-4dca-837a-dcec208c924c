<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <appSettings>
        <!-- Trace -->
        <add key="EnableTrace" value="true"/>
        <add key="EnableDebugTrace" value="true"/>
        <add key="EnableDetailedExceptionTrace" value="true"/>
        <add key="TraceTermInDays" value="30"/>
        <!-- 子ウインドウexeパス -->
        <add key="ChildExe" value="D:\eDoktor\Taikoban\Client\DisplayAPI\LockDetailMessage\eDoktor.Taikoban.LockDetailMessage.exe"/>
        <!-- コンテンツ表示画面Load終了検索チェックタイムアウト値 単位 100ms 省略値=50(5秒) Max=400(40秒)-->
        <add key="ContentFormLoadCheckTimeout" value="400"/>
        <!-- コンテンツ表示のタイトル検索時のFindwindowチェックタイムアウト値 単位 100ms 省略値=50(5秒) Max=200(20秒)-->
        <add key="FindWindowCheckTimeout" value="200"/>
    </appSettings>
</configuration>