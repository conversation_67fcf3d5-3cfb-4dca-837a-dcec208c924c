1 08:52:50.900 eDoktor.AutoComplete.Agent.Program.Main                              eDoktor.AutoComplete.Agent, Version=1.0.0.0, Culture=neutral, PublicKeyToken=0ff7bf58c14a5b51
0 08:52:51.931 eDoktor.AutoComplete.Agent.Program.Main                              elevation=True
0 08:52:52.650 eDoktor.Common.TcpClient.OnSocketConnected                           172.19.51.7:49901
0 08:52:52.665 eDoktor.AutoComplete.Interprocess.TcpClient.GetNextSequence          172.19.51.7:49901 sequence=1
0 08:52:52.665 eDoktor.Common.TcpClient.OnPacketTransmitted                         172.19.51.7:49901 [65-44-6F-6B-74-6F-72-20-00-01-01-00-01-00-A0-00-DF-AE-BF-65-45-BC-58-A0-94-8F-60-0A-44-51-96-09-C6-EC-27-8F-22-99-4A-F7-ED-D6-46-1E-13-28-A4-5D-1A-85-F6-B5-A0-60-32-72-91-AA-5F-DE-EC-8E-61-E1-05-DA-02-2A-2F-59-19-9B-72-B2-C1-BD-E4-A8-A5-CA-5D-2B-9D-21-B6-3B-BD-72-9D-47-38-B5-26-91-C0-8D-73-EA-7C-7D-AA-D7-81-CC-AB-63-8F-53-6C-48-1D-10-25-A4-DD-27-40-B2-65-53-AA-05-E0-E4-68-DB-34-29-E0-90-8C-1C-B9-DA-4C-8C-0F-46-B4-36-3C-3B-4D-A8-DF-ED-27-42-30-5B-13-30-9F-95-84-89-3F-22-65-65-F0-44-A6-CA-37-FD-B3-45-9C-0E-B3-3D-06-CD-06-95-0D-0A]
0 08:52:52.681 eDoktor.Common.TcpClient.OnPacketReceived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
0 08:52:52.696 eDoktor.AutoComplete.Agent.ApplicationContext.get_DefaultAutoCompleteInfo AutoCompleteInfoDataStoreDirectoryPath=D:\eDoktor\Taikoban\AutoCompleteAgent\
0 09:22:38.400 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0 対象ウィンドウ発見[:131222]
0 09:23:21.992 eDoktor.Common.TcpClient.OnSocketConnected                           172.19.51.7:49001
0 09:23:22.005 eDoktor.Auth.Interprocess.TcpClient.GetNextSequence                  172.19.51.7:49001 sequence=1
0 09:23:22.005 eDoktor.Common.TcpClient.OnPacketTransmitted                         172.19.51.7:49001 [65-44-6F-6B-74-6F-72-20-00-01-0B-00-01-00-E0-00-4A-D9-E8-00-52-63-02-F5-11-0B-ED-21-C6-35-5A-F1-29-CA-27-8E-CC-43-68-A5-C4-97-D2-3E-58-6D-84-25-F2-95-96-74-81-24-B8-F6-36-DA-96-EE-75-C4-31-CE-C1-0D-17-84-8C-A0-2B-4C-E4-44-91-BB-E2-75-EC-2B-07-17-F4-E3-3A-71-F6-74-C6-6F-1C-DD-33-ED-24-01-42-4F-CE-A7-D1-72-41-EC-6F-8C-4F-2E-A8-A9-2C-99-11-5E-5F-E7-12-73-34-20-1F-E6-3B-B6-F5-34-93-88-39-78-3C-ED-07-5A-94-EC-93-F7-24-99-3F-74-09-A9-27-27-A6-B6-A2-27-4C-63-9F-02-E1-B2-59-AD-8B-21-34-62-73-53-03-90-35-C4-9E-79-4C-26-5E-15-0F-F8-F0-03-16-98-EC-CD-A8-D3-A0-F5-A8-FD-EB-75-57-8E-E5-5C-21-09-CD-3D-54-EB-5D-36-D1-65-3B-87-D6-7A-82-E6-EA-92-F9-5D-CC-78-FC-F0-49-AC-D6-2E-97-45-4D-FB-4C-D1-B6-E8-55-57-97-08-53-8A-21-EE-62-F7-0D-0A]
0 09:23:22.010 eDoktor.Common.TcpClient.OnPacketReceived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
0 09:23:22.022 eDoktor.Common.TcpClient.OnSocketDisconnected                        172.19.51.7:49001
4 09:23:24.540 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0 System.Windows.Automation.ElementNotAvailableException
   場所 MS.Internal.Automation.UiaCoreApi.CheckError(Int32 hr)
   場所 System.Windows.Automation.AutomationElement.GetCurrentPropertyValue(AutomationProperty property, Boolean ignoreDefaultValue)
   場所 System.Windows.Automation.AutomationElement.AutomationElementInformation.get_AutomationId()
   場所 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0(IntPtr hWnd, IntPtr lParam)
4 09:23:28.433 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0 System.Windows.Automation.ElementNotAvailableException
   場所 MS.Internal.Automation.UiaCoreApi.CheckError(Int32 hr)
   場所 System.Windows.Automation.AutomationElement.GetCurrentPropertyValue(AutomationProperty property, Boolean ignoreDefaultValue)
   場所 System.Windows.Automation.AutomationElement.AutomationElementInformation.get_AutomationId()
   場所 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0(IntPtr hWnd, IntPtr lParam)
4 09:23:36.464 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0 System.Windows.Automation.ElementNotAvailableException
   場所 MS.Internal.Automation.UiaCoreApi.CheckError(Int32 hr)
   場所 System.Windows.Automation.AutomationElement.GetCurrentPropertyValue(AutomationProperty property, Boolean ignoreDefaultValue)
   場所 System.Windows.Automation.AutomationElement.AutomationElementInformation.get_AutomationId()
   場所 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0(IntPtr hWnd, IntPtr lParam)
4 09:23:40.480 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0 System.Windows.Automation.ElementNotAvailableException
   場所 MS.Internal.Automation.UiaCoreApi.CheckError(Int32 hr)
   場所 System.Windows.Automation.AutomationElement.GetCurrentPropertyValue(AutomationProperty property, Boolean ignoreDefaultValue)
   場所 System.Windows.Automation.AutomationElement.AutomationElementInformation.get_AutomationId()
   場所 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0(IntPtr hWnd, IntPtr lParam)
4 09:23:41.628 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0 System.Windows.Automation.ElementNotAvailableException
   場所 MS.Internal.Automation.UiaCoreApi.CheckError(Int32 hr)
   場所 System.Windows.Automation.AutomationElement.GetCurrentPropertyValue(AutomationProperty property, Boolean ignoreDefaultValue)
   場所 System.Windows.Automation.AutomationElement.AutomationElementInformation.get_AutomationId()
   場所 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0(IntPtr hWnd, IntPtr lParam)
4 09:23:43.665 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0 System.Windows.Automation.ElementNotAvailableException
   場所 MS.Internal.Automation.UiaCoreApi.CheckError(Int32 hr)
   場所 System.Windows.Automation.AutomationElement.GetCurrentPropertyValue(AutomationProperty property, Boolean ignoreDefaultValue)
   場所 System.Windows.Automation.AutomationElement.AutomationElementInformation.get_AutomationId()
   場所 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0(IntPtr hWnd, IntPtr lParam)
4 09:23:45.786 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0 System.Windows.Automation.ElementNotAvailableException
   場所 MS.Internal.Automation.UiaCoreApi.CheckError(Int32 hr)
   場所 System.Windows.Automation.AutomationElement.GetCurrentPropertyValue(AutomationProperty property, Boolean ignoreDefaultValue)
   場所 System.Windows.Automation.AutomationElement.AutomationElementInformation.get_AutomationId()
   場所 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0(IntPtr hWnd, IntPtr lParam)
4 09:23:46.544 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0 System.Windows.Automation.ElementNotAvailableException
   場所 MS.Internal.Automation.UiaCoreApi.CheckError(Int32 hr)
   場所 System.Windows.Automation.AutomationElement.GetCurrentPropertyValue(AutomationProperty property, Boolean ignoreDefaultValue)
   場所 System.Windows.Automation.AutomationElement.AutomationElementInformation.get_AutomationId()
   場所 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0(IntPtr hWnd, IntPtr lParam)
4 09:23:50.451 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0 System.Windows.Automation.ElementNotAvailableException
   場所 MS.Internal.Automation.UiaCoreApi.CheckError(Int32 hr)
   場所 System.Windows.Automation.AutomationElement.GetCurrentPropertyValue(AutomationProperty property, Boolean ignoreDefaultValue)
   場所 System.Windows.Automation.AutomationElement.AutomationElementInformation.get_AutomationId()
   場所 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0(IntPtr hWnd, IntPtr lParam)
4 09:23:58.459 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0 System.Windows.Automation.ElementNotAvailableException
   場所 MS.Internal.Automation.UiaCoreApi.CheckError(Int32 hr)
   場所 System.Windows.Automation.AutomationElement.GetCurrentPropertyValue(AutomationProperty property, Boolean ignoreDefaultValue)
   場所 System.Windows.Automation.AutomationElement.AutomationElementInformation.get_AutomationId()
   場所 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0(IntPtr hWnd, IntPtr lParam)
4 09:24:02.507 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0 System.Windows.Automation.ElementNotAvailableException
   場所 MS.Internal.Automation.UiaCoreApi.CheckError(Int32 hr)
   場所 System.Windows.Automation.AutomationElement.GetCurrentPropertyValue(AutomationProperty property, Boolean ignoreDefaultValue)
   場所 System.Windows.Automation.AutomationElement.AutomationElementInformation.get_AutomationId()
   場所 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0(IntPtr hWnd, IntPtr lParam)
4 09:24:08.483 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0 System.Windows.Automation.ElementNotAvailableException
   場所 MS.Internal.Automation.UiaCoreApi.CheckError(Int32 hr)
   場所 System.Windows.Automation.AutomationElement.GetCurrentPropertyValue(AutomationProperty property, Boolean ignoreDefaultValue)
   場所 System.Windows.Automation.AutomationElement.AutomationElementInformation.get_AutomationId()
   場所 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0(IntPtr hWnd, IntPtr lParam)
4 09:24:12.436 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0 System.Windows.Automation.ElementNotAvailableException
   場所 MS.Internal.Automation.UiaCoreApi.CheckError(Int32 hr)
   場所 System.Windows.Automation.AutomationElement.GetCurrentPropertyValue(AutomationProperty property, Boolean ignoreDefaultValue)
   場所 System.Windows.Automation.AutomationElement.AutomationElementInformation.get_AutomationId()
   場所 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0(IntPtr hWnd, IntPtr lParam)
4 09:24:13.532 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0 System.Windows.Automation.ElementNotAvailableException
   場所 MS.Internal.Automation.UiaCoreApi.CheckError(Int32 hr)
   場所 System.Windows.Automation.AutomationElement.GetCurrentPropertyValue(AutomationProperty property, Boolean ignoreDefaultValue)
   場所 System.Windows.Automation.AutomationElement.AutomationElementInformation.get_AutomationId()
   場所 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0(IntPtr hWnd, IntPtr lParam)
4 09:24:14.532 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0 System.Windows.Automation.ElementNotAvailableException
   場所 MS.Internal.Automation.UiaCoreApi.CheckError(Int32 hr)
   場所 System.Windows.Automation.AutomationElement.GetCurrentPropertyValue(AutomationProperty property, Boolean ignoreDefaultValue)
   場所 System.Windows.Automation.AutomationElement.AutomationElementInformation.get_AutomationId()
   場所 eDoktor.AutoComplete.Agent.LoginWindowMonitor.<PerformMainRoutine>b__30_0(IntPtr hWnd, IntPtr lParam)