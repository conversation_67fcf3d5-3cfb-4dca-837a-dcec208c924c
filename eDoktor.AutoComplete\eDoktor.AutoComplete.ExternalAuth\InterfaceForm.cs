﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace eDoktor.AutoComplete.ExternalAuth
{
	public partial class InterfaceForm : Form
    {
        #region アプリケーション間メッセージ
        private const int WM_APP_MSG_2 = 0x8001;
        private const int WM_APP_AUTHEND = 0x8003;
        private const int WM_APP_AUTH_FORM_LOADED = 0x8004;
        #endregion

        #region Fields
        private ExternalAuthClient _authClient = null;
        private IntPtr _mainWindowHandle = IntPtr.Zero;
        private int _wmAppAuthEnd = WM_APP_AUTHEND;
        #endregion

        #region DLLImport
        [System.Runtime.InteropServices.DllImport("user32.dll", CharSet = System.Runtime.InteropServices.CharSet.Auto)]
        public static extern bool PostMessage(System.IntPtr hWnd, int Msg, int wParam, int lParam);
        #endregion

        #region Constructors
        public InterfaceForm(ExternalAuthClient authClient)
        {
            InitializeComponent();
            this._authClient = authClient;


        }
        #endregion

        #region Public Methods
        public void SetMainWindowHandle(IntPtr hWnd)
        {
            this._mainWindowHandle = hWnd;
        }
        #endregion

        #region Private Methods
        private void SetWmAppAuthEndConde()
        {
            try
            {
                string code = eDoktor.Common.Configuration.AppSetting("WmAppAuthEnd");
                if (string.IsNullOrWhiteSpace(code))
                {
                    this._wmAppAuthEnd = WM_APP_AUTHEND;
                }
                else
                {
                    this._wmAppAuthEnd = Convert.ToInt32(code, 16);
                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
                this._wmAppAuthEnd = WM_APP_AUTHEND;
            }
        }

        private void PostAuthEndMessage(int wParam, int lParam)
        {
            try
            {
                eDoktor.Common.Trace.OutputTrace("PostMessage実行 wParam={0} lParam={1}", wParam, lParam);
                PostMessage(this._mainWindowHandle, this._wmAppAuthEnd, wParam, lParam);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }
        #endregion

        #region override
        protected override void WndProc(ref Message m)
        {
            const int WM_SYSCOMMAND = 0x112;
            const long SC_CLOSE = 0xF060L;

            if (m.Msg == WM_SYSCOMMAND &&
                (m.WParam.ToInt64() & 0xFFF0L) == SC_CLOSE)
            {
                return;
            }

            try
            {
                if (m.Msg == WM_APP_MSG_2)
                {
                    string msg = string.Format("メッセージ受信 Lpara ={0} Wparam={1}", (int)m.LParam.ToInt64(), (int)m.WParam.ToInt64());
                    eDoktor.Common.Trace.OutputTrace(msg);

                    int exitNotificationType = this._authClient.GetExitNotificationType();
                    if (exitNotificationType == 0)
                    {
                        this._authClient.SetExternalAuthState((int)m.WParam.ToInt64());
                        this._authClient.SetExecutedExternalAuthKind((int)m.LParam.ToInt64());
                    }
                    else if (exitNotificationType == 1)
                    {
                        PostAuthEndMessage((int)m.WParam.ToInt64(), (int)m.LParam.ToInt64());
                    }
                    else
                    {
                        this._authClient.SetExternalAuthState((int)m.WParam.ToInt64());
                        this._authClient.SetExecutedExternalAuthKind((int)m.LParam.ToInt64());
                        PostAuthEndMessage((int)m.WParam.ToInt64(), (int)m.LParam.ToInt64());
                    }

                    return;
                }
                else if (m.Msg == WM_APP_AUTH_FORM_LOADED)
                {
                    eDoktor.Common.Trace.OutputTrace("認証画面ロード済みメッセージを受信しました。");

                    this._authClient.SetExternalFormLoaded(1);

                    return;
                }

            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }

            base.WndProc(ref m);
        }
        #endregion
    }
}
