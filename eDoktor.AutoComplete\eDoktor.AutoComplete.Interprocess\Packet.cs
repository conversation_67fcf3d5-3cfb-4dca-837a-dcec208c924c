﻿// Packet.cs

namespace eDoktor.AutoComplete.Interprocess
{
	using Common.ByteArrayExtensions;

	public class Packet : Common.Packet
	{
		#region Enumerations
		private enum HeaderSection
		{
			Signature,		// シグネチャ
			Flag,			// フラグ
			Response,		// 応答
			Command,		// コマンド
			Sequence,		// シーケンス番号
			DataLength,		// データ長
		}
		#endregion

		#region Fields
		public static readonly ushort MaxSequence = ushort.MaxValue;
		public static readonly ushort MinSequence = 1;
		public static readonly ushort BroadcastSequence = 0;
		private const int HeaderLength = 16;
		private const int DataOffset = HeaderLength;
		private const int MaxDataLength = ushort.MaxValue;
		private const string SignatureString = "eDoktor ";
		private const string Terminator = "\r\n";
		private static readonly byte[] SignatureBytes;
		private static readonly byte[] TerminatorBytes;
		private static readonly int MaxPacketLength;
		private static readonly int MinPacketLength;
		private static readonly System.Text.Encoding Encoding;
		private static readonly Common.SectionCollection<HeaderSection> HeaderSections;
		private static readonly Common.Section<HeaderSection> SignatureSection;
		private static readonly Common.Section<HeaderSection> FlagSection;
		private static readonly Common.Section<HeaderSection> ResponseSection;
		private static readonly Common.Section<HeaderSection> CommandSection;
		private static readonly Common.Section<HeaderSection> SequenceSection;
		private static readonly Common.Section<HeaderSection> DataLengthSection;
		#endregion

		#region Properties
		private string Signature
		{
			get { return ReadString(SignatureSection.Offset, SignatureSection.Length, Encoding); }
			set { WriteString(SignatureSection.Offset, value, Encoding); }
		}

		public byte Flag
		{
			get { return ReadByte(FlagSection.Offset); }
			set { WriteByte(FlagSection.Offset, value); }
		}

		public Response Response
		{
			get { return (Response)ReadByte(ResponseSection.Offset); }
			set { WriteByte(ResponseSection.Offset, (byte)value); }
		}

		public Command Command
		{
			get { return (Command)ReadUInt16(CommandSection.Offset); }
			set { WriteUInt16(CommandSection.Offset, (ushort)value); }
		}

		public ushort Sequence
		{
			get { return ReadUInt16(SequenceSection.Offset); }
			set { WriteUInt16(SequenceSection.Offset, value); }
		}

		public ushort DataLength
		{
			get { return ReadUInt16(DataLengthSection.Offset); }
			set { WriteUInt16(DataLengthSection.Offset, value); }
		}

		public byte[] Data
		{
			get { return Read(DataOffset, DataLength); }
			set
			{
				if ((value != null) && (value.Length != 0))
				{
					if (value.Length > MaxDataLength)
					{
						throw new System.ArgumentOutOfRangeException("value", string.Format("length={0}", value.Length));
					}

					int newPacketLength = HeaderLength + value.Length + TerminatorBytes.Length;
					Resize(newPacketLength);
					DataLength = (ushort)value.Length;
					Write(DataOffset, value);
					Write(DataOffset + value.Length, TerminatorBytes);
				}
				else
				{
					Resize(MinPacketLength);
					DataLength = 0;
					Write(DataOffset, TerminatorBytes);
				}
			}
		}
		#endregion

		#region Constructors
		static Packet()
		{
			try
			{
				HeaderSections = new Common.SectionCollection<HeaderSection>(new Common.Section<HeaderSection>[]
				{
					SignatureSection = new Common.Section<HeaderSection>(HeaderSection.Signature, 0, 8),
					FlagSection = new Common.Section<HeaderSection>(HeaderSection.Flag, 8, 1),
					ResponseSection = new Common.Section<HeaderSection>(HeaderSection.Response, 9, 1),
					CommandSection = new Common.Section<HeaderSection>(HeaderSection.Command, 10, 2),
					SequenceSection = new Common.Section<HeaderSection>(HeaderSection.Sequence, 12, 2),
					DataLengthSection = new Common.Section<HeaderSection>(HeaderSection.DataLength, 14, 2),
				});

				Encoding = System.Text.Encoding.UTF8;
				SignatureBytes = Encoding.GetBytes(SignatureString);
				TerminatorBytes = Encoding.GetBytes(Terminator);
				MinPacketLength = HeaderLength + TerminatorBytes.Length;
				MaxPacketLength = HeaderLength + MaxDataLength + TerminatorBytes.Length;
			}
			catch (System.Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}

		private Packet(int length)
			: base(length)
		{

		}

		private Packet(byte[] rawData)
			: base(rawData)
		{

		}
		#endregion

		#region Public Methods
		public static Packet CreateRequestPacket<T>(Command command, T data) where T : class
		{
			return Create<T>(0, Response.ACK, command, 0, data);
		}

		public Packet CreateResponsePacket<T>(T data) where T : class
		{
			return Create<T>(0, (data != null) ? Response.ACK : Response.NAK, Command, Sequence, data);
		}

		public T GetData<T>() where T : class
		{
			return Deserialize<T>(Data);
		}

		public void SetData<T>(T data) where T : class
		{
			Data = Serialize(data);
		}
		#endregion

		#region Internal Methods
		internal static Packet[] SplitBytesIntoPackets(byte[] bytes, out byte[] fragment)
		{
			fragment = null;
			System.Collections.Generic.List<Packet> packets = null;

			while (true)
			{
				if ((bytes == null) || (bytes.Length == 0))
				{
					break;
				}

				int length = (bytes.Length > SignatureSection.Length) ? SignatureSection.Length : bytes.Length;

				for (int i = 0; i < length; i++)
				{
					if (bytes[i] != SignatureBytes[i])
					{
						throw new Common.InvalidPacketException("signature");
					}
				}

				if (bytes.Length < MinPacketLength)
				{
					break;
				}

				ushort dataLength = bytes.ReadUInt16(DataLengthSection.Offset);
				int packetLength = HeaderLength + dataLength + TerminatorBytes.Length;
				int terminatorOffset = packetLength - TerminatorBytes.Length;

				if (bytes.Length <= terminatorOffset)
				{
					break;
				}

				length = (bytes.Length >= terminatorOffset + TerminatorBytes.Length) ? TerminatorBytes.Length : bytes.Length - terminatorOffset;

				for (int i = 0; i < length; i++)
				{
					if (bytes[terminatorOffset + i] != TerminatorBytes[i])
					{
						throw new Common.InvalidPacketException("terminator");
					}
				}

				if (bytes.Length < packetLength)
				{
					break;
				}

				if (packets == null)
				{
					packets = new System.Collections.Generic.List<Packet>();
				}

				packets.Add(new Packet(Common.ByteArray.Split(ref bytes, packetLength)));
			}

			fragment = bytes;

			return ((packets != null) && (packets.Count != 0)) ? packets.ToArray() : null;
		}

		internal static Packet Create(byte flag, Response response, Command command, ushort sequence)
		{
			Packet packet = new Packet(MinPacketLength);
			packet.Flag = flag;
			packet.Response = response;
			packet.Command = command;
			packet.Signature = SignatureString;

			if (sequence != 0)
			{
				packet.Sequence = sequence;
			}

			return packet;
		}

		internal static Packet Create<T>(byte flag, Response response, Command command, ushort sequence, T data) where T : class
		{
			Packet packet = new Packet(null);
			packet.SetData<T>(data);
			packet.Flag = flag;
			packet.Response = response;
			packet.Command = command;
			packet.Signature = SignatureString;

			if (sequence != 0)
			{
				packet.Sequence = sequence;
			}

			return packet;
		}

		internal byte[] Serialize<T>(T data) where T : class
		{
			return Crypto.Serialize<T>(data);
		}

		internal T Deserialize<T>(byte[] bytes) where T : class
		{
			return Crypto.Deserialize<T>(bytes);
		}
		#endregion
	}
}
