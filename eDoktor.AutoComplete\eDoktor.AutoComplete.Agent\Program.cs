﻿// Program.cs

using System;
using System.Windows.Forms;

namespace eDoktor.AutoComplete.Agent
{
	static class Program
	{
		#region Main
		[STAThread]
		static void Main(string[] args)
		{
			try
			{
				Common.Process.SetCurrentDirectory();
				string traceContainingFolderPath = Common.Configuration.AppSetting("TraceContainingFolderPath");

				if (!string.IsNullOrWhiteSpace(traceContainingFolderPath))
				{
					if (!System.IO.Directory.Exists(traceContainingFolderPath))
					{
						System.IO.Directory.CreateDirectory(traceContainingFolderPath);
					}

					Common.Trace.ContainingFolderPath = traceContainingFolderPath;
				}
				Common.Trace.EnableDebugTrace = Common.Configuration.AppSetting("EnableDebugTrace", false);
				Common.Trace.EnableDetailedExceptionTrace = Common.Configuration.AppSetting("EnableDetailedExceptionTrace", false);
				Common.Trace.TraceTerm = TimeSpan.FromDays(Common.Configuration.AppSetting("TraceTermInDays", Common.Trace.DefaultTraceTermInDays));
				Common.Trace.SafeEnableTrace = Common.Configuration.AppSetting("EnableTrace", false);
				Application.EnableVisualStyles();
				Application.SetCompatibleTextRenderingDefault(false);

				Common.Trace.OutputDebugTrace("elevation={0}", Common.Environment.IsProcessElevated());

				using (var mutex = new Common.Mutex(Common.Mutex.GetDefaultMutexName(false)))
				{
					mutex.Acquire(0);

					try
					{
						Application.Run(ApplicationContext.Create(Properties.Resources.taikoban16));
					}
					finally
					{
						GC.KeepAlive(mutex);
					}
				}
			}
			catch (Exception ex)
			{
				Common.Trace.OutputExceptionTrace(ex);
			}
		}
		#endregion
	}
}
