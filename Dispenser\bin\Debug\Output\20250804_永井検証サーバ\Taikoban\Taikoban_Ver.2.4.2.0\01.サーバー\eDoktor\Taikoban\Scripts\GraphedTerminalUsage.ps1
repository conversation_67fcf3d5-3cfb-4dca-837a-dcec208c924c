# �萔���`����
[int]$sourceNumber = 18
[int]$debug = 1
[string]$connectionString = "Network=DBMSSOCN;Data Source=*************;failover partner=*************;Initial Catalog=Taikoban;Integrated Security=False;User ID=Taikoban;Password=************"   # �ڑ�������
[string]$executionPath = "D:\eDoktor\Taikoban\Scripts\" # ���s�p�X
[string]$macroPath = $executionPath + "GraphedTerminalUsage.xlsm" # �}�N���t�@�C���p�X
[string]$inCsv = $executionPath + "output\TerminalUsage_%yyyyMMdd%.csv" # ����CSV�t�@�C���p�X
[string]$outExcel = $executionPath + "output\TerminalUsage_%yyyyMMdd%.xlsx" # �o��Excel�t�@�C���p�X
#[string]$outExcel = "\\*************\share\�a�@�l�`CIM�A���p\Taikoban\�[�����p��\%yyyy%\%MM%\TerminalUsage_%yyyyMMdd%.xlsx" # �o��Excel�t�@�C���p�X
[string]$movePath = "D:\eDoktor\Taikoban\Scripts\output\�[���X�e�[�^�X�O���t��\%yyyy%\%MM%\�[�����p��_%yyyyMMdd%.xlsx" # �o��Excel�ړ��p�X
#[string]$movePath = "" # �o��Excel�ړ��p�X
[string]$logPath = $executionPath + "Logs\exec_graphed.log" # ���O�t�@�C���p�X
#[Microsoft.Office.Interop.Excel.ApplicationClass]$xls = $null # ExcelCOM
[object]$xls = $null # ExcelCOM

# �ϐ���ݒ肷��
[int]$result = 0 
[string]$errorMessage = ""  # �G���[���b�Z�[�W

if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " �����J�n") | Out-File $logPath }

# �֐���ǂݍ���
try
{
    # ��ƃf�B���N�g����ړ�����
    Set-Location $executionPath
    
    . ".\Common.ps1"
}
catch [Exception]
{
    # �G���[�ʒm�ɓo�^
    if ($_.exception -ne "") { $errorMessage = $_.exception }
    # Notificate-Error $sourceNumber ([string]::Format("�����ݒ�ŃG���[���������܂���: Exception=" + $errorMessage))
    
    return
}

if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " ������������") | Out-File $logPath -Append }

# Excel�}�N������s���[�����p�󋵂�O���t������
try
{
    
    if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " ���o�̓p�X�ݒ�") | Out-File $logPath -Append }
    # ���o�̓t�@�C���p�X��ݒ肷��
    [int]$addDays = -1
    [string]$yesterday = $(Get-Date).AddDays($addDays).ToString("yyyyMMdd")
    [string]$year = $(Get-Date).AddDays($addDays).ToString("yyyy")
    [string]$month = $(Get-Date).AddDays($addDays).ToString("MM")
    $inCsv = $inCsv.Replace("%yyyyMMdd%", $yesterday)
    $outExcel = $outExcel.Replace("%yyyy%", $year)
    $outExcel = $outExcel.Replace("%MM%", $month)
    $outExcel = $outExcel.Replace("%yyyyMMdd%", $yesterday)
    if ($movePath -ne "")
    {
        $movePath = $movePath.Replace("%yyyy%", $year)
        $movePath = $movePath.Replace("%MM%", $month)
        $movePath = $movePath.Replace("%yyyyMMdd%", $yesterday)
    }

    if (-not (Test-Path $inCsv))
    {
        # Notificate-Error $sourceNumber ("CSV�t�@�C�������݂��܂���: Path=" + $inCsv)
        if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " CSV�t�@�C�������݂��܂���: Path=" + $inCsv) | Out-File $logPath -Append }

        return
    }

    if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " �o�̓t�H���_�쐬") | Out-File $logPath -Append }
    # �o�̓t�H���_��쐬����
    New-Item (Split-Path $outExcel -parent) -itemType Directory -Force
    if ($movePath -ne "")
    {
        New-Item (Split-Path $movePath -parent) -itemType Directory -Force
    }

    # Excel��COM�R���|�[�l���g��Ăяo��
    $xls = New-Object -ComObject Excel.Application
    $xls.Visible = $False

    if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " Excel�}�N�����s") | Out-File $logPath -Append }
    # Excel�t�@�C����J��
    $xls.Workbooks.Open($macroPath)

    if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " Excel�}�N�����s�O") | Out-File $logPath -Append }
    
    # Excel�}�N���̎��s
    $inCsv | Out-File $logPath -Append
    $outExcel | Out-File $logPath -Append
    $result = $xls.Run("ExecGraphed", $inCsv, $outExcel)
    
    if ($result -ne 0)
    {
        # Notificate-Error $sourceNumber ([string]::Format("�}�N�������ŃG���[���������܂���: Error=" + $result))
        if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + [string]::Format(" �}�N�������ŃG���[���������܂���: Error=" + $result)) | Out-File $logPath -Append }
    }
    
    # �I������
    $xls.Quit()
    $xls = $null
    [GC]::Collect()
    
    # �o�̓t�@�C����ړ�����
    if ($movePath -ne "")
    {
        Write-Host "�ړ��J�n"
        $movePath | Out-File $logPath -Append
        For ($i=1; $i -le 10; $i++)
        {
            try
            {
                Move-Item $outExcel $movePath -Force -ErrorAction:Stop
                
                break;
            }
            catch [Exception]
            {
                Write-Host ($i.ToString() + "/10")
                if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " �o�̓t�@�C���̈ړ��ŃG���[���������܂���(" + $i.ToString() + "/10): Exception=" + $errorMessage) | Out-File $logPath -Append }
                
                Start-Sleep -s 1
            }
        }
    }
}
catch [Exception]
{
    # �G���[�ʒm�ɓo�^
    if ($_.exception -ne "") { $errorMessage = $_.exception }
    # Notificate-Error $sourceNumber ([string]::Format("�[�����p�󋵂̃O���t���ŃG���[���������܂���: Exception=", $errorMessage))
    if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " �[�����p�󋵂̃O���t���ŃG���[���������܂���: Exception=" + $errorMessage) | Out-File $logPath -Append }

    # �I������
    if ($xls -ne $null)
    {
        $xls.Quit()
        $xls = $null
        [GC]::Collect()
    }
    
    return
}

if ($debug -eq 1) { ("7 " + $(Get-Date).ToString() + " �[�����p�󋵃O���t������") | Out-File $logPath -Append }
