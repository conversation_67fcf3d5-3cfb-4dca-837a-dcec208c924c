﻿// FormExtensions.cs

using System;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace eDoktor.AutoComplete.AuthModule.FormExtensions
{
	public static class FormExtensions
	{
		#region P/Invoke
		[DllImport("user32.dll")]
		[return: MarshalAs(UnmanagedType.Bool)]
		static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

		[StructLayout(LayoutKind.Sequential)]
		public struct RECT
		{
			public int Left;
			public int Top;
			public int Right;
			public int Bottom;

			public Rectangle ToRectangle()
			{
				return new Rectangle() { X = Left, Y = Top, Width = Right - Left, Height = Bottom - Top };
			}

			public static RECT ToRect(Rectangle rectangle)
			{
				return new RECT() { Left = rectangle.X, Top = rectangle.Y, Right = rectangle.X + rectangle.Width, Bottom = rectangle.Y + rectangle.Height };
			}
		}
		#endregion

		#region Public Methods
		public static void SetLocationFromDisplayPosition(this AuthFormBase self)
		{
			var authRequest = self.AuthRequest;

			if ((authRequest == null) || (authRequest.DisplayPosition == DisplayPositionType.Unspecified))
			{
				return;
			}

			var displayPosition = authRequest.DisplayPosition;
			var margin = authRequest.Margin;
			var newLocation = Point.Empty;
			var screen = Screen.FromControl(self);

			if (displayPosition == DisplayPositionType.CenterOwner)
			{
				var ownerHandle = authRequest.OwnerHandle;

				if (ownerHandle == IntPtr.Zero)
				{
					return;
				}

				RECT rect;

				if (!GetWindowRect(ownerHandle, out rect))
				{
					return;
				}

				var rectangle = rect.ToRectangle();

				int centerX = rectangle.X + (rectangle.Width / 2);
				int centerY = rectangle.Y + (rectangle.Height / 2);
				newLocation.X = centerX - (self.Width / 2);
				newLocation.Y = centerY - (self.Height / 2);
			}
			else
			{
				switch (authRequest.DisplayPosition)
				{
					case DisplayPositionType.Center:
						newLocation.X = ((screen.WorkingArea.Width - self.Width) / 2);
						newLocation.Y = ((screen.WorkingArea.Height - self.Height) / 2);
						break;
					case DisplayPositionType.TopLeft:
						newLocation.X = margin.Left;
						newLocation.Y = margin.Top;
						break;
					case DisplayPositionType.Top:
						newLocation.X = ((screen.WorkingArea.Width - self.Width) / 2);
						newLocation.Y = margin.Top;
						break;
					case DisplayPositionType.TopRight:
						newLocation.X = screen.WorkingArea.Width - self.Width - margin.Right;
						newLocation.Y = margin.Top;
						break;
					case DisplayPositionType.Right:
						newLocation.X = screen.WorkingArea.Width - self.Width - margin.Right;
						newLocation.Y = ((screen.WorkingArea.Height - self.Height) / 2);
						break;
					case DisplayPositionType.BottomRight:
						newLocation.X = screen.WorkingArea.Width - self.Width - margin.Right;
						newLocation.Y = screen.WorkingArea.Height - self.Height - margin.Bottom;
						break;
					case DisplayPositionType.Bottom:
						newLocation.X = ((screen.WorkingArea.Width - self.Width) / 2);
						newLocation.Y = screen.WorkingArea.Height - self.Height - margin.Bottom;
						break;
					case DisplayPositionType.LeftBottom:
						newLocation.X = margin.Left;
						newLocation.Y = screen.WorkingArea.Height - self.Height - margin.Bottom;
						break;
					case DisplayPositionType.Left:
						newLocation.X = margin.Left;
						newLocation.Y = ((screen.WorkingArea.Height - self.Height) / 2);
						break;
					case DisplayPositionType.Unspecified:
					default:
						return;
				}
			}

			// 余白は左と上を右や下より優先するとし、優先度の低いほうから調整する

			if (newLocation.X + self.Width > screen.WorkingArea.Width - margin.Right)
			{
				newLocation.X = screen.WorkingArea.Width - self.Width - margin.Right;
			}

			if (newLocation.Y + self.Height > screen.WorkingArea.Height - margin.Bottom)
			{
				newLocation.Y = screen.WorkingArea.Height - self.Height - margin.Bottom;
			}

			if (newLocation.X < margin.Left)
			{
				newLocation.X = margin.Left;
			}

			if (newLocation.Y < margin.Top)
			{
				newLocation.Y = margin.Top;
			}

			if (self.Location != newLocation)
			{
				self.Location = newLocation;
			}
		}
		#endregion
	}
}
