﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace eDoktor.AutoComplete.ExternalAuth
{
	public class ExternalAuthClient
    {
        #region Consts
        private const uint WM_CLOSE = 0x0010;
        private const string ChildCaption = "__*AoSMPcfjIHAG9dKF8j0T5g__";
        private const int DefaultTimerForEventCheck = 500;
        private const int ExternalAuthNoExecuting = 0;
        private const int ExternalAuthExecuting = 1;
        private const int ExternalAuthSuccess = 2;
        private const int ExternalAuthCancel = 3;
        private const int ExternalAuthFailure = 4;
        private const int ExternalAuthTimeout = 5;
        #endregion

        #region Fields
        private int _externalAuthState = 0;
        private int _externalAuthKind = 0;
        private readonly object _syncExternalAuthStateRoot = new object();
        private readonly object _syncExternalAuthKindRoot = new object();
        private int _externalFormLoaded = 0;
        private readonly object _syncExternalFormLoaded = new object();
        private int _exitNotification = -1;

        private System.IntPtr _parentFormHandle;
        private System.IntPtr _interfaceHandle;
        private System.Threading.Thread _interfaceThread = null;
        private InterfaceForm _interfaceForm = null;
        private bool _externalAuthExec = false;
        private int _retryCount = 0;
        private string _userId = null;
        private bool _performExternalAuthCalled = false;
        private System.Threading.Timer _stateCheckTimer;
        private readonly object _syncTimerRoot = new object();
        #endregion

        #region P/Invoke
        [System.Runtime.InteropServices.DllImport("user32.dll", SetLastError = true)]
        private static extern System.IntPtr FindWindow(string lpClassName, string lpWindowName);
        [System.Runtime.InteropServices.DllImport("user32.dll", EntryPoint = "SendMessage", CharSet = System.Runtime.InteropServices.CharSet.Unicode)]
        private static extern int SendMessage(IntPtr hwnd, uint msg, IntPtr wParam, IntPtr lParam);
        #endregion

        #region Events
        [System.Runtime.InteropServices.ComVisible(false)]
        public delegate void ExternalAuthStateChangingEventHandler(int state, int externalKind);

        public event ExternalAuthStateChangingEventHandler ExternalAuthStateChanging;
        #endregion

        #region Enum
        private enum DefFormPosition
        {
            TopLeft = 0,
            TopCenter,
            TopRight,
            CenterLeft,
            Center,
            CenterRight,
            BottomLeft,
            BottomCenter,
            BottomRight,
        }
        #endregion

        #region Internal Methods
        /// <summary>
        /// 外部認証プログラムとのインターフェース画面作成後、外部認証プログラムを起動する。
        /// </summary>
        /// <param name="parentWindow">外部認証プログラムの所有者となるウインドウハンドル</param>
        /// <param name="startX">外部認証プログラムの画面表示位置 X(画面の左端上隅の位置を0,0とする時のピクセル単位でのX位置)</param>
        /// <param name="startY">外部認証プログラムの画面表示位置 Y(画面の左端上隅の位置を0,0とする時のピクセル単位でのY位置)</param>
        /// <param name="retryCount">外部認証リトライ回数</param>
        /// <param name="exitNotification">外部認証終了通知方法</param>
        /// <returns>
        /// <para>1:正常に起動しました。</para>
        /// <para>2:既に起動しています。</para>
        /// <para>3:他プロセスで起動中です。</para>
        /// <para>4:起動に失敗しました。</para>
        /// <para>99:システムエラー。</para>
        /// </returns>
        public int Start(int parentWindow, int startX, int startY, int retryCount, int exitNotification)
        {
            int retCode = 0;
            try
            {
                if (this._externalAuthExec != true)
                {
                    this._parentFormHandle = new System.IntPtr(parentWindow);
                    this._retryCount = retryCount;

                    if (CheckExternalAuthExeAlreadyStarting() == true)
                    {
                        retCode = 3;
                    }
                    else
                    {
                        CreateInterfaceForm();
                        if (ExecuteChildProcess(startX, startY, retryCount) == 0)
                        {
                            this._externalAuthExec = true;
                            this._exitNotification = exitNotification;
                            ClearExternalAuthStateEtc();
                            retCode = 1;
                        }
                        else
                        {
                            retCode = 4;
                        }
                    }
                }
                else
                {
                    retCode = 2;
                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
                retCode = 99;
            }
            return retCode;
        }

        /// <summary>
        /// 生体認証処理を開始します。この関数の呼出し前に認証するユーザーIDはQueryUserIdにて存在チェックを行っている必要がある。
        /// </summary>
        /// <returns>
        /// <para>1:正常終了(実行開始しました。)</para>
        /// <para>2:既に実行しています。</para>
        /// <para>3:ユーザーIDが指定されていません。</para>
        /// <para>4:開始処理が呼ばれていません。</para>
        /// <para>99:システムエラー。</para>
        /// </returns>
        public int PerformBioAuth()
        {
            try
            {
                if (_externalAuthExec != true)
                {
                    return 4;
                }

                if (this._performExternalAuthCalled == true)
                {
                    return 2;
                }
                eDoktor.Taikoban.VeinAuthMessage.VeinAuthMessage externalMessage = new eDoktor.Taikoban.VeinAuthMessage.VeinAuthMessage();
                int counter = GetAuthFormLoadCheckTimeout();      // チェックタイムアウト値取得
                System.IntPtr childWindowPtr = WaitUntilChildWindowIsFound(counter);
                if (childWindowPtr == System.IntPtr.Zero)
                {
                    return 4;
                }

                if (this._userId == null)
                {
                    return 3;
                }
                string msg = string.Format("子画面ハンドル={0}", childWindowPtr.ToString());
                eDoktor.Common.Trace.OutputTrace(msg);

                int ret = externalMessage.PerformBioAuth(childWindowPtr);
                if (ret == 1)
                {
                    SetExternalAuthState(ExternalAuthExecuting);
                    //***debug***
                    NativeMethods.RECT rect;
                    NativeMethods.GetWindowRect(childWindowPtr, out rect);
                    System.Drawing.Point pos = GetDispPosition(rect);
                    NativeMethods.SetWindowPos(childWindowPtr, NativeMethods.HWND_TOP, pos.X, pos.Y, 0, 0, NativeMethods.SWP_NOSIZE);
                    //***debug***
                    this._performExternalAuthCalled = true;
                }
                else
                {
                    ret = 99;
                }
                return ret;
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
                return 99;
            }
        }

        /// <summary>
        /// 生体認証を終了させます。
        /// 生体認証プログラム自体は終了しません。
        /// </summary>
        /// <returns>
        /// <para>1:正常終了</para>
        /// <para>2:開始処理が呼ばれていません。</para>
        /// <para>99:システムエラー。</para>
        /// </returns>
        public int TerminateBioAuth()
        {
            try
            {
                if (this._externalAuthExec != true)
                {
                    return 2;
                }

                StopStateCheckTimer();
                eDoktor.Taikoban.VeinAuthMessage.VeinAuthMessage veinMessage = new eDoktor.Taikoban.VeinAuthMessage.VeinAuthMessage();
                System.IntPtr childWindowPtr = FindWindow(null, ChildCaption);
                if (childWindowPtr == System.IntPtr.Zero)
                {
                    return 2;
                }
                string msg = string.Format("子画面ハンドル={0}", childWindowPtr.ToString());
                eDoktor.Common.Trace.OutputTrace(msg);
                ClearExternalAuthStateEtc();
                int ret = veinMessage.TerminateBioAuth(childWindowPtr);
                ret = 1;
                return ret;
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
                return 99;
            }
        }

        /// <summary>
        /// 指定ユーザーIDが存在するかをサーバーに問い合わせます。
        /// </summary>
        /// <param name="userId">ユーザーID</param>
        /// <returns>
        /// <para>1:登録されています。</para>
        /// <para>2:未登録です。</para>
        /// <para>3:認証サーバーに接続できませんでした。</para>
        /// <para>4:ユーザーIDチェックは既に行われています。</para>
        /// <para>5:開始処理が呼ばれていません。</para>
        /// <para>99:システムエラー。</para>
        /// </returns>
        public int QueryUserId(string userId)
        {
            try
            {
                if (_externalAuthExec != true)
                {
                    return 5;
                }

                eDoktor.Taikoban.VeinAuthMessage.VeinAuthMessage veinMessage = new eDoktor.Taikoban.VeinAuthMessage.VeinAuthMessage();
                int counter = GetAuthFormLoadCheckTimeout();      // チェックタイムアウト値取得
                System.IntPtr childWindowPtr = WaitUntilChildWindowIsFound(counter);
                if (childWindowPtr == System.IntPtr.Zero)
                {
                    return 5;
                }

                if (this._userId != null)
                {
                    return 4;
                }
                string msg = string.Format("子画面ハンドル={0}", childWindowPtr.ToString());
                eDoktor.Common.Trace.OutputTrace(msg);

                int ret = veinMessage.QueryUserId(childWindowPtr, userId);
                if (ret <= 0 || ret > 3)
                {
                    return 99;
                }
                if (ret == 1)
                {
                    this._userId = userId;
                    StartStateCheckTimer();
                }
                return ret;
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
                return 99;
            }
        }

        /// <summary>
        /// 現在の生体認証実行状態を取得します。
        /// 
        /// </summary>
        /// <returns></returns>
        public int CheckBioAuthState()
        {
            return GetExternalAuthState();
        }

        /// <summary>
        /// 生体認証プログラムを終了させます。
        /// </summary>
        /// <returns></returns>
        public int End()
        {
            try
            {
                if (this._externalAuthExec != true)
                {
                    // インターフェース画面を終了させる。
                    CloseInterfaceForm();
                    ClearExternalAuthStateEtc();
                    return 2;
                }

                StopStateCheckTimer();
                eDoktor.Taikoban.VeinAuthMessage.VeinAuthMessage veinMessage = new eDoktor.Taikoban.VeinAuthMessage.VeinAuthMessage();
                System.IntPtr childWindowPtr = FindWindow(null, ChildCaption);
                if (childWindowPtr == System.IntPtr.Zero)
                {
                    // インターフェース画面を終了させる。
                    CloseInterfaceForm();
                    ClearExternalAuthStateEtc();
                    return 2;
                }
                this._externalAuthExec = false;
                string msg = string.Format("子画面ハンドル={0}", childWindowPtr.ToString());
                eDoktor.Common.Trace.OutputTrace(msg);
                int ret = veinMessage.End(childWindowPtr);
                ret = 1;

                // インターフェース画面を終了させる。
                CloseInterfaceForm();
                ClearExternalAuthStateEtc();
                return ret;
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
                return 99;
            }
        }
        #endregion

        #region Private Methods
        /// <summary>
        /// 外部認証状態設定
        /// </summary>
        /// <param name="newState">外部認証状態</param>
        public void SetExternalAuthState(int newState)
        {
            lock (_syncExternalAuthStateRoot)
            {
                _externalAuthState = newState;
            }
        }

        /// <summary>
        /// 外部認証状態取得
        /// </summary>
        /// <returns>外部認証状態</returns>
        public int GetExternalAuthState()
        {
            lock (_syncExternalAuthStateRoot)
            {
                return _externalAuthState;
            }
        }

        /// <summary>
        /// 外部認証種類設定
        /// </summary>
        /// <param name="kind">外部認証種類</param>
        public void SetExecutedExternalAuthKind(int kind)
        {
            lock (_syncExternalAuthKindRoot)
            {
                _externalAuthKind = kind;
            }
        }

        /// <summary>
        /// 外部認証種類取得
        /// </summary>
        /// <returns>外部認証種類</returns>
        public int GetExecutedExternalAuthKind()
        {
            lock (_syncExternalAuthKindRoot)
            {
                return _externalAuthKind;
            }
        }

        /// <summary>
        /// 外部認証結果通知タイプ取得
        /// </summary>
        /// <returns>外部認証結果通知タイプ</returns>
        public int GetExitNotificationType()
        {
            return _exitNotification;
        }

        /// <summary>
        /// 外部認証画面ロード状態設定
        /// </summary>
        /// <param name="state">ロード状態</param>
        public void SetExternalFormLoaded(int state)
        {
            lock (_syncExternalFormLoaded)
            {
                _externalFormLoaded = state;
            }
        }

        /// <summary>
        /// 外部認証画面ロード状態取得
        /// </summary>
        /// <returns>ロード状態</returns>
        public int GetExternalFormLoaded()
        {
            lock (_syncExternalFormLoaded)
            {
                return _externalFormLoaded;
            }
        }


        /// <summary>
        /// 外部認証用プロセスが起動されているかチェックする
        /// </summary>
        /// <returns>true:起動されている false:起動されていない</returns>
        private bool CheckExternalAuthExeAlreadyStarting()
        {
            string pgPath = eDoktor.Common.Configuration.AppSetting(string.Format("ChildExe"));
            string processName = System.IO.Path.GetFileName(pgPath);
            if (string.IsNullOrWhiteSpace(processName) == true)
            {
                return false;
            }
            if (processName.Length <= 4)
            {
                return false;
            }
            processName = processName.Substring(0, processName.Length - 4);
            System.Diagnostics.Process[] pses = System.Diagnostics.Process.GetProcessesByName(processName);

            string msg = string.Empty;
            if (pses.Length > 0)
            {
                eDoktor.Common.Trace.OutputTrace("プロセス={0}が見つかりました。", processName);
                System.IntPtr childWindowPtr = FindWindow(null, ChildCaption);
                if (childWindowPtr == System.IntPtr.Zero)
                {
                    eDoktor.Common.Trace.OutputTrace("タイトルが一致するプロセスではありませんでした。");
                    return false;
                }
                else
                {
                    eDoktor.Common.Trace.OutputTrace("タイトルが一致するプロセスでした。");
                    return true;
                }
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// 認証用exeと通信を行う画面スレッドを作成する。
        /// </summary>
        private void CreateInterfaceForm()
        {
            if (this._interfaceThread == null)
            {
                try
                {
                    this._interfaceThread = new System.Threading.Thread(new System.Threading.ThreadStart(this.ExecInterfaceForm));
                    this._interfaceThread.SetApartmentState(System.Threading.ApartmentState.STA);
                    this._interfaceThread.IsBackground = true;
                    this._interfaceThread.Start();
                    System.Threading.Thread.Sleep(0);
                    WaitUntilInterfaceScreenDisplayed();
                }
                catch (Exception ex)
                {
                    eDoktor.Common.Trace.OutputExceptionTrace(ex);
                }
            }
        }

        /// <summary>
        ///インターフェース画面(通信用非表示画面)を作成して受信状態にする。
        /// </summary>
        private void ExecInterfaceForm()
        {
            if (this._interfaceForm == null)
            {
                eDoktor.Common.Trace.OutputTrace("インターフェース画面開始");
                this._interfaceForm = new InterfaceForm(this);
                this._interfaceForm.SetMainWindowHandle(this._parentFormHandle);
                this._interfaceHandle = this._interfaceForm.Handle;
                this._interfaceForm.ShowDialog();
                try
                {
                    this._interfaceForm.Dispose();
                    eDoktor.Common.Trace.OutputTrace("インターフェース画面終了");
                }
                catch (Exception ex)
                {
                    eDoktor.Common.Trace.OutputExceptionTrace(ex);
                }
                this._interfaceForm = null;
                this._interfaceThread = null;
            }
        }

        /// <summary>
        /// インターフェース画面が表示されるまで待機する。最大2秒待機。
        /// </summary>
        private void WaitUntilInterfaceScreenDisplayed()
        {
            try
            {
                int count = 20;
                while (count > 0)
                {
                    System.Threading.Thread.Sleep(100);
                    if (this._interfaceHandle != System.IntPtr.Zero)
                    {
                        break;
                    }
                    count--;
                }
                System.Threading.Thread.Sleep(100);
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 子画面が見つかるまで待機する
        /// </summary>
        /// <param name="counter">待ち合わせ初期カウンター値 50=5秒</param>
        /// <returns>System.IntPtr.Zero:見つかりませんでした。 false:見つかりました。</returns>
        private System.IntPtr WaitUntilChildWindowIsFound(int counter)
        {
            string msg = string.Format("子画面が見つかるまで待機する。 開始カウンター値={0}", counter);
            eDoktor.Common.Trace.OutputTrace(msg);

            System.IntPtr newChildWindowPtr = System.IntPtr.Zero;
            try
            {
                int count = counter;
                while (count > 0)
                {
                    if (GetExternalFormLoaded() != 1)
                    {
                        System.Threading.Thread.Sleep(100);
                        count--;
                        continue;
                    }
                    //***debug***
                    eDoktor.Common.Trace.OutputDebugTrace(ChildCaption);
                    System.IntPtr childWindowPtr = FindWindow(null, ChildCaption);
                    if (childWindowPtr != System.IntPtr.Zero)
                    {
                        msg = string.Format("子画面が見つかりました。カウンター値={0} 子画面のWindowハンドル = {1}。", count, childWindowPtr.ToString());
                        eDoktor.Common.Trace.OutputTrace(msg);
                        return childWindowPtr;
                    }
                    else
                    {
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
            msg = string.Format("子画面が見つかりませんでした。");
            eDoktor.Common.Trace.OutputTrace(msg);
            return System.IntPtr.Zero;
        }

        /// <summary>
        /// 外部認証用EXEを起動する
        /// </summary>
        /// <param name="x">表示場所X</param>
        /// <param name="y">表示場所Y</param>
        /// <param name="retryCount">外部認証のリトライ回数</param>
        /// <returns>結果（0=成功）</returns>
        private int ExecuteChildProcess(int x, int y, int retryCount)
        {
            int rtnCode = 0;
            string handle = ((int)this._parentFormHandle).ToString();
            string interfaceHandle = ((int)this._interfaceHandle).ToString();

            string pgPath = eDoktor.Common.Configuration.AppSetting(string.Format("ChildExe"));
            //string args = string.Format("{0} {1} {2} {3} {4}", handle, interfaceHandle, x, y, retryCount);
            string args = string.Format("{0} {1} {2} {3} {4} {5}", handle, interfaceHandle, x, y, retryCount, ChildCaption);
            eDoktor.Common.Trace.OutputTrace("起動引数 = {0}", args);

            // プログラム起動
            int r = ExecProgarm(pgPath, args, true);
            if (r < -100)
            {
                if (r == -101)
                {
                    rtnCode = -1;
                }
                else if (r == -102)
                {
                    rtnCode = -2;
                }
                else
                {
                    rtnCode = -3;
                }

            }

            return rtnCode;
        }

        /// <summary>
        /// 指定ファイルのプログラムを起動する
        /// </summary>
        /// <param name="fileName">起動プログラムパス</param>
        /// <param name="args">起動プログラムに渡す引数</param>
        /// <param name="createNoWindow">プロセスを新しいウインドウで起動するかどうかを示す true:新しいウインドウは作成しない false:作成する</param>
        /// <returns>
        /// <para>-101:ファイルが見つからない -102:プロセス応答タイムアウト -103:例外発生</para>
        /// <para>上記以外プロセスの戻り値</para></returns>
        private int ExecProgarm(string fileName, string args, bool createNoWindow)
        {
            try
            {
                string msg = string.Empty;
                if ((string.IsNullOrWhiteSpace(fileName)) || (!System.IO.File.Exists(fileName)))
                {
                    msg = string.IsNullOrWhiteSpace(fileName) == true ? "定義されていません" : fileName;
                    eDoktor.Common.Trace.OutputTrace("起動プログラムが見つかりません。fileName={0} ", msg);
                    return -101;
                }

                string arguments = args;
                eDoktor.Common.Trace.OutputTrace("プログラム起動 fileName={0}, arguments={1}", fileName, arguments);

                System.Diagnostics.ProcessStartInfo psi = new System.Diagnostics.ProcessStartInfo();
                psi.FileName = fileName;
                psi.Arguments = arguments;
                psi.CreateNoWindow = createNoWindow;
                psi.UseShellExecute = false;
                int rc = 0;
                using (System.Diagnostics.Process p = System.Diagnostics.Process.Start(psi))
                {
                }
                return rc;
            }
            catch (System.Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
                return -103;
            }
        }




        /// <summary>
        /// 認証状態チェック処理
        /// </summary>
        /// <param name="state">タイマー起動時に設定したチェック用オブジェジュト</param>
        private void ExecStateCheck(object state)
        {
            //eDoktor.Common.Trace.OutputTrace("タイマーコールバック");
            int externalAuthState = GetExternalAuthState();
            int externalAuthKind = GetExecutedExternalAuthKind();
            if (externalAuthState == ExternalAuthNoExecuting || externalAuthState == ExternalAuthExecuting)
            {
                return;
            }
            StopStateCheckTimer();
            try
            {
                eDoktor.Common.Trace.OutputTrace("イベントハンドラが登録されていればイベントを送信する。");
                if (ExternalAuthStateChanging != null)
                {
                    string msg = string.Format("イベント送信します。状態={0} 実行済み生体認証種類={1}", externalAuthState, externalAuthKind);
                    eDoktor.Common.Trace.OutputTrace(msg);
                    ExternalAuthStateChanging(externalAuthState, externalAuthKind);
                    msg = "イベント送信完了";
                    eDoktor.Common.Trace.OutputTrace(msg);
                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
            return;

        }

        /// <summary>
        /// ステータス確認タイマーを開始する
        /// </summary>
        private void StartStateCheckTimer()
        {
            try
            {
                using (eDoktor.Common.TimedLock.Lock(_syncTimerRoot))
                {
                    StopStateCheckTimer();
                    this._stateCheckTimer = new System.Threading.Timer(ExecStateCheck, null, DefaultTimerForEventCheck, DefaultTimerForEventCheck);
                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// ステータス確認タイマーを終了する
        /// </summary>
        private void StopStateCheckTimer()
        {
            try
            {
                using (eDoktor.Common.TimedLock.Lock(_syncTimerRoot))
                {
                    if (this._stateCheckTimer != null)
                    {
                        this._stateCheckTimer.Dispose();
                        this._stateCheckTimer = null;
                    }
                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// インターフェース画面終了処理
        /// </summary>
        private void CloseInterfaceForm()
        {
            try
            {
                if (this._interfaceHandle != System.IntPtr.Zero)
                {
                    eDoktor.Common.Trace.OutputTrace("インターフェース画面を終了します。");
                    SendMessage(this._interfaceHandle, WM_CLOSE, System.IntPtr.Zero, System.IntPtr.Zero);
                    this._interfaceHandle = System.IntPtr.Zero;
                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
            }
        }

        /// <summary>
        /// 外部認証状態などを初期状態にする。
        /// </summary>
        private void ClearExternalAuthStateEtc()
        {
            this._userId = null;
            this._performExternalAuthCalled = false;
            SetExternalAuthState(ExternalAuthNoExecuting);
        }

        /// <summary>
        /// 認証画面のロード確認タイムアウト値を取得する
        /// </summary>
        /// <returns></returns>
        private int GetAuthFormLoadCheckTimeout()
        {
            int timeoutVal = 0;
            try
            {
                timeoutVal = eDoktor.Common.Configuration.AppSetting("AuthFormLoadCheckTimeout", 50);
                if (timeoutVal <= 0)
                {
                    timeoutVal = 1;
                }
                else
                {
                    if (timeoutVal > 200)
                    {
                        timeoutVal = 200;
                    }
                }
            }
            catch (Exception ex)
            {
                eDoktor.Common.Trace.OutputExceptionTrace(ex);
                timeoutVal = 50;
            }
            return timeoutVal;
        }


        #region 画面位置調整
        /// <summary>
        /// 画面の表示位置を決める
        /// </summary>
        /// <returns></returns>
        private System.Drawing.Point GetDispPosition(NativeMethods.RECT rect)
        {
            // 表示位置xy
            int x = 0;
            int y = 0;
            // メインディスプレイサイズ
            int h = System.Windows.Forms.Screen.PrimaryScreen.Bounds.Height;
            int w = System.Windows.Forms.Screen.PrimaryScreen.Bounds.Width;
            // サイズ
            int authWidth = rect.right - rect.left;
            int authHeight = rect.bottom - rect.top;

            // 基本位置
            switch (eDoktor.Common.Configuration.AppSetting("ExternalAuthDispPosition", 0))
            {
                case (int)DefFormPosition.TopLeft:
                    // 左上
                    x = 0;
                    y = 0;
                    break;
                case (int)DefFormPosition.TopCenter:
                    // 中央上
                    x = w / 2 - (authWidth / 2);
                    y = 0;
                    break;
                case (int)DefFormPosition.TopRight:
                    // 右上
                    x = w - authWidth;
                    y = 0;
                    break;
                case (int)DefFormPosition.CenterLeft:
                    // 中央左
                    x = 0;
                    y = h / 2 - (authHeight / 2);
                    break;
                case (int)DefFormPosition.Center:
                    // 中央
                    x = w / 2 - (authWidth / 2);
                    y = h / 2 - (authHeight / 2);
                    break;
                case (int)DefFormPosition.CenterRight:
                    // 中央右
                    x = w - authWidth;
                    y = h / 2 - (authHeight / 2);
                    break;
                case (int)DefFormPosition.BottomLeft:
                    // 左下
                    x = 0;
                    y = h - authHeight;
                    break;
                case (int)DefFormPosition.BottomCenter:
                    // 中央下
                    x = w / 2 - (authWidth / 2);
                    y = h - authHeight;
                    break;
                case (int)DefFormPosition.BottomRight:
                    // 右下
                    x = w - authWidth;
                    y = h - authHeight;
                    break;
                default:
                    x = 0;
                    y = 0;
                    break;
            }

            return new System.Drawing.Point(x, y);
        }
        #endregion
        #endregion
    }
}
