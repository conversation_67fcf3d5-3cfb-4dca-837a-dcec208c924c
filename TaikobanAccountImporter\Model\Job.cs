﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace eDoktor.Taikoban.AccountImporter.Model
{
    class Job
    {
        #region Properties
        public int Id { get; set; }
        public String JobCode { get; set; }
        public String JobName { get; set; }
        public int AccountGroupId { get; set; }
        public int PrivilegeTemplateId { get; set; }
        #endregion

        #region Constructors
        public Job()
        {
            Id = -1;
            JobCode = string.Empty;
            JobName = string.Empty;
            AccountGroupId = -1;
            PrivilegeTemplateId = -1;
        }
        #endregion
    }
}
