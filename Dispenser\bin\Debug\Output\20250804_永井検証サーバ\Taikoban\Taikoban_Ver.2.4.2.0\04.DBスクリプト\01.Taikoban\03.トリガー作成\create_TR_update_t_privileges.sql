USE [$(Param1)]
GO

/****** Object:  Trigger [dbo].[TR_update_t_privileges]    Script Date: 10/06/2014 15:54:05 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*** CREATE TRIGGER ***/
IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[TR_update_t_privileges]') AND type in (N'TR'))
DROP TRIGGER [dbo].[TR_update_t_privileges]
GO

CREATE TRIGGER [dbo].[TR_update_t_privileges] ON [dbo].[t_privileges]
FOR UPDATE
AS 
BEGIN
UPDATE t_privileges
SET modification_datetime = GETDATE()
WHERE id IN (SELECT id FROM inserted)
END
RETURN


GO

