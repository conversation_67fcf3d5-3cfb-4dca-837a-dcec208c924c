<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="sso" type="eDoktor.Auth.Agent.SsoConfigurationSection,eDoktor.Auth.Agent"/>
  </configSections>
  <sso>
    <loginWindows>
      <loginWindow name="ログイン画面" processName="MTOL001D" targetWindowClassName="" targetWindowCaption="ログイン画面" userElementIndex="4" userElementAutomationId="" userElementClassName="" userElementCaption="" passwordElementIndex="3" passwordElementAutomationId="" passwordElementClassName="" passwordElementCaption="" passwordElementIsPassword="false" buttonElementIndex="0" buttonElementAutomationId="" buttonElementClassName="" buttonElementCaption="" buttonClickMethod="1" buttonClickKeyCode="123" buttonClickX="58" buttonClickY="37"/>
      <loginWindow name="従業員メンテ" processName="MMNT0260" targetWindowClassName="" targetWindowCaption="ログイン画面" userElementIndex="4" userElementAutomationId="" userElementClassName="" userElementCaption="" passwordElementIndex="3" passwordElementAutomationId="" passwordElementClassName="" passwordElementCaption="" passwordElementIsPassword="false" buttonElementIndex="0" buttonElementAutomationId="" buttonElementClassName="" buttonElementCaption="" buttonClickMethod="1" buttonClickKeyCode="123" buttonClickX="63" buttonClickY="34"/>
      <loginWindow name="処方せん" processName="MMDG001C" targetWindowClassName="" targetWindowCaption="ログイン画面" userElementIndex="4" userElementAutomationId="" userElementClassName="" userElementCaption="" passwordElementIndex="3" passwordElementAutomationId="" passwordElementClassName="" passwordElementCaption="" passwordElementIsPassword="false" buttonElementIndex="0" buttonElementAutomationId="" buttonElementClassName="" buttonElementCaption="" buttonClickMethod="1" buttonClickKeyCode="123" buttonClickX="63" buttonClickY="34"/>
      <loginWindow name="投薬指導" processName="MDYK001C" targetWindowClassName="" targetWindowCaption="ログイン画面" userElementIndex="4" userElementAutomationId="" userElementClassName="" userElementCaption="" passwordElementIndex="3" passwordElementAutomationId="" passwordElementClassName="" passwordElementCaption="" passwordElementIsPassword="false" buttonElementIndex="0" buttonElementAutomationId="" buttonElementClassName="" buttonElementCaption="" buttonClickMethod="1" buttonClickKeyCode="123" buttonClickX="63" buttonClickY="34"/>
      <loginWindow name="日報確定処理" processName="QDIA0040" targetWindowClassName="" targetWindowCaption="ログイン画面" userElementIndex="4" userElementAutomationId="" userElementClassName="" userElementCaption="" passwordElementIndex="3" passwordElementAutomationId="" passwordElementClassName="" passwordElementCaption="" passwordElementIsPassword="false" buttonElementIndex="0" buttonElementAutomationId="" buttonElementClassName="" buttonElementCaption="" buttonClickMethod="1" buttonClickKeyCode="123" buttonClickX="63" buttonClickY="34"/>
      <loginWindow name="管理ツール" processName="eDoktor.Taikoban.AdminConsole" targetWindowClassName="" targetWindowCaption="ログイン画面" userElementIndex="9" userElementAutomationId="" userElementClassName="" userElementCaption="" passwordElementIndex="14" passwordElementAutomationId="" passwordElementClassName="" passwordElementCaption="" passwordElementIsPassword="true" buttonElementIndex="0" buttonElementAutomationId="" buttonElementClassName="" buttonElementCaption="" buttonClickMethod="1" buttonClickKeyCode="13" buttonClickX="125" buttonClickY="83"/>
	</loginWindows>
  </sso>
  <appSettings>
    <!-- Shortcut Key Combinations -->
    <add key="LockShortcutKeyCombination" value="162|91"/>
    <!-- Context Menu -->
    <add key="TerminalMenuEnabled" value="true"/>
    <add key="AboutMenuEnabled" value="true"/>
    <add key="ExitMenuEnabled" value="true"/>
    <!-- Secure Desktop  -->
    <add key="SecureDesktopEnabled" value="true"/>
    <add key="SecureDesktopMessage" value="デスクトップを準備しています。"/>
    <add key="SecureDesktopPersistenceTimeout" value="10000"/>
    <!-- On-Screen Display -->
    <add key="OSDLockFormat" value="{0}秒後にロックされます。"/>
    <add key="OSDLogoffFormat" value="{0}秒後にログオフします。"/>
    <add key="OSDLogoutFormat" value="{0}秒後にログアウトされます。"/>
    <add key="SystemFaultRecoveryLockEnabled" value="false"/>
    <add key="SystemFaultRecoveryLockMessageFormat" value="障害復旧のため、{0}秒後にロックされます。"/>
    <add key="SystemFaultRecoveryLockCountingSeconds" value="61"/>
    <add key="SystemFaultRecoveryLockDisplaySeconds" value="60"/>
    <add key="IgnoreInvalidCard" value="false"/>
    <!-- DurationInSecondsBeforeLogoutAfterLeavingSeatLock 0=無効 -->
    <add key="DurationInSecondsBeforeLogoutAfterLeavingSeatLock" value="0"/>
    <!-- Auth Module -->
    <add key="AuthModuleAssemblyNameOrPath" value="eDoktor.Auth.AuthUI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=0ff7bf58c14a5b51"/>
    <add key="AuthModuleTypeName" value="eDoktor.Auth.AuthUI.AuthModule"/>
    <add key="AuthModuleTopMost" value="false"/>
    <!-- LoginWindowMonitor -->
    <add key="LoginWindowMonitorMuteTimeout" value="5000"/>
    <add key="LoginWindowMonitorTimerPeriod" value="100"/>
    <add key="LoginWindowMonitorUseFindWindow" value="true"/>
  </appSettings>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0"/></startup></configuration>
